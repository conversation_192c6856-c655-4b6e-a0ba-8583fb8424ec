<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config=":android:lingxia" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/github/LingXia/lingxia-sdk/android/lingxia/build/intermediates/assets/debug/mergeDebugAssets"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/github/LingXia/examples/android/app/src/main/assets"><file name="app.json" path="/Users/<USER>/github/LingXia/examples/android/app/src/main/assets/app.json"/><file name="404.html" path="/Users/<USER>/github/LingXia/examples/android/app/src/main/assets/404.html"/><file name="homelxapp/lxapp.css" path="/Users/<USER>/github/LingXia/examples/android/app/src/main/assets/homelxapp/lxapp.css"/><file name="homelxapp/logic.js" path="/Users/<USER>/github/LingXia/examples/android/app/src/main/assets/homelxapp/logic.js"/><file name="homelxapp/images/futuristic.jpg" path="/Users/<USER>/github/LingXia/examples/android/app/src/main/assets/homelxapp/images/futuristic.jpg"/><file name="homelxapp/images/home.png" path="/Users/<USER>/github/LingXia/examples/android/app/src/main/assets/homelxapp/images/home.png"/><file name="homelxapp/images/todo.png" path="/Users/<USER>/github/LingXia/examples/android/app/src/main/assets/homelxapp/images/todo.png"/><file name="homelxapp/images/todo_selected.png" path="/Users/<USER>/github/LingXia/examples/android/app/src/main/assets/homelxapp/images/todo_selected.png"/><file name="homelxapp/images/api.png" path="/Users/<USER>/github/LingXia/examples/android/app/src/main/assets/homelxapp/images/api.png"/><file name="homelxapp/images/home_selected.png" path="/Users/<USER>/github/LingXia/examples/android/app/src/main/assets/homelxapp/images/home_selected.png"/><file name="homelxapp/lxapp.json" path="/Users/<USER>/github/LingXia/examples/android/app/src/main/assets/homelxapp/lxapp.json"/><file name="homelxapp/pages/home/<USER>" path="/Users/<USER>/github/LingXia/examples/android/app/src/main/assets/homelxapp/pages/home/<USER>"/><file name="homelxapp/pages/home/<USER>" path="/Users/<USER>/github/LingXia/examples/android/app/src/main/assets/homelxapp/pages/home/<USER>"/><file name="homelxapp/pages/home/<USER>" path="/Users/<USER>/github/LingXia/examples/android/app/src/main/assets/homelxapp/pages/home/<USER>"/><file name="homelxapp/pages/todo/index.vue" path="/Users/<USER>/github/LingXia/examples/android/app/src/main/assets/homelxapp/pages/todo/index.vue"/><file name="homelxapp/pages/todo/index.css" path="/Users/<USER>/github/LingXia/examples/android/app/src/main/assets/homelxapp/pages/todo/index.css"/><file name="homelxapp/pages/todo/view.js" path="/Users/<USER>/github/LingXia/examples/android/app/src/main/assets/homelxapp/pages/todo/view.js"/><file name="homelxapp/pages/todo/index.json" path="/Users/<USER>/github/LingXia/examples/android/app/src/main/assets/homelxapp/pages/todo/index.json"/><file name="homelxapp/pages/API/index.tsx" path="/Users/<USER>/github/LingXia/examples/android/app/src/main/assets/homelxapp/pages/API/index.tsx"/><file name="homelxapp/pages/API/index.css" path="/Users/<USER>/github/LingXia/examples/android/app/src/main/assets/homelxapp/pages/API/index.css"/><file name="homelxapp/pages/API/view.js" path="/Users/<USER>/github/LingXia/examples/android/app/src/main/assets/homelxapp/pages/API/view.js"/><file name="homelxapp/pages/API/index.json" path="/Users/<USER>/github/LingXia/examples/android/app/src/main/assets/homelxapp/pages/API/index.json"/><file name="webview-bridge.js" path="/Users/<USER>/github/LingXia/examples/android/app/src/main/assets/webview-bridge.js"/></source></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/github/LingXia/examples/android/app/src/debug/assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="/Users/<USER>/github/LingXia/examples/android/app/build/intermediates/shader_assets/debug/compileDebugShaders/out"/></dataSet></merger>