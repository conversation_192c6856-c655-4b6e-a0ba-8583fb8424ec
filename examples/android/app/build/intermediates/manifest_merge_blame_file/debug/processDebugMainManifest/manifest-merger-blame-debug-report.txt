1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.lingxia.example.lxapp"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="29"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->/Users/<USER>/github/LingXia/examples/android/app/src/main/AndroidManifest.xml:4:4-66
11-->/Users/<USER>/github/LingXia/examples/android/app/src/main/AndroidManifest.xml:4:21-63
12
13    <permission
13-->[androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0d91ac19c8ae0a8ba1ecbbc6d7b079d9/transformed/core-1.10.1/AndroidManifest.xml:22:5-24:47
14        android:name="com.lingxia.example.lxapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
14-->[androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0d91ac19c8ae0a8ba1ecbbc6d7b079d9/transformed/core-1.10.1/AndroidManifest.xml:23:9-81
15        android:protectionLevel="signature" />
15-->[androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0d91ac19c8ae0a8ba1ecbbc6d7b079d9/transformed/core-1.10.1/AndroidManifest.xml:24:9-44
16
17    <uses-permission android:name="com.lingxia.example.lxapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
17-->[androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0d91ac19c8ae0a8ba1ecbbc6d7b079d9/transformed/core-1.10.1/AndroidManifest.xml:26:5-97
17-->[androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0d91ac19c8ae0a8ba1ecbbc6d7b079d9/transformed/core-1.10.1/AndroidManifest.xml:26:22-94
18
19    <application
19-->/Users/<USER>/github/LingXia/examples/android/app/src/main/AndroidManifest.xml:6:5-21:19
20        android:allowBackup="true"
20-->/Users/<USER>/github/LingXia/examples/android/app/src/main/AndroidManifest.xml:7:9-35
21        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
21-->[androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0d91ac19c8ae0a8ba1ecbbc6d7b079d9/transformed/core-1.10.1/AndroidManifest.xml:28:18-86
22        android:debuggable="true"
23        android:extractNativeLibs="false"
24        android:icon="@mipmap/ic_launcher"
24-->/Users/<USER>/github/LingXia/examples/android/app/src/main/AndroidManifest.xml:8:9-43
25        android:label="LingXia"
25-->/Users/<USER>/github/LingXia/examples/android/app/src/main/AndroidManifest.xml:9:9-32
26        android:theme="@style/Theme.AppCompat.Light.NoActionBar" >
26-->/Users/<USER>/github/LingXia/examples/android/app/src/main/AndroidManifest.xml:10:9-65
27        <activity
27-->/Users/<USER>/github/LingXia/examples/android/app/src/main/AndroidManifest.xml:13:9-20:20
28            android:name="com.lingxia.example.lxapp.MainActivity"
28-->/Users/<USER>/github/LingXia/examples/android/app/src/main/AndroidManifest.xml:14:13-41
29            android:exported="true" >
29-->/Users/<USER>/github/LingXia/examples/android/app/src/main/AndroidManifest.xml:15:13-36
30            <intent-filter>
30-->/Users/<USER>/github/LingXia/examples/android/app/src/main/AndroidManifest.xml:16:13-19:29
31                <action android:name="android.intent.action.MAIN" />
31-->/Users/<USER>/github/LingXia/examples/android/app/src/main/AndroidManifest.xml:17:17-69
31-->/Users/<USER>/github/LingXia/examples/android/app/src/main/AndroidManifest.xml:17:25-66
32
33                <category android:name="android.intent.category.LAUNCHER" />
33-->/Users/<USER>/github/LingXia/examples/android/app/src/main/AndroidManifest.xml:18:17-77
33-->/Users/<USER>/github/LingXia/examples/android/app/src/main/AndroidManifest.xml:18:27-74
34            </intent-filter>
35        </activity>
36        <activity
36-->[:android:lingxia] /Users/<USER>/github/LingXia/lingxia-sdk/android/lingxia/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:9-14:72
37            android:name="com.lingxia.lxapp.LxAppActivity"
37-->[:android:lingxia] /Users/<USER>/github/LingXia/lingxia-sdk/android/lingxia/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-59
38            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
38-->[:android:lingxia] /Users/<USER>/github/LingXia/lingxia-sdk/android/lingxia/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:13-87
39            android:exported="false"
39-->[:android:lingxia] /Users/<USER>/github/LingXia/lingxia-sdk/android/lingxia/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:13:13-37
40            android:theme="@style/Theme.AppCompat.Light.NoActionBar" />
40-->[:android:lingxia] /Users/<USER>/github/LingXia/lingxia-sdk/android/lingxia/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:13-69
41
42        <provider
42-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a4bb52afeab294a72a4fa0e19fbd1111/transformed/emoji2-1.2.0/AndroidManifest.xml:24:9-32:20
43            android:name="androidx.startup.InitializationProvider"
43-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a4bb52afeab294a72a4fa0e19fbd1111/transformed/emoji2-1.2.0/AndroidManifest.xml:25:13-67
44            android:authorities="com.lingxia.example.lxapp.androidx-startup"
44-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a4bb52afeab294a72a4fa0e19fbd1111/transformed/emoji2-1.2.0/AndroidManifest.xml:26:13-68
45            android:exported="false" >
45-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a4bb52afeab294a72a4fa0e19fbd1111/transformed/emoji2-1.2.0/AndroidManifest.xml:27:13-37
46            <meta-data
46-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a4bb52afeab294a72a4fa0e19fbd1111/transformed/emoji2-1.2.0/AndroidManifest.xml:29:13-31:52
47                android:name="androidx.emoji2.text.EmojiCompatInitializer"
47-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a4bb52afeab294a72a4fa0e19fbd1111/transformed/emoji2-1.2.0/AndroidManifest.xml:30:17-75
48                android:value="androidx.startup" />
48-->[androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a4bb52afeab294a72a4fa0e19fbd1111/transformed/emoji2-1.2.0/AndroidManifest.xml:31:17-49
49            <meta-data
49-->[androidx.lifecycle:lifecycle-process:2.6.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/48da0d55994ba0b2c509c375efe718f1/transformed/lifecycle-process-2.6.1/AndroidManifest.xml:29:13-31:52
50                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
50-->[androidx.lifecycle:lifecycle-process:2.6.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/48da0d55994ba0b2c509c375efe718f1/transformed/lifecycle-process-2.6.1/AndroidManifest.xml:30:17-78
51                android:value="androidx.startup" />
51-->[androidx.lifecycle:lifecycle-process:2.6.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/48da0d55994ba0b2c509c375efe718f1/transformed/lifecycle-process-2.6.1/AndroidManifest.xml:31:17-49
52            <meta-data
52-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/13c3ee8dc78cb69700ba4b67845ca1f3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:29:13-31:52
53                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
53-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/13c3ee8dc78cb69700ba4b67845ca1f3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:30:17-85
54                android:value="androidx.startup" />
54-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/13c3ee8dc78cb69700ba4b67845ca1f3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:31:17-49
55        </provider>
56
57        <receiver
57-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/13c3ee8dc78cb69700ba4b67845ca1f3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:34:9-52:20
58            android:name="androidx.profileinstaller.ProfileInstallReceiver"
58-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/13c3ee8dc78cb69700ba4b67845ca1f3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:35:13-76
59            android:directBootAware="false"
59-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/13c3ee8dc78cb69700ba4b67845ca1f3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:36:13-44
60            android:enabled="true"
60-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/13c3ee8dc78cb69700ba4b67845ca1f3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:37:13-35
61            android:exported="true"
61-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/13c3ee8dc78cb69700ba4b67845ca1f3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:38:13-36
62            android:permission="android.permission.DUMP" >
62-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/13c3ee8dc78cb69700ba4b67845ca1f3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:39:13-57
63            <intent-filter>
63-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/13c3ee8dc78cb69700ba4b67845ca1f3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:40:13-42:29
64                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
64-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/13c3ee8dc78cb69700ba4b67845ca1f3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:41:17-91
64-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/13c3ee8dc78cb69700ba4b67845ca1f3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:41:25-88
65            </intent-filter>
66            <intent-filter>
66-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/13c3ee8dc78cb69700ba4b67845ca1f3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:43:13-45:29
67                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
67-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/13c3ee8dc78cb69700ba4b67845ca1f3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:44:17-85
67-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/13c3ee8dc78cb69700ba4b67845ca1f3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:44:25-82
68            </intent-filter>
69            <intent-filter>
69-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/13c3ee8dc78cb69700ba4b67845ca1f3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:46:13-48:29
70                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
70-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/13c3ee8dc78cb69700ba4b67845ca1f3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:47:17-88
70-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/13c3ee8dc78cb69700ba4b67845ca1f3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:47:25-85
71            </intent-filter>
72            <intent-filter>
72-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/13c3ee8dc78cb69700ba4b67845ca1f3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:49:13-51:29
73                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
73-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/13c3ee8dc78cb69700ba4b67845ca1f3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:50:17-95
73-->[androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/13c3ee8dc78cb69700ba4b67845ca1f3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:50:25-92
74            </intent-filter>
75        </receiver>
76    </application>
77
78</manifest>
