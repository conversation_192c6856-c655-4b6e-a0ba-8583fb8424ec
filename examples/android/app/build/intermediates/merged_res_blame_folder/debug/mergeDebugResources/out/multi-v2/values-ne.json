{"logs": [{"outputFile": "com.lingxia.example.lxapp-mergeDebugResources-29:/values-ne/values-ne.xml", "map": [{"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/0d91ac19c8ae0a8ba1ecbbc6d7b079d9/transformed/core-1.10.1/res/values-ne/values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,261,363,469,567,667,775", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "153,256,358,464,562,662,770,871"}, "to": {"startLines": "38,39,40,41,42,43,44,109", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3515,3618,3721,3823,3929,4027,4127,9438", "endColumns": "102,102,101,105,97,99,107,100", "endOffsets": "3613,3716,3818,3924,4022,4122,4230,9534"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/4643b75b1daab90ab156003819617d74/transformed/material-1.10.0/res/values-ne/values-ne.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,262,351,439,521,616,705,807,917,1004,1070,1166,1232,1293,1398,1462,1534,1592,1666,1728,1782,1895,1955,2016,2075,2153,2277,2358,2443,2579,2660,2743,2874,2957,3043,3105,3159,3225,3302,3381,3469,3552,3621,3697,3778,3846,3950,4041,4119,4212,4309,4383,4462,4560,4620,4708,4774,4862,4950,5012,5080,5143,5209,5314,5420,5515,5620,5686,5744", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,88,87,81,94,88,101,109,86,65,95,65,60,104,63,71,57,73,61,53,112,59,60,58,77,123,80,84,135,80,82,130,82,85,61,53,65,76,78,87,82,68,75,80,67,103,90,77,92,96,73,78,97,59,87,65,87,87,61,67,62,65,104,105,94,104,65,57,83", "endOffsets": "257,346,434,516,611,700,802,912,999,1065,1161,1227,1288,1393,1457,1529,1587,1661,1723,1777,1890,1950,2011,2070,2148,2272,2353,2438,2574,2655,2738,2869,2952,3038,3100,3154,3220,3297,3376,3464,3547,3616,3692,3773,3841,3945,4036,4114,4207,4304,4378,4457,4555,4615,4703,4769,4857,4945,5007,5075,5138,5204,5309,5415,5510,5615,5681,5739,5823"}, "to": {"startLines": "2,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3072,3161,3249,3331,3426,4235,4337,4447,4534,4600,4696,4762,4823,4928,4992,5064,5122,5196,5258,5312,5425,5485,5546,5605,5683,5807,5888,5973,6109,6190,6273,6404,6487,6573,6635,6689,6755,6832,6911,6999,7082,7151,7227,7308,7376,7480,7571,7649,7742,7839,7913,7992,8090,8150,8238,8304,8392,8480,8542,8610,8673,8739,8844,8950,9045,9150,9216,9274", "endLines": "5,33,34,35,36,37,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107", "endColumns": "12,88,87,81,94,88,101,109,86,65,95,65,60,104,63,71,57,73,61,53,112,59,60,58,77,123,80,84,135,80,82,130,82,85,61,53,65,76,78,87,82,68,75,80,67,103,90,77,92,96,73,78,97,59,87,65,87,87,61,67,62,65,104,105,94,104,65,57,83", "endOffsets": "307,3156,3244,3326,3421,3510,4332,4442,4529,4595,4691,4757,4818,4923,4987,5059,5117,5191,5253,5307,5420,5480,5541,5600,5678,5802,5883,5968,6104,6185,6268,6399,6482,6568,6630,6684,6750,6827,6906,6994,7077,7146,7222,7303,7371,7475,7566,7644,7737,7834,7908,7987,8085,8145,8233,8299,8387,8475,8537,8605,8668,8734,8839,8945,9040,9145,9211,9269,9353"}}, {"source": "/Users/<USER>/.gradle/caches/8.11.1/transforms/c7b0afe7c3458fc2740c9e83afd87484/transformed/appcompat-1.6.1/res/values-ne/values-ne.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,214,325,433,524,631,751,835,914,1005,1098,1193,1287,1387,1480,1575,1669,1760,1851,1937,2050,2151,2247,2360,2470,2587,2754,2865", "endColumns": "108,110,107,90,106,119,83,78,90,92,94,93,99,92,94,93,90,90,85,112,100,95,112,109,116,166,110,79", "endOffsets": "209,320,428,519,626,746,830,909,1000,1093,1188,1282,1382,1475,1570,1664,1755,1846,1932,2045,2146,2242,2355,2465,2582,2749,2860,2940"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "312,421,532,640,731,838,958,1042,1121,1212,1305,1400,1494,1594,1687,1782,1876,1967,2058,2144,2257,2358,2454,2567,2677,2794,2961,9358", "endColumns": "108,110,107,90,106,119,83,78,90,92,94,93,99,92,94,93,90,90,85,112,100,95,112,109,116,166,110,79", "endOffsets": "416,527,635,726,833,953,1037,1116,1207,1300,1395,1489,1589,1682,1777,1871,1962,2053,2139,2252,2353,2449,2562,2672,2789,2956,3067,9433"}}]}]}