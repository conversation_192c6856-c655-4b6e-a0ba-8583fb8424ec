[{"merged": "com.lingxia.example.lxapp-debug-31:/mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "com.lingxia.example.lxapp-main-33:/mipmap-xxxhdpi/ic_launcher.webp"}, {"merged": "com.lingxia.example.lxapp-debug-31:/mipmap-anydpi_ic_launcher_round.xml.flat", "source": "com.lingxia.example.lxapp-main-33:/mipmap-anydpi/ic_launcher_round.xml"}, {"merged": "com.lingxia.example.lxapp-debug-31:/mipmap-mdpi_ic_launcher.webp.flat", "source": "com.lingxia.example.lxapp-main-33:/mipmap-mdpi/ic_launcher.webp"}, {"merged": "com.lingxia.example.lxapp-debug-31:/mipmap-hdpi_ic_launcher.webp.flat", "source": "com.lingxia.example.lxapp-main-33:/mipmap-hdpi/ic_launcher.webp"}, {"merged": "com.lingxia.example.lxapp-debug-31:/mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "com.lingxia.example.lxapp-main-33:/mipmap-xxhdpi/ic_launcher_round.webp"}, {"merged": "com.lingxia.example.lxapp-debug-31:/drawable_ic_launcher_foreground.xml.flat", "source": "com.lingxia.example.lxapp-main-33:/drawable/ic_launcher_foreground.xml"}, {"merged": "com.lingxia.example.lxapp-debug-31:/mipmap-xxhdpi_ic_launcher.webp.flat", "source": "com.lingxia.example.lxapp-main-33:/mipmap-xxhdpi/ic_launcher.webp"}, {"merged": "com.lingxia.example.lxapp-debug-31:/mipmap-xhdpi_ic_launcher.webp.flat", "source": "com.lingxia.example.lxapp-main-33:/mipmap-xhdpi/ic_launcher.webp"}, {"merged": "com.lingxia.example.lxapp-debug-31:/mipmap-hdpi_ic_launcher_round.webp.flat", "source": "com.lingxia.example.lxapp-main-33:/mipmap-hdpi/ic_launcher_round.webp"}, {"merged": "com.lingxia.example.lxapp-debug-31:/mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "com.lingxia.example.lxapp-main-33:/mipmap-xhdpi/ic_launcher_round.webp"}, {"merged": "com.lingxia.example.lxapp-debug-31:/drawable_ic_launcher_background.xml.flat", "source": "com.lingxia.example.lxapp-main-33:/drawable/ic_launcher_background.xml"}, {"merged": "com.lingxia.example.lxapp-debug-31:/mipmap-mdpi_ic_launcher_round.webp.flat", "source": "com.lingxia.example.lxapp-main-33:/mipmap-mdpi/ic_launcher_round.webp"}, {"merged": "com.lingxia.example.lxapp-debug-31:/mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "com.lingxia.example.lxapp-main-33:/mipmap-xxxhdpi/ic_launcher_round.webp"}, {"merged": "com.lingxia.example.lxapp-debug-31:/mipmap-anydpi_ic_launcher.xml.flat", "source": "com.lingxia.example.lxapp-main-33:/mipmap-anydpi/ic_launcher.xml"}]