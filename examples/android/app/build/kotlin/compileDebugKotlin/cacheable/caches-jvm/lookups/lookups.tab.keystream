  Log android.app.Activity  LxApp android.app.Activity  
LxAppActivity android.app.Activity  configureTransparentSystemBars android.app.Activity  finish android.app.Activity  
initialize android.app.Activity  onCreate android.app.Activity  
openHomeLxApp android.app.Activity  Log android.content.Context  LxApp android.content.Context  
LxAppActivity android.content.Context  configureTransparentSystemBars android.content.Context  
initialize android.content.Context  
openHomeLxApp android.content.Context  Log android.content.ContextWrapper  LxApp android.content.ContextWrapper  
LxAppActivity android.content.ContextWrapper  configureTransparentSystemBars android.content.ContextWrapper  
initialize android.content.ContextWrapper  
openHomeLxApp android.content.ContextWrapper  Bundle 
android.os  Log android.util  d android.util.Log  Log  android.view.ContextThemeWrapper  LxApp  android.view.ContextThemeWrapper  
LxAppActivity  android.view.ContextThemeWrapper  configureTransparentSystemBars  android.view.ContextThemeWrapper  
initialize  android.view.ContextThemeWrapper  
openHomeLxApp  android.view.ContextThemeWrapper  Log #androidx.activity.ComponentActivity  LxApp #androidx.activity.ComponentActivity  
LxAppActivity #androidx.activity.ComponentActivity  configureTransparentSystemBars #androidx.activity.ComponentActivity  
initialize #androidx.activity.ComponentActivity  
openHomeLxApp #androidx.activity.ComponentActivity  AppCompatActivity androidx.appcompat.app  Log (androidx.appcompat.app.AppCompatActivity  LxApp (androidx.appcompat.app.AppCompatActivity  
LxAppActivity (androidx.appcompat.app.AppCompatActivity  configureTransparentSystemBars (androidx.appcompat.app.AppCompatActivity  
initialize (androidx.appcompat.app.AppCompatActivity  onCreate (androidx.appcompat.app.AppCompatActivity  
openHomeLxApp (androidx.appcompat.app.AppCompatActivity  Log #androidx.core.app.ComponentActivity  LxApp #androidx.core.app.ComponentActivity  
LxAppActivity #androidx.core.app.ComponentActivity  configureTransparentSystemBars #androidx.core.app.ComponentActivity  
initialize #androidx.core.app.ComponentActivity  
openHomeLxApp #androidx.core.app.ComponentActivity  Log &androidx.fragment.app.FragmentActivity  LxApp &androidx.fragment.app.FragmentActivity  
LxAppActivity &androidx.fragment.app.FragmentActivity  configureTransparentSystemBars &androidx.fragment.app.FragmentActivity  
initialize &androidx.fragment.app.FragmentActivity  onCreate &androidx.fragment.app.FragmentActivity  
openHomeLxApp &androidx.fragment.app.FragmentActivity  AppCompatActivity com.lingxia.example.lxapp  Bundle com.lingxia.example.lxapp  Log com.lingxia.example.lxapp  LxApp com.lingxia.example.lxapp  
LxAppActivity com.lingxia.example.lxapp  MainActivity com.lingxia.example.lxapp  configureTransparentSystemBars com.lingxia.example.lxapp  
initialize com.lingxia.example.lxapp  
openHomeLxApp com.lingxia.example.lxapp  Log &com.lingxia.example.lxapp.MainActivity  LxApp &com.lingxia.example.lxapp.MainActivity  
LxAppActivity &com.lingxia.example.lxapp.MainActivity  TAG &com.lingxia.example.lxapp.MainActivity  configureTransparentSystemBars &com.lingxia.example.lxapp.MainActivity  finish &com.lingxia.example.lxapp.MainActivity  
initialize &com.lingxia.example.lxapp.MainActivity  
openHomeLxApp &com.lingxia.example.lxapp.MainActivity  LxApp com.lingxia.lxapp  
LxAppActivity com.lingxia.lxapp  	Companion com.lingxia.lxapp.LxApp  
initialize com.lingxia.lxapp.LxApp  
openHomeLxApp com.lingxia.lxapp.LxApp  
initialize !com.lingxia.lxapp.LxApp.Companion  
openHomeLxApp !com.lingxia.lxapp.LxApp.Companion  	Companion com.lingxia.lxapp.LxAppActivity  configureTransparentSystemBars com.lingxia.lxapp.LxAppActivity  configureTransparentSystemBars )com.lingxia.lxapp.LxAppActivity.Companion                                                                                                                                                                                                         