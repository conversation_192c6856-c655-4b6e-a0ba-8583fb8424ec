-- Merging decision tree log ---
manifest
ADDED from /Users/<USER>/github/LingXia/examples/android/app/src/main/AndroidManifest.xml:2:1-23:12
INJECTED from /Users/<USER>/github/LingXia/examples/android/app/src/main/AndroidManifest.xml:2:1-23:12
INJECTED from /Users/<USER>/github/LingXia/examples/android/app/src/main/AndroidManifest.xml:2:1-23:12
INJECTED from /Users/<USER>/github/LingXia/examples/android/app/src/main/AndroidManifest.xml:2:1-23:12
MERGED from [:android:lingxia] /Users/<USER>/github/LingXia/lingxia-sdk/android/lingxia/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:2:1-17:12
MERGED from [com.google.android.material:material:1.10.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/4643b75b1daab90ab156003819617d74/transformed/material-1.10.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/66dbf15d4154265b76933e82f27f9540/transformed/appcompat-resources-1.6.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/ad7bcfe34e97adfa411aa950406132d4/transformed/constraintlayout-2.0.1/AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat:1.6.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/c7b0afe7c3458fc2740c9e83afd87484/transformed/appcompat-1.6.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/8e4026d624e03c57f931374d44d2764f/transformed/emoji2-views-helper-1.2.0/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a4bb52afeab294a72a4fa0e19fbd1111/transformed/emoji2-1.2.0/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a737e9fe22889a7d3a192b9633498e00/transformed/viewpager2-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.3.6] /Users/<USER>/.gradle/caches/8.11.1/transforms/ba9e820325d4126ef7f547ed757b9795/transformed/fragment-1.3.6/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.8.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/91299c07976fbf18edffa3614bea55af/transformed/activity-1.8.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/80b892cd732eeab12c8775ff272e189b/transformed/drawerlayout-1.1.1/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/91a9962d3e0141d5b5d9eb8a9469e9c7/transformed/coordinatorlayout-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/b6ee7b9f67e39ce3a6278804582be556/transformed/dynamicanimation-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.transition:transition:1.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/74c931671939f7f1d94292488e4f207f/transformed/transition-1.2.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/0895ffaa7a7efd3ee2476922070deb08/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/d2837e1457cd20f6e2691c0ae81a779d/transformed/vectordrawable-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/23e01b57f1777f0fefbb7b513a0529aa/transformed/viewpager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/443be98b022af6b407b5972557483c6c/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/46399dd644a29cd25ee820a4be97cf95/transformed/loader-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/9d618f46ccac580f5d66116475fa8abd/transformed/recyclerview-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/d4bf6f009d8edac1ad0a06197978fff6/transformed/customview-1.1.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0d91ac19c8ae0a8ba1ecbbc6d7b079d9/transformed/core-1.10.1/AndroidManifest.xml:17:1-30:12
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/5310e95640327131de0b263e4766a72b/transformed/savedstate-1.2.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/48da0d55994ba0b2c509c375efe718f1/transformed/lifecycle-process-2.6.1/AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/5f12c256480d63f12f7431f5fa8f89f8/transformed/lifecycle-livedata-core-2.6.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/dfa682a6d300c1861f3945535e3ba570/transformed/lifecycle-livedata-2.6.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/5e572d76a6c5980bd15512b87394f523/transformed/lifecycle-viewmodel-2.6.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/3244d0e2f528340ea8d32916426b367e/transformed/lifecycle-runtime-2.6.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/43dc294937bd9d3a124414df04b6cf97/transformed/lifecycle-viewmodel-savedstate-2.6.1/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.10.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/78d2d38befa7f6743d0bd68fef4f1c98/transformed/core-ktx-1.10.1/AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/61bed10104e4b2606290d5f831958f8b/transformed/annotation-experimental-1.3.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/fcd1ff6e096483b4ec912617624c4346/transformed/cursoradapter-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/ba22826f7658ec9d23c92079e71fd4bf/transformed/cardview-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/b59298e4944666a81389a1aaaa3d0b19/transformed/interpolator-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/dc5a3c3a86554c2634733269bd64983b/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:17:1-27:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/13c3ee8dc78cb69700ba4b67845ca1f3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/82e29ce0dbd967999c23d05d90e9d331/transformed/startup-runtime-1.1.1/AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/1090ac4b94afca9b5fb61ee3bf9cc58b/transformed/tracing-1.0.0/AndroidManifest.xml:17:1-24:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/74a5476c6fe10ca5478b5b82c9b0b27b/transformed/core-runtime-2.2.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a8aa93528f7533280f5c9d3a24bc6916/transformed/documentfile-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/6ce592aeefb8798e129bf39a79096809/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/fbccbd61157947704cf2c158edc316fc/transformed/print-1.0.0/AndroidManifest.xml:17:1-22:12
	package
		INJECTED from /Users/<USER>/github/LingXia/examples/android/app/src/main/AndroidManifest.xml
	android:versionName
		INJECTED from /Users/<USER>/github/LingXia/examples/android/app/src/main/AndroidManifest.xml
	xmlns:tools
		ADDED from /Users/<USER>/github/LingXia/examples/android/app/src/main/AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from /Users/<USER>/github/LingXia/examples/android/app/src/main/AndroidManifest.xml
	xmlns:android
		ADDED from /Users/<USER>/github/LingXia/examples/android/app/src/main/AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from /Users/<USER>/github/LingXia/examples/android/app/src/main/AndroidManifest.xml:4:4-66
MERGED from [:android:lingxia] /Users/<USER>/github/LingXia/lingxia-sdk/android/lingxia/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-67
MERGED from [:android:lingxia] /Users/<USER>/github/LingXia/lingxia-sdk/android/lingxia/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:7:5-67
	android:name
		ADDED from /Users/<USER>/github/LingXia/examples/android/app/src/main/AndroidManifest.xml:4:21-63
application
ADDED from /Users/<USER>/github/LingXia/examples/android/app/src/main/AndroidManifest.xml:6:5-21:19
INJECTED from /Users/<USER>/github/LingXia/examples/android/app/src/main/AndroidManifest.xml:6:5-21:19
MERGED from [:android:lingxia] /Users/<USER>/github/LingXia/lingxia-sdk/android/lingxia/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:5-15:19
MERGED from [:android:lingxia] /Users/<USER>/github/LingXia/lingxia-sdk/android/lingxia/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:9:5-15:19
MERGED from [com.google.android.material:material:1.10.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/4643b75b1daab90ab156003819617d74/transformed/material-1.10.0/AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.10.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/4643b75b1daab90ab156003819617d74/transformed/material-1.10.0/AndroidManifest.xml:22:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/ad7bcfe34e97adfa411aa950406132d4/transformed/constraintlayout-2.0.1/AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/ad7bcfe34e97adfa411aa950406132d4/transformed/constraintlayout-2.0.1/AndroidManifest.xml:9:5-20
MERGED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a4bb52afeab294a72a4fa0e19fbd1111/transformed/emoji2-1.2.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a4bb52afeab294a72a4fa0e19fbd1111/transformed/emoji2-1.2.0/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0d91ac19c8ae0a8ba1ecbbc6d7b079d9/transformed/core-1.10.1/AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0d91ac19c8ae0a8ba1ecbbc6d7b079d9/transformed/core-1.10.1/AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/48da0d55994ba0b2c509c375efe718f1/transformed/lifecycle-process-2.6.1/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/48da0d55994ba0b2c509c375efe718f1/transformed/lifecycle-process-2.6.1/AndroidManifest.xml:23:5-33:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/dc5a3c3a86554c2634733269bd64983b/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/dc5a3c3a86554c2634733269bd64983b/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/13c3ee8dc78cb69700ba4b67845ca1f3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/13c3ee8dc78cb69700ba4b67845ca1f3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/82e29ce0dbd967999c23d05d90e9d331/transformed/startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/82e29ce0dbd967999c23d05d90e9d331/transformed/startup-runtime-1.1.1/AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from /Users/<USER>/github/LingXia/examples/android/app/src/main/AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0d91ac19c8ae0a8ba1ecbbc6d7b079d9/transformed/core-1.10.1/AndroidManifest.xml:28:18-86
	android:label
		ADDED from /Users/<USER>/github/LingXia/examples/android/app/src/main/AndroidManifest.xml:9:9-32
	tools:targetApi
		ADDED from /Users/<USER>/github/LingXia/examples/android/app/src/main/AndroidManifest.xml:11:9-29
	android:icon
		ADDED from /Users/<USER>/github/LingXia/examples/android/app/src/main/AndroidManifest.xml:8:9-43
	android:allowBackup
		ADDED from /Users/<USER>/github/LingXia/examples/android/app/src/main/AndroidManifest.xml:7:9-35
	android:theme
		ADDED from /Users/<USER>/github/LingXia/examples/android/app/src/main/AndroidManifest.xml:10:9-65
activity#com.lingxia.example.lxapp.MainActivity
ADDED from /Users/<USER>/github/LingXia/examples/android/app/src/main/AndroidManifest.xml:13:9-20:20
	android:exported
		ADDED from /Users/<USER>/github/LingXia/examples/android/app/src/main/AndroidManifest.xml:15:13-36
	android:name
		ADDED from /Users/<USER>/github/LingXia/examples/android/app/src/main/AndroidManifest.xml:14:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from /Users/<USER>/github/LingXia/examples/android/app/src/main/AndroidManifest.xml:16:13-19:29
action#android.intent.action.MAIN
ADDED from /Users/<USER>/github/LingXia/examples/android/app/src/main/AndroidManifest.xml:17:17-69
	android:name
		ADDED from /Users/<USER>/github/LingXia/examples/android/app/src/main/AndroidManifest.xml:17:25-66
category#android.intent.category.LAUNCHER
ADDED from /Users/<USER>/github/LingXia/examples/android/app/src/main/AndroidManifest.xml:18:17-77
	android:name
		ADDED from /Users/<USER>/github/LingXia/examples/android/app/src/main/AndroidManifest.xml:18:27-74
uses-sdk
INJECTED from /Users/<USER>/github/LingXia/examples/android/app/src/main/AndroidManifest.xml reason: use-sdk injection requested
INJECTED from /Users/<USER>/github/LingXia/examples/android/app/src/main/AndroidManifest.xml
INJECTED from /Users/<USER>/github/LingXia/examples/android/app/src/main/AndroidManifest.xml
MERGED from [:android:lingxia] /Users/<USER>/github/LingXia/lingxia-sdk/android/lingxia/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [:android:lingxia] /Users/<USER>/github/LingXia/lingxia-sdk/android/lingxia/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:5:5-44
MERGED from [com.google.android.material:material:1.10.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/4643b75b1daab90ab156003819617d74/transformed/material-1.10.0/AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.10.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/4643b75b1daab90ab156003819617d74/transformed/material-1.10.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/66dbf15d4154265b76933e82f27f9540/transformed/appcompat-resources-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/66dbf15d4154265b76933e82f27f9540/transformed/appcompat-resources-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/ad7bcfe34e97adfa411aa950406132d4/transformed/constraintlayout-2.0.1/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/ad7bcfe34e97adfa411aa950406132d4/transformed/constraintlayout-2.0.1/AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat:1.6.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/c7b0afe7c3458fc2740c9e83afd87484/transformed/appcompat-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/c7b0afe7c3458fc2740c9e83afd87484/transformed/appcompat-1.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/8e4026d624e03c57f931374d44d2764f/transformed/emoji2-views-helper-1.2.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/8e4026d624e03c57f931374d44d2764f/transformed/emoji2-views-helper-1.2.0/AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a4bb52afeab294a72a4fa0e19fbd1111/transformed/emoji2-1.2.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a4bb52afeab294a72a4fa0e19fbd1111/transformed/emoji2-1.2.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a737e9fe22889a7d3a192b9633498e00/transformed/viewpager2-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a737e9fe22889a7d3a192b9633498e00/transformed/viewpager2-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] /Users/<USER>/.gradle/caches/8.11.1/transforms/ba9e820325d4126ef7f547ed757b9795/transformed/fragment-1.3.6/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] /Users/<USER>/.gradle/caches/8.11.1/transforms/ba9e820325d4126ef7f547ed757b9795/transformed/fragment-1.3.6/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.8.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/91299c07976fbf18edffa3614bea55af/transformed/activity-1.8.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/91299c07976fbf18edffa3614bea55af/transformed/activity-1.8.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/80b892cd732eeab12c8775ff272e189b/transformed/drawerlayout-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/80b892cd732eeab12c8775ff272e189b/transformed/drawerlayout-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/91a9962d3e0141d5b5d9eb8a9469e9c7/transformed/coordinatorlayout-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/91a9962d3e0141d5b5d9eb8a9469e9c7/transformed/coordinatorlayout-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/b6ee7b9f67e39ce3a6278804582be556/transformed/dynamicanimation-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/b6ee7b9f67e39ce3a6278804582be556/transformed/dynamicanimation-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.transition:transition:1.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/74c931671939f7f1d94292488e4f207f/transformed/transition-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/74c931671939f7f1d94292488e4f207f/transformed/transition-1.2.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/0895ffaa7a7efd3ee2476922070deb08/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/0895ffaa7a7efd3ee2476922070deb08/transformed/vectordrawable-animated-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/d2837e1457cd20f6e2691c0ae81a779d/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/d2837e1457cd20f6e2691c0ae81a779d/transformed/vectordrawable-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/23e01b57f1777f0fefbb7b513a0529aa/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/23e01b57f1777f0fefbb7b513a0529aa/transformed/viewpager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/443be98b022af6b407b5972557483c6c/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/443be98b022af6b407b5972557483c6c/transformed/legacy-support-core-utils-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/46399dd644a29cd25ee820a4be97cf95/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/46399dd644a29cd25ee820a4be97cf95/transformed/loader-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/9d618f46ccac580f5d66116475fa8abd/transformed/recyclerview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/9d618f46ccac580f5d66116475fa8abd/transformed/recyclerview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/d4bf6f009d8edac1ad0a06197978fff6/transformed/customview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/d4bf6f009d8edac1ad0a06197978fff6/transformed/customview-1.1.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0d91ac19c8ae0a8ba1ecbbc6d7b079d9/transformed/core-1.10.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0d91ac19c8ae0a8ba1ecbbc6d7b079d9/transformed/core-1.10.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/5310e95640327131de0b263e4766a72b/transformed/savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/5310e95640327131de0b263e4766a72b/transformed/savedstate-1.2.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/48da0d55994ba0b2c509c375efe718f1/transformed/lifecycle-process-2.6.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/48da0d55994ba0b2c509c375efe718f1/transformed/lifecycle-process-2.6.1/AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/5f12c256480d63f12f7431f5fa8f89f8/transformed/lifecycle-livedata-core-2.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/5f12c256480d63f12f7431f5fa8f89f8/transformed/lifecycle-livedata-core-2.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/dfa682a6d300c1861f3945535e3ba570/transformed/lifecycle-livedata-2.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/dfa682a6d300c1861f3945535e3ba570/transformed/lifecycle-livedata-2.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/5e572d76a6c5980bd15512b87394f523/transformed/lifecycle-viewmodel-2.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/5e572d76a6c5980bd15512b87394f523/transformed/lifecycle-viewmodel-2.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/3244d0e2f528340ea8d32916426b367e/transformed/lifecycle-runtime-2.6.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/3244d0e2f528340ea8d32916426b367e/transformed/lifecycle-runtime-2.6.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/43dc294937bd9d3a124414df04b6cf97/transformed/lifecycle-viewmodel-savedstate-2.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/43dc294937bd9d3a124414df04b6cf97/transformed/lifecycle-viewmodel-savedstate-2.6.1/AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.10.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/78d2d38befa7f6743d0bd68fef4f1c98/transformed/core-ktx-1.10.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.10.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/78d2d38befa7f6743d0bd68fef4f1c98/transformed/core-ktx-1.10.1/AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/61bed10104e4b2606290d5f831958f8b/transformed/annotation-experimental-1.3.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/61bed10104e4b2606290d5f831958f8b/transformed/annotation-experimental-1.3.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/fcd1ff6e096483b4ec912617624c4346/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/fcd1ff6e096483b4ec912617624c4346/transformed/cursoradapter-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/ba22826f7658ec9d23c92079e71fd4bf/transformed/cardview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/ba22826f7658ec9d23c92079e71fd4bf/transformed/cardview-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/b59298e4944666a81389a1aaaa3d0b19/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/b59298e4944666a81389a1aaaa3d0b19/transformed/interpolator-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/dc5a3c3a86554c2634733269bd64983b/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/dc5a3c3a86554c2634733269bd64983b/transformed/versionedparcelable-1.1.1/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/13c3ee8dc78cb69700ba4b67845ca1f3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/13c3ee8dc78cb69700ba4b67845ca1f3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/82e29ce0dbd967999c23d05d90e9d331/transformed/startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/82e29ce0dbd967999c23d05d90e9d331/transformed/startup-runtime-1.1.1/AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/1090ac4b94afca9b5fb61ee3bf9cc58b/transformed/tracing-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/1090ac4b94afca9b5fb61ee3bf9cc58b/transformed/tracing-1.0.0/AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/74a5476c6fe10ca5478b5b82c9b0b27b/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/74a5476c6fe10ca5478b5b82c9b0b27b/transformed/core-runtime-2.2.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a8aa93528f7533280f5c9d3a24bc6916/transformed/documentfile-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a8aa93528f7533280f5c9d3a24bc6916/transformed/documentfile-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/6ce592aeefb8798e129bf39a79096809/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/6ce592aeefb8798e129bf39a79096809/transformed/localbroadcastmanager-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/fbccbd61157947704cf2c158edc316fc/transformed/print-1.0.0/AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/fbccbd61157947704cf2c158edc316fc/transformed/print-1.0.0/AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from /Users/<USER>/github/LingXia/examples/android/app/src/main/AndroidManifest.xml
	android:minSdkVersion
		INJECTED from /Users/<USER>/github/LingXia/examples/android/app/src/main/AndroidManifest.xml
activity#com.lingxia.lxapp.LxAppActivity
ADDED from [:android:lingxia] /Users/<USER>/github/LingXia/lingxia-sdk/android/lingxia/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:10:9-14:72
	android:exported
		ADDED from [:android:lingxia] /Users/<USER>/github/LingXia/lingxia-sdk/android/lingxia/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:13:13-37
	android:configChanges
		ADDED from [:android:lingxia] /Users/<USER>/github/LingXia/lingxia-sdk/android/lingxia/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:12:13-87
	android:theme
		ADDED from [:android:lingxia] /Users/<USER>/github/LingXia/lingxia-sdk/android/lingxia/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:14:13-69
	android:name
		ADDED from [:android:lingxia] /Users/<USER>/github/LingXia/lingxia-sdk/android/lingxia/build/intermediates/merged_manifest/debug/processDebugManifest/AndroidManifest.xml:11:13-59
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a4bb52afeab294a72a4fa0e19fbd1111/transformed/emoji2-1.2.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/48da0d55994ba0b2c509c375efe718f1/transformed/lifecycle-process-2.6.1/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/48da0d55994ba0b2c509c375efe718f1/transformed/lifecycle-process-2.6.1/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/13c3ee8dc78cb69700ba4b67845ca1f3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/13c3ee8dc78cb69700ba4b67845ca1f3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/82e29ce0dbd967999c23d05d90e9d331/transformed/startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/82e29ce0dbd967999c23d05d90e9d331/transformed/startup-runtime-1.1.1/AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a4bb52afeab294a72a4fa0e19fbd1111/transformed/emoji2-1.2.0/AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a4bb52afeab294a72a4fa0e19fbd1111/transformed/emoji2-1.2.0/AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a4bb52afeab294a72a4fa0e19fbd1111/transformed/emoji2-1.2.0/AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a4bb52afeab294a72a4fa0e19fbd1111/transformed/emoji2-1.2.0/AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a4bb52afeab294a72a4fa0e19fbd1111/transformed/emoji2-1.2.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a4bb52afeab294a72a4fa0e19fbd1111/transformed/emoji2-1.2.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.2.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/a4bb52afeab294a72a4fa0e19fbd1111/transformed/emoji2-1.2.0/AndroidManifest.xml:30:17-75
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0d91ac19c8ae0a8ba1ecbbc6d7b079d9/transformed/core-1.10.1/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0d91ac19c8ae0a8ba1ecbbc6d7b079d9/transformed/core-1.10.1/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0d91ac19c8ae0a8ba1ecbbc6d7b079d9/transformed/core-1.10.1/AndroidManifest.xml:23:9-81
permission#com.lingxia.example.lxapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0d91ac19c8ae0a8ba1ecbbc6d7b079d9/transformed/core-1.10.1/AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0d91ac19c8ae0a8ba1ecbbc6d7b079d9/transformed/core-1.10.1/AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0d91ac19c8ae0a8ba1ecbbc6d7b079d9/transformed/core-1.10.1/AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0d91ac19c8ae0a8ba1ecbbc6d7b079d9/transformed/core-1.10.1/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0d91ac19c8ae0a8ba1ecbbc6d7b079d9/transformed/core-1.10.1/AndroidManifest.xml:26:22-94
uses-permission#com.lingxia.example.lxapp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0d91ac19c8ae0a8ba1ecbbc6d7b079d9/transformed/core-1.10.1/AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.10.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/0d91ac19c8ae0a8ba1ecbbc6d7b079d9/transformed/core-1.10.1/AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/48da0d55994ba0b2c509c375efe718f1/transformed/lifecycle-process-2.6.1/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/48da0d55994ba0b2c509c375efe718f1/transformed/lifecycle-process-2.6.1/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.1] /Users/<USER>/.gradle/caches/8.11.1/transforms/48da0d55994ba0b2c509c375efe718f1/transformed/lifecycle-process-2.6.1/AndroidManifest.xml:30:17-78
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/13c3ee8dc78cb69700ba4b67845ca1f3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/13c3ee8dc78cb69700ba4b67845ca1f3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/13c3ee8dc78cb69700ba4b67845ca1f3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/13c3ee8dc78cb69700ba4b67845ca1f3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/13c3ee8dc78cb69700ba4b67845ca1f3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/13c3ee8dc78cb69700ba4b67845ca1f3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/13c3ee8dc78cb69700ba4b67845ca1f3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/13c3ee8dc78cb69700ba4b67845ca1f3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/13c3ee8dc78cb69700ba4b67845ca1f3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/13c3ee8dc78cb69700ba4b67845ca1f3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/13c3ee8dc78cb69700ba4b67845ca1f3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/13c3ee8dc78cb69700ba4b67845ca1f3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/13c3ee8dc78cb69700ba4b67845ca1f3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/13c3ee8dc78cb69700ba4b67845ca1f3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/13c3ee8dc78cb69700ba4b67845ca1f3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/13c3ee8dc78cb69700ba4b67845ca1f3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/13c3ee8dc78cb69700ba4b67845ca1f3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/13c3ee8dc78cb69700ba4b67845ca1f3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/13c3ee8dc78cb69700ba4b67845ca1f3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/13c3ee8dc78cb69700ba4b67845ca1f3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] /Users/<USER>/.gradle/caches/8.11.1/transforms/13c3ee8dc78cb69700ba4b67845ca1f3/transformed/profileinstaller-1.3.0/AndroidManifest.xml:50:25-92
