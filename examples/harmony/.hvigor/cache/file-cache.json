{"/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/AppScope/app.json5": {"hashValue": "36091b139d40ada3087d4f8a8647d154", "name": "app.json5", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/AppScope/app.json5", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 215, "lastModifiedTime": 1753960222079}}, "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/module.json5": {"hashValue": "14e323fe74b7ddcff36e4ff44d9d9bc4", "name": "module.json5", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/module.json5", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 915, "lastModifiedTime": 1750927498360}}, "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/build-profile.json5": {"hashValue": "3e0a1635140d2e384da5f6d6abc14b04", "name": "build-profile.json5", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/build-profile.json5", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1763, "lastModifiedTime": 1753960930141}}, "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build-profile.json5": {"hashValue": "92d3474cb4e87a83329089af21dccd5f", "name": "build-profile.json5", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build-profile.json5", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 422, "lastModifiedTime": 1750927498353}}, "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/resources/base/profile/main_pages.json": {"hashValue": "682660fa371fd9b2cfa28e8054cfd1a9", "name": "main_pages.json", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/resources/base/profile/main_pages.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 37, "lastModifiedTime": 1750927498367}}, "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/hvigor/hvigor-config.json5": {"hashValue": "a7927226ebb534ac621a86c5a6406d1c", "name": "hvigor-config.json5", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/hvigor/hvigor-config.json5", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1301, "lastModifiedTime": 1750927498368}}, "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/oh-package.json5": {"hashValue": "7bf96d255b0ea688f9db92b28bd056fa", "name": "oh-package.json5", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/oh-package.json5", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 238, "lastModifiedTime": 1753959930813}}, "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/oh-package.json5": {"hashValue": "f4a4cebbabf106df318f4073cee0a070", "name": "oh-package.json5", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/oh-package.json5", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 200, "lastModifiedTime": 1750927498370}}, "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/module.json5": {"hashValue": "d1e04b294833c3b0aabf4ee492042755", "name": "module.json5", "path": "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/module.json5", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 216, "lastModifiedTime": 1753953304891}}, "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build-profile.json5": {"hashValue": "117dd35b0a260dbc88aa7dcfde0d2609", "name": "build-profile.json5", "path": "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build-profile.json5", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 146, "lastModifiedTime": 1753953304883}}, "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/oh-package.json5": {"hashValue": "40de8b8b9c6b9e68c3cfc79af5606691", "name": "oh-package.json5", "path": "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/oh-package.json5", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 178, "lastModifiedTime": 1753953304883}}, "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/hap_metadata/default/output_metadata.json": {"hashValue": "b75776129296a1036e215d36fc666b5d", "name": "output_metadata.json", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/hap_metadata/default/output_metadata.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 112, "lastModifiedTime": 1753960941339}}, "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/loader/default/pkgContextInfo.json": {"hashValue": "e61962aa7133a39ef10ce3492929696c", "name": "pkgContextInfo.json", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/loader/default/pkgContextInfo.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 729, "lastModifiedTime": 1753960941355}}, "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/BuildProfile.ets": {"hashValue": "c731847ec63c1ad42905d1f9c262702d", "name": "BuildProfile.ets", "path": "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/BuildProfile.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 519, "lastModifiedTime": 1753969473757}}, "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/merge_profile/default/module.json": {"hashValue": "2e0f7cb3afdd69724e6dbcad1d43ce9b", "name": "module.json", "path": "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/merge_profile/default/module.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 604, "lastModifiedTime": 1753969473856}}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/toolchains/syscap_tool": {"hashValue": "5b1af7e84dac76a2d5a61ee5b4a739a3", "name": "syscap_tool", "path": "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/toolchains/syscap_tool", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 406544, "lastModifiedTime": 1746674408000}}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/device-define": {"hashValue": "ee94c9a7b689d79dae11d607736e8fba", "name": "device-define", "path": "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/device-define", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "23caa8b6081f1ff1da5f84caf003ebd6", "name": "default.json", "path": "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/device-define/default.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 12543, "lastModifiedTime": 978278400000}}, {"hashValue": "f7852f8b02ad609f5b992308bcd019b2", "name": "liteWearable.json", "path": "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/device-define/liteWearable.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 949, "lastModifiedTime": 978278400000}}, {"hashValue": "9668479bdec34aedd9a3c77168ac5289", "name": "tablet.json", "path": "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/device-define/tablet.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 12056, "lastModifiedTime": 978278400000}}, {"hashValue": "92fd76632497068a2e8e8a4782b85ae0", "name": "wearable.json", "path": "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/device-define/wearable.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 10768, "lastModifiedTime": 978278400000}}]}, "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/router_map/default/temp-router-map.json": {"hashValue": "fbb91d1909479d058737b13355a52412", "name": "temp-router-map.json", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/router_map/default/temp-router-map.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 16, "lastModifiedTime": 1753960941388}}, "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/router_map/default/loader-router-map.json": {"hashValue": "2db2e6a0e7bb90bb54584a0601aa8a7f", "name": "loader-router-map.json", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/router_map/default/loader-router-map.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 16, "lastModifiedTime": 1753960941389}}, "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/generated/profile/default/BuildProfile.ets": {"hashValue": "b1ccc53dbf182040aa58f3226988e05e", "name": "BuildProfile.ets", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/generated/profile/default/BuildProfile.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 868, "lastModifiedTime": 1753960941404}}, "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/merge_profile/default/module.json": {"hashValue": "b7153538e431457fd43b4275bd1c45cd", "name": "module.json", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/merge_profile/default/module.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1344, "lastModifiedTime": 1753960941417}}, "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/loader/default/loader.json": {"hashValue": "ab675d092d3f5ad75a00877b87ef8afb", "name": "loader.json", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/loader/default/loader.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 938, "lastModifiedTime": 1753960941434}}, "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/outputs/default/pack.info": {"hashValue": "0146b676de484bb1bc86e95ddd45f6d9", "name": "pack.info", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/outputs/default/pack.info", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 564, "lastModifiedTime": 1753960941448}}, "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/process_profile/default/module.json": {"hashValue": "61024aaae8e4212dc7c2e5c3a506779d", "name": "module.json", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/process_profile/default/module.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1430, "lastModifiedTime": 1753960941563}}, "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/libs/default": {"hashValue": "", "name": "default", "path": "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/libs/default", "type": "directory", "isSymbolicLink": false, "children": []}, "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resConfig.json": {"hashValue": "5dd5517b232256159562e76d3c4ca43c", "name": "resConfig.json", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resConfig.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1266, "lastModifiedTime": 1753960941602}}, "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/opt-compression.json": {"hashValue": "85560f8699b4f030552fcaf230a013b8", "name": "opt-compression.json", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/opt-compression.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 204, "lastModifiedTime": 1753960941601}}, "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/resources": {"hashValue": "e3762a7d8f974e523880f0ed2ae12d4c", "name": "resources", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/resources", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "b92dba667a75e2e06ab16ca7b1ce340e", "name": "base", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/resources/base", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "0bf8f84f3d3dd14988f926484bfe8961", "name": "element", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/resources/base/element", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "5f17e2220fc9ac8f83fb6d7125be494d", "name": "color.json", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/resources/base/element/color.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 98, "lastModifiedTime": 1750927498361}}, {"hashValue": "f63d908cd19a446e1446b1d81f05502b", "name": "float.json", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/resources/base/element/float.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 92, "lastModifiedTime": 1750927498362}}, {"hashValue": "d3e202074e2d5f80462c4aed03623df5", "name": "string.json", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/resources/base/element/string.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 220, "lastModifiedTime": 1750927498363}}]}, {"hashValue": "03b6149c7f1f7ae2c16a51d3f1b4cad9", "name": "media", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/resources/base/media", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "6510342a375804dd0af0677792d28650", "name": "background.png", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/resources/base/media/background.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 91942, "lastModifiedTime": 1750927498364}}, {"hashValue": "f733ceb3654d584351ff4707e6041d3f", "name": "foreground.png", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/resources/base/media/foreground.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 8805, "lastModifiedTime": 1750927498365}}, {"hashValue": "9dcb15caa500a500b6e7e28534511ca7", "name": "layered_image.json", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/resources/base/media/layered_image.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 109, "lastModifiedTime": 1750927498365}}, {"hashValue": "33a4b02169005a7f85a1c229af5ae6e0", "name": "startIcon.png", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/resources/base/media/startIcon.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 20093, "lastModifiedTime": 1750927498366}}]}, {"hashValue": "c26723b627d154c5d09fd89b4725acbc", "name": "profile", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/resources/base/profile", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "682660fa371fd9b2cfa28e8054cfd1a9", "name": "main_pages.json", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/resources/base/profile/main_pages.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 37, "lastModifiedTime": 1750927498367}}]}]}, {"hashValue": "824b2187822356c07f745c476eaf961e", "name": "dark", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/resources/dark", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "096d66666692368dd814924c1f1f4cba", "name": "element", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/resources/dark/element", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "994a4f4b7189d0d8d8a5c8236c80f877", "name": "color.json", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/resources/dark/element/color.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 98, "lastModifiedTime": 1750927498368}}]}]}, {"hashValue": "5d0760d852eed8e20988f2320924f333", "name": "rawfile", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/resources/rawfile", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "a090ef87a4d5bce407e4bfdfeb3cbfd9", "name": "404.html", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/resources/rawfile/404.html", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1747, "lastModifiedTime": 1753960799126}}, {"hashValue": "8ae5bf55f58316d4b567db057907cefb", "name": "app.json", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/resources/rawfile/app.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 265, "lastModifiedTime": 1753960799138}}, {"hashValue": "7b5e4ec8e13ca32ec55afb4b770dde71", "name": "<PERSON><PERSON><PERSON><PERSON>", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/resources/rawfile/homelxapp", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "b340d0e618520eb1da1922e9c765aad2", "name": "images", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/resources/rawfile/homelxapp/images", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "b0299eb7c0d0916ab63f118e3fa1f08c", "name": ".DS_Store", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/resources/rawfile/homelxapp/images/.DS_Store", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 6148, "lastModifiedTime": 1753960799152}}, {"hashValue": "f1db7cc100b2ccf876e112048309b496", "name": "api.png", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/resources/rawfile/homelxapp/images/api.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 365, "lastModifiedTime": 1753960799160}}, {"hashValue": "3eb86c8aaa3ae2c2c8579b00dedf6cb7", "name": "futuristic.jpg", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/resources/rawfile/homelxapp/images/futuristic.jpg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 431380, "lastModifiedTime": 1753960799151}}, {"hashValue": "cdd01c2c3e3c161b27c54ed22a5e7d62", "name": "home_selected.png", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/resources/rawfile/homelxapp/images/home_selected.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 244, "lastModifiedTime": 1753960799161}}, {"hashValue": "27ae24a4ae8c58c45a320b51019030d9", "name": "home.png", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/resources/rawfile/homelxapp/images/home.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 341, "lastModifiedTime": 1753960799155}}, {"hashValue": "2c65a6ca17f674c95e0f2c0150ef8f7b", "name": "todo_selected.png", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/resources/rawfile/homelxapp/images/todo_selected.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 300, "lastModifiedTime": 1753960799158}}, {"hashValue": "f71f0be648b2897bea2ef745681e84dd", "name": "todo.png", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/resources/rawfile/homelxapp/images/todo.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 326, "lastModifiedTime": 1753960799157}}]}, {"hashValue": "f31b84dd8aec4a43409aa9bf13f0d0d3", "name": "logic.js", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/resources/rawfile/homelxapp/logic.js", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 10219, "lastModifiedTime": 1753960799162}}, {"hashValue": "95957392d4cbd65f5c69a28db551bee0", "name": "lxapp.css", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/resources/rawfile/homelxapp/lxapp.css", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 360, "lastModifiedTime": 1753960799164}}, {"hashValue": "204491575c709a9125c2844e52cc79ee", "name": "lxapp.json", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/resources/rawfile/homelxapp/lxapp.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 896, "lastModifiedTime": 1753960799165}}, {"hashValue": "c3015bc8e183d821317105e4ccec5f5e", "name": "pages", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/resources/rawfile/homelxapp/pages", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "7aad655cc56c97b3a2f1b233b174adf9", "name": "API", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/resources/rawfile/homelxapp/pages/API", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "e0c78023432a073610d73af9044a721f", "name": "index.css", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/resources/rawfile/homelxapp/pages/API/index.css", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 43612, "lastModifiedTime": 1753960799181}}, {"hashValue": "6fb7c30875bd6edcfb889f537340c418", "name": "index.json", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/resources/rawfile/homelxapp/pages/API/index.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 171, "lastModifiedTime": 1753960799183}}, {"hashValue": "0d50b6618935a5e0a402379a48cc823b", "name": "index.tsx", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/resources/rawfile/homelxapp/pages/API/index.tsx", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 610, "lastModifiedTime": 1753960799180}}, {"hashValue": "8f25110d72409b5f1a1b00fd04a27abd", "name": "view.js", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/resources/rawfile/homelxapp/pages/API/view.js", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 197606, "lastModifiedTime": 1753960799182}}]}, {"hashValue": "960e1a637e7e2b1d256fc18328eab10d", "name": "home", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/resources/rawfile/homelxapp/pages/home", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "3bf9ea631147abaf2cf6bbc861eba8d3", "name": "index.css", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/resources/rawfile/homelxapp/pages/home/<USER>", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1699, "lastModifiedTime": 1753960799169}}, {"hashValue": "d7dbb9991411a8e37bafe36a5b9e5835", "name": "index.html", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/resources/rawfile/homelxapp/pages/home/<USER>", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3796, "lastModifiedTime": 1753960799168}}, {"hashValue": "0c629c4a2fb697296d1e4420475918d0", "name": "index.json", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/resources/rawfile/homelxapp/pages/home/<USER>", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 171, "lastModifiedTime": 1753960799171}}]}, {"hashValue": "a5d870145475031e21b9d227d8372584", "name": "todo", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/resources/rawfile/homelxapp/pages/todo", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "ec7e112f9c1172c355cedcc374c6142c", "name": "index.css", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/resources/rawfile/homelxapp/pages/todo/index.css", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 11225, "lastModifiedTime": 1753960799174}}, {"hashValue": "86b71a818ab8461cca1cd2bf87955b2d", "name": "index.json", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/resources/rawfile/homelxapp/pages/todo/index.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 171, "lastModifiedTime": 1753960799177}}, {"hashValue": "cee1a1cdbb4fe929826462c25581057f", "name": "index.vue", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/resources/rawfile/homelxapp/pages/todo/index.vue", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 639, "lastModifiedTime": 1753960799173}}, {"hashValue": "9c1db2f04c1a264920dddf03ec6de67b", "name": "view.js", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/resources/rawfile/homelxapp/pages/todo/view.js", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 65846, "lastModifiedTime": 1753960799176}}]}]}]}, {"hashValue": "b148b4c4fe967f6f4247c86d6d69627c", "name": "webview-bridge.js", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/resources/rawfile/webview-bridge.js", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 13632, "lastModifiedTime": 1753960799131}}]}]}, "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/AppScope/resources": {"hashValue": "ba9fce7ba8087b0a8857ac2c4db3d1d6", "name": "resources", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/AppScope/resources", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "b69a3d925213a13f21ffd6edefbc0b30", "name": "base", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/AppScope/resources/base", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "8dcaaabeff1edf12b23304a53162bba2", "name": "element", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/AppScope/resources/base/element", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "bd75009e87d622c7bda84e876a4b4ecd", "name": "string.json", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/AppScope/resources/base/element/string.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 88, "lastModifiedTime": 1750927498347}}]}, {"hashValue": "99b18ce929f788a86d1b5af17206e716", "name": "media", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/AppScope/resources/base/media", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "bc97a2620012dccbd11be2c243d4b1d6", "name": "background.png", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/AppScope/resources/base/media/background.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 91942, "lastModifiedTime": 1750927498348}}, {"hashValue": "e6feeb0f446d4ff33622924d7195e1ec", "name": "foreground.png", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/AppScope/resources/base/media/foreground.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 8805, "lastModifiedTime": 1750927498349}}, {"hashValue": "70eebe895a586323dab4cd1f3dacd3c4", "name": "layered_image.json", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/AppScope/resources/base/media/layered_image.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 109, "lastModifiedTime": 1750927498350}}]}]}]}, "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default": {"hashValue": "759a8d97e54008d7581517e2a3595efd", "name": "default", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "37b949d69620feefca84367597bcecc5", "name": ".caches", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/.caches", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "1c7d27c4cb756372915e2bca64e2da2c", "name": "base", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/.caches/base", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "813890f940f50fb9a501dcdf5f7541a0", "name": "media", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/.caches/base/media", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "394d726b400caea5c419d2f614f70b72", "name": "background.png", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/.caches/base/media/background.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 70283, "lastModifiedTime": 1753960941798}}, {"hashValue": "c89369a7674af1606409517ce499b2d7", "name": "foreground.png", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/.caches/base/media/foreground.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 9325, "lastModifiedTime": 1753960941845}}]}]}]}, {"hashValue": "e18777f83d294e7f722ce36f4416f1f5", "name": "ark_module.json", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/ark_module.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1146, "lastModifiedTime": 1753960941562}}, {"hashValue": "0039e1a3dd9775c3f73de9446b06b934", "name": "ids_map", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/ids_map", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "9df67376c42ed515eead6fbeb6718afd", "name": "id_defined.json", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/ids_map/id_defined.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 873, "lastModifiedTime": 1753960941847}}]}, {"hashValue": "2c1161d8d08fad966fdda3829e6a46c2", "name": "module.json", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/module.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1594, "lastModifiedTime": 1753960941736}}, {"hashValue": "85560f8699b4f030552fcaf230a013b8", "name": "opt-compression.json", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/opt-compression.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 204, "lastModifiedTime": 1753960941601}}, {"hashValue": "5dd5517b232256159562e76d3c4ca43c", "name": "resConfig.json", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resConfig.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1266, "lastModifiedTime": 1753960941602}}, {"hashValue": "e9cb6aa5df2c21733c972eefd1d1626c", "name": "resources", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "c8f518ebd347c395f810672388ebd1cc", "name": "base", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/base", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "6815477a34812e004d8af783a5cc46a1", "name": "media", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/base/media", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "c40b7fd6bf2e8e0e0b4b0a0c432960bd", "name": "background.png", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/base/media/background.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 70283, "lastModifiedTime": 1753960941799}}, {"hashValue": "e00bf295bb6ae5b6a1c7ea828a3bbc51", "name": "foreground.png", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/base/media/foreground.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 9325, "lastModifiedTime": 1753960941846}}, {"hashValue": "dbbc9a4046ae1232eace782272f266fd", "name": "layered_image.json", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/base/media/layered_image.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 95, "lastModifiedTime": 1753960941734}}, {"hashValue": "50ecc5e241a1cdcdd8e85ff958423db5", "name": "startIcon.png", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/base/media/startIcon.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 20093, "lastModifiedTime": 1753960941732}}]}, {"hashValue": "0ec269290e75522b54a6495671056e99", "name": "profile", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/base/profile", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "36b6ff172c7819f4de5abdae96682cf2", "name": "main_pages.json", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/base/profile/main_pages.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 37, "lastModifiedTime": 1753960941733}}]}]}, {"hashValue": "3f54d54c7b65cfd0123711f9bdb0c94a", "name": "rawfile", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "2ab46893823ae48e3a90dd43abb273a2", "name": "404.html", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/404.html", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1747, "lastModifiedTime": 1753960941724}}, {"hashValue": "14aab610b682e2fe792e9485fe895096", "name": "app.json", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/app.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 265, "lastModifiedTime": 1753960941723}}, {"hashValue": "c9550d5fad73447fc24ba47f95d1c6b7", "name": "arkdata", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/arkdata", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "", "name": "schema", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/arkdata/schema", "type": "directory", "isSymbolicLink": false, "children": []}]}, {"hashValue": "db27f61ad7008e668f649d98ed4e5aa9", "name": "<PERSON><PERSON><PERSON><PERSON>", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "491635531236c6d953dcbca86ed60c3a", "name": "images", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/images", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "0e0dfeb80e494e0bdc8654a576f6826a", "name": "api.png", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/images/api.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 365, "lastModifiedTime": 1753960941735}}, {"hashValue": "8339b94b54b9b433e93d1bb3a8a5ebce", "name": "futuristic.jpg", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/images/futuristic.jpg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 431380, "lastModifiedTime": 1753960941733}}, {"hashValue": "cc03d750cbd46d36ffa888a9513a5347", "name": "home_selected.png", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/images/home_selected.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 244, "lastModifiedTime": 1753960941735}}, {"hashValue": "4e73281a1e583e674d7b17e0760f468f", "name": "home.png", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/images/home.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 341, "lastModifiedTime": 1753960941734}}, {"hashValue": "ac2030e50d62f3d4d5ca488c04eccf1b", "name": "todo_selected.png", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/images/todo_selected.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 300, "lastModifiedTime": 1753960941735}}, {"hashValue": "84f1437599562f98ae52fd7a366704cd", "name": "todo.png", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/images/todo.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 326, "lastModifiedTime": 1753960941734}}]}, {"hashValue": "e0631814ccaddba3cf0cf7d27b94d9f8", "name": "logic.js", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/logic.js", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 10219, "lastModifiedTime": 1753960941724}}, {"hashValue": "99f9797c49b8c30eede1a0478e6c6177", "name": "lxapp.css", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/lxapp.css", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 360, "lastModifiedTime": 1753960941724}}, {"hashValue": "016156bd4929754350fea7bfeef903e3", "name": "lxapp.json", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/lxapp.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 896, "lastModifiedTime": 1753960941736}}, {"hashValue": "4093b503a7810c560d959aff4aab4e0d", "name": "pages", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "9f785a8ddf8bd23b6219ec5424cbb7ca", "name": "API", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/API", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "c5f64b1721c5349a4ada03b68cf12baa", "name": "index.css", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/API/index.css", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 43612, "lastModifiedTime": 1753960941741}}, {"hashValue": "63b58dd25d59c5bce47e7d97e7a3c3f1", "name": "index.json", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/API/index.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 171, "lastModifiedTime": 1753960941744}}, {"hashValue": "1c59abeafcb45d3d0f5e6068465f142d", "name": "index.tsx", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/API/index.tsx", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 610, "lastModifiedTime": 1753960941740}}, {"hashValue": "bd782ad63411c66f525db59d8b3e4889", "name": "view.js", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/API/view.js", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 197606, "lastModifiedTime": 1753960941744}}]}, {"hashValue": "711c48c3f575df9a2e7b732bb1bf6d5e", "name": "home", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/home", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "0063dfa2acf8f1707773b40c011a1773", "name": "index.css", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/home/<USER>", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1699, "lastModifiedTime": 1753960941736}}, {"hashValue": "6202a514ac8c093ac7faabac9ec89f15", "name": "index.html", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/home/<USER>", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3796, "lastModifiedTime": 1753960941736}}, {"hashValue": "79a32e2eacc6a393e1a15a4f65d8bdc7", "name": "index.json", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/home/<USER>", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 171, "lastModifiedTime": 1753960941737}}]}, {"hashValue": "e25345b120b2da835a027cb2ee3f4efe", "name": "todo", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/todo", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "c2c81ae2af48a0e0072695abf478ee0a", "name": "index.css", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/todo/index.css", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 11225, "lastModifiedTime": 1753960941738}}, {"hashValue": "620faf9359f1860f2d708d75eb37b87f", "name": "index.json", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/todo/index.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 171, "lastModifiedTime": 1753960941739}}, {"hashValue": "2123f155596f0bbc906b6222c8d1d135", "name": "index.vue", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/todo/index.vue", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 639, "lastModifiedTime": 1753960941737}}, {"hashValue": "20a0729850f970cd270aee0d909ad2ac", "name": "view.js", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/todo/view.js", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 65846, "lastModifiedTime": 1753960941739}}]}]}]}, {"hashValue": "b2700ee9d0f12a03dc52c4a5f352d9bc", "name": "webview-bridge.js", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/webview-bridge.js", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 13632, "lastModifiedTime": 1753960941745}}]}]}, {"hashValue": "5c2a98ebd0ebb6ed2eb6c5e76fcdcf7d", "name": "resources.index", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources.index", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 913, "lastModifiedTime": 1753960941847}}, {"hashValue": "603a7c983af8162aa3000f5f6c27ae48", "name": "ResourceTable.txt", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/ResourceTable.txt", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 351, "lastModifiedTime": 1753960941735}}]}, "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/generated/r/default/ResourceTable.h": {"hashValue": "cfc66eb69757b6fa234ec3c3cc44001b", "name": "ResourceTable.h", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/generated/r/default/ResourceTable.h", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1244, "lastModifiedTime": 1753960941735}}, "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/generated/r/default": {"hashValue": "c52e3d64ed03c3ccbc8950f278a09892", "name": "default", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/generated/r/default", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "cfc66eb69757b6fa234ec3c3cc44001b", "name": "ResourceTable.h", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/generated/r/default/ResourceTable.h", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1244, "lastModifiedTime": 1753960941735}}]}, "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/loader/default": {"hashValue": "b6a321793be24d2bfb5753993bbb0b4c", "name": "default", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/loader/default", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "ab675d092d3f5ad75a00877b87ef8afb", "name": "loader.json", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/loader/default/loader.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 938, "lastModifiedTime": 1753960941434}}, {"hashValue": "e61962aa7133a39ef10ce3492929696c", "name": "pkgContextInfo.json", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/loader/default/pkgContextInfo.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 729, "lastModifiedTime": 1753960941355}}]}, "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/Index.ets": {"hashValue": "7e9bff683cbe1b6262cf0cd24b2df5e0", "name": "Index.ets", "path": "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/Index.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 991, "lastModifiedTime": 1753953304883}}, "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets": {"hashValue": "29ba1360fadd24ac7794638ff7f006cf", "name": "ets", "path": "/Users/<USER>/github/Ling<PERSON>ia/lingxia-sdk/harmony/lingxia/src/main/ets", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "35acd922e658c2053327132d27b2da31", "name": "lxapp", "path": "/Users/<USER>/github/Ling<PERSON>ia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "de808ec890b5161a54f3ab72a9d5cb1e", "name": "LxApp.ets", "path": "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/LxApp.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 16628, "lastModifiedTime": 1753954054502}}, {"hashValue": "4104d95979c3bcd1744b31ee63263226", "name": "LxAppContainer.ets", "path": "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/LxAppContainer.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 21305, "lastModifiedTime": 1753953304886}}, {"hashValue": "2dacf9d7abe30e5cffb231c139fc6e82", "name": "LxAppNavigation.ets", "path": "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/LxAppNavigation.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 8589, "lastModifiedTime": 1753953304887}}, {"hashValue": "58d4e8d803e0bf5b7271306bbda5ea6c", "name": "NativeBridge.ets", "path": "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/NativeBridge.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 4561, "lastModifiedTime": 1753953304887}}, {"hashValue": "26a2cb4f30b18ad32ec1e3cad1280891", "name": "NavigationBar.ets", "path": "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/NavigationBar.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 8286, "lastModifiedTime": 1753953304888}}, {"hashValue": "9bcffc30e628a6603b06311378f2ebc8", "name": "TabBar.ets", "path": "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/TabBar.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 14074, "lastModifiedTime": 1753953304889}}, {"hashValue": "10ade26ebb3f87444b1511346f3a06cd", "name": "WebView.ets", "path": "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/WebView.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 14410, "lastModifiedTime": 1753953304890}}]}, {"hashValue": "68190bb032cd5bf070b9fbcc8beac8e2", "name": "types", "path": "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/types", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "204a35f099c5409fe412d478c874d321", "name": "liblingxia.d.ts", "path": "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/types/liblingxia.d.ts", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3360, "lastModifiedTime": 1753954049494}}]}]}, "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile": {"hashValue": "3f54d54c7b65cfd0123711f9bdb0c94a", "name": "rawfile", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "2ab46893823ae48e3a90dd43abb273a2", "name": "404.html", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/404.html", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1747, "lastModifiedTime": 1753960941724}}, {"hashValue": "14aab610b682e2fe792e9485fe895096", "name": "app.json", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/app.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 265, "lastModifiedTime": 1753960941723}}, {"hashValue": "c9550d5fad73447fc24ba47f95d1c6b7", "name": "arkdata", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/arkdata", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "", "name": "schema", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/arkdata/schema", "type": "directory", "isSymbolicLink": false, "children": []}]}, {"hashValue": "db27f61ad7008e668f649d98ed4e5aa9", "name": "<PERSON><PERSON><PERSON><PERSON>", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "491635531236c6d953dcbca86ed60c3a", "name": "images", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/images", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "0e0dfeb80e494e0bdc8654a576f6826a", "name": "api.png", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/images/api.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 365, "lastModifiedTime": 1753960941735}}, {"hashValue": "8339b94b54b9b433e93d1bb3a8a5ebce", "name": "futuristic.jpg", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/images/futuristic.jpg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 431380, "lastModifiedTime": 1753960941733}}, {"hashValue": "cc03d750cbd46d36ffa888a9513a5347", "name": "home_selected.png", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/images/home_selected.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 244, "lastModifiedTime": 1753960941735}}, {"hashValue": "4e73281a1e583e674d7b17e0760f468f", "name": "home.png", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/images/home.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 341, "lastModifiedTime": 1753960941734}}, {"hashValue": "ac2030e50d62f3d4d5ca488c04eccf1b", "name": "todo_selected.png", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/images/todo_selected.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 300, "lastModifiedTime": 1753960941735}}, {"hashValue": "84f1437599562f98ae52fd7a366704cd", "name": "todo.png", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/images/todo.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 326, "lastModifiedTime": 1753960941734}}]}, {"hashValue": "e0631814ccaddba3cf0cf7d27b94d9f8", "name": "logic.js", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/logic.js", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 10219, "lastModifiedTime": 1753960941724}}, {"hashValue": "99f9797c49b8c30eede1a0478e6c6177", "name": "lxapp.css", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/lxapp.css", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 360, "lastModifiedTime": 1753960941724}}, {"hashValue": "016156bd4929754350fea7bfeef903e3", "name": "lxapp.json", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/lxapp.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 896, "lastModifiedTime": 1753960941736}}, {"hashValue": "4093b503a7810c560d959aff4aab4e0d", "name": "pages", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "9f785a8ddf8bd23b6219ec5424cbb7ca", "name": "API", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/API", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "c5f64b1721c5349a4ada03b68cf12baa", "name": "index.css", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/API/index.css", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 43612, "lastModifiedTime": 1753960941741}}, {"hashValue": "63b58dd25d59c5bce47e7d97e7a3c3f1", "name": "index.json", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/API/index.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 171, "lastModifiedTime": 1753960941744}}, {"hashValue": "1c59abeafcb45d3d0f5e6068465f142d", "name": "index.tsx", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/API/index.tsx", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 610, "lastModifiedTime": 1753960941740}}, {"hashValue": "bd782ad63411c66f525db59d8b3e4889", "name": "view.js", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/API/view.js", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 197606, "lastModifiedTime": 1753960941744}}]}, {"hashValue": "711c48c3f575df9a2e7b732bb1bf6d5e", "name": "home", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/home", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "0063dfa2acf8f1707773b40c011a1773", "name": "index.css", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/home/<USER>", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1699, "lastModifiedTime": 1753960941736}}, {"hashValue": "6202a514ac8c093ac7faabac9ec89f15", "name": "index.html", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/home/<USER>", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3796, "lastModifiedTime": 1753960941736}}, {"hashValue": "79a32e2eacc6a393e1a15a4f65d8bdc7", "name": "index.json", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/home/<USER>", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 171, "lastModifiedTime": 1753960941737}}]}, {"hashValue": "e25345b120b2da835a027cb2ee3f4efe", "name": "todo", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/todo", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "c2c81ae2af48a0e0072695abf478ee0a", "name": "index.css", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/todo/index.css", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 11225, "lastModifiedTime": 1753960941738}}, {"hashValue": "620faf9359f1860f2d708d75eb37b87f", "name": "index.json", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/todo/index.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 171, "lastModifiedTime": 1753960941739}}, {"hashValue": "2123f155596f0bbc906b6222c8d1d135", "name": "index.vue", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/todo/index.vue", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 639, "lastModifiedTime": 1753960941737}}, {"hashValue": "20a0729850f970cd270aee0d909ad2ac", "name": "view.js", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/todo/view.js", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 65846, "lastModifiedTime": 1753960941739}}]}]}]}, {"hashValue": "b2700ee9d0f12a03dc52c4a5f352d9bc", "name": "webview-bridge.js", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/webview-bridge.js", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 13632, "lastModifiedTime": 1753960941745}}]}, "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/ResourceTable.txt": {"hashValue": "603a7c983af8162aa3000f5f6c27ae48", "name": "ResourceTable.txt", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/ResourceTable.txt", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 351, "lastModifiedTime": 1753960941735}}, "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/ark_module.json": {"hashValue": "e18777f83d294e7f722ce36f4416f1f5", "name": "ark_module.json", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/ark_module.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1146, "lastModifiedTime": 1753960941562}}, "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/base/profile": {"hashValue": "0ec269290e75522b54a6495671056e99", "name": "profile", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/base/profile", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "36b6ff172c7819f4de5abdae96682cf2", "name": "main_pages.json", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/base/profile/main_pages.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 37, "lastModifiedTime": 1753960941733}}]}, "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/ets": {"hashValue": "5cd26bd6e4b89710c4c64b5c4f539033", "name": "ets", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/ets", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "290065c628213cf47e8560e3548dabd1", "name": "entryability", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/ets/entryability", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "893817bd0a8464315c94dfbc42a85125", "name": "EntryAbility.ets", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/ets/entryability/EntryAbility.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1676, "lastModifiedTime": 1752040554877}}]}, {"hashValue": "1dc2114251d9c84e7a292c3c5ad8046d", "name": "pages", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/ets/pages", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "74ca9e110549d13ce097d0890850be58", "name": "Index.ets", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/ets/pages/Index.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3639, "lastModifiedTime": 1753960222071}}]}]}, "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/loader_out/default/ets": {"hashValue": "943babe5b6e6ffcb2c4bd47c779cd7b3", "name": "ets", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/loader_out/default/ets", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "454f5fab3d90d4a5cfbd6bf92c6fc206", "name": "modules.abc", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/loader_out/default/ets/modules.abc", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 225164, "lastModifiedTime": 1753960950585}}, {"hashValue": "bf6756bf0a63dc4e5baccc44257e5935", "name": "sourceMaps.map", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/loader_out/default/ets/sourceMaps.map", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 88944, "lastModifiedTime": 1753960950552}}]}, "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/stripped_native_libs/default": {"hashValue": "", "name": "default", "path": "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/stripped_native_libs/default", "type": "directory", "isSymbolicLink": false, "children": []}, "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/libs": {"hashValue": "a040a97b7da8cab9e4526d50e1c64d34", "name": "libs", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/libs", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "bed9288909026e4c34e4b137267c4c9e", "name": "arm64-v8a", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/libs/arm64-v8a", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "eb13b3688a6cf2bcbdd5e5cbe65a260b", "name": "liblingxia.so", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/libs/arm64-v8a/liblingxia.so", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 9028808, "lastModifiedTime": 1753960799098}}]}]}, "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/libs/default": {"hashValue": "a040a97b7da8cab9e4526d50e1c64d34", "name": "default", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/libs/default", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "bed9288909026e4c34e4b137267c4c9e", "name": "arm64-v8a", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/libs/default/arm64-v8a", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "eb13b3688a6cf2bcbdd5e5cbe65a260b", "name": "liblingxia.so", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/libs/default/arm64-v8a/liblingxia.so", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 9028808, "lastModifiedTime": 1753960799098}}]}]}, "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/stripped_native_libs/default": {"hashValue": "37da541985af7ba5db46e1fdb51aebc0", "name": "default", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/stripped_native_libs/default", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "088ac4b034a887075492ef5dde2f03c8", "name": "arm64-v8a", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/stripped_native_libs/default/arm64-v8a", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "4a74a2873f988b96b86e31127089d71b", "name": "liblingxia.so", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/stripped_native_libs/default/arm64-v8a/liblingxia.so", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 6130184, "lastModifiedTime": 1753960942783}}]}]}, "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/patch/default/base_native_libs.json": {"hashValue": "e30989e3d047a342fbba892835a60d20", "name": "base_native_libs.json", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/patch/default/base_native_libs.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 381, "lastModifiedTime": 1753960943660}}, "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/module.json": {"hashValue": "2c1161d8d08fad966fdda3829e6a46c2", "name": "module.json", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/module.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1594, "lastModifiedTime": 1753960941736}}, "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/package/default/module.json": {"hashValue": "fba2f28bf4f233066cfbc1e1868b92c1", "name": "module.json", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/package/default/module.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1594, "lastModifiedTime": 1753960950615}}, "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources": {"hashValue": "e9cb6aa5df2c21733c972eefd1d1626c", "name": "resources", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "c8f518ebd347c395f810672388ebd1cc", "name": "base", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/base", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "6815477a34812e004d8af783a5cc46a1", "name": "media", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/base/media", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "c40b7fd6bf2e8e0e0b4b0a0c432960bd", "name": "background.png", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/base/media/background.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 70283, "lastModifiedTime": 1753960941799}}, {"hashValue": "e00bf295bb6ae5b6a1c7ea828a3bbc51", "name": "foreground.png", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/base/media/foreground.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 9325, "lastModifiedTime": 1753960941846}}, {"hashValue": "dbbc9a4046ae1232eace782272f266fd", "name": "layered_image.json", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/base/media/layered_image.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 95, "lastModifiedTime": 1753960941734}}, {"hashValue": "50ecc5e241a1cdcdd8e85ff958423db5", "name": "startIcon.png", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/base/media/startIcon.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 20093, "lastModifiedTime": 1753960941732}}]}, {"hashValue": "0ec269290e75522b54a6495671056e99", "name": "profile", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/base/profile", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "36b6ff172c7819f4de5abdae96682cf2", "name": "main_pages.json", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/base/profile/main_pages.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 37, "lastModifiedTime": 1753960941733}}]}]}, {"hashValue": "3f54d54c7b65cfd0123711f9bdb0c94a", "name": "rawfile", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "2ab46893823ae48e3a90dd43abb273a2", "name": "404.html", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/404.html", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1747, "lastModifiedTime": 1753960941724}}, {"hashValue": "14aab610b682e2fe792e9485fe895096", "name": "app.json", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/app.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 265, "lastModifiedTime": 1753960941723}}, {"hashValue": "c9550d5fad73447fc24ba47f95d1c6b7", "name": "arkdata", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/arkdata", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "", "name": "schema", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/arkdata/schema", "type": "directory", "isSymbolicLink": false, "children": []}]}, {"hashValue": "db27f61ad7008e668f649d98ed4e5aa9", "name": "<PERSON><PERSON><PERSON><PERSON>", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "491635531236c6d953dcbca86ed60c3a", "name": "images", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/images", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "0e0dfeb80e494e0bdc8654a576f6826a", "name": "api.png", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/images/api.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 365, "lastModifiedTime": 1753960941735}}, {"hashValue": "8339b94b54b9b433e93d1bb3a8a5ebce", "name": "futuristic.jpg", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/images/futuristic.jpg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 431380, "lastModifiedTime": 1753960941733}}, {"hashValue": "cc03d750cbd46d36ffa888a9513a5347", "name": "home_selected.png", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/images/home_selected.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 244, "lastModifiedTime": 1753960941735}}, {"hashValue": "4e73281a1e583e674d7b17e0760f468f", "name": "home.png", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/images/home.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 341, "lastModifiedTime": 1753960941734}}, {"hashValue": "ac2030e50d62f3d4d5ca488c04eccf1b", "name": "todo_selected.png", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/images/todo_selected.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 300, "lastModifiedTime": 1753960941735}}, {"hashValue": "84f1437599562f98ae52fd7a366704cd", "name": "todo.png", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/images/todo.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 326, "lastModifiedTime": 1753960941734}}]}, {"hashValue": "e0631814ccaddba3cf0cf7d27b94d9f8", "name": "logic.js", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/logic.js", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 10219, "lastModifiedTime": 1753960941724}}, {"hashValue": "99f9797c49b8c30eede1a0478e6c6177", "name": "lxapp.css", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/lxapp.css", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 360, "lastModifiedTime": 1753960941724}}, {"hashValue": "016156bd4929754350fea7bfeef903e3", "name": "lxapp.json", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/lxapp.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 896, "lastModifiedTime": 1753960941736}}, {"hashValue": "4093b503a7810c560d959aff4aab4e0d", "name": "pages", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "9f785a8ddf8bd23b6219ec5424cbb7ca", "name": "API", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/API", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "c5f64b1721c5349a4ada03b68cf12baa", "name": "index.css", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/API/index.css", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 43612, "lastModifiedTime": 1753960941741}}, {"hashValue": "63b58dd25d59c5bce47e7d97e7a3c3f1", "name": "index.json", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/API/index.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 171, "lastModifiedTime": 1753960941744}}, {"hashValue": "1c59abeafcb45d3d0f5e6068465f142d", "name": "index.tsx", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/API/index.tsx", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 610, "lastModifiedTime": 1753960941740}}, {"hashValue": "bd782ad63411c66f525db59d8b3e4889", "name": "view.js", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/API/view.js", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 197606, "lastModifiedTime": 1753960941744}}]}, {"hashValue": "711c48c3f575df9a2e7b732bb1bf6d5e", "name": "home", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/home", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "0063dfa2acf8f1707773b40c011a1773", "name": "index.css", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/home/<USER>", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1699, "lastModifiedTime": 1753960941736}}, {"hashValue": "6202a514ac8c093ac7faabac9ec89f15", "name": "index.html", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/home/<USER>", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3796, "lastModifiedTime": 1753960941736}}, {"hashValue": "79a32e2eacc6a393e1a15a4f65d8bdc7", "name": "index.json", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/home/<USER>", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 171, "lastModifiedTime": 1753960941737}}]}, {"hashValue": "e25345b120b2da835a027cb2ee3f4efe", "name": "todo", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/todo", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "c2c81ae2af48a0e0072695abf478ee0a", "name": "index.css", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/todo/index.css", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 11225, "lastModifiedTime": 1753960941738}}, {"hashValue": "620faf9359f1860f2d708d75eb37b87f", "name": "index.json", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/todo/index.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 171, "lastModifiedTime": 1753960941739}}, {"hashValue": "2123f155596f0bbc906b6222c8d1d135", "name": "index.vue", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/todo/index.vue", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 639, "lastModifiedTime": 1753960941737}}, {"hashValue": "20a0729850f970cd270aee0d909ad2ac", "name": "view.js", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/todo/view.js", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 65846, "lastModifiedTime": 1753960941739}}]}]}]}, {"hashValue": "b2700ee9d0f12a03dc52c4a5f352d9bc", "name": "webview-bridge.js", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/webview-bridge.js", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 13632, "lastModifiedTime": 1753960941745}}]}]}, "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources.index": {"hashValue": "5c2a98ebd0ebb6ed2eb6c5e76fcdcf7d", "name": "resources.index", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/res/default/resources.index", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 913, "lastModifiedTime": 1753960941847}}, "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/loader_out/default/ets/sourceMaps.map": {"hashValue": "bf6756bf0a63dc4e5baccc44257e5935", "name": "sourceMaps.map", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/loader_out/default/ets/sourceMaps.map", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 88944, "lastModifiedTime": 1753960950552}}, "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/outputs/default/entry-default-unsigned.hap": {"hashValue": "96f649cc3a18241b1189747e25b7d76b", "name": "entry-default-unsigned.hap", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/outputs/default/entry-default-unsigned.hap", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 7338538, "lastModifiedTime": 1753960951219}}, "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/source_map/default/sourceMaps.map": {"hashValue": "3765c079baf4259f20d3e282725b4dfc", "name": "sourceMaps.map", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/intermediates/source_map/default/sourceMaps.map", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 88944, "lastModifiedTime": 1753960950632}}, "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/outputs/default/mapping/sourceMaps.map": {"hashValue": "af0da183fb45974d45e865975e82a8aa", "name": "sourceMaps.map", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/outputs/default/mapping/sourceMaps.map", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 88944, "lastModifiedTime": 1753960950634}}, "/Users/<USER>/.ohos/config/default_harmony_Ox-3HNfvclQdbq4KOWhaXIc2hg7i7fx0_2L33NsWpQw=.cer": {"hashValue": "c16801588f67eec495a15015d0cd5727", "name": "default_harmony_Ox-3HNfvclQdbq4KOWhaXIc2hg7i7fx0_2L33NsWpQw=.cer", "path": "/Users/<USER>/.ohos/config/default_harmony_Ox-3HNfvclQdbq4KOWhaXIc2hg7i7fx0_2L33NsWpQw=.cer", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 2910, "lastModifiedTime": 1753960925542}}, "/Users/<USER>/.ohos/config/default_harmony_Ox-3HNfvclQdbq4KOWhaXIc2hg7i7fx0_2L33NsWpQw=.p7b": {"hashValue": "70b46642533b215af092571c3e47f2e2", "name": "default_harmony_Ox-3HNfvclQdbq4KOWhaXIc2hg7i7fx0_2L33NsWpQw=.p7b", "path": "/Users/<USER>/.ohos/config/default_harmony_Ox-3HNfvclQdbq4KOWhaXIc2hg7i7fx0_2L33NsWpQw=.p7b", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 4079, "lastModifiedTime": 1753960926722}}, "/Users/<USER>/.ohos/config/default_harmony_Ox-3HNfvclQdbq4KOWhaXIc2hg7i7fx0_2L33NsWpQw=.p12": {"hashValue": "2fee1e8d7e250424dbff0400315929d6", "name": "default_harmony_Ox-3HNfvclQdbq4KOWhaXIc2hg7i7fx0_2L33NsWpQw=.p12", "path": "/Users/<USER>/.ohos/config/default_harmony_Ox-3HNfvclQdbq4KOWhaXIc2hg7i7fx0_2L33NsWpQw=.p12", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1128, "lastModifiedTime": 1753960923958}}, "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/outputs/default/entry-default-signed.hap": {"hashValue": "a79c64a7d5afcf63c4368498426b2919", "name": "entry-default-signed.hap", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/build/default/outputs/default/entry-default-signed.hap", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 7431808, "lastModifiedTime": 1753960952774}}, "/Users/<USER>/github/LingXia/examples/harmony/AppScope/app.json5": {"hashValue": "c2861bcdef7a4cb63f0e8fa643418e3c", "name": "app.json5", "path": "/Users/<USER>/github/LingXia/examples/harmony/AppScope/app.json5", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 215, "lastModifiedTime": 1753961187139}}, "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/module.json5": {"hashValue": "14e323fe74b7ddcff36e4ff44d9d9bc4", "name": "module.json5", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/module.json5", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 915, "lastModifiedTime": 1750927498360}}, "/Users/<USER>/github/LingXia/examples/harmony/build-profile.json5": {"hashValue": "6eb54cb63c5d584e50b8d97dbe6b421a", "name": "build-profile.json5", "path": "/Users/<USER>/github/LingXia/examples/harmony/build-profile.json5", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1761, "lastModifiedTime": 1753968981819}}, "/Users/<USER>/github/LingXia/examples/harmony/entry/build-profile.json5": {"hashValue": "92d3474cb4e87a83329089af21dccd5f", "name": "build-profile.json5", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build-profile.json5", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 422, "lastModifiedTime": 1750927498353}}, "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources/base/profile/main_pages.json": {"hashValue": "682660fa371fd9b2cfa28e8054cfd1a9", "name": "main_pages.json", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources/base/profile/main_pages.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 37, "lastModifiedTime": 1750927498367}}, "/Users/<USER>/github/LingXia/examples/harmony/hvigor/hvigor-config.json5": {"hashValue": "a7927226ebb534ac621a86c5a6406d1c", "name": "hvigor-config.json5", "path": "/Users/<USER>/github/<PERSON><PERSON><PERSON>/examples/harmony/hvigor/hvigor-config.json5", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1301, "lastModifiedTime": 1750927498368}}, "/Users/<USER>/github/LingXia/examples/harmony/entry/oh-package.json5": {"hashValue": "e4dfe5e5c36f3b193ed88ac5cfe2408a", "name": "oh-package.json5", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/oh-package.json5", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 235, "lastModifiedTime": 1753969156323}}, "/Users/<USER>/github/LingXia/examples/harmony/oh-package.json5": {"hashValue": "f4a4cebbabf106df318f4073cee0a070", "name": "oh-package.json5", "path": "/Users/<USER>/github/LingXia/examples/harmony/oh-package.json5", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 200, "lastModifiedTime": 1750927498370}}, "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/hap_metadata/default/output_metadata.json": {"hashValue": "25c76a186c92e901149f81aa42418a98", "name": "output_metadata.json", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/hap_metadata/default/output_metadata.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 112, "lastModifiedTime": 1753968771405}}, "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/merge_profile/default/module.json": {"hashValue": "279cd50159bdf31d0da5cdc84a187821", "name": "module.json", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/merge_profile/default/module.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1344, "lastModifiedTime": 1753969473898}}, "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/generated/profile/default/BuildProfile.ets": {"hashValue": "88de96158c7c0813855ac57df1798f9c", "name": "BuildProfile.ets", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/generated/profile/default/BuildProfile.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 868, "lastModifiedTime": 1753968986810}}, "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default/pkgContextInfo.json": {"hashValue": "3192530e945484106518e8afe659c6f4", "name": "pkgContextInfo.json", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default/pkgContextInfo.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 729, "lastModifiedTime": 1753969211307}}, "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/outputs/default/pack.info": {"hashValue": "a3db5b0fd88d9d2895dcd9a8b784fddd", "name": "pack.info", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/outputs/default/pack.info", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 564, "lastModifiedTime": 1753968986838}}, "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/process_profile/default/module.json": {"hashValue": "5d08ead63f5536f741724b93a3ddd2c6", "name": "module.json", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/process_profile/default/module.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1430, "lastModifiedTime": 1753969474181}}, "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/router_map/default/temp-router-map.json": {"hashValue": "00ccae06acccc36438de234928e092c1", "name": "temp-router-map.json", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/router_map/default/temp-router-map.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 16, "lastModifiedTime": 1753969211515}}, "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/router_map/default/loader-router-map.json": {"hashValue": "fb6e14ef7290a924db04941a5ad70fe3", "name": "loader-router-map.json", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/router_map/default/loader-router-map.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 16, "lastModifiedTime": 1753969211515}}, "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resConfig.json": {"hashValue": "3093504c4e879b3203adde81d8eaaf02", "name": "resConfig.json", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resConfig.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1138, "lastModifiedTime": 1753968771692}}, "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/opt-compression.json": {"hashValue": "0839ecaa02ca43cb75af78b360e37cfa", "name": "opt-compression.json", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/opt-compression.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 204, "lastModifiedTime": 1753968771692}}, "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default/loader.json": {"hashValue": "51fd4b71b3e258f67bb87b0b0524ede1", "name": "loader.json", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default/loader.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 874, "lastModifiedTime": 1753969211549}}, "/Users/<USER>/github/LingXia/examples/harmony/entry/libs": {"hashValue": "7bb2250dcc809b94910595040be3bab5", "name": "libs", "path": "/Users/<USER>/github/Ling<PERSON>ia/examples/harmony/entry/libs", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "3e2b294556d8b621e1767b83512a3083", "name": "arm64-v8a", "path": "/Users/<USER>/github/Ling<PERSON>ia/examples/harmony/entry/libs/arm64-v8a", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "ce7cea8ff6c0423a0ce4e22aecb74167", "name": "liblingxia.so", "path": "/Users/<USER>/github/Ling<PERSON>ia/examples/harmony/entry/libs/arm64-v8a/liblingxia.so", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 9028088, "lastModifiedTime": 1753969382923}}]}]}, "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/libs/default": {"hashValue": "7bb2250dcc809b94910595040be3bab5", "name": "default", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/libs/default", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "3e2b294556d8b621e1767b83512a3083", "name": "arm64-v8a", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/libs/default/arm64-v8a", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "ce7cea8ff6c0423a0ce4e22aecb74167", "name": "liblingxia.so", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/libs/default/arm64-v8a/liblingxia.so", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 9028088, "lastModifiedTime": 1753969382923}}]}]}, "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources": {"hashValue": "69b4e708ecb2d68f96dbf55086ca3633", "name": "resources", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "b92dba667a75e2e06ab16ca7b1ce340e", "name": "base", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources/base", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "0bf8f84f3d3dd14988f926484bfe8961", "name": "element", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources/base/element", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "5f17e2220fc9ac8f83fb6d7125be494d", "name": "color.json", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources/base/element/color.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 98, "lastModifiedTime": 1750927498361}}, {"hashValue": "f63d908cd19a446e1446b1d81f05502b", "name": "float.json", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources/base/element/float.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 92, "lastModifiedTime": 1750927498362}}, {"hashValue": "d3e202074e2d5f80462c4aed03623df5", "name": "string.json", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources/base/element/string.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 220, "lastModifiedTime": 1750927498363}}]}, {"hashValue": "03b6149c7f1f7ae2c16a51d3f1b4cad9", "name": "media", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources/base/media", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "6510342a375804dd0af0677792d28650", "name": "background.png", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources/base/media/background.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 91942, "lastModifiedTime": 1750927498364}}, {"hashValue": "f733ceb3654d584351ff4707e6041d3f", "name": "foreground.png", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources/base/media/foreground.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 8805, "lastModifiedTime": 1750927498365}}, {"hashValue": "9dcb15caa500a500b6e7e28534511ca7", "name": "layered_image.json", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources/base/media/layered_image.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 109, "lastModifiedTime": 1750927498365}}, {"hashValue": "33a4b02169005a7f85a1c229af5ae6e0", "name": "startIcon.png", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources/base/media/startIcon.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 20093, "lastModifiedTime": 1750927498366}}]}, {"hashValue": "c26723b627d154c5d09fd89b4725acbc", "name": "profile", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources/base/profile", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "682660fa371fd9b2cfa28e8054cfd1a9", "name": "main_pages.json", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources/base/profile/main_pages.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 37, "lastModifiedTime": 1750927498367}}]}]}, {"hashValue": "824b2187822356c07f745c476eaf961e", "name": "dark", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources/dark", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "096d66666692368dd814924c1f1f4cba", "name": "element", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources/dark/element", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "994a4f4b7189d0d8d8a5c8236c80f877", "name": "color.json", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources/dark/element/color.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 98, "lastModifiedTime": 1750927498368}}]}]}, {"hashValue": "f942c8bb7ce5a4f54fb00f98cf6bcce9", "name": "rawfile", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources/rawfile", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "82a0e26bd869718639fe7be8b226cdf8", "name": "404.html", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources/rawfile/404.html", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1747, "lastModifiedTime": 1753969456474}}, {"hashValue": "ab2b8d8b8b1d5c000227e6ec649eabe2", "name": "app.json", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources/rawfile/app.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 265, "lastModifiedTime": 1753969456500}}, {"hashValue": "59fdaf78ffab07df6922b9df3dfad0a6", "name": "<PERSON><PERSON><PERSON><PERSON>", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources/rawfile/homelxapp", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "db5b34f79665be2f6e478f6a0405aaf0", "name": "images", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources/rawfile/homelxapp/images", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "13cfee38dcdf915e9a8939ab05c2bc4c", "name": ".DS_Store", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources/rawfile/homelxapp/images/.DS_Store", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 6148, "lastModifiedTime": 1753969456526}}, {"hashValue": "f3d7678a64697619db9b1298befbc7e4", "name": "api.png", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources/rawfile/homelxapp/images/api.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 365, "lastModifiedTime": 1753969456532}}, {"hashValue": "d82408ab8b51459b18eb04a5f0a6ab50", "name": "futuristic.jpg", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources/rawfile/homelxapp/images/futuristic.jpg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 431380, "lastModifiedTime": 1753969456524}}, {"hashValue": "7be934594d6865b1b13bab8e0a6dcb3e", "name": "home_selected.png", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources/rawfile/homelxapp/images/home_selected.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 244, "lastModifiedTime": 1753969456534}}, {"hashValue": "6bb11087cbea087438320272fe922d14", "name": "home.png", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources/rawfile/homelxapp/images/home.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 341, "lastModifiedTime": 1753969456528}}, {"hashValue": "fad96ef0cd00621f17d08f883c0fdbc0", "name": "todo_selected.png", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources/rawfile/homelxapp/images/todo_selected.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 300, "lastModifiedTime": 1753969456531}}, {"hashValue": "b5b9fcd63537c7497d52fe90a0c824b9", "name": "todo.png", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources/rawfile/homelxapp/images/todo.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 326, "lastModifiedTime": 1753969456529}}]}, {"hashValue": "4a5c73ce64ea8641cd61750128056701", "name": "logic.js", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources/rawfile/homelxapp/logic.js", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 10219, "lastModifiedTime": 1753969456536}}, {"hashValue": "daaf1419b5ef2e33d59a796070ca05b5", "name": "lxapp.css", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources/rawfile/homelxapp/lxapp.css", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 360, "lastModifiedTime": 1753969456537}}, {"hashValue": "6b5369d262ea03bcb5241378050c3869", "name": "lxapp.json", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources/rawfile/homelxapp/lxapp.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 896, "lastModifiedTime": 1753969456539}}, {"hashValue": "4a98df8e215b83647aba048bfad2c81b", "name": "pages", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources/rawfile/homelxapp/pages", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "5330ad966bd5abb3c45561d0ff9254b8", "name": "API", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources/rawfile/homelxapp/pages/API", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "f71a36d397f9b5fe26087fa1790ec74b", "name": "index.css", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources/rawfile/homelxapp/pages/API/index.css", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 43612, "lastModifiedTime": 1753969456556}}, {"hashValue": "fcb328bf1e8d60e8216d68d77d760e1c", "name": "index.json", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources/rawfile/homelxapp/pages/API/index.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 171, "lastModifiedTime": 1753969456560}}, {"hashValue": "8394d163296c931ce9c0da3e95092be6", "name": "index.tsx", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources/rawfile/homelxapp/pages/API/index.tsx", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 610, "lastModifiedTime": 1753969456555}}, {"hashValue": "c891385995e3229dc269f37d0c7ff32e", "name": "view.js", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources/rawfile/homelxapp/pages/API/view.js", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 197606, "lastModifiedTime": 1753969456559}}]}, {"hashValue": "c9dcf9532e7140e153aa87a6bb060368", "name": "home", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources/rawfile/homelxapp/pages/home", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "e09dbedd4d31e296f4b39b40a71ad910", "name": "index.css", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources/rawfile/homelxapp/pages/home/<USER>", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1699, "lastModifiedTime": 1753969456542}}, {"hashValue": "c3c33a97e0222c709f8866a9718faebc", "name": "index.html", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources/rawfile/homelxapp/pages/home/<USER>", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3796, "lastModifiedTime": 1753969456541}}, {"hashValue": "5e2d250d7fce979c98c74b8e87a78877", "name": "index.json", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources/rawfile/homelxapp/pages/home/<USER>", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 171, "lastModifiedTime": 1753969456544}}]}, {"hashValue": "0ea2b15dcf6e305148c4d71eb7cdced4", "name": "todo", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources/rawfile/homelxapp/pages/todo", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "bfe647497f495fe8313d82e94c8e9118", "name": "index.css", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources/rawfile/homelxapp/pages/todo/index.css", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 11225, "lastModifiedTime": 1753969456548}}, {"hashValue": "0dbd1f3e0195361034ce61ae7a807a7b", "name": "index.json", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources/rawfile/homelxapp/pages/todo/index.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 171, "lastModifiedTime": 1753969456551}}, {"hashValue": "7bf709a2f2c07976e14e0c37ab490e88", "name": "index.vue", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources/rawfile/homelxapp/pages/todo/index.vue", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 639, "lastModifiedTime": 1753969456547}}, {"hashValue": "865b902e00fcbf735db50c0ec54ca3ba", "name": "view.js", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources/rawfile/homelxapp/pages/todo/view.js", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 65846, "lastModifiedTime": 1753969456550}}]}]}]}, {"hashValue": "9c0b98d64a257b23ee5143dd3f4ca52f", "name": "webview-bridge.js", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources/rawfile/webview-bridge.js", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 13632, "lastModifiedTime": 1753969456487}}]}]}, "/Users/<USER>/github/LingXia/examples/harmony/AppScope/resources": {"hashValue": "ba9fce7ba8087b0a8857ac2c4db3d1d6", "name": "resources", "path": "/Users/<USER>/github/LingXia/examples/harmony/AppScope/resources", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "b69a3d925213a13f21ffd6edefbc0b30", "name": "base", "path": "/Users/<USER>/github/LingXia/examples/harmony/AppScope/resources/base", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "8dcaaabeff1edf12b23304a53162bba2", "name": "element", "path": "/Users/<USER>/github/LingXia/examples/harmony/AppScope/resources/base/element", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "bd75009e87d622c7bda84e876a4b4ecd", "name": "string.json", "path": "/Users/<USER>/github/LingXia/examples/harmony/AppScope/resources/base/element/string.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 88, "lastModifiedTime": 1750927498347}}]}, {"hashValue": "99b18ce929f788a86d1b5af17206e716", "name": "media", "path": "/Users/<USER>/github/LingXia/examples/harmony/AppScope/resources/base/media", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "bc97a2620012dccbd11be2c243d4b1d6", "name": "background.png", "path": "/Users/<USER>/github/LingXia/examples/harmony/AppScope/resources/base/media/background.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 91942, "lastModifiedTime": 1750927498348}}, {"hashValue": "e6feeb0f446d4ff33622924d7195e1ec", "name": "foreground.png", "path": "/Users/<USER>/github/LingXia/examples/harmony/AppScope/resources/base/media/foreground.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 8805, "lastModifiedTime": 1750927498349}}, {"hashValue": "70eebe895a586323dab4cd1f3dacd3c4", "name": "layered_image.json", "path": "/Users/<USER>/github/LingXia/examples/harmony/AppScope/resources/base/media/layered_image.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 109, "lastModifiedTime": 1750927498350}}]}]}]}, "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default": {"hashValue": "c4d1491ba0749250777b107141ef99cc", "name": "default", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "6b9583f62b474443db4f3a1da4c28c5c", "name": ".caches", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/.caches", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "051a797454cc595909a65b8c23b3c646", "name": "base", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/.caches/base", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "d38e5438576868a1d857cc169125b1f3", "name": "media", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/.caches/base/media", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "4d694440f0156cca947a4c08e91481a5", "name": "background.png", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/.caches/base/media/background.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 70283, "lastModifiedTime": 1753969474532}}, {"hashValue": "5a7788073d8e00bb0ee777f94d96716d", "name": "foreground.png", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/.caches/base/media/foreground.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 9325, "lastModifiedTime": 1753969474586}}]}]}]}, {"hashValue": "74f11857a902aed86ea84b930ee9a38b", "name": "ark_module.json", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/ark_module.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1146, "lastModifiedTime": 1753969474181}}, {"hashValue": "bd592b819d82aa3f5371ee549b0bf5c2", "name": "ids_map", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/ids_map", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "530f529d0cd5af67d6eb7bde27aca4c3", "name": "id_defined.json", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/ids_map/id_defined.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 873, "lastModifiedTime": 1753969474588}}]}, {"hashValue": "a8e286f47128c603278bb9b4199401d8", "name": "module.json", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/module.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1594, "lastModifiedTime": 1753969474453}}, {"hashValue": "0839ecaa02ca43cb75af78b360e37cfa", "name": "opt-compression.json", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/opt-compression.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 204, "lastModifiedTime": 1753968771692}}, {"hashValue": "3093504c4e879b3203adde81d8eaaf02", "name": "resConfig.json", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resConfig.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1138, "lastModifiedTime": 1753968771692}}, {"hashValue": "9d33b788a08376439199123a338c4317", "name": "resources", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "79d9877db733a58ef232ba62ed110002", "name": "base", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/base", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "67f1d85248471ce261982ad5b8a1602f", "name": "media", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/base/media", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "048220a306f7256e280d4f043432d986", "name": "background.png", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/base/media/background.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 70283, "lastModifiedTime": 1753969474533}}, {"hashValue": "9bd436242d379fe289a0614e2642abaf", "name": "foreground.png", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/base/media/foreground.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 9325, "lastModifiedTime": 1753969474587}}, {"hashValue": "affec2e82a4911464e9216713e722c4f", "name": "layered_image.json", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/base/media/layered_image.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 95, "lastModifiedTime": 1753969474451}}, {"hashValue": "c899833159141c1bd37126a37c30c3de", "name": "startIcon.png", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/base/media/startIcon.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 20093, "lastModifiedTime": 1753969474448}}]}, {"hashValue": "54b350358360d8974d08c49e376d2ae0", "name": "profile", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/base/profile", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "f5fa1ecb81b25da4d5382c2ba766ceb3", "name": "main_pages.json", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/base/profile/main_pages.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 37, "lastModifiedTime": 1753969474450}}]}]}, {"hashValue": "4457d9478d554df8c9f6bcd3f2c3c2a2", "name": "rawfile", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "ef3e2703b6dc3e2ca1820ded0098ff47", "name": "404.html", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/404.html", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1747, "lastModifiedTime": 1753969474437}}, {"hashValue": "1d0c7bef733b7e07cf123199c0a28a4a", "name": "app.json", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/app.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 265, "lastModifiedTime": 1753969474437}}, {"hashValue": "c9550d5fad73447fc24ba47f95d1c6b7", "name": "arkdata", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/arkdata", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "", "name": "schema", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/arkdata/schema", "type": "directory", "isSymbolicLink": false, "children": []}]}, {"hashValue": "6ec8cdc3aed4b1e5484fb041ad3382f2", "name": "<PERSON><PERSON><PERSON><PERSON>", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "75236c9effb6449c10b216d58a42d817", "name": "images", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/images", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "92aaab256cc9f4b108218a53efddf4dd", "name": "api.png", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/images/api.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 365, "lastModifiedTime": 1753969474448}}, {"hashValue": "7aa30d5b4540713726693de2cb53b322", "name": "futuristic.jpg", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/images/futuristic.jpg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 431380, "lastModifiedTime": 1753969474445}}, {"hashValue": "b7e7b92adca6844c467ca4a78e8a2fd1", "name": "home_selected.png", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/images/home_selected.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 244, "lastModifiedTime": 1753969474448}}, {"hashValue": "f6bf6c1cd9795206e5e3ac9ea8ccaad0", "name": "home.png", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/images/home.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 341, "lastModifiedTime": 1753969474446}}, {"hashValue": "98a2ec07c0b5e3764a7a3d28e26556f8", "name": "todo_selected.png", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/images/todo_selected.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 300, "lastModifiedTime": 1753969474447}}, {"hashValue": "425a5b91fd736d97833a96e79199e584", "name": "todo.png", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/images/todo.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 326, "lastModifiedTime": 1753969474447}}]}, {"hashValue": "0d1c003e500c24691039415a3f47f445", "name": "logic.js", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/logic.js", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 10219, "lastModifiedTime": 1753969474438}}, {"hashValue": "ac17aa3be9885a6b328656a7b5c7d14b", "name": "lxapp.css", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/lxapp.css", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 360, "lastModifiedTime": 1753969474437}}, {"hashValue": "65db131ac7e7cb44c189bd78529ddd5c", "name": "lxapp.json", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/lxapp.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 896, "lastModifiedTime": 1753969474449}}, {"hashValue": "6d6dc95b7ce545aec7a1e7be527452e9", "name": "pages", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "66302198dd948fba72835ccef00cde7a", "name": "API", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/API", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "b2c8dd56c83ae636918a53a00325bd1e", "name": "index.css", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/API/index.css", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 43612, "lastModifiedTime": 1753969474456}}, {"hashValue": "2bb078f089729c3bd723f92a9dafa665", "name": "index.json", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/API/index.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 171, "lastModifiedTime": 1753969474460}}, {"hashValue": "d982c25882ef1bf0d6b10bdc3699d35c", "name": "index.tsx", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/API/index.tsx", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 610, "lastModifiedTime": 1753969474455}}, {"hashValue": "e1f025ea1a7f0b8e8b84c17eec88d303", "name": "view.js", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/API/view.js", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 197606, "lastModifiedTime": 1753969474460}}]}, {"hashValue": "77ad1f09cfb281a31a28ceec5daed2de", "name": "home", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/home", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "3b4a5f6836dfddd4ca604fad3bd0990c", "name": "index.css", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/home/<USER>", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1699, "lastModifiedTime": 1753969474450}}, {"hashValue": "206d5c3c5bb00b37d69ffc66aa496b07", "name": "index.html", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/home/<USER>", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3796, "lastModifiedTime": 1753969474449}}, {"hashValue": "e7403fee4d1cac8390bba94aab2ee765", "name": "index.json", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/home/<USER>", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 171, "lastModifiedTime": 1753969474451}}]}, {"hashValue": "6b141965b6976fa6de715d166b24d4ad", "name": "todo", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/todo", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "ae968071d9d675c8fd0d4d5e69cd224d", "name": "index.css", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/todo/index.css", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 11225, "lastModifiedTime": 1753969474452}}, {"hashValue": "6b55a43dc59aca816e7f92f6d0a91f6b", "name": "index.json", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/todo/index.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 171, "lastModifiedTime": 1753969474455}}, {"hashValue": "592055c8943e0f2d5511de2db16a1942", "name": "index.vue", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/todo/index.vue", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 639, "lastModifiedTime": 1753969474451}}, {"hashValue": "cc812bee299dddd9d781138e374820c7", "name": "view.js", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/todo/view.js", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 65846, "lastModifiedTime": 1753969474454}}]}]}]}, {"hashValue": "ffb7d453bda99e4ae7248e41332a0938", "name": "webview-bridge.js", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/webview-bridge.js", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 13632, "lastModifiedTime": 1753969474461}}]}]}, {"hashValue": "b73bab1d5c14bd4e10c11a33707f5c6f", "name": "resources.index", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources.index", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 913, "lastModifiedTime": 1753969474588}}, {"hashValue": "892947a1fea9975b01c61c5d3f704fc6", "name": "ResourceTable.txt", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/ResourceTable.txt", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 351, "lastModifiedTime": 1753969474452}}]}, "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/generated/r/default/ResourceTable.h": {"hashValue": "022a80d6a657ad553bc75aabed366564", "name": "ResourceTable.h", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/generated/r/default/ResourceTable.h", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1244, "lastModifiedTime": 1753969474452}}, "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/generated/r/default": {"hashValue": "d653e13f0385ddab1ea4e2912764b871", "name": "default", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/generated/r/default", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "022a80d6a657ad553bc75aabed366564", "name": "ResourceTable.h", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/generated/r/default/ResourceTable.h", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1244, "lastModifiedTime": 1753969474452}}]}, "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default": {"hashValue": "afe82750633a28db98d2a81d372150d6", "name": "default", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "51fd4b71b3e258f67bb87b0b0524ede1", "name": "loader.json", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default/loader.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 874, "lastModifiedTime": 1753969211549}}, {"hashValue": "3192530e945484106518e8afe659c6f4", "name": "pkgContextInfo.json", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default/pkgContextInfo.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 729, "lastModifiedTime": 1753969211307}}]}, "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile": {"hashValue": "4457d9478d554df8c9f6bcd3f2c3c2a2", "name": "rawfile", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "ef3e2703b6dc3e2ca1820ded0098ff47", "name": "404.html", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/404.html", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1747, "lastModifiedTime": 1753969474437}}, {"hashValue": "1d0c7bef733b7e07cf123199c0a28a4a", "name": "app.json", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/app.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 265, "lastModifiedTime": 1753969474437}}, {"hashValue": "c9550d5fad73447fc24ba47f95d1c6b7", "name": "arkdata", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/arkdata", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "", "name": "schema", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/arkdata/schema", "type": "directory", "isSymbolicLink": false, "children": []}]}, {"hashValue": "6ec8cdc3aed4b1e5484fb041ad3382f2", "name": "<PERSON><PERSON><PERSON><PERSON>", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "75236c9effb6449c10b216d58a42d817", "name": "images", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/images", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "92aaab256cc9f4b108218a53efddf4dd", "name": "api.png", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/images/api.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 365, "lastModifiedTime": 1753969474448}}, {"hashValue": "7aa30d5b4540713726693de2cb53b322", "name": "futuristic.jpg", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/images/futuristic.jpg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 431380, "lastModifiedTime": 1753969474445}}, {"hashValue": "b7e7b92adca6844c467ca4a78e8a2fd1", "name": "home_selected.png", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/images/home_selected.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 244, "lastModifiedTime": 1753969474448}}, {"hashValue": "f6bf6c1cd9795206e5e3ac9ea8ccaad0", "name": "home.png", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/images/home.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 341, "lastModifiedTime": 1753969474446}}, {"hashValue": "98a2ec07c0b5e3764a7a3d28e26556f8", "name": "todo_selected.png", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/images/todo_selected.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 300, "lastModifiedTime": 1753969474447}}, {"hashValue": "425a5b91fd736d97833a96e79199e584", "name": "todo.png", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/images/todo.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 326, "lastModifiedTime": 1753969474447}}]}, {"hashValue": "0d1c003e500c24691039415a3f47f445", "name": "logic.js", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/logic.js", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 10219, "lastModifiedTime": 1753969474438}}, {"hashValue": "ac17aa3be9885a6b328656a7b5c7d14b", "name": "lxapp.css", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/lxapp.css", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 360, "lastModifiedTime": 1753969474437}}, {"hashValue": "65db131ac7e7cb44c189bd78529ddd5c", "name": "lxapp.json", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/lxapp.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 896, "lastModifiedTime": 1753969474449}}, {"hashValue": "6d6dc95b7ce545aec7a1e7be527452e9", "name": "pages", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "66302198dd948fba72835ccef00cde7a", "name": "API", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/API", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "b2c8dd56c83ae636918a53a00325bd1e", "name": "index.css", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/API/index.css", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 43612, "lastModifiedTime": 1753969474456}}, {"hashValue": "2bb078f089729c3bd723f92a9dafa665", "name": "index.json", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/API/index.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 171, "lastModifiedTime": 1753969474460}}, {"hashValue": "d982c25882ef1bf0d6b10bdc3699d35c", "name": "index.tsx", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/API/index.tsx", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 610, "lastModifiedTime": 1753969474455}}, {"hashValue": "e1f025ea1a7f0b8e8b84c17eec88d303", "name": "view.js", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/API/view.js", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 197606, "lastModifiedTime": 1753969474460}}]}, {"hashValue": "77ad1f09cfb281a31a28ceec5daed2de", "name": "home", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/home", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "3b4a5f6836dfddd4ca604fad3bd0990c", "name": "index.css", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/home/<USER>", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1699, "lastModifiedTime": 1753969474450}}, {"hashValue": "206d5c3c5bb00b37d69ffc66aa496b07", "name": "index.html", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/home/<USER>", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3796, "lastModifiedTime": 1753969474449}}, {"hashValue": "e7403fee4d1cac8390bba94aab2ee765", "name": "index.json", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/home/<USER>", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 171, "lastModifiedTime": 1753969474451}}]}, {"hashValue": "6b141965b6976fa6de715d166b24d4ad", "name": "todo", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/todo", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "ae968071d9d675c8fd0d4d5e69cd224d", "name": "index.css", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/todo/index.css", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 11225, "lastModifiedTime": 1753969474452}}, {"hashValue": "6b55a43dc59aca816e7f92f6d0a91f6b", "name": "index.json", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/todo/index.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 171, "lastModifiedTime": 1753969474455}}, {"hashValue": "592055c8943e0f2d5511de2db16a1942", "name": "index.vue", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/todo/index.vue", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 639, "lastModifiedTime": 1753969474451}}, {"hashValue": "cc812bee299dddd9d781138e374820c7", "name": "view.js", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/todo/view.js", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 65846, "lastModifiedTime": 1753969474454}}]}]}]}, {"hashValue": "ffb7d453bda99e4ae7248e41332a0938", "name": "webview-bridge.js", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/webview-bridge.js", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 13632, "lastModifiedTime": 1753969474461}}]}, "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/ResourceTable.txt": {"hashValue": "892947a1fea9975b01c61c5d3f704fc6", "name": "ResourceTable.txt", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/ResourceTable.txt", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 351, "lastModifiedTime": 1753969474452}}, "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/ark_module.json": {"hashValue": "74f11857a902aed86ea84b930ee9a38b", "name": "ark_module.json", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/ark_module.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1146, "lastModifiedTime": 1753969474181}}, "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/base/profile": {"hashValue": "54b350358360d8974d08c49e376d2ae0", "name": "profile", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/base/profile", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "f5fa1ecb81b25da4d5382c2ba766ceb3", "name": "main_pages.json", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/base/profile/main_pages.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 37, "lastModifiedTime": 1753969474450}}]}, "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/ets": {"hashValue": "3d06fb5eed97c88b616099da56002deb", "name": "ets", "path": "/Users/<USER>/github/Ling<PERSON>ia/examples/harmony/entry/src/main/ets", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "290065c628213cf47e8560e3548dabd1", "name": "entryability", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/ets/entryability", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "893817bd0a8464315c94dfbc42a85125", "name": "EntryAbility.ets", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/ets/entryability/EntryAbility.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1676, "lastModifiedTime": 1752040554877}}]}, {"hashValue": "0ce0c38e338e3e493067e1b4e2576d76", "name": "pages", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/ets/pages", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "3d1a552a1b17b67fcd9ade321b1862e4", "name": "Index.ets", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/ets/pages/Index.ets", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 301, "lastModifiedTime": 1753964728117}}]}]}, "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader_out/default/ets": {"hashValue": "a790e55f93c576540608303cac7f268a", "name": "ets", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader_out/default/ets", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "2bc14883d8536bd9018f6b9bcd39c306", "name": "modules.abc", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader_out/default/ets/modules.abc", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 220796, "lastModifiedTime": 1753969476541}}, {"hashValue": "a65bd86e9a9d9791507d22b0e0e692ff", "name": "sourceMaps.map", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader_out/default/ets/sourceMaps.map", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 86360, "lastModifiedTime": 1753969476441}}]}, "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/stripped_native_libs/default": {"hashValue": "effa3d2b5b5cd03b5b12742dcf5df6e4", "name": "default", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/stripped_native_libs/default", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "b82d226097ac3f9c69b0c2a20dd98011", "name": "arm64-v8a", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/stripped_native_libs/default/arm64-v8a", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "461016ddc72bbc7375ec6aac1121d9cf", "name": "liblingxia.so", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/stripped_native_libs/default/arm64-v8a/liblingxia.so", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 6129688, "lastModifiedTime": 1753969477385}}]}]}, "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/patch/default/base_native_libs.json": {"hashValue": "aa1ae45d961b90e236a80f542efff9a2", "name": "base_native_libs.json", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/patch/default/base_native_libs.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 349, "lastModifiedTime": 1753969478962}}, "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/module.json": {"hashValue": "a8e286f47128c603278bb9b4199401d8", "name": "module.json", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/module.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1594, "lastModifiedTime": 1753969474453}}, "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/package/default/module.json": {"hashValue": "ed6e267ccc90d063c81e7bbf03d724e2", "name": "module.json", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/package/default/module.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1594, "lastModifiedTime": 1753969478985}}, "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources": {"hashValue": "9d33b788a08376439199123a338c4317", "name": "resources", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "79d9877db733a58ef232ba62ed110002", "name": "base", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/base", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "67f1d85248471ce261982ad5b8a1602f", "name": "media", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/base/media", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "048220a306f7256e280d4f043432d986", "name": "background.png", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/base/media/background.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 70283, "lastModifiedTime": 1753969474533}}, {"hashValue": "9bd436242d379fe289a0614e2642abaf", "name": "foreground.png", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/base/media/foreground.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 9325, "lastModifiedTime": 1753969474587}}, {"hashValue": "affec2e82a4911464e9216713e722c4f", "name": "layered_image.json", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/base/media/layered_image.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 95, "lastModifiedTime": 1753969474451}}, {"hashValue": "c899833159141c1bd37126a37c30c3de", "name": "startIcon.png", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/base/media/startIcon.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 20093, "lastModifiedTime": 1753969474448}}]}, {"hashValue": "54b350358360d8974d08c49e376d2ae0", "name": "profile", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/base/profile", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "f5fa1ecb81b25da4d5382c2ba766ceb3", "name": "main_pages.json", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/base/profile/main_pages.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 37, "lastModifiedTime": 1753969474450}}]}]}, {"hashValue": "4457d9478d554df8c9f6bcd3f2c3c2a2", "name": "rawfile", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "ef3e2703b6dc3e2ca1820ded0098ff47", "name": "404.html", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/404.html", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1747, "lastModifiedTime": 1753969474437}}, {"hashValue": "1d0c7bef733b7e07cf123199c0a28a4a", "name": "app.json", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/app.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 265, "lastModifiedTime": 1753969474437}}, {"hashValue": "c9550d5fad73447fc24ba47f95d1c6b7", "name": "arkdata", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/arkdata", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "", "name": "schema", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/arkdata/schema", "type": "directory", "isSymbolicLink": false, "children": []}]}, {"hashValue": "6ec8cdc3aed4b1e5484fb041ad3382f2", "name": "<PERSON><PERSON><PERSON><PERSON>", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "75236c9effb6449c10b216d58a42d817", "name": "images", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/images", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "92aaab256cc9f4b108218a53efddf4dd", "name": "api.png", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/images/api.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 365, "lastModifiedTime": 1753969474448}}, {"hashValue": "7aa30d5b4540713726693de2cb53b322", "name": "futuristic.jpg", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/images/futuristic.jpg", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 431380, "lastModifiedTime": 1753969474445}}, {"hashValue": "b7e7b92adca6844c467ca4a78e8a2fd1", "name": "home_selected.png", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/images/home_selected.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 244, "lastModifiedTime": 1753969474448}}, {"hashValue": "f6bf6c1cd9795206e5e3ac9ea8ccaad0", "name": "home.png", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/images/home.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 341, "lastModifiedTime": 1753969474446}}, {"hashValue": "98a2ec07c0b5e3764a7a3d28e26556f8", "name": "todo_selected.png", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/images/todo_selected.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 300, "lastModifiedTime": 1753969474447}}, {"hashValue": "425a5b91fd736d97833a96e79199e584", "name": "todo.png", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/images/todo.png", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 326, "lastModifiedTime": 1753969474447}}]}, {"hashValue": "0d1c003e500c24691039415a3f47f445", "name": "logic.js", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/logic.js", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 10219, "lastModifiedTime": 1753969474438}}, {"hashValue": "ac17aa3be9885a6b328656a7b5c7d14b", "name": "lxapp.css", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/lxapp.css", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 360, "lastModifiedTime": 1753969474437}}, {"hashValue": "65db131ac7e7cb44c189bd78529ddd5c", "name": "lxapp.json", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/lxapp.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 896, "lastModifiedTime": 1753969474449}}, {"hashValue": "6d6dc95b7ce545aec7a1e7be527452e9", "name": "pages", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "66302198dd948fba72835ccef00cde7a", "name": "API", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/API", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "b2c8dd56c83ae636918a53a00325bd1e", "name": "index.css", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/API/index.css", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 43612, "lastModifiedTime": 1753969474456}}, {"hashValue": "2bb078f089729c3bd723f92a9dafa665", "name": "index.json", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/API/index.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 171, "lastModifiedTime": 1753969474460}}, {"hashValue": "d982c25882ef1bf0d6b10bdc3699d35c", "name": "index.tsx", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/API/index.tsx", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 610, "lastModifiedTime": 1753969474455}}, {"hashValue": "e1f025ea1a7f0b8e8b84c17eec88d303", "name": "view.js", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/API/view.js", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 197606, "lastModifiedTime": 1753969474460}}]}, {"hashValue": "77ad1f09cfb281a31a28ceec5daed2de", "name": "home", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/home", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "3b4a5f6836dfddd4ca604fad3bd0990c", "name": "index.css", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/home/<USER>", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 1699, "lastModifiedTime": 1753969474450}}, {"hashValue": "206d5c3c5bb00b37d69ffc66aa496b07", "name": "index.html", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/home/<USER>", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 3796, "lastModifiedTime": 1753969474449}}, {"hashValue": "e7403fee4d1cac8390bba94aab2ee765", "name": "index.json", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/home/<USER>", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 171, "lastModifiedTime": 1753969474451}}]}, {"hashValue": "6b141965b6976fa6de715d166b24d4ad", "name": "todo", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/todo", "type": "directory", "isSymbolicLink": false, "children": [{"hashValue": "ae968071d9d675c8fd0d4d5e69cd224d", "name": "index.css", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/todo/index.css", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 11225, "lastModifiedTime": 1753969474452}}, {"hashValue": "6b55a43dc59aca816e7f92f6d0a91f6b", "name": "index.json", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/todo/index.json", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 171, "lastModifiedTime": 1753969474455}}, {"hashValue": "592055c8943e0f2d5511de2db16a1942", "name": "index.vue", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/todo/index.vue", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 639, "lastModifiedTime": 1753969474451}}, {"hashValue": "cc812bee299dddd9d781138e374820c7", "name": "view.js", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/homelxapp/pages/todo/view.js", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 65846, "lastModifiedTime": 1753969474454}}]}]}]}, {"hashValue": "ffb7d453bda99e4ae7248e41332a0938", "name": "webview-bridge.js", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile/webview-bridge.js", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 13632, "lastModifiedTime": 1753969474461}}]}]}, "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources.index": {"hashValue": "b73bab1d5c14bd4e10c11a33707f5c6f", "name": "resources.index", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources.index", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 913, "lastModifiedTime": 1753969474588}}, "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader_out/default/ets/sourceMaps.map": {"hashValue": "a65bd86e9a9d9791507d22b0e0e692ff", "name": "sourceMaps.map", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader_out/default/ets/sourceMaps.map", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 86360, "lastModifiedTime": 1753969476441}}, "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/outputs/default/entry-default-unsigned.hap": {"hashValue": "eb2e3b7d008d364e90d25b7696a72794", "name": "entry-default-unsigned.hap", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/outputs/default/entry-default-unsigned.hap", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 7331090, "lastModifiedTime": 1753969479988}}, "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/source_map/default/sourceMaps.map": {"hashValue": "9794f6f48584bd71a8c9736f5dc4dc5f", "name": "sourceMaps.map", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/source_map/default/sourceMaps.map", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 86360, "lastModifiedTime": 1753969479022}}, "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/outputs/default/mapping/sourceMaps.map": {"hashValue": "8d3625a4e21cd7f953e35ff0a9fd18bd", "name": "sourceMaps.map", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/outputs/default/mapping/sourceMaps.map", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 86360, "lastModifiedTime": 1753969479025}}, "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/outputs/default/entry-default-signed.hap": {"hashValue": "57cdde4bdf21a2ffdd41d11190397aa1", "name": "entry-default-signed.hap", "path": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/outputs/default/entry-default-signed.hap", "type": "file", "isSymbolicLink": false, "fileMetaData": {"size": 7424383, "lastModifiedTime": 1753969480446}}, "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/generated/profile/default/ModuleInfo.ts": {"hashValue": "", "name": "emptyroot", "path": "", "type": "unknown", "isSymbolicLink": false, "children": []}, "/Users/<USER>/github/LingXia/examples/harmony/build/cache/default/integrated_hsp/integratedHspCache.json": {"hashValue": "", "name": "emptyroot", "path": "", "type": "unknown", "isSymbolicLink": false, "children": []}, "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/syscap/default/rpcid.sc": {"hashValue": "", "name": "emptyroot", "path": "", "type": "unknown", "isSymbolicLink": false, "children": []}, "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader_out/default/js": {"hashValue": "", "name": "emptyroot", "path": "", "type": "unknown", "isSymbolicLink": false, "children": []}, "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/outputs/default/symbol": {"hashValue": "", "name": "emptyroot", "path": "", "type": "unknown", "isSymbolicLink": false, "children": []}}