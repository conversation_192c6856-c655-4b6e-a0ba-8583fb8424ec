{":harmony:entry:default@PreBuild": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"apiType\",\"_value\":\"stageMode\",\"_valueType\":\"string\",\"_hash\":\"699c7565645ea3ea8c88551a4926a3d5\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"byteCodeHar\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"1918a3ccb645ccacd0aedd84fc6cf8a4\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"codeType\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"d6dcf21a9f078b661283507536e57ce8\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compatibleApiVersion\",\"_value\":17,\"_valueType\":\"number\",\"_hash\":\"7ebeb63c8f8703f7a7166363b4ae2928\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compileApiVersion\",\"_value\":17,\"_valueType\":\"number\",\"_hash\":\"7ed709b82b73f46ab4cfd211b8cb709b\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"configuration\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"d12f0038691f8f34d654391bbcee2f8e\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"configurationFile<PERSON><PERSON>\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"2b55287f40f7e8896b21bab4028e156b\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"customTypes\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"970a2695bffac1c5a4fa283dc36050e3\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"deviceType\",\"_value\":[\"phone\",\"tablet\"],\"_valueType\":\"object\",\"_hash\":\"6b8c09abb74b3d778d7ce1bc1efb26ef\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"integrated_hsp\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"2538281751f182d9123d2ab28efaf9be\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isSupportOhpmProj\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"f3a249d7e3f751316e931b8a08b074b4\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"moduleJsonOpt\",\"_value\":\"{\\\"jsonFilePath\\\":\\\"/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/module.json5\\\",\\\"profile\\\":{\\\"module\\\":{\\\"name\\\":\\\"entry\\\",\\\"type\\\":\\\"entry\\\",\\\"description\\\":\\\"$string:module_desc\\\",\\\"mainElement\\\":\\\"EntryAbility\\\",\\\"deviceTypes\\\":[\\\"phone\\\",\\\"tablet\\\"],\\\"deliveryWithInstall\\\":true,\\\"installationFree\\\":false,\\\"pages\\\":\\\"$profile:main_pages\\\",\\\"abilities\\\":[{\\\"name\\\":\\\"EntryAbility\\\",\\\"srcEntry\\\":\\\"./ets/entryability/EntryAbility.ets\\\",\\\"description\\\":\\\"$string:EntryAbility_desc\\\",\\\"icon\\\":\\\"$media:layered_image\\\",\\\"label\\\":\\\"$string:EntryAbility_label\\\",\\\"startWindowIcon\\\":\\\"$media:startIcon\\\",\\\"startWindowBackground\\\":\\\"$color:start_window_background\\\",\\\"exported\\\":true,\\\"skills\\\":[{\\\"entities\\\":[\\\"entity.system.home\\\"],\\\"actions\\\":[\\\"action.system.home\\\"]}]}]}},\\\"deviceTypes\\\":[\\\"phone\\\",\\\"tablet\\\"],\\\"deviceConfig\\\":\\\"deviceTypes\\\",\\\"configurationProfile\\\":\\\"module.json5\\\"}\",\"_valueType\":\"string\",\"_hash\":\"69c5d6c50ceed5acbc1000a995d5055a\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"profileModuleName\",\"_value\":\"entry\",\"_valueType\":\"string\",\"_hash\":\"b174ea6ff5824844dde5ad92f6b3ef2b\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"removePermissions\",\"_valueType\":\"undefined\",\"_hash\":\"0703cfc523a152c6195a8fd1935503e5\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"sdkToolchainsComponentVersion\",\"_value\":\"5.0.5.165\",\"_valueType\":\"string\",\"_hash\":\"9159f11811aaec1d86cd2ff98ff51b00\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"sourceRoots\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"a369c115d2c4122f3819759804ec9d35\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"targetStatusCode\",\"_value\":2,\"_valueType\":\"number\",\"_hash\":\"5a9255c0f4ee50904a9349ebafca8369\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"transformLib\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"7d270d0ce7ae5c6e2e32760cb396ea5a\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"useNormalizedOHMUrl\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"05eabcf12e6fe230e579e0fd37679db6\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "entry", "_taskName": "default@PreBuild", "_key": ":harmony:entry:default@PreBuild", "_executionId": ":harmony:entry:default@PreBuild:1753969211125", "_inputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/examples/harmony/AppScope/app.json5", {"isDirectory": false, "fileSnapShotHashValue": "c2861bcdef7a4cb63f0e8fa643418e3c"}], ["/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/module.json5", {"isDirectory": false, "fileSnapShotHashValue": "14e323fe74b7ddcff36e4ff44d9d9bc4"}], ["/Users/<USER>/github/LingXia/examples/harmony/build-profile.json5", {"fileSnapShotHashValue": "6eb54cb63c5d584e50b8d97dbe6b421a"}], ["/Users/<USER>/github/LingXia/examples/harmony/entry/build-profile.json5", {"fileSnapShotHashValue": "92d3474cb4e87a83329089af21dccd5f"}], ["/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources/base/profile/main_pages.json", {"fileSnapShotHashValue": "682660fa371fd9b2cfa28e8054cfd1a9"}], ["/Users/<USER>/github/<PERSON><PERSON><PERSON>/examples/harmony/hvigor/hvigor-config.json5", {"isDirectory": true, "fileSnapShotHashValue": "a7927226ebb534ac621a86c5a6406d1c"}], ["/Users/<USER>/github/LingXia/examples/harmony/entry/oh-package.json5", {"fileSnapShotHashValue": "e4dfe5e5c36f3b193ed88ac5cfe2408a"}], ["/Users/<USER>/github/LingXia/examples/harmony/oh-package.json5", {"fileSnapShotHashValue": "f4a4cebbabf106df318f4073cee0a070"}]]}, "_outputFiles": {"dataType": "Map", "value": []}}, ":harmony:lingxia:default@PreBuild": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"apiType\",\"_value\":\"stageMode\",\"_valueType\":\"string\",\"_hash\":\"699c7565645ea3ea8c88551a4926a3d5\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"byteCodeHar\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"1918a3ccb645ccacd0aedd84fc6cf8a4\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"codeType\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"d6dcf21a9f078b661283507536e57ce8\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compatibleApiVersion\",\"_value\":17,\"_valueType\":\"number\",\"_hash\":\"7ebeb63c8f8703f7a7166363b4ae2928\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compileApiVersion\",\"_value\":17,\"_valueType\":\"number\",\"_hash\":\"7ed709b82b73f46ab4cfd211b8cb709b\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"configuration\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"d12f0038691f8f34d654391bbcee2f8e\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"configurationFile<PERSON><PERSON>\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"2b55287f40f7e8896b21bab4028e156b\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"customTypes\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"970a2695bffac1c5a4fa283dc36050e3\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"deviceType\",\"_value\":[\"default\",\"tablet\"],\"_valueType\":\"object\",\"_hash\":\"3f44547af15457ff8e1ec09282ca9922\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"integrated_hsp\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"2538281751f182d9123d2ab28efaf9be\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isSupportOhpmProj\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"f3a249d7e3f751316e931b8a08b074b4\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"moduleJsonOpt\",\"_value\":\"{\\\"jsonFilePath\\\":\\\"/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/module.json5\\\",\\\"profile\\\":{\\\"module\\\":{\\\"name\\\":\\\"lingxia\\\",\\\"type\\\":\\\"har\\\",\\\"deviceTypes\\\":[\\\"default\\\",\\\"tablet\\\"],\\\"requestPermissions\\\":[{\\\"name\\\":\\\"ohos.permission.INTERNET\\\"}]}},\\\"deviceTypes\\\":[\\\"default\\\",\\\"tablet\\\"],\\\"deviceConfig\\\":\\\"deviceTypes\\\",\\\"configurationProfile\\\":\\\"module.json5\\\"}\",\"_valueType\":\"string\",\"_hash\":\"f5a26e11bb22897675f723598d98a3b6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"profileModuleName\",\"_value\":\"lingxia\",\"_valueType\":\"string\",\"_hash\":\"1f20e709cdcc3e410c5dfb43b0608109\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"removePermissions\",\"_valueType\":\"undefined\",\"_hash\":\"0703cfc523a152c6195a8fd1935503e5\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"sdkToolchainsComponentVersion\",\"_value\":\"5.0.5.165\",\"_valueType\":\"string\",\"_hash\":\"9159f11811aaec1d86cd2ff98ff51b00\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"sourceRoots\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"a369c115d2c4122f3819759804ec9d35\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"targetStatusCode\",\"_value\":2,\"_valueType\":\"number\",\"_hash\":\"5a9255c0f4ee50904a9349ebafca8369\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"transformLib\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"7d270d0ce7ae5c6e2e32760cb396ea5a\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"useNormalizedOHMUrl\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"05eabcf12e6fe230e579e0fd37679db6\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "lingxia", "_taskName": "default@PreBuild", "_key": ":harmony:lingxia:default@PreBuild", "_executionId": ":harmony:lingxia:default@PreBuild:1753969211140", "_inputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/examples/harmony/AppScope/app.json5", {"isDirectory": false, "fileSnapShotHashValue": "c2861bcdef7a4cb63f0e8fa643418e3c"}], ["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/module.json5", {"isDirectory": false, "fileSnapShotHashValue": "d1e04b294833c3b0aabf4ee492042755"}], ["/Users/<USER>/github/LingXia/examples/harmony/build-profile.json5", {"fileSnapShotHashValue": "6eb54cb63c5d584e50b8d97dbe6b421a"}], ["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build-profile.json5", {"fileSnapShotHashValue": "117dd35b0a260dbc88aa7dcfde0d2609"}], ["/Users/<USER>/github/<PERSON><PERSON><PERSON>/examples/harmony/hvigor/hvigor-config.json5", {"isDirectory": true, "fileSnapShotHashValue": "a7927226ebb534ac621a86c5a6406d1c"}], ["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/oh-package.json5", {"fileSnapShotHashValue": "40de8b8b9c6b9e68c3cfc79af5606691"}], ["/Users/<USER>/github/LingXia/examples/harmony/oh-package.json5", {"fileSnapShotHashValue": "f4a4cebbabf106df318f4073cee0a070"}]]}, "_outputFiles": {"dataType": "Map", "value": []}}, ":harmony:entry:default@CreateModuleInfo": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"ohosUiTransformOptimization\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": false, "_projectName": "harmony", "_moduleName": "entry", "_taskName": "default@CreateModuleInfo", "_key": ":harmony:entry:default@CreateModuleInfo", "_executionId": ":harmony:entry:default@CreateModuleInfo:1753969473724", "_inputFiles": {"dataType": "Map", "value": []}, "_outputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/generated/profile/default/ModuleInfo.ts", {"isDirectory": false, "fileSnapShotHashValue": ""}]]}}, ":harmony:entry:default@GenerateMetadata": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"artifactName\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"7f42b9cbe0181813f8dc60881bf90988\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isSigned\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"64af347b4db9779050781c231d4eec1b\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"relatedEntryModules\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"85b4d1fa598cae3838191eb99a09f4dd\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"remoteHspMetaData\",\"_value\":[],\"_valueType\":\"object\",\"_hash\":\"fbb8220e0c8d7b6197c24bdb06eb568c\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"targetDeviceType\",\"_value\":[\"phone\",\"tablet\"],\"_valueType\":\"object\",\"_hash\":\"7c784b382cfa2c3a26c881fb43380eab\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "entry", "_taskName": "default@GenerateMetadata", "_key": ":harmony:entry:default@GenerateMetadata", "_executionId": ":harmony:entry:default@GenerateMetadata:1753968771403", "_inputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/module.json5", {"fileSnapShotHashValue": "14e323fe74b7ddcff36e4ff44d9d9bc4"}]]}, "_outputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/hap_metadata/default/output_metadata.json", {"fileSnapShotHashValue": "25c76a186c92e901149f81aa42418a98"}]]}}, ":harmony:entry:default@GeneratePkgContextInfo": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"pkgContextInfoMap-@ohos/hamock\",\"_value\":\"566d0d1ba1afb93928c8984a8fae6421\",\"_valueType\":\"string\",\"_hash\":\"52638b5b8d5967d85f7d558e6c0897dd\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"pkgContextInfoMap-@ohos/hypium\",\"_value\":\"69dbd9141bebef47c71175aada1bd7bf\",\"_valueType\":\"string\",\"_hash\":\"65eb1ae89d72758b386a93dddd5db61d\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"pkgContextInfoMap-entry\",\"_value\":\"a8164913e569f3b3b2485e1811ddf44d\",\"_valueType\":\"string\",\"_hash\":\"6fbb0d2287cd1f34051162075393f37a\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"pkgContextInfoMap-liblingxia.so\",\"_value\":\"c2b4970ae341e7f9b4eb6cd9fe3b5ee5\",\"_valueType\":\"string\",\"_hash\":\"3d563f46f4da390a2c28e027ea863a70\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"pkgContextInfoMap-lingxia\",\"_value\":\"5a2a61f3b933e38f82721abab99ff541\",\"_valueType\":\"string\",\"_hash\":\"4bf723009a5db4cc17ac958c8a21a21f\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"useNormalizedOHMUrl\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"05eabcf12e6fe230e579e0fd37679db6\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "entry", "_taskName": "default@GeneratePkgContextInfo", "_key": ":harmony:entry:default@GeneratePkgContextInfo", "_executionId": ":harmony:entry:default@GeneratePkgContextInfo:1753969211306", "_inputFiles": {"dataType": "Map", "value": []}, "_outputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default/pkgContextInfo.json", {"isDirectory": false, "fileSnapShotHashValue": "3192530e945484106518e8afe659c6f4"}]]}}, ":harmony:entry:default@ProcessIntegratedHsp": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "entry", "_taskName": "default@ProcessIntegratedHsp", "_key": ":harmony:entry:default@ProcessIntegratedHsp", "_executionId": ":harmony:entry:default@ProcessIntegratedHsp:1753969473749", "_inputFiles": {"dataType": "Map", "value": []}, "_outputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/examples/harmony/build/cache/default/integrated_hsp/integratedHspCache.json", {"isDirectory": false, "fileSnapShotHashValue": ""}]]}}, ":harmony:lingxia:default@CreateHarBuildProfile": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"buildModeName\",\"_value\":\"debug\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"buildMode\",\"_value\":\"Debug\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"buildProfileFields\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"har<PERSON><PERSON><PERSON>\",\"_value\":\"1.0.0\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"harTargetName\",\"_value\":\"default\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "lingxia", "_taskName": "default@CreateHarBuildProfile", "_key": ":harmony:lingxia:default@CreateHarBuildProfile", "_executionId": ":harmony:lingxia:default@CreateHarBuildProfile:1753969473755", "_inputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/examples/harmony/AppScope/app.json5", {"fileSnapShotHashValue": "c2861bcdef7a4cb63f0e8fa643418e3c"}], ["/Users/<USER>/github/LingXia/examples/harmony/build-profile.json5", {"fileSnapShotHashValue": "6eb54cb63c5d584e50b8d97dbe6b421a"}]]}, "_outputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/BuildProfile.ets", {"fileSnapShotHashValue": "c731847ec63c1ad42905d1f9c262702d"}]]}}, ":harmony:lingxia:default@MergeProfile": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"targetSdkVersion\",\"_value\":17,\"_valueType\":\"number\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compatibleSdkVersion\",\"_value\":17,\"_valueType\":\"number\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"releaseType\",\"_value\":\"Release\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"buildRoot\",\"_value\":\"build\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isDebug\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"asanEnable\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"tsan<PERSON>nable\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"hwasan<PERSON><PERSON><PERSON>\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"ubsanEnable\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"module<PERSON>sonOpt\",\"_value\":\"{\\\"module\\\":{\\\"name\\\":\\\"lingxia\\\",\\\"type\\\":\\\"har\\\",\\\"deviceTypes\\\":[\\\"default\\\",\\\"tablet\\\"],\\\"requestPermissions\\\":[{\\\"name\\\":\\\"ohos.permission.INTERNET\\\"}]}}\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isHarModule\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"multiProjects\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"buildProfileAbilities\",\"_valueType\":\"undefined\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"projectConfigAppOpt\",\"_valueType\":\"undefined\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"appJsonOpt\",\"_value\":\"{\\\"app\\\":{\\\"bundleName\\\":\\\"app.lingxia.lxapp.example\\\",\\\"vendor\\\":\\\"example\\\",\\\"versionCode\\\":1000000,\\\"versionName\\\":\\\"1.0.0\\\",\\\"icon\\\":\\\"$media:layered_image\\\",\\\"label\\\":\\\"$string:app_name\\\"}}\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"integratedHsp\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"removePermissions\",\"_valueType\":\"undefined\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isBundledDependencies\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"packageName\",\"_value\":\"lingxia\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "lingxia", "_taskName": "default@MergeProfile", "_key": ":harmony:lingxia:default@MergeProfile", "_executionId": ":harmony:lingxia:default@MergeProfile:1753969473765", "_inputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/examples/harmony/AppScope/app.json5", {"fileSnapShotHashValue": "c2861bcdef7a4cb63f0e8fa643418e3c"}], ["/Users/<USER>/github/LingXia/examples/harmony/build-profile.json5", {"fileSnapShotHashValue": "6eb54cb63c5d584e50b8d97dbe6b421a"}], ["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/module.json5", {"fileSnapShotHashValue": "d1e04b294833c3b0aabf4ee492042755"}]]}, "_outputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/merge_profile/default/module.json", {"fileSnapShotHashValue": "2e0f7cb3afdd69724e6dbcad1d43ce9b"}]]}}, ":harmony:entry:default@SyscapTransform": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"deviceTypes\",\"_value\":[\"phone\",\"tablet\"],\"_valueType\":\"object\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "entry", "_taskName": "default@SyscapTransform", "_key": ":harmony:entry:default@SyscapTransform", "_executionId": ":harmony:entry:default@SyscapTransform:1753969473865", "_inputFiles": {"dataType": "Map", "value": [["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/toolchains/syscap_tool", {"fileSnapShotHashValue": "5b1af7e84dac76a2d5a61ee5b4a739a3"}], ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/device-define", {"fileSnapShotHashValue": "ee94c9a7b689d79dae11d607736e8fba"}]]}, "_outputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/syscap/default/rpcid.sc", {"fileSnapShotHashValue": ""}]]}}, ":harmony:entry:default@ProcessRouterMap": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"byteCodeHar\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"1918a3ccb645ccacd0aedd84fc6cf8a4\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"obfuscated\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"a69c27b9cf01a6710d3662cfe180239f\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"useNormalizedOHMUrl\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"05eabcf12e6fe230e579e0fd37679db6\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "entry", "_taskName": "default@ProcessRouterMap", "_key": ":harmony:entry:default@ProcessRouterMap", "_executionId": ":harmony:entry:default@ProcessRouterMap:1753969211512", "_inputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/examples/harmony/entry/oh-package.json5", {"fileSnapShotHashValue": "e4dfe5e5c36f3b193ed88ac5cfe2408a"}], ["/Users/<USER>/github/LingXia/examples/harmony/oh-package.json5", {"fileSnapShotHashValue": "f4a4cebbabf106df318f4073cee0a070"}], ["/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/module.json5", {"fileSnapShotHashValue": "14e323fe74b7ddcff36e4ff44d9d9bc4"}], ["/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default/pkgContextInfo.json", {"fileSnapShotHashValue": "3192530e945484106518e8afe659c6f4"}]]}, "_outputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/router_map/default/temp-router-map.json", {"fileSnapShotHashValue": "00ccae06acccc36438de234928e092c1"}], ["/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/router_map/default/loader-router-map.json", {"fileSnapShotHashValue": "fb6e14ef7290a924db04941a5ad70fe3"}]]}}, ":harmony:entry:default@CreateBuildProfile": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"buildMode\",\"_value\":\"Debug\",\"_valueType\":\"string\",\"_hash\":\"8120d22ada0d6de22b101e1f4ea16e81\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"buildModeName\",\"_value\":\"debug\",\"_valueType\":\"string\",\"_hash\":\"3f0246ea410fd9efa9fc7196cca045e3\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"buildProfileFields\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"21090e125326cef17357e44b789a1ab5\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"projectOhosConfigAppOpt\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"7613f1b2cb16d78bb723d12882b0d923\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "entry", "_taskName": "default@CreateBuildProfile", "_key": ":harmony:entry:default@CreateBuildProfile", "_executionId": ":harmony:entry:default@CreateBuildProfile:1753968986808", "_inputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/examples/harmony/AppScope/app.json5", {"fileSnapShotHashValue": "c2861bcdef7a4cb63f0e8fa643418e3c"}], ["/Users/<USER>/github/LingXia/examples/harmony/build-profile.json5", {"fileSnapShotHashValue": "6eb54cb63c5d584e50b8d97dbe6b421a"}]]}, "_outputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/generated/profile/default/BuildProfile.ets", {"fileSnapShotHashValue": "88de96158c7c0813855ac57df1798f9c"}]]}}, ":harmony:entry:default@MergeProfile": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"targetSdkVersion\",\"_value\":17,\"_valueType\":\"number\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compatibleSdkVersion\",\"_value\":17,\"_valueType\":\"number\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"releaseType\",\"_value\":\"Release\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"buildRoot\",\"_value\":\"build\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isDebug\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"asanEnable\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"tsan<PERSON>nable\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"hwasan<PERSON><PERSON><PERSON>\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"ubsanEnable\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"moduleJsonOpt\",\"_value\":\"{\\\"module\\\":{\\\"name\\\":\\\"entry\\\",\\\"type\\\":\\\"entry\\\",\\\"description\\\":\\\"$string:module_desc\\\",\\\"mainElement\\\":\\\"EntryAbility\\\",\\\"deviceTypes\\\":[\\\"phone\\\",\\\"tablet\\\"],\\\"deliveryWithInstall\\\":true,\\\"installationFree\\\":false,\\\"pages\\\":\\\"$profile:main_pages\\\",\\\"abilities\\\":[{\\\"name\\\":\\\"EntryAbility\\\",\\\"srcEntry\\\":\\\"./ets/entryability/EntryAbility.ets\\\",\\\"description\\\":\\\"$string:EntryAbility_desc\\\",\\\"icon\\\":\\\"$media:layered_image\\\",\\\"label\\\":\\\"$string:EntryAbility_label\\\",\\\"startWindowIcon\\\":\\\"$media:startIcon\\\",\\\"startWindowBackground\\\":\\\"$color:start_window_background\\\",\\\"exported\\\":true,\\\"skills\\\":[{\\\"entities\\\":[\\\"entity.system.home\\\"],\\\"actions\\\":[\\\"action.system.home\\\"]}]}]}}\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isHarModule\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"multiProjects\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"buildProfileAbilities\",\"_valueType\":\"undefined\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"projectConfigAppOpt\",\"_valueType\":\"undefined\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"appJsonOpt\",\"_value\":\"{\\\"app\\\":{\\\"bundleName\\\":\\\"app.lingxia.lxapp.example\\\",\\\"vendor\\\":\\\"example\\\",\\\"versionCode\\\":1000000,\\\"versionName\\\":\\\"1.0.0\\\",\\\"icon\\\":\\\"$media:layered_image\\\",\\\"label\\\":\\\"$string:app_name\\\"}}\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"integratedHsp\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"removePermissions\",\"_valueType\":\"undefined\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isBundledDependencies\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"packageName\",\"_value\":\"entry\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "entry", "_taskName": "default@MergeProfile", "_key": ":harmony:entry:default@MergeProfile", "_executionId": ":harmony:entry:default@MergeProfile:1753969473892", "_inputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/examples/harmony/AppScope/app.json5", {"fileSnapShotHashValue": "c2861bcdef7a4cb63f0e8fa643418e3c"}], ["/Users/<USER>/github/LingXia/examples/harmony/build-profile.json5", {"fileSnapShotHashValue": "6eb54cb63c5d584e50b8d97dbe6b421a"}], ["/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/module.json5", {"fileSnapShotHashValue": "14e323fe74b7ddcff36e4ff44d9d9bc4"}], ["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/merge_profile/default/module.json", {"fileSnapShotHashValue": "2e0f7cb3afdd69724e6dbcad1d43ce9b"}]]}, "_outputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/merge_profile/default/module.json", {"fileSnapShotHashValue": "279cd50159bdf31d0da5cdc84a187821"}]]}}, ":harmony:entry:default@GenerateLoaderJson": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"anBuildMode\",\"_value\":\"type\",\"_valueType\":\"string\",\"_hash\":\"de241a1eec94a2a622ff1c89f32846a2\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"apPath\",\"_value\":\"/Users/<USER>/github/LingXia/examples/harmony/entry/modules.ap\",\"_valueType\":\"string\",\"_hash\":\"75f886eee6de7c0fde32d3c4abc1751c\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compatibleApiVersion\",\"_value\":17,\"_valueType\":\"number\",\"_hash\":\"7ebeb63c8f8703f7a7166363b4ae2928\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compileApiVersion\",\"_value\":17,\"_valueType\":\"number\",\"_hash\":\"7ed709b82b73f46ab4cfd211b8cb709b\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compileMode\",\"_value\":\"esmodule\",\"_valueType\":\"string\",\"_hash\":\"dacafc8e0b77a950178a0b4d142dc32c\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"dependencyModuleAbility\",\"_valueType\":\"undefined\",\"_hash\":\"3fd1daff9f22581e3cadc0099d1d2fa3\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"fallbackAnBuild\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"a63e98bb3f368ced6ed4d5579ea7ca39\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"harNameOhmMap\",\"_value\":\"{}\",\"_valueType\":\"string\",\"_hash\":\"0faf006bccbcdc2c7f04ff2d8c87894f\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"hspNameOhmMap\",\"_value\":\"{}\",\"_valueType\":\"string\",\"_hash\":\"0e2d87e0c1ed279c66bc3efb8683e5d1\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isBundledDependencies\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"a69aa552317c7a6e087cb84a5722e050\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isByteCodeHar\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"c5f9b9e5cabee4253d52d0d01ca64ba2\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isFullCompilationEnabled\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"98b683d049e4e7fc32ef1be5321fe0b6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isHarWithCoverage\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"7224a86bd9c5f83cb9a1a61584afcfb4\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isOhosTest\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"d82a43074b4d7726f9a69dbce1ae80d2\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"modulePathMap\",\"_value\":\"{\\\"entry\\\":\\\"/Users/<USER>/github/LingXia/examples/harmony/entry\\\",\\\"lingxia\\\":\\\"/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia\\\"}\",\"_valueType\":\"string\",\"_hash\":\"3a180759fa7dd33fc0445176e2bc3783\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"module_dependencies\",\"_value\":\"{\\\"lingxia\\\":\\\"file:../../../lingxia-sdk/harmony/lingxia\\\"}\",\"_valueType\":\"string\",\"_hash\":\"a3bf87085e695aa1612ced6c6541255a\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"needSubmitArkTsWidget\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"dc1ab65720a503a3d9098eab280b7116\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"nodeModulesPath\",\"_value\":\"/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader_out/default/node_modules\",\"_valueType\":\"string\",\"_hash\":\"96f41742cdc77c0e0acdde13f0903f25\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"overrides\",\"_valueType\":\"undefined\",\"_hash\":\"0e8f66f8eb79c6f33fb153c3fc3942f4\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"projectRootPath\",\"_value\":\"/Users/<USER>/github/LingXia/examples/harmony\",\"_valueType\":\"string\",\"_hash\":\"12609be7955fc8693034be67890181a6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"project_dependencies\",\"_value\":\"{}\",\"_valueType\":\"string\",\"_hash\":\"e15496bf1de2273597f444f07f1ca6d5\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"shouldTreatHarAsHap\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"24c9a48f68bd9cc73238825ca10c9629\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"targetConfig\",\"_value\":\"{\\\"name\\\":\\\"default\\\"}\",\"_valueType\":\"string\",\"_hash\":\"44f0c01d44e2bbf4013c5bb1f232e1fe\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"useNormalizedOHMUrl\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"05eabcf12e6fe230e579e0fd37679db6\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "entry", "_taskName": "default@GenerateLoaderJson", "_key": ":harmony:entry:default@GenerateLoaderJson", "_executionId": ":harmony:entry:default@GenerateLoaderJson:1753969211540", "_inputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default/pkgContextInfo.json", {"fileSnapShotHashValue": "3192530e945484106518e8afe659c6f4"}], ["/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/router_map/default/temp-router-map.json", {"fileSnapShotHashValue": "00ccae06acccc36438de234928e092c1"}]]}, "_outputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default/loader.json", {"isDirectory": false, "fileSnapShotHashValue": "51fd4b71b3e258f67bb87b0b0524ede1"}]]}}, ":harmony:entry:default@MakePackInfo": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"appResOpt\",\"_value\":\"{\\\"app\\\":{\\\"bundleName\\\":\\\"app.lingxia.lxapp.example\\\",\\\"vendor\\\":\\\"example\\\",\\\"versionCode\\\":1000000,\\\"versionName\\\":\\\"1.0.0\\\",\\\"icon\\\":\\\"$media:layered_image\\\",\\\"label\\\":\\\"$string:app_name\\\"}}\",\"_valueType\":\"string\",\"_hash\":\"fa555500d1478cff8126c5cf7e3f60ab\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compatibleSdkVersion\",\"_value\":17,\"_valueType\":\"number\",\"_hash\":\"67f3677f856d8ed1468ea78c69f4fbd9\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compileSdkVersion\",\"_value\":17,\"_valueType\":\"number\",\"_hash\":\"d61ea2791cb90d3498ff2a5a5aa1c22a\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"deviceTypes\",\"_value\":[\"phone\",\"tablet\"],\"_valueType\":\"object\",\"_hash\":\"c166d88837153e59136adb6a4b649962\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"integratedHsp\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"e67900c25b1f9fb70cc779de77dc6912\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"moduleJsonOpt\",\"_value\":\"{\\\"module\\\":{\\\"name\\\":\\\"entry\\\",\\\"type\\\":\\\"entry\\\",\\\"description\\\":\\\"$string:module_desc\\\",\\\"mainElement\\\":\\\"EntryAbility\\\",\\\"deviceTypes\\\":[\\\"phone\\\",\\\"tablet\\\"],\\\"deliveryWithInstall\\\":true,\\\"pages\\\":\\\"$profile:main_pages\\\",\\\"abilities\\\":[{\\\"name\\\":\\\"EntryAbility\\\",\\\"srcEntry\\\":\\\"./ets/entryability/EntryAbility.ets\\\",\\\"description\\\":\\\"$string:EntryAbility_desc\\\",\\\"icon\\\":\\\"$media:layered_image\\\",\\\"label\\\":\\\"$string:EntryAbility_label\\\",\\\"startWindowIcon\\\":\\\"$media:startIcon\\\",\\\"startWindowBackground\\\":\\\"$color:start_window_background\\\",\\\"exported\\\":true,\\\"skills\\\":[{\\\"entities\\\":[\\\"entity.system.home\\\"],\\\"actions\\\":[\\\"action.system.home\\\"]}]}]}}\",\"_valueType\":\"string\",\"_hash\":\"161619e78a966b681de89392596d79ea\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"projectConfigAppOpt\",\"_valueType\":\"undefined\",\"_hash\":\"b52997704fa206ed96a13a1f2e464a85\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"releaseType\",\"_value\":\"Release\",\"_valueType\":\"string\",\"_hash\":\"bbcabdda034e97584f8c36f85b3ec517\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "entry", "_taskName": "default@MakePackInfo", "_key": ":harmony:entry:default@MakePackInfo", "_executionId": ":harmony:entry:default@MakePackInfo:1753968986835", "_inputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/examples/harmony/AppScope/app.json5", {"fileSnapShotHashValue": "c2861bcdef7a4cb63f0e8fa643418e3c"}], ["/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/module.json5", {"fileSnapShotHashValue": "14e323fe74b7ddcff36e4ff44d9d9bc4"}], ["/Users/<USER>/github/LingXia/examples/harmony/build-profile.json5", {"fileSnapShotHashValue": "6eb54cb63c5d584e50b8d97dbe6b421a"}]]}, "_outputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/outputs/default/pack.info", {"fileSnapShotHashValue": "a3db5b0fd88d9d2895dcd9a8b784fddd"}]]}}, ":harmony:entry:default@ProcessProfile": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"arkEnable\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compileMode\",\"_value\":\"esmodule\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"deviceTypes\",\"_value\":[\"phone\",\"tablet\"],\"_valueType\":\"object\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"dependency\",\"_value\":\"[]\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compatibleSdkVersionStage\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "entry", "_taskName": "default@ProcessProfile", "_key": ":harmony:entry:default@ProcessProfile", "_executionId": ":harmony:entry:default@ProcessProfile:1753969473989", "_inputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/merge_profile/default/module.json", {"fileSnapShotHashValue": "279cd50159bdf31d0da5cdc84a187821"}]]}, "_outputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/process_profile/default/module.json", {"fileSnapShotHashValue": "5d08ead63f5536f741724b93a3ddd2c6"}]]}}, ":harmony:lingxia:default@ProcessLibs": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"excludeFromHar\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"c33cbd85982f2e758a878e3aa30a9f01\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isBundledDependencies\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"a69aa552317c7a6e087cb84a5722e050\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isByteCodeHar\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"530d026140bb1ca73510f7bb41e3aee3\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "lingxia", "_taskName": "default@ProcessLibs", "_key": ":harmony:lingxia:default@ProcessLibs", "_executionId": ":harmony:lingxia:default@ProcessLibs:1753969211714", "_inputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/examples/harmony/build-profile.json5", {"fileSnapShotHashValue": "6eb54cb63c5d584e50b8d97dbe6b421a"}], ["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build-profile.json5", {"fileSnapShotHashValue": "117dd35b0a260dbc88aa7dcfde0d2609"}]]}, "_outputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/libs/default", {"isDirectory": true, "fileSnapShotHashValue": ""}]]}}, ":harmony:entry:default@ProcessResource": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/opt-compression.json\",\"_value\":\"{\\\"context\\\":{\\\"extensionPath\\\":\\\"/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/hms/toolchains/lib/libimage_transcoder_shared.dylib\\\"},\\\"compression\\\":{\\\"media\\\":{\\\"enable\\\":false},\\\"filters\\\":[]}}\",\"_valueType\":\"string\",\"_hash\":\"42a819097b79518335af130488f473b7\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"resConfigJsonContent\",\"_value\":\"{\\\"configPath\\\":\\\"/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/process_profile/default/module.json\\\",\\\"packageName\\\":\\\"app.lingxia.lxapp.example\\\",\\\"output\\\":\\\"/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default\\\",\\\"moduleNames\\\":\\\"entry,lingxia\\\",\\\"ResourceTable\\\":[\\\"/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/generated/r/default/ResourceTable.h\\\"],\\\"applicationResource\\\":\\\"/Users/<USER>/github/LingXia/examples/harmony/AppScope/resources\\\",\\\"moduleResources\\\":[\\\"/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources\\\"],\\\"dependencies\\\":[],\\\"iconCheck\\\":true,\\\"ids\\\":\\\"/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/ids_map\\\",\\\"definedIds\\\":\\\"/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/ids_map/id_defined.json\\\",\\\"definedSysIds\\\":\\\"/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/hms/toolchains/id_defined.json\\\"}\",\"_valueType\":\"string\",\"_hash\":\"613b0ba1b651edb03a308f7877c6fc6f\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"resource_str\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"c59ebe125e2aaa014a4454c9564cf3c6\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "entry", "_taskName": "default@ProcessResource", "_key": ":harmony:entry:default@ProcessResource", "_executionId": ":harmony:entry:default@ProcessResource:1753968771690", "_inputFiles": {"dataType": "Map", "value": []}, "_outputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resConfig.json", {"isDirectory": false, "fileSnapShotHashValue": "3093504c4e879b3203adde81d8eaaf02"}], ["/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/opt-compression.json", {"isDirectory": false, "fileSnapShotHashValue": "0839ecaa02ca43cb75af78b360e37cfa"}]]}}, ":harmony:entry:default@CompileResource": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"TARGET_CONFIG\",\"_value\":\"{\\\"name\\\":\\\"default\\\"}\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_COMMAND\",\"_value\":\"/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/toolchains/restool,-l,/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resConfig.json\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/toolchains/restool\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "entry", "_taskName": "default@CompileResource", "_key": ":harmony:entry:default@CompileResource", "_executionId": ":harmony:entry:default@CompileResource:1753969474223", "_inputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources", {"fileSnapShotHashValue": "69b4e708ecb2d68f96dbf55086ca3633"}], ["/Users/<USER>/github/LingXia/examples/harmony/AppScope/resources", {"fileSnapShotHashValue": "ba9fce7ba8087b0a8857ac2c4db3d1d6"}], ["/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/process_profile/default/module.json", {"isDirectory": false, "fileSnapShotHashValue": "5d08ead63f5536f741724b93a3ddd2c6"}], ["/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resConfig.json", {"isDirectory": false, "fileSnapShotHashValue": "3093504c4e879b3203adde81d8eaaf02"}]]}, "_outputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default", {"isDirectory": true, "fileSnapShotHashValue": "c4d1491ba0749250777b107141ef99cc"}], ["/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/generated/r/default/ResourceTable.h", {"isDirectory": false, "fileSnapShotHashValue": "022a80d6a657ad553bc75aabed366564"}], ["/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/generated/r/default", {"isDirectory": true, "fileSnapShotHashValue": "d653e13f0385ddab1ea4e2912764b871"}]]}}, ":harmony:entry:default@CompileArkTS": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"debuggable\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isArk\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"needCoverageInsert\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"ark.tsImportSendable\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"customTypes\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"lingxia\",\"_value\":\"lingxia: file:../../../lingxia-sdk/harmony/lingxia\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"caseSensitive<PERSON><PERSON><PERSON>\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"useNormalizedOHMUrl\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"transformLib\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compatibleSdkVersionStage\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"autoLazyImport\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"allowEmptyBundleName\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"reExportCheckMode\",\"_value\":\"noCheck\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"arkTsWdiget\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"OBFUSCATION_ENABLE\",\"_value\":\"undefined\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"copyCodeResourceEnable\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"copyCodeResourceExcludes\",\"_value\":[],\"_valueType\":\"object\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"noExternalImportByPath\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"ohos.uiTransform.Optimization\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "entry", "_taskName": "default@CompileArkTS", "_key": ":harmony:entry:default@CompileArkTS", "_executionId": ":harmony:entry:default@CompileArkTS:1753969474615", "_inputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default", {"isDirectory": true, "fileSnapShotHashValue": "afe82750633a28db98d2a81d372150d6"}], ["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/Index.ets", {"fileSnapShotHashValue": "7e9bff683cbe1b6262cf0cd24b2df5e0"}], ["/Users/<USER>/github/Ling<PERSON>ia/lingxia-sdk/harmony/lingxia/src/main/ets", {"isDirectory": true, "fileSnapShotHashValue": "29ba1360fadd24ac7794638ff7f006cf"}], ["/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile", {"isDirectory": true, "fileSnapShotHashValue": "4457d9478d554df8c9f6bcd3f2c3c2a2"}], ["/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/ResourceTable.txt", {"fileSnapShotHashValue": "892947a1fea9975b01c61c5d3f704fc6"}], ["/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/ark_module.json", {"fileSnapShotHashValue": "74f11857a902aed86ea84b930ee9a38b"}], ["/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/base/profile", {"isDirectory": true, "fileSnapShotHashValue": "54b350358360d8974d08c49e376d2ae0"}], ["/Users/<USER>/github/Ling<PERSON>ia/examples/harmony/entry/src/main/ets", {"isDirectory": true, "fileSnapShotHashValue": "3d06fb5eed97c88b616099da56002deb"}], ["/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default/pkgContextInfo.json", {"fileSnapShotHashValue": "3192530e945484106518e8afe659c6f4"}], ["/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/generated/profile/default/BuildProfile.ets", {"fileSnapShotHashValue": "88de96158c7c0813855ac57df1798f9c"}], ["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/BuildProfile.ets", {"fileSnapShotHashValue": "c731847ec63c1ad42905d1f9c262702d"}]]}, "_outputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader_out/default/ets", {"isDirectory": true, "fileSnapShotHashValue": "a790e55f93c576540608303cac7f268a"}]]}}, ":harmony:entry:default@BuildJS": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"debuggable\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isArk\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"needCoverageInsert\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"ark.tsImportSendable\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"customTypes\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"lingxia\",\"_value\":\"lingxia: file:../../../lingxia-sdk/harmony/lingxia\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"caseSensitive<PERSON><PERSON><PERSON>\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"useNormalizedOHMUrl\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"transformLib\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compatibleSdkVersionStage\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"autoLazyImport\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"allowEmptyBundleName\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"reExportCheckMode\",\"_value\":\"noCheck\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": false, "_projectName": "harmony", "_moduleName": "entry", "_taskName": "default@BuildJS", "_key": ":harmony:entry:default@BuildJS", "_executionId": ":harmony:entry:default@BuildJS:1753969474649", "_inputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default", {"isDirectory": true, "fileSnapShotHashValue": "afe82750633a28db98d2a81d372150d6"}], ["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/Index.ets", {"fileSnapShotHashValue": "7e9bff683cbe1b6262cf0cd24b2df5e0"}], ["/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile", {"isDirectory": true, "fileSnapShotHashValue": "4457d9478d554df8c9f6bcd3f2c3c2a2"}], ["/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/ResourceTable.txt", {"fileSnapShotHashValue": "892947a1fea9975b01c61c5d3f704fc6"}], ["/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/ark_module.json", {"fileSnapShotHashValue": "74f11857a902aed86ea84b930ee9a38b"}], ["/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/base/profile", {"isDirectory": true, "fileSnapShotHashValue": "54b350358360d8974d08c49e376d2ae0"}], ["/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default/pkgContextInfo.json", {"fileSnapShotHashValue": "3192530e945484106518e8afe659c6f4"}]]}, "_outputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader_out/default/js", {"isDirectory": true, "fileSnapShotHashValue": ""}]]}}, ":harmony:lingxia:default@DoNativeStrip": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"debugSymbol\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"055eeab6818b3f1a4978705cfba272fa\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "lingxia", "_taskName": "default@DoNativeStrip", "_key": ":harmony:lingxia:default@DoNativeStrip", "_executionId": ":harmony:lingxia:default@DoNativeStrip:1753960942666", "_inputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/libs/default", {"isDirectory": true, "fileSnapShotHashValue": ""}]]}, "_outputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/stripped_native_libs/default", {"isDirectory": true, "fileSnapShotHashValue": ""}]]}}, ":harmony:entry:default@ProcessLibs": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"isBundledDependencies\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isByteCodeHar\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"excludeFromHar\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "entry", "_taskName": "default@ProcessLibs", "_key": ":harmony:entry:default@ProcessLibs", "_executionId": ":harmony:entry:default@ProcessLibs:1753969474662", "_inputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/Ling<PERSON>ia/examples/harmony/entry/libs", {"isDirectory": true, "fileSnapShotHashValue": "7bb2250dcc809b94910595040be3bab5"}], ["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/libs/default", {"fileSnapShotHashValue": ""}], ["/Users/<USER>/github/LingXia/examples/harmony/build-profile.json5", {"fileSnapShotHashValue": "6eb54cb63c5d584e50b8d97dbe6b421a"}], ["/Users/<USER>/github/LingXia/examples/harmony/entry/build-profile.json5", {"fileSnapShotHashValue": "92d3474cb4e87a83329089af21dccd5f"}]]}, "_outputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/libs/default", {"isDirectory": true, "fileSnapShotHashValue": "7bb2250dcc809b94910595040be3bab5"}]]}}, ":harmony:entry:default@DoNativeStrip": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"debugSymbol\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "entry", "_taskName": "default@DoNativeStrip", "_key": ":harmony:entry:default@DoNativeStrip", "_executionId": ":harmony:entry:default@DoNativeStrip:1753969476815", "_inputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/libs/default", {"isDirectory": true, "fileSnapShotHashValue": "7bb2250dcc809b94910595040be3bab5"}]]}, "_outputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/stripped_native_libs/default", {"isDirectory": true, "fileSnapShotHashValue": "effa3d2b5b5cd03b5b12742dcf5df6e4"}]]}}, ":harmony:entry:default@CacheNativeLibs": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"debugSymbol\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "entry", "_taskName": "default@CacheNativeLibs", "_key": ":harmony:entry:default@CacheNativeLibs", "_executionId": ":harmony:entry:default@CacheNativeLibs:1753969477409", "_inputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/stripped_native_libs/default", {"isDirectory": true, "fileSnapShotHashValue": "effa3d2b5b5cd03b5b12742dcf5df6e4"}], ["/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/libs/default", {"isDirectory": true, "fileSnapShotHashValue": "7bb2250dcc809b94910595040be3bab5"}]]}, "_outputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/patch/default/base_native_libs.json", {"isDirectory": false, "fileSnapShotHashValue": "aa1ae45d961b90e236a80f542efff9a2"}]]}}, ":harmony:entry:default@GeneratePkgModuleJson": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "entry", "_taskName": "default@GeneratePkgModuleJson", "_key": ":harmony:entry:default@GeneratePkgModuleJson", "_executionId": ":harmony:entry:default@GeneratePkgModuleJson:1753969478983", "_inputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/module.json", {"fileSnapShotHashValue": "a8e286f47128c603278bb9b4199401d8"}]]}, "_outputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/package/default/module.json", {"fileSnapShotHashValue": "ed6e267ccc90d063c81e7bbf03d724e2"}]]}}, ":harmony:entry:default@PackageHap": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"hotReload\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"integrated_hsp\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"projectConfigAppOpt\",\"_valueType\":\"undefined\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"sourceMapDir\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"bundleType\",\"_value\":\"app\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_COMMAND\",\"_value\":\"java,-Dfile.encoding=utf-8,-jar,/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/toolchains/lib/app_packing_tool.jar,--mode,hap,--force,true,--lib-path,/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/stripped_native_libs/default,--json-path,/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/package/default/module.json,--resources-path,/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources,--index-path,/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources.index,--pack-info-path,/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/outputs/default/pack.info,--out-path,/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/outputs/default/entry-default-unsigned.hap,--ets-path,/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader_out/default/ets,--pkg-context-path,/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default/pkgContextInfo.json\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/toolchains/lib/app_packing_tool.jar\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "entry", "_taskName": "default@PackageHap", "_key": ":harmony:entry:default@PackageHap", "_executionId": ":harmony:entry:default@PackageHap:1753969479006", "_inputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/stripped_native_libs/default", {"isDirectory": false, "fileSnapShotHashValue": "effa3d2b5b5cd03b5b12742dcf5df6e4"}], ["/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/module.json", {"isDirectory": false, "fileSnapShotHashValue": "a8e286f47128c603278bb9b4199401d8"}], ["/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources", {"isDirectory": false, "fileSnapShotHashValue": "9d33b788a08376439199123a338c4317"}], ["/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources.index", {"isDirectory": false, "fileSnapShotHashValue": "b73bab1d5c14bd4e10c11a33707f5c6f"}], ["/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/outputs/default/pack.info", {"isDirectory": false, "fileSnapShotHashValue": "a3db5b0fd88d9d2895dcd9a8b784fddd"}], ["/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader_out/default/ets", {"isDirectory": false, "fileSnapShotHashValue": "a790e55f93c576540608303cac7f268a"}], ["/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default/pkgContextInfo.json", {"isDirectory": false, "fileSnapShotHashValue": "3192530e945484106518e8afe659c6f4"}], ["/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader_out/default/ets/sourceMaps.map", {"isDirectory": false, "fileSnapShotHashValue": "a65bd86e9a9d9791507d22b0e0e692ff"}], ["/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/generated/profile/default/BuildProfile.ets", {"fileSnapShotHashValue": "88de96158c7c0813855ac57df1798f9c"}]]}, "_outputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/outputs/default/entry-default-unsigned.hap", {"isDirectory": false, "fileSnapShotHashValue": "eb2e3b7d008d364e90d25b7696a72794"}], ["/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/source_map/default/sourceMaps.map", {"fileSnapShotHashValue": "9794f6f48584bd71a8c9736f5dc4dc5f"}], ["/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/outputs/default/mapping/sourceMaps.map", {"fileSnapShotHashValue": "8d3625a4e21cd7f953e35ff0a9fd18bd"}]]}}, ":harmony:entry:default@SignHap": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"enableSignTask\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"sdkToolchainsComponentVersion\",\"_value\":\"5.0.5.165\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"existSigningConfig\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"signingConfig_name\",\"_value\":\"default\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"signingConfig_type\",\"_value\":\"HarmonyOS\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"existMaterial\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"signingConfig_signAlg\",\"_value\":\"SHA256withECDSA\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"signingConfig_keyAlias\",\"_value\":\"debugKey\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"signingConfig_keyPassword\",\"_value\":\"0000001B546CA00405B83F107A8AE22ED7B1A17AD917F0506B4694ECA8E9444311D7A2EBC7778BEDE5D2C8\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"signingConfig_storePassword\",\"_value\":\"0000001B6A4DD3A948AD59B3ACA2B669AF826D64198D90F564E2A3D1194FDF103F7918FFCC5F510B27698A\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "entry", "_taskName": "default@SignHap", "_key": ":harmony:entry:default@SignHap", "_executionId": ":harmony:entry:default@SignHap:1753969480018", "_inputFiles": {"dataType": "Map", "value": [["/Users/<USER>/.ohos/config/default_harmony_Ox-3HNfvclQdbq4KOWhaXIc2hg7i7fx0_2L33NsWpQw=.cer", {"isDirectory": false, "fileSnapShotHashValue": "c16801588f67eec495a15015d0cd5727"}], ["/Users/<USER>/.ohos/config/default_harmony_Ox-3HNfvclQdbq4KOWhaXIc2hg7i7fx0_2L33NsWpQw=.p7b", {"isDirectory": false, "fileSnapShotHashValue": "70b46642533b215af092571c3e47f2e2"}], ["/Users/<USER>/.ohos/config/default_harmony_Ox-3HNfvclQdbq4KOWhaXIc2hg7i7fx0_2L33NsWpQw=.p12", {"isDirectory": false, "fileSnapShotHashValue": "2fee1e8d7e250424dbff0400315929d6"}], ["/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/outputs/default/entry-default-unsigned.hap", {"isDirectory": false, "fileSnapShotHashValue": "eb2e3b7d008d364e90d25b7696a72794"}]]}, "_outputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/outputs/default/entry-default-signed.hap", {"isDirectory": false, "fileSnapShotHashValue": "57cdde4bdf21a2ffdd41d11190397aa1"}]]}}, ":harmony:entry:default@CollectDebugSymbol": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": false, "_projectName": "harmony", "_moduleName": "entry", "_taskName": "default@CollectDebugSymbol", "_key": ":harmony:entry:default@CollectDebugSymbol", "_executionId": ":harmony:entry:default@CollectDebugSymbol:1753969480475", "_inputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader_out/default/ets/sourceMaps.map", {"fileSnapShotHashValue": "a65bd86e9a9d9791507d22b0e0e692ff"}], ["/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/libs/default", {"isDirectory": true, "fileSnapShotHashValue": "7bb2250dcc809b94910595040be3bab5"}]]}, "_outputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/outputs/default/symbol", {"isDirectory": true, "fileSnapShotHashValue": ""}]]}}}