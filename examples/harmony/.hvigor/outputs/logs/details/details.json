{"HVIGOR": {"IS_INCREMENTAL": true, "IS_DAEMON": true, "IS_PARALLEL": true, "IS_HVIGORFILE_TYPE_CHECK": false, "TASK_TIME": {"1043bfc77febe75fafec0c4309faccf1": {"CreateModuleInfo": 2261605, "PreCheckSyscap": 755164, "ProcessIntegratedHsp": 3001666, "SyscapTransform": 5104234, "MergeProfile": 6660569, "ConfigureCmake": 2131951, "ProcessProfile": 193402920, "BuildNativeWithCmake": 1427733, "CompileResource": 374120340, "BuildNativeWithNinja": 2955105, "BuildJS": 5785210, "CompileArkTS": 1969352728, "ProcessLibs": 2151580759, "DoNativeStrip": 588698232, "CacheNativeLibs": 1558321822, "GeneratePkgModuleJson": 4237288, "PackageHap": 1005795921, "SignHap": 450139435, "CollectDebugSymbol": 5537335, "assembleHap": 830523}, "7069fb83d8ce6f61be364a66a7bb21a7": {"CreateHarBuildProfile": 2800753, "ConfigureCmake": 726793, "MergeProfile": 91098511, "BuildNativeWithCmake": 643263, "BuildNativeWithNinja": 3232350}}, "TOTAL_TIME": 7299930966, "BUILD_ID": "202507312144331820"}, "HVIGOR_OHOS_PLUGIN": {"MODULES": [{"MODULE_NAME": "1043bfc77febe75fafec0c4309faccf1", "API_TYPE": "stageMode", "INCREMENTAL_TASKS": {"COMPILE_ARKTS": true}}, {"MODULE_NAME": "7069fb83d8ce6f61be364a66a7bb21a7", "API_TYPE": "stageMode"}], "BUILD_MODE": "debug", "USE_NORMALIZED_OHMURL": true}}