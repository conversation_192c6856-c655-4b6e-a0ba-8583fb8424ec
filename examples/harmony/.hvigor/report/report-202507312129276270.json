{"version": "2.0", "ppid": 50563, "events": [{"head": {"id": "3061138a-8cd7-42e4-a7b2-7c7a52f940fa", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 651955945225893}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82e6305f-745b-44de-a2e7-0ca03cb7f844", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 651955980096135}, "additional": {"children": ["82941fbd-a137-4d4a-a059-7f1bef908cde", "701a705e-f830-4a44-8354-aebd863b66fd", "acc99dce-7e39-4fbd-8243-1acf14eeee64", "633c5e83-e9e0-45f9-822d-a0a55b1e656f"], "state": "running", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": []}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "82941fbd-a137-4d4a-a059-7f1bef908cde", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 651955980106971}, "additional": {"children": [], "state": "running", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "82e6305f-745b-44de-a2e7-0ca03cb7f844"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "01ffb214-d100-4de7-9fe1-31fbba1bb4cf", "name": "hvigor build process will be closed with an error.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 651955996817455}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8836541-e500-4c20-bc58-03ccb944b1f1", "name": "ERROR: stacktrace = Error: Path not found: '/Users/<USER>/github/LingXia/examples/lingxia-sdk/harmony/lingxia'. Please check field: 'modules' in file: '/Users/<USER>/github/LingXia/examples/harmony/build-profile.json5'.\n    at HvigorLogger.errorMessageExit (/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/src/base/log/hvigor-log.js:1:3224)\n    at exitIfNotExists (/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/src/base/util/file-util.js:1:1265)\n    at /Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/src/base/external/core/hvigor-config.js:1:1304\n    at Array.forEach (<anonymous>)\n    at new HvigorConfig (/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/src/base/external/core/hvigor-config.js:1:1091)\n    at hvigorConfigInit (/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/src/base/external/core/hvigor-config.js:1:3418)\n    at init (/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/src/base/internal/lifecycle/init.js:1:2867)\n    at start (/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/src/base/boot/index.js:1:2462)\n    at boot (/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/src/base/boot/index.js:1:1762)\n    at Worker.<anonymous> (/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/src/base/daemon/cluster/worker-process-lifecycle.js:1:3340)", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 651956001472684}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24c52863-5926-4718-b27f-6bf14381bd46", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 651956007774032, "endTime": 651956008122867}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "09f69738-317c-4988-95e5-63deda5aeb76", "logId": "1363db2f-f8e8-4552-a889-9bdc7a7bd555"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1363db2f-f8e8-4552-a889-9bdc7a7bd555", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 651956007774032, "endTime": 651956008122867}, "additional": {"logType": "info", "children": [], "durationId": "24c52863-5926-4718-b27f-6bf14381bd46"}}, {"head": {"id": "e025ac2d-1a6a-4ee6-aca2-10b661c7d4d4", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 651955971241606, "endTime": 651956012042799}, "additional": {"time": {"year": 2025, "month": 7, "day": 31, "hour": 21, "minute": 29}, "completeCommand": "{\"prop\":[],\"_\":[\"assembleHap\"]};assembleHap", "hvigorVersion": "5.17.2", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "failed"}}, {"head": {"id": "55c24bc8-1038-4b49-8dc8-bc3928c303c9", "name": "BUILD FAILED in 38 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 651956012938124}, "additional": {"logType": "error", "children": []}}]}