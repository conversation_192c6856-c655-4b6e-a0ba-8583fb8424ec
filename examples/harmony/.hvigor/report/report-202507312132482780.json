{"version": "2.0", "ppid": 50563, "events": [{"head": {"id": "58c0609f-fc25-4884-8ab0-bff4da712c08", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 651956054638061}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a44d512a-87aa-4090-98a9-873a0e8ec20a", "name": "worker[0] exits with exit code 0.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 651956081491730}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4aaa54d-21f9-4ee0-85e7-a48e15d2f563", "name": "worker[1] exits with exit code 0.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 651956082649206}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8dcda06-3794-4c0f-9f2c-085ad1b78b4f", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 651956084548855}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "921226a0-5664-49dd-a8fd-f2e1ccb8d78f", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 651956084909420}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af72a55d-12bd-4326-a9fc-1f9dc7aba495", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652156611417949}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bda13103-4fe5-474d-879a-6d37d2d75fdb", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652156621038638, "endTime": 652159550868274}, "additional": {"children": ["1876b146-61bf-4186-b2d8-83ddc93933be", "773b8b73-48bc-447e-bbb0-788ad41aadd5", "fae080ec-187b-4e64-aa10-c0930afae876", "43d9f667-8e02-4d72-9326-d6cec082760e", "92fa2d8d-2cef-42dd-90c1-86b81f0fd7f4", "b02f4424-3a7a-4a31-9db2-b2b157cebbf1", "32dca32b-45ef-4273-a8d3-d2601168a297"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "13824596-f3f7-425d-96a1-b56127d5f4dc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1876b146-61bf-4186-b2d8-83ddc93933be", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652156621042340, "endTime": 652156643770311}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bda13103-4fe5-474d-879a-6d37d2d75fdb", "logId": "32f057f3-5c80-49e1-b7fa-e98778d9c631"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "773b8b73-48bc-447e-bbb0-788ad41aadd5", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652156643824958, "endTime": 652159547428986}, "additional": {"children": ["f629d456-cb15-471b-bf1d-be7e29552f70", "2c7db586-b05d-455b-a3aa-99fba15c8b05", "8dab676f-71b7-43f8-aa24-739e0ae86417", "4ab90aef-cfde-42aa-9f10-20980f26b9d2", "e8a33ae0-b82d-4a48-a1b1-7e11a7b96480", "675f7dce-a78a-4877-a487-38841fa7762b", "ff70f298-80d7-415d-b79d-36f7d1e10cd9", "c5c72430-bc2f-45b4-aaf3-447509742020", "1e71d470-b768-4b03-8588-30e41feb6f7d"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bda13103-4fe5-474d-879a-6d37d2d75fdb", "logId": "9b584efa-2944-4752-b341-fdb4e6b332e9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fae080ec-187b-4e64-aa10-c0930afae876", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159547467080, "endTime": 652159550825918}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bda13103-4fe5-474d-879a-6d37d2d75fdb", "logId": "6d361901-a1f6-42e4-bf77-c35581d4f725"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "43d9f667-8e02-4d72-9326-d6cec082760e", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159550833752, "endTime": 652159550855252}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bda13103-4fe5-474d-879a-6d37d2d75fdb", "logId": "96fc5383-8059-402f-b045-7decb7cc9e04"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "92fa2d8d-2cef-42dd-90c1-86b81f0fd7f4", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652156625661625, "endTime": 652156625764044}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bda13103-4fe5-474d-879a-6d37d2d75fdb", "logId": "baa0daa0-2e94-4406-8901-e77ac8a7f3a9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "baa0daa0-2e94-4406-8901-e77ac8a7f3a9", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652156625661625, "endTime": 652156625764044}, "additional": {"logType": "info", "children": [], "durationId": "92fa2d8d-2cef-42dd-90c1-86b81f0fd7f4", "parent": "13824596-f3f7-425d-96a1-b56127d5f4dc"}}, {"head": {"id": "b02f4424-3a7a-4a31-9db2-b2b157cebbf1", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652156633585294, "endTime": 652156633668753}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bda13103-4fe5-474d-879a-6d37d2d75fdb", "logId": "2381ea90-ae29-4830-80a7-8a55506e2b91"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2381ea90-ae29-4830-80a7-8a55506e2b91", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652156633585294, "endTime": 652156633668753}, "additional": {"logType": "info", "children": [], "durationId": "b02f4424-3a7a-4a31-9db2-b2b157cebbf1", "parent": "13824596-f3f7-425d-96a1-b56127d5f4dc"}}, {"head": {"id": "71e7af9c-2618-41bd-83c9-54c00fa12299", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652156634749992}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c839a92-75cb-48b6-9ceb-7b5799b2d589", "name": "Cache service initialization finished in 9 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652156643611470}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32f057f3-5c80-49e1-b7fa-e98778d9c631", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652156621042340, "endTime": 652156643770311}, "additional": {"logType": "info", "children": [], "durationId": "1876b146-61bf-4186-b2d8-83ddc93933be", "parent": "13824596-f3f7-425d-96a1-b56127d5f4dc"}}, {"head": {"id": "f629d456-cb15-471b-bf1d-be7e29552f70", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652156649452459, "endTime": 652156649475325}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "773b8b73-48bc-447e-bbb0-788ad41aadd5", "logId": "40f426ea-da7b-4b4d-89e1-2d3ed8826d05"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2c7db586-b05d-455b-a3aa-99fba15c8b05", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652156649561068, "endTime": 652156653767558}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "773b8b73-48bc-447e-bbb0-788ad41aadd5", "logId": "612fcc9d-efac-42db-80d9-76f1d49974ab"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8dab676f-71b7-43f8-aa24-739e0ae86417", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652156653935591, "endTime": 652159292418192}, "additional": {"children": ["aa59a706-150d-4701-9d7f-851570ea8dab", "5ea5ca91-828a-4092-a311-63101039b9e7", "fd4fb2de-5ff6-4437-9d6f-b8dcc3bb124a"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "773b8b73-48bc-447e-bbb0-788ad41aadd5", "logId": "2fe4858c-8e00-40e3-b1e1-70a63b8344b4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4ab90aef-cfde-42aa-9f10-20980f26b9d2", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159292599594, "endTime": 652159404612650}, "additional": {"children": ["77ed6562-df54-4d7a-98e3-33a77cd05135", "1737a14e-e52c-4c42-b79f-9952de9f9ccc"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "773b8b73-48bc-447e-bbb0-788ad41aadd5", "logId": "fa72db64-dbd8-465e-9457-19aff54ddeb9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e8a33ae0-b82d-4a48-a1b1-7e11a7b96480", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159404705422, "endTime": 652159510027053}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "773b8b73-48bc-447e-bbb0-788ad41aadd5", "logId": "3ee9826e-487e-463a-88a6-e580eac65064"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "675f7dce-a78a-4877-a487-38841fa7762b", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159511569777, "endTime": 652159522527696}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "773b8b73-48bc-447e-bbb0-788ad41aadd5", "logId": "63e6bae5-0553-417f-91bb-a6f4250f47f2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ff70f298-80d7-415d-b79d-36f7d1e10cd9", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159522553293, "endTime": 652159547255595}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "773b8b73-48bc-447e-bbb0-788ad41aadd5", "logId": "c66fbc98-c6ee-41f2-8356-a8d093cfe56a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c5c72430-bc2f-45b4-aaf3-447509742020", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159547279585, "endTime": 652159547414048}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "773b8b73-48bc-447e-bbb0-788ad41aadd5", "logId": "817523e4-fd90-400e-b259-9c673f55521e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "40f426ea-da7b-4b4d-89e1-2d3ed8826d05", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652156649452459, "endTime": 652156649475325}, "additional": {"logType": "info", "children": [], "durationId": "f629d456-cb15-471b-bf1d-be7e29552f70", "parent": "9b584efa-2944-4752-b341-fdb4e6b332e9"}}, {"head": {"id": "612fcc9d-efac-42db-80d9-76f1d49974ab", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652156649561068, "endTime": 652156653767558}, "additional": {"logType": "info", "children": [], "durationId": "2c7db586-b05d-455b-a3aa-99fba15c8b05", "parent": "9b584efa-2944-4752-b341-fdb4e6b332e9"}}, {"head": {"id": "aa59a706-150d-4701-9d7f-851570ea8dab", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652156654695387, "endTime": 652156654758432}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8dab676f-71b7-43f8-aa24-739e0ae86417", "logId": "35586908-230b-4ba6-a8c3-0d964739736b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "35586908-230b-4ba6-a8c3-0d964739736b", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652156654695387, "endTime": 652156654758432}, "additional": {"logType": "info", "children": [], "durationId": "aa59a706-150d-4701-9d7f-851570ea8dab", "parent": "2fe4858c-8e00-40e3-b1e1-70a63b8344b4"}}, {"head": {"id": "5ea5ca91-828a-4092-a311-63101039b9e7", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652156658110950, "endTime": 652159291525545}, "additional": {"children": ["23d9bc92-f1b7-43b8-8bd3-7149bea9044b", "6dc42cd9-963a-47a2-8011-3d7337798037"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8dab676f-71b7-43f8-aa24-739e0ae86417", "logId": "eb90a569-fe6c-4cff-96cc-a5136eb94d37"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "23d9bc92-f1b7-43b8-8bd3-7149bea9044b", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652156658112630, "endTime": 652158924123866}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5ea5ca91-828a-4092-a311-63101039b9e7", "logId": "241dcec5-c80b-462d-bfbb-14226dc3d044"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6dc42cd9-963a-47a2-8011-3d7337798037", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652158924161797, "endTime": 652159291498413}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "5ea5ca91-828a-4092-a311-63101039b9e7", "logId": "e963e0ef-6666-4c29-b71c-60f6cc5d4438"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "36e656a6-507f-4ca1-994c-8c20ffbc4188", "name": "hvigorfile, resolving /Users/<USER>/github/Ling<PERSON>ia/examples/harmony/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652156658124412}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22fd20bb-527d-4d11-8506-3eeab50b4389", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652158923894992}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "241dcec5-c80b-462d-bfbb-14226dc3d044", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652156658112630, "endTime": 652158924123866}, "additional": {"logType": "info", "children": [], "durationId": "23d9bc92-f1b7-43b8-8bd3-7149bea9044b", "parent": "eb90a569-fe6c-4cff-96cc-a5136eb94d37"}}, {"head": {"id": "64f8810f-3107-417d-9d83-18320e432fa3", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652158924347015}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6ad812b-47a9-45bc-bf64-381d1626e815", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159195862660}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14d2907a-d485-4a08-9421-aaa3c2e63266", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159196172195}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37778cdd-140c-47b1-b895-ed8e8deadd49", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159197009098}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c93a081-5226-4916-8682-dc62e3ec9f6b", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159197143243}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c781d8b7-8ae5-4acb-ba4b-bbd738403991", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159200693632}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "468f8066-5240-4bde-ac05-e01be0934e14", "name": "not found resModel json file in : /Users/<USER>/github/LingXia/examples/harmony/entry/src/ohosTest/module.json5", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159202771450}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ffeb2a30-2977-45e2-8434-40fdcbee3ea8", "name": "not found resModel json file in : /Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/ohosTest/module.json5", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159215638082}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13b31951-b59a-4b53-a5e2-70c54577b250", "name": "harmonyOS sdk scan", "description": "HarmonyOS sdk scan", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159224827731, "endTime": 652159229953589}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "Other", "taskRunReasons": []}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f87e3251-fe58-4e64-bcb0-de3024365628", "name": "Local scan or download HarmonyOS sdk components toolchains,ets,js,native,previewer", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159225398772}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e15ae07d-2e0c-4eb3-9178-71ad076053c9", "name": "hmscore sdk scan", "description": "Hmscore sdk scan", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159230812974, "endTime": 652159232175605}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "Other", "taskRunReasons": []}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "056c61a9-cf34-426a-abe0-f763b83c4353", "name": "Local scan or download hmscore sdk components toolchains,ets,native", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159231305186}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02966bc5-323a-4b9f-b074-ef5fc744d6e4", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159237165497}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4bd3c06-3f95-47bb-94ba-0d142b6b15e2", "name": "Sdk init in 47 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159269130634}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83f55f77-1877-4bb4-a747-c57d217a6514", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159269415832}, "additional": {"time": {"year": 2025, "month": 7, "day": 31, "hour": 21, "minute": 32}, "markType": "other"}}, {"head": {"id": "2af3a794-754b-4f8a-bbde-200fe01aa22f", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159269523308}, "additional": {"time": {"year": 2025, "month": 7, "day": 31, "hour": 21, "minute": 32}, "markType": "other"}}, {"head": {"id": "a086e185-46e5-4c7b-9c5b-0f9595746b90", "name": "Project task initialization takes 20 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159290978803}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19f2e34e-4413-4d02-8a0b-8db93fe22f7d", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159291279224}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0243a2b2-3c9d-4e75-9425-e9409fdcd9ee", "name": "hvigorfile, no custom plugins were found in /Users/<USER>/github/LingXia/examples/harmony/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159291365064}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9542a92-13e7-4f84-bd03-707625b6e904", "name": "hvigorfile, resolve finished /Users/<USER>/github/Ling<PERSON>ia/examples/harmony/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159291421166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e963e0ef-6666-4c29-b71c-60f6cc5d4438", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652158924161797, "endTime": 652159291498413}, "additional": {"logType": "info", "children": [], "durationId": "6dc42cd9-963a-47a2-8011-3d7337798037", "parent": "eb90a569-fe6c-4cff-96cc-a5136eb94d37"}}, {"head": {"id": "eb90a569-fe6c-4cff-96cc-a5136eb94d37", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652156658110950, "endTime": 652159291525545}, "additional": {"logType": "info", "children": ["241dcec5-c80b-462d-bfbb-14226dc3d044", "e963e0ef-6666-4c29-b71c-60f6cc5d4438"], "durationId": "5ea5ca91-828a-4092-a311-63101039b9e7", "parent": "2fe4858c-8e00-40e3-b1e1-70a63b8344b4"}}, {"head": {"id": "fd4fb2de-5ff6-4437-9d6f-b8dcc3bb124a", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159292319413, "endTime": 652159292380801}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8dab676f-71b7-43f8-aa24-739e0ae86417", "logId": "7d7e98c6-6896-41c6-be50-2e1599e52873"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7d7e98c6-6896-41c6-be50-2e1599e52873", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159292319413, "endTime": 652159292380801}, "additional": {"logType": "info", "children": [], "durationId": "fd4fb2de-5ff6-4437-9d6f-b8dcc3bb124a", "parent": "2fe4858c-8e00-40e3-b1e1-70a63b8344b4"}}, {"head": {"id": "2fe4858c-8e00-40e3-b1e1-70a63b8344b4", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652156653935591, "endTime": 652159292418192}, "additional": {"logType": "info", "children": ["35586908-230b-4ba6-a8c3-0d964739736b", "eb90a569-fe6c-4cff-96cc-a5136eb94d37", "7d7e98c6-6896-41c6-be50-2e1599e52873"], "durationId": "8dab676f-71b7-43f8-aa24-739e0ae86417", "parent": "9b584efa-2944-4752-b341-fdb4e6b332e9"}}, {"head": {"id": "77ed6562-df54-4d7a-98e3-33a77cd05135", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159293126006, "endTime": 652159335035856}, "additional": {"children": ["909d258a-4420-4041-8e21-4ee2b480ff0b", "9d16c7c4-3ab4-4a6c-90bf-84076f182d44", "1920cb1a-bb2f-4b3a-b4a2-9c25d6eee8ce"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4ab90aef-cfde-42aa-9f10-20980f26b9d2", "logId": "8b080126-08c8-4be2-a160-42df9fa812c0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "909d258a-4420-4041-8e21-4ee2b480ff0b", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159295537233, "endTime": 652159295564049}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "77ed6562-df54-4d7a-98e3-33a77cd05135", "logId": "0f59ea2b-60cd-42ec-b3ea-26c2abccccab"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0f59ea2b-60cd-42ec-b3ea-26c2abccccab", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159295537233, "endTime": 652159295564049}, "additional": {"logType": "info", "children": [], "durationId": "909d258a-4420-4041-8e21-4ee2b480ff0b", "parent": "8b080126-08c8-4be2-a160-42df9fa812c0"}}, {"head": {"id": "9d16c7c4-3ab4-4a6c-90bf-84076f182d44", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159296982374, "endTime": 652159333500201}, "additional": {"children": ["e5732c38-24f1-4880-a66f-1ef0d1ba67a1", "1f17e6bc-0436-4510-9d2b-8eae9bb9fbf4"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "77ed6562-df54-4d7a-98e3-33a77cd05135", "logId": "6c3c435f-f712-4754-bc22-437cbabd3f65"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e5732c38-24f1-4880-a66f-1ef0d1ba67a1", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159296983991, "endTime": 652159305729824}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9d16c7c4-3ab4-4a6c-90bf-84076f182d44", "logId": "8cb3f9a1-fdea-4658-91d8-9d58cce73cfd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1f17e6bc-0436-4510-9d2b-8eae9bb9fbf4", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159305753403, "endTime": 652159333485056}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "9d16c7c4-3ab4-4a6c-90bf-84076f182d44", "logId": "259b955c-dcb6-482f-a1cf-78ead5642702"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1573da84-45a8-40a4-8280-a4814b157b04", "name": "hvigorfile, resolving /Users/<USER>/github/Ling<PERSON>ia/examples/harmony/entry/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159296991734}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c8860f4-a339-4e59-abfc-c366c74fd3a0", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159305583847}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8cb3f9a1-fdea-4658-91d8-9d58cce73cfd", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159296983991, "endTime": 652159305729824}, "additional": {"logType": "info", "children": [], "durationId": "e5732c38-24f1-4880-a66f-1ef0d1ba67a1", "parent": "6c3c435f-f712-4754-bc22-437cbabd3f65"}}, {"head": {"id": "80714cd4-f1f9-4d49-aac7-4cb400c9d877", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159305877550}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fba7151c-f0c7-45ea-8e4c-198194daa611", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159320054454}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c004275-5694-47cc-9882-907b37251fbc", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159320760646}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a97d1343-e074-42f8-bc3c-fac798362981", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159321623562}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab50a1ad-dd2e-4e17-b93c-19bc18947629", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159322010143}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50000c04-e18c-4fb9-9ba5-12fb5c62e04b", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159322100591}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "580a8485-21c4-4d65-a3a7-8ca204b49672", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159322156664}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4de79445-d184-4f5d-b282-2a06179d8e74", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159322478514}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d81f63e-2aa5-4c80-9b76-f717b64d4f27", "name": "Module entry task initialization takes 6 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159333050480}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cae6b4dc-99f4-44ae-a093-536050a6c6ff", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159333295495}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ccc401ec-ad0c-48a2-9811-ee6cd919cd98", "name": "hvigorfile, no custom plugins were found in /Users/<USER>/github/LingXia/examples/harmony/entry/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159333381940}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "448132d5-a267-4adc-a30d-4f45b04167a0", "name": "hvigorfile, resolve finished /Users/<USER>/github/LingXia/examples/harmony/entry/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159333437360}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "259b955c-dcb6-482f-a1cf-78ead5642702", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159305753403, "endTime": 652159333485056}, "additional": {"logType": "info", "children": [], "durationId": "1f17e6bc-0436-4510-9d2b-8eae9bb9fbf4", "parent": "6c3c435f-f712-4754-bc22-437cbabd3f65"}}, {"head": {"id": "6c3c435f-f712-4754-bc22-437cbabd3f65", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159296982374, "endTime": 652159333500201}, "additional": {"logType": "info", "children": ["8cb3f9a1-fdea-4658-91d8-9d58cce73cfd", "259b955c-dcb6-482f-a1cf-78ead5642702"], "durationId": "9d16c7c4-3ab4-4a6c-90bf-84076f182d44", "parent": "8b080126-08c8-4be2-a160-42df9fa812c0"}}, {"head": {"id": "1920cb1a-bb2f-4b3a-b4a2-9c25d6eee8ce", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159334999525, "endTime": 652159335015794}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "77ed6562-df54-4d7a-98e3-33a77cd05135", "logId": "e729d252-3a1e-44af-a32c-dfbf3ee3e9de"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e729d252-3a1e-44af-a32c-dfbf3ee3e9de", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159334999525, "endTime": 652159335015794}, "additional": {"logType": "info", "children": [], "durationId": "1920cb1a-bb2f-4b3a-b4a2-9c25d6eee8ce", "parent": "8b080126-08c8-4be2-a160-42df9fa812c0"}}, {"head": {"id": "8b080126-08c8-4be2-a160-42df9fa812c0", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159293126006, "endTime": 652159335035856}, "additional": {"logType": "info", "children": ["0f59ea2b-60cd-42ec-b3ea-26c2abccccab", "6c3c435f-f712-4754-bc22-437cbabd3f65", "e729d252-3a1e-44af-a32c-dfbf3ee3e9de"], "durationId": "77ed6562-df54-4d7a-98e3-33a77cd05135", "parent": "fa72db64-dbd8-465e-9457-19aff54ddeb9"}}, {"head": {"id": "1737a14e-e52c-4c42-b79f-9952de9f9ccc", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159335705554, "endTime": 652159404595185}, "additional": {"children": ["f097eb38-9753-4704-9770-d7edcfa2dd53", "a5614e09-e764-442b-968b-27d1d8876a23", "f55b8b98-7274-4718-bba5-1951a6aa7484"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4ab90aef-cfde-42aa-9f10-20980f26b9d2", "logId": "6998c3c3-d4cc-450e-9b4c-062617f602e3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f097eb38-9753-4704-9770-d7edcfa2dd53", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159338383321, "endTime": 652159338402682}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1737a14e-e52c-4c42-b79f-9952de9f9ccc", "logId": "8d4e9e4a-43cd-43ca-aae3-8416e8fb3f7a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8d4e9e4a-43cd-43ca-aae3-8416e8fb3f7a", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159338383321, "endTime": 652159338402682}, "additional": {"logType": "info", "children": [], "durationId": "f097eb38-9753-4704-9770-d7edcfa2dd53", "parent": "6998c3c3-d4cc-450e-9b4c-062617f602e3"}}, {"head": {"id": "a5614e09-e764-442b-968b-27d1d8876a23", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159339968604, "endTime": 652159403129691}, "additional": {"children": ["5a6fa7f4-40da-456c-b591-c8ce9d50da7f", "74e157c8-2c88-4bbe-8046-b6bcf3f1c84c"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1737a14e-e52c-4c42-b79f-9952de9f9ccc", "logId": "4000ce64-7f88-4784-a641-867b99a23b81"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5a6fa7f4-40da-456c-b591-c8ce9d50da7f", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159339969991, "endTime": 652159346225369}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a5614e09-e764-442b-968b-27d1d8876a23", "logId": "99c13cb4-4148-46cb-919b-3e6d93302439"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "74e157c8-2c88-4bbe-8046-b6bcf3f1c84c", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159346249056, "endTime": 652159403114771}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a5614e09-e764-442b-968b-27d1d8876a23", "logId": "00375a49-3067-49aa-9958-c93ff2c07efd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eeb87321-7f8f-47fb-81ed-4fb54e79698a", "name": "hvigorfile, resolving /Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159339977327}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7863611-df2d-4773-bea7-c5ab5592f7a2", "name": "hvigorfile, require result:  { default: { system: [Function: harTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159346080577}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99c13cb4-4148-46cb-919b-3e6d93302439", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159339969991, "endTime": 652159346225369}, "additional": {"logType": "info", "children": [], "durationId": "5a6fa7f4-40da-456c-b591-c8ce9d50da7f", "parent": "4000ce64-7f88-4784-a641-867b99a23b81"}}, {"head": {"id": "4e784bb3-c401-478b-846d-b02223d508bb", "name": "hvigorfile, binding system plugins [Function: harTasks]", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159346268271}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c47b8f05-8bce-4c7b-aecc-c8e6f299ed53", "name": "Start initialize module-target build option map, moduleName=lingxia, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159398432324}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d062ba72-11ca-41aa-9504-55d481a560eb", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159398577395}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ec45cd1-da35-4108-aa8b-de2f38590674", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159398768027}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a382c707-49b7-440b-b889-18ccc4da605a", "name": "Module 'lingxia' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159398906099}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00f41131-28aa-4158-9eee-00496d7744c8", "name": "Module 'lingxia' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159398983044}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d734cc2d-e7c9-40c0-926a-a89e96522fb2", "name": "End initialize module-target build option map, moduleName=lingxia", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159399035686}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bacd2184-908a-4ee8-92a9-2a702e0fc3c5", "name": "Module 'lingxia' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159399094731}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8583d343-db53-4713-95b6-5c6842a1438f", "name": "Module lingxia task initialization takes 2 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159402627152}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25a6bb9e-2280-467e-ad76-bcd945e050ad", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159402831038}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60d6c19d-deec-446d-9215-42ad3d2455f5", "name": "hvigorfile, no custom plugins were found in /Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159402959419}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "305158bc-6e29-401b-83cb-7e908e4f45f5", "name": "hvigorfile, resolve finished /Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159403056216}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00375a49-3067-49aa-9958-c93ff2c07efd", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159346249056, "endTime": 652159403114771}, "additional": {"logType": "info", "children": [], "durationId": "74e157c8-2c88-4bbe-8046-b6bcf3f1c84c", "parent": "4000ce64-7f88-4784-a641-867b99a23b81"}}, {"head": {"id": "4000ce64-7f88-4784-a641-867b99a23b81", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159339968604, "endTime": 652159403129691}, "additional": {"logType": "info", "children": ["99c13cb4-4148-46cb-919b-3e6d93302439", "00375a49-3067-49aa-9958-c93ff2c07efd"], "durationId": "a5614e09-e764-442b-968b-27d1d8876a23", "parent": "6998c3c3-d4cc-450e-9b4c-062617f602e3"}}, {"head": {"id": "f55b8b98-7274-4718-bba5-1951a6aa7484", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159404553266, "endTime": 652159404570416}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1737a14e-e52c-4c42-b79f-9952de9f9ccc", "logId": "71a36035-a983-4953-8974-7b0caea21429"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "71a36035-a983-4953-8974-7b0caea21429", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159404553266, "endTime": 652159404570416}, "additional": {"logType": "info", "children": [], "durationId": "f55b8b98-7274-4718-bba5-1951a6aa7484", "parent": "6998c3c3-d4cc-450e-9b4c-062617f602e3"}}, {"head": {"id": "6998c3c3-d4cc-450e-9b4c-062617f602e3", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159335705554, "endTime": 652159404595185}, "additional": {"logType": "info", "children": ["8d4e9e4a-43cd-43ca-aae3-8416e8fb3f7a", "4000ce64-7f88-4784-a641-867b99a23b81", "71a36035-a983-4953-8974-7b0caea21429"], "durationId": "1737a14e-e52c-4c42-b79f-9952de9f9ccc", "parent": "fa72db64-dbd8-465e-9457-19aff54ddeb9"}}, {"head": {"id": "fa72db64-dbd8-465e-9457-19aff54ddeb9", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159292599594, "endTime": 652159404612650}, "additional": {"logType": "info", "children": ["8b080126-08c8-4be2-a160-42df9fa812c0", "6998c3c3-d4cc-450e-9b4c-062617f602e3"], "durationId": "4ab90aef-cfde-42aa-9f10-20980f26b9d2", "parent": "9b584efa-2944-4752-b341-fdb4e6b332e9"}}, {"head": {"id": "c0d5b42e-1e41-40d7-b120-0dbb0c0757e0", "name": "watch files: [\n  '/Users/<USER>/github/LingXia/examples/harmony/hvigorfile.ts',\n  '/Users/<USER>/github/LingXia/examples/harmony/entry/hvigorfile.ts',\n  '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/hvigorfile.ts',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor-ohos-plugin/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor-ohos-plugin/src/plugin/factory/plugin-factory.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/log4js.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/debug/src/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/debug/src/node.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/debug/src/common.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/ms/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/rfdc/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/configuration.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/layouts.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/date-format/lib/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/levels.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/clustering.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/LoggingEvent.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/flatted/cjs/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/adapters.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/console.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/stdout.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/stderr.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/logLevelFilter.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/categoryFilter.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/noLogFilter.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/file.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/lib/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/lib/RollingFileWriteStream.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/fs/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/universalify/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/graceful-fs/graceful-fs.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/graceful-fs/polyfills.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/graceful-fs/legacy-streams.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/graceful-fs/clone.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/copy-sync/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/copy-sync/copy-sync.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/mkdirs/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/mkdirs/mkdirs.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/mkdirs/win32.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/mkdirs/mkdirs-sync.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/util/utimes.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/util/stat.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/copy/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/copy/copy.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/path-exists/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/empty/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/remove/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/remove/rimraf.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/file.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/link.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/symlink.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/symlink-paths.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/symlink-type.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/json/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/json/jsonfile.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/jsonfile/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/json/output-json.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/json/output-json-sync.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/move-sync/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/move-sync/move-sync.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/move/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/move/move.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/output/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/lib/now.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/lib/fileNameFormatter.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/lib/fileNameParser.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/lib/moveAndMaybeCompressFile.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/lib/RollingFileStream.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/lib/DateRollingFileStream.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/dateFile.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/fileSync.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/tcp.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/categories.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/logger.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/connect-logger.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/recording.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/src/common/util/local-file-writer.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/fs/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/universalify/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/copy/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/copy/copy.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/mkdirs/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/mkdirs/make-dir.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/mkdirs/utils.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/path-exists/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/util/utimes.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/util/stat.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/copy/copy-sync.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/empty/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/remove/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/ensure/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/ensure/file.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/ensure/link.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/ensure/symlink.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/ensure/symlink-paths.js',\n  ... 1869 more items\n]", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159440557775}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eaf65530-521d-48b6-92b8-24968dac5af9", "name": "hvigorfile, resolve hvigorfile dependencies in 106 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159509878463}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ee9826e-487e-463a-88a6-e580eac65064", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159404705422, "endTime": 652159510027053}, "additional": {"logType": "info", "children": [], "durationId": "e8a33ae0-b82d-4a48-a1b1-7e11a7b96480", "parent": "9b584efa-2944-4752-b341-fdb4e6b332e9"}}, {"head": {"id": "1e71d470-b768-4b03-8588-30e41feb6f7d", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159510748577, "endTime": 652159511554156}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "773b8b73-48bc-447e-bbb0-788ad41aadd5", "logId": "22ad0760-3915-4425-935f-8512d10b99d1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ad4147b8-9b79-4433-a871-e6b94cb88608", "name": "project has submodules:entry,lingxia", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159510978611}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b46aa40-a926-422c-867c-b5defdbbe1f9", "name": "module:lingxia no need to execute packageHap", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159511462376}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22ad0760-3915-4425-935f-8512d10b99d1", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159510748577, "endTime": 652159511554156}, "additional": {"logType": "info", "children": [], "durationId": "1e71d470-b768-4b03-8588-30e41feb6f7d", "parent": "9b584efa-2944-4752-b341-fdb4e6b332e9"}}, {"head": {"id": "5b4f74ce-5d8c-46d1-8f7e-0f1151d42c81", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159513605052}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "117d143e-4484-452c-b4e6-abbe1632261a", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159521588013}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63e6bae5-0553-417f-91bb-a6f4250f47f2", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159511569777, "endTime": 652159522527696}, "additional": {"logType": "info", "children": [], "durationId": "675f7dce-a78a-4877-a487-38841fa7762b", "parent": "9b584efa-2944-4752-b341-fdb4e6b332e9"}}, {"head": {"id": "586fa807-aa35-4be6-8d65-ae7731d7cd99", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159522650681}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb858aa9-065e-4249-b53d-5010fc90d5e2", "name": "Module harmony Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159532104141}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31fcdb85-f933-46a7-90ba-1f224b97c3d3", "name": "Module harmony's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159532244226}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d404401a-e3dc-4add-ba40-a3c168c4dcfc", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159533528298}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ab26f33-dc16-49d7-a4ec-56945fb0a445", "name": "Please confirm the symbolic link is valid:  /Users/<USER>/github/LingXia/examples/harmony/entry/oh_modules/lingxia", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159534664585}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "88ec63a7-e938-4ac7-a648-673b47501f9b", "name": "The current module 'entry' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159535967680}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "0866110e-7f4a-43d6-88d3-9da015bf88f6", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159536826008}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "187f6cb9-ca8a-445a-87b6-4ea130c1b973", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159536935124}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5302225d-3750-45e0-b3f2-59147bd85fe3", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159543447152}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ab3af89-576a-4963-8426-3fe3fcca6c82", "name": "Module ling<PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159544636627}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9ed99ef-9568-4803-9fde-e160c37eda31", "name": "Module lingxia's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159544733241}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c66fbc98-c6ee-41f2-8356-a8d093cfe56a", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159522553293, "endTime": 652159547255595}, "additional": {"logType": "info", "children": [], "durationId": "ff70f298-80d7-415d-b79d-36f7d1e10cd9", "parent": "9b584efa-2944-4752-b341-fdb4e6b332e9"}}, {"head": {"id": "26975b14-3b98-4ef5-a57f-14a72b5f2fca", "name": "Configuration phase cost:2 s 898 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159547320931}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "817523e4-fd90-400e-b259-9c673f55521e", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159547279585, "endTime": 652159547414048}, "additional": {"logType": "info", "children": [], "durationId": "c5c72430-bc2f-45b4-aaf3-447509742020", "parent": "9b584efa-2944-4752-b341-fdb4e6b332e9"}}, {"head": {"id": "9b584efa-2944-4752-b341-fdb4e6b332e9", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652156643824958, "endTime": 652159547428986}, "additional": {"logType": "info", "children": ["40f426ea-da7b-4b4d-89e1-2d3ed8826d05", "612fcc9d-efac-42db-80d9-76f1d49974ab", "2fe4858c-8e00-40e3-b1e1-70a63b8344b4", "fa72db64-dbd8-465e-9457-19aff54ddeb9", "3ee9826e-487e-463a-88a6-e580eac65064", "63e6bae5-0553-417f-91bb-a6f4250f47f2", "c66fbc98-c6ee-41f2-8356-a8d093cfe56a", "817523e4-fd90-400e-b259-9c673f55521e", "22ad0760-3915-4425-935f-8512d10b99d1"], "durationId": "773b8b73-48bc-447e-bbb0-788ad41aadd5", "parent": "13824596-f3f7-425d-96a1-b56127d5f4dc"}}, {"head": {"id": "32dca32b-45ef-4273-a8d3-d2601168a297", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159550735280, "endTime": 652159550806897}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bda13103-4fe5-474d-879a-6d37d2d75fdb", "logId": "8370837b-db99-4caa-92a1-379e8ec95b91"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8370837b-db99-4caa-92a1-379e8ec95b91", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159550735280, "endTime": 652159550806897}, "additional": {"logType": "info", "children": [], "durationId": "32dca32b-45ef-4273-a8d3-d2601168a297", "parent": "13824596-f3f7-425d-96a1-b56127d5f4dc"}}, {"head": {"id": "6d361901-a1f6-42e4-bf77-c35581d4f725", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159547467080, "endTime": 652159550825918}, "additional": {"logType": "info", "children": [], "durationId": "fae080ec-187b-4e64-aa10-c0930afae876", "parent": "13824596-f3f7-425d-96a1-b56127d5f4dc"}}, {"head": {"id": "96fc5383-8059-402f-b045-7decb7cc9e04", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159550833752, "endTime": 652159550855252}, "additional": {"logType": "info", "children": [], "durationId": "43d9f667-8e02-4d72-9326-d6cec082760e", "parent": "13824596-f3f7-425d-96a1-b56127d5f4dc"}}, {"head": {"id": "13824596-f3f7-425d-96a1-b56127d5f4dc", "name": "init", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652156621038638, "endTime": 652159550868274}, "additional": {"logType": "info", "children": ["32f057f3-5c80-49e1-b7fa-e98778d9c631", "9b584efa-2944-4752-b341-fdb4e6b332e9", "6d361901-a1f6-42e4-bf77-c35581d4f725", "96fc5383-8059-402f-b045-7decb7cc9e04", "baa0daa0-2e94-4406-8901-e77ac8a7f3a9", "2381ea90-ae29-4830-80a7-8a55506e2b91", "8370837b-db99-4caa-92a1-379e8ec95b91"], "durationId": "bda13103-4fe5-474d-879a-6d37d2d75fdb"}}, {"head": {"id": "f24c18a5-1b58-4a7e-a0cb-a9643fd007cd", "name": "Configuration task cost before running: 2 s 936 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159551873971}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ae05e46-89e0-4d23-8a9f-0fac01715f40", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159558867862, "endTime": 652159726494600}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": ["The input file '/Users/<USER>/github/LingXia/examples/harmony/AppScope/app.json5' has been changed."], "detailId": "1905366a-95ab-49da-bf19-1b81004ab653", "logId": "de2040b8-b4d7-4b0c-810b-7a3952cf67db"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1905366a-95ab-49da-bf19-1b81004ab653", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159553804774}, "additional": {"logType": "detail", "children": [], "durationId": "7ae05e46-89e0-4d23-8a9f-0fac01715f40"}}, {"head": {"id": "6244a80a-3263-4a47-8eb7-80a22fe489e9", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159554586962}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed13f2ab-401c-4640-b99b-18c9418d9a83", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159554881731}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb95de4c-72dc-4631-9150-ca4e9b94e36c", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159558926711}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c07e1892-8aa9-49ff-a15c-b2ab552297b2", "name": "entry:default@PreBuild is not up-to-date, since the input file '/Users/<USER>/github/LingXia/examples/harmony/AppScope/app.json5' has been changed.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159563791158}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7836545-0d49-4002-b968-b18d7e284224", "name": "Incremental task entry:default@PreBuild pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159563981591}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc553ded-3ae6-4a9a-b506-98e49697b6a5", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159564273455}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b573441d-de23-4af5-8f33-66acf832b9b7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159564353485}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4cadf26c-1ae0-4718-b719-8d2b168acd08", "name": "current product is not Atomic service.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159724626048}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16af11e7-ced2-4a84-a27a-3732b7c57311", "name": "Use tool [darwin: JAVA_HOME, CLASSPATH]\n [\n  {\n    JAVA_HOME: '/Applications/Android Studio.app/Contents/jbr/Contents/Home'\n  },\n  { CLASSPATH: undefined }\n]", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159724975807}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44245432-154b-49a0-95d0-896991714b84", "name": "Use tool [darwin: NODE_HOME]\n [ { NODE_HOME: undefined } ]", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159725264150}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6133406-c198-41c0-9c20-0a21af91ee4e", "name": "entry : default@PreBuild cost memory 3.1415557861328125", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159726072009}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6eb2d61-1643-4c47-bf87-4d2dbfdd681a", "name": "runTaskFromQueue task cost before running: 3 s 111 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159726306466}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de2040b8-b4d7-4b0c-810b-7a3952cf67db", "name": "Finished :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159558867862, "endTime": 652159726494600, "totalTime": 167378323}, "additional": {"logType": "info", "children": [], "durationId": "7ae05e46-89e0-4d23-8a9f-0fac01715f40"}}, {"head": {"id": "71902ffc-13f7-4c7d-8a39-bbdccc589b89", "name": "entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159730420316, "endTime": 652159733134714}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "239d3dbf-e750-4615-b4e2-695a22290d71", "logId": "1c3e3b19-bb7f-4c77-b3b2-5bd1795ddef8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "239d3dbf-e750-4615-b4e2-695a22290d71", "name": "create entry:default@CreateModuleInfo task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159729440574}, "additional": {"logType": "detail", "children": [], "durationId": "71902ffc-13f7-4c7d-8a39-bbdccc589b89"}}, {"head": {"id": "a22afe4a-3493-4ccb-a836-d59876bc25cb", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159729738418}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9352b14-a025-4eca-b6a9-9f0026c7b3f2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159729845877}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57efa827-5fba-47fa-a9e0-9eaf1a9f32c9", "name": "Executing task :entry:default@CreateModuleInfo", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159730434073}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66a37c07-bd21-4b22-a61f-aa3fd59319fd", "name": "Task 'entry:default@CreateModuleInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159731480103}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "673fdb78-d873-49d6-90ae-d2544cfa7eca", "name": "entry : default@CreateModuleInfo cost memory 0.0550689697265625", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159732878823}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7a52184-3767-4f8b-af11-177aeefe9ab0", "name": "runTaskFromQueue task cost before running: 3 s 118 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159733060715}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c3e3b19-bb7f-4c77-b3b2-5bd1795ddef8", "name": "Finished :entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159730420316, "endTime": 652159733134714, "totalTime": 2600224}, "additional": {"logType": "info", "children": [], "durationId": "71902ffc-13f7-4c7d-8a39-bbdccc589b89"}}, {"head": {"id": "c9c7bcc5-5a89-45b9-a6ca-1b628f5fcd98", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159738743068, "endTime": 652159742582375}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The input file '/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/module.json5' has been changed."], "detailId": "f9a8bf93-8f2c-40c0-8dbe-3df8e3e1afbb", "logId": "bb81ba7e-fb6b-44d1-9806-a02818e2d3a3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f9a8bf93-8f2c-40c0-8dbe-3df8e3e1afbb", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159735076744}, "additional": {"logType": "detail", "children": [], "durationId": "c9c7bcc5-5a89-45b9-a6ca-1b628f5fcd98"}}, {"head": {"id": "169e7610-0fe9-48c2-9380-c98cf9239d47", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159735486799}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "628af0c4-58ae-425c-b709-81fe8889ed5f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159735609350}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47873f8e-3796-44c0-ab93-890b33896a95", "name": "Please confirm the symbolic link is valid:  /Users/<USER>/github/LingXia/examples/harmony/entry/oh_modules/lingxia", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159737151721}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "1ae60ce3-b229-4bf4-8da2-a4dd5da8fb7b", "name": "The current module 'entry' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159737894669}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "8a9af5f5-dfe5-4bc8-8802-19d11175dcf9", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159738757490}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efcefbb0-d299-40b7-8473-ce9bfadff4f3", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159739868054}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3c8fe30-57f8-40e0-821d-7c52754b2144", "name": "entry:default@GenerateMetadata is not up-to-date, since the input file '/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/module.json5' has been changed.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159740235052}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e33b8373-942a-49e2-a86c-d99a765b06ff", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159740345834}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22ffc72f-da1a-4235-b183-d3330724aaeb", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159740439691}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba478e84-187f-4563-8b98-2d31cccb8e86", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159740506646}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07fb6d25-a6c4-4418-b58b-a179eee265bd", "name": "entry : default@GenerateMetadata cost memory 0.13592529296875", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159742299795}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "706af19e-b6dd-4cda-8da6-346e62d5596e", "name": "runTaskFromQueue task cost before running: 3 s 127 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159742502738}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb81ba7e-fb6b-44d1-9806-a02818e2d3a3", "name": "Finished :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159738743068, "endTime": 652159742582375, "totalTime": 3710306}, "additional": {"logType": "info", "children": [], "durationId": "c9c7bcc5-5a89-45b9-a6ca-1b628f5fcd98"}}, {"head": {"id": "2f3ef9da-b8e1-4697-a616-10bee6f439b7", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159745173325, "endTime": 652159745665535}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "6418160b-e6dc-4d8d-9bc8-96d3292e5419", "logId": "bfa3526f-26ab-4083-9664-d9b3aa6d285a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6418160b-e6dc-4d8d-9bc8-96d3292e5419", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159744395271}, "additional": {"logType": "detail", "children": [], "durationId": "2f3ef9da-b8e1-4697-a616-10bee6f439b7"}}, {"head": {"id": "a6cbf489-9a88-4cad-aee0-9388ab365751", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159744677800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e42bce65-c466-40b6-9915-032c9e842c88", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159744758421}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dabae95a-9b75-4780-8a1e-6a32005bd694", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159745186176}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "baca0337-944c-4cb3-9dc6-2335e313a075", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159745317486}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be270bec-399f-412a-a55d-8074cbd88321", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159745383267}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7337dc01-aa1a-4438-82e5-91a3045eda7b", "name": "entry : default@ConfigureCmake cost memory 0.03917694091796875", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159745504980}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1a14c99-bec0-4f52-a408-3990763841a3", "name": "runTaskFromQueue task cost before running: 3 s 130 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159745602246}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfa3526f-26ab-4083-9664-d9b3aa6d285a", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159745173325, "endTime": 652159745665535, "totalTime": 403163}, "additional": {"logType": "info", "children": [], "durationId": "2f3ef9da-b8e1-4697-a616-10bee6f439b7"}}, {"head": {"id": "e21d9d3f-d6da-46e6-b7f8-b0bebb035d2e", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159748791488, "endTime": 652159755050943}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": ["The input file '/Users/<USER>/github/LingXia/examples/harmony/AppScope/app.json5' has been changed."], "detailId": "5c309aed-5654-4602-9ad9-9d96df2fcc65", "logId": "e2f87967-001c-499d-be01-c4aad3d91a65"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5c309aed-5654-4602-9ad9-9d96df2fcc65", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159747409335}, "additional": {"logType": "detail", "children": [], "durationId": "e21d9d3f-d6da-46e6-b7f8-b0bebb035d2e"}}, {"head": {"id": "846bcb62-aad7-4350-85e8-ccfc02577fbb", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159747862948}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b55d79ba-188d-4757-89bc-9bf810231a27", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159747975515}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88879e3f-b18a-48f6-998d-1ec2dc87964d", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159748813527}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fdcafd4f-ee04-42c7-9bcc-e80a9eb5afbb", "name": "entry:default@MergeProfile is not up-to-date, since the input file '/Users/<USER>/github/LingXia/examples/harmony/AppScope/app.json5' has been changed.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159749762280}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73e204c1-3798-40f2-b8f0-d441d226c8ed", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159749861867}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6dba3558-0e91-451f-a61b-590416382c79", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159749949928}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77d4b118-cff3-4c21-b527-5973b59888d7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159750005315}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94b8d93d-67ae-405b-a461-99e9119b4929", "name": "Change app api release type with 'Release'", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159750515917}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b76daa30-f8ed-4bcd-9a8f-0f0ba58405a7", "name": "Change app compile API version with '*********'", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159750885805}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31b60025-d64a-4710-95b5-0a9dda841f94", "name": "Change app target API version with '50005017'", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159750970548}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3935ba3-8c2e-472f-b4a3-a85231cc7b1e", "name": "Change app minimum API version with '50005017'", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159751023593}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "410d719d-53ce-4979-9830-2bc7d9a2c09b", "name": "Use cli appEnvironment", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159751188276}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e91462a1-7c02-447e-a928-74cca2a82f9c", "name": "entry : default@MergeProfile cost memory 0.235137939453125", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159754768910}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5827401-6c66-44b3-b265-a4d293ef88b8", "name": "runTaskFromQueue task cost before running: 3 s 140 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159754965649}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2f87967-001c-499d-be01-c4aad3d91a65", "name": "Finished :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159748791488, "endTime": 652159755050943, "totalTime": 6269550}, "additional": {"logType": "info", "children": [], "durationId": "e21d9d3f-d6da-46e6-b7f8-b0bebb035d2e"}}, {"head": {"id": "24bb2e8a-6505-46aa-b9b0-cd31803c8b52", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159758215543, "endTime": 652159761127102}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The input file '/Users/<USER>/github/LingXia/examples/harmony/AppScope/app.json5' has been changed."], "detailId": "af613b8b-ca10-4e5c-9402-12064a45b72e", "logId": "19c47da5-4f59-4895-a3a2-6c1f32b99650"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "af613b8b-ca10-4e5c-9402-12064a45b72e", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159756918729}, "additional": {"logType": "detail", "children": [], "durationId": "24bb2e8a-6505-46aa-b9b0-cd31803c8b52"}}, {"head": {"id": "741c4c4e-4ff4-4b1b-86f4-5a9f098a1ef0", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159757231200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6adab2f4-df50-41de-974c-f6d0dddfcd5c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159757343748}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86c48f40-ad4d-4573-a6e7-5ffb5e6f36ff", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159758231537}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a7577d1-a94b-46f6-a8f8-1a533a16eafd", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159759049128}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f80bb136-207b-4b58-ad9c-9e264dc632f5", "name": "entry:default@CreateBuildProfile is not up-to-date, since the input file '/Users/<USER>/github/LingXia/examples/harmony/AppScope/app.json5' has been changed.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159759461395}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "271704a2-7136-4474-930d-cbe6c7ea6335", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159759554440}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63c1b610-34f7-4d31-b7cb-9ffad61cba3d", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159759667097}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f140cd54-f5c4-4bbe-bd22-befb036747c9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159759730797}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d49874c-640e-4e6a-a1fa-6ce746cb35d6", "name": "entry : default@CreateBuildProfile cost memory 0.13372039794921875", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159760890069}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1cfe8e9-017e-4f22-a5ee-c759b18d045c", "name": "runTaskFromQueue task cost before running: 3 s 146 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159761060403}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19c47da5-4f59-4895-a3a2-6c1f32b99650", "name": "Finished :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159758215543, "endTime": 652159761127102, "totalTime": 2809348}, "additional": {"logType": "info", "children": [], "durationId": "24bb2e8a-6505-46aa-b9b0-cd31803c8b52"}}, {"head": {"id": "9d7d6f9d-7db0-49e5-b370-e62e434022f2", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159763728935, "endTime": 652159764351475}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "97f149db-72c5-4885-ac7e-0d1104342fa1", "logId": "f9fc8b3e-b4d5-420e-9731-428c76f4dfbf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "97f149db-72c5-4885-ac7e-0d1104342fa1", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159762734703}, "additional": {"logType": "detail", "children": [], "durationId": "9d7d6f9d-7db0-49e5-b370-e62e434022f2"}}, {"head": {"id": "3dcef759-24a0-4c9f-8e70-6d2d04dfafc7", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159763039132}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d95ec266-210b-4ad7-b27c-ed17d17e5c39", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159763140354}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52222c35-0557-4636-a2a3-92dc8f3905f7", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159763743705}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b68d95a-c211-48d6-a507-4e521316a7db", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159763853605}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1446b006-016e-47d8-ba1e-64aeac29e2c6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159763926749}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "112a4a3d-09d7-4a65-a619-a5226924f122", "name": "entry : default@PreCheckSyscap cost memory 0.0394134521484375", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159764188973}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8933b08-28e9-4373-afca-974dfa620ece", "name": "runTaskFromQueue task cost before running: 3 s 149 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159764294738}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9fc8b3e-b4d5-420e-9731-428c76f4dfbf", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159763728935, "endTime": 652159764351475, "totalTime": 537896}, "additional": {"logType": "info", "children": [], "durationId": "9d7d6f9d-7db0-49e5-b370-e62e434022f2"}}, {"head": {"id": "2f4f2798-6833-40bc-afec-615496c48ce9", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159774309477, "endTime": 652159776292564}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default/pkgContextInfo.json' has been changed."], "detailId": "0d02a3b8-7eaa-4504-9a7a-ee7c3281c239", "logId": "ae6ddcdd-61d5-418b-a7f7-b1ea862bba75"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0d02a3b8-7eaa-4504-9a7a-ee7c3281c239", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159766401324}, "additional": {"logType": "detail", "children": [], "durationId": "2f4f2798-6833-40bc-afec-615496c48ce9"}}, {"head": {"id": "6f3e7f0d-98db-4c72-9516-bfc04ff72cd9", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159766709491}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df25629d-fd1c-4396-8a8b-83c93945959e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159766822973}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3434138a-d99b-4bd5-a8dc-1aa11ae0a255", "name": "Please confirm the symbolic link is valid:  /Users/<USER>/github/LingXia/examples/harmony/entry/oh_modules/lingxia", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159768410944}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "a45789e8-44bb-4032-b5a9-c9410c786412", "name": "The current module 'entry' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159769035131}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "926cd205-5eaa-4a3c-8dd9-1275fa395849", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159774331317}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43d63ddb-b5cc-4abf-b82d-4f768a940422", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159774784908}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b18a62fe-9005-462f-87a9-8eb93d9c09b4", "name": "entry:default@GeneratePkgContextInfo is not up-to-date, since the output file '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default/pkgContextInfo.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159775261821}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b4254f5-1d82-4ddb-9ded-2cda7a0147b6", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159775369675}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e95b8a3-fb89-49fa-83c9-936760f647b5", "name": "entry : default@GeneratePkgContextInfo cost memory 0.077239990234375", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159776091259}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3a691f8-60fc-4aa5-83b8-8d95b30a8f76", "name": "runTaskFromQueue task cost before running: 3 s 161 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159776223646}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae6ddcdd-61d5-418b-a7f7-b1ea862bba75", "name": "Finished :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159774309477, "endTime": 652159776292564, "totalTime": 1881698}, "additional": {"logType": "info", "children": [], "durationId": "2f4f2798-6833-40bc-afec-615496c48ce9"}}, {"head": {"id": "7245e7fb-04a2-4afe-8f24-c9e0351091ca", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159779942690, "endTime": 652159781702286}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file '/Users/<USER>/github/LingXia/examples/harmony/build/cache/default/integrated_hsp/integratedHspCache.json' does not exist."], "detailId": "fb2fa46e-af52-429d-a814-3517af6e1170", "logId": "8ec5ed5e-1a8f-439d-9614-abdaff52621d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fb2fa46e-af52-429d-a814-3517af6e1170", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159778047608}, "additional": {"logType": "detail", "children": [], "durationId": "7245e7fb-04a2-4afe-8f24-c9e0351091ca"}}, {"head": {"id": "32865f87-6cd7-43b9-b7dc-beb97c73b48d", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159778350165}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd747550-3bed-4123-a767-5e88e4cab078", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159778443722}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7607d6f1-a456-4eb2-9987-7fd584e0f7d9", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159779958959}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a69703e-f283-4e54-a12b-240c63f4b019", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file '/Users/<USER>/github/LingXia/examples/harmony/build/cache/default/integrated_hsp/integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159781122983}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40dbd6f0-d7dd-46af-955b-934e5ddab395", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159781221210}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87b226ec-2c79-4b22-9088-5273aaacf562", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159781305153}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d637b42-8c0c-4ec1-9a74-030012ff7671", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159781360417}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d73f1504-b89d-45f8-960e-1dc1d93f7708", "name": "entry : default@ProcessIntegratedHsp cost memory 0.11305999755859375", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159781540785}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7fca402e-0ad3-454f-919a-2021f4ef8bf7", "name": "runTaskFromQueue task cost before running: 3 s 166 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159781646883}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ec5ed5e-1a8f-439d-9614-abdaff52621d", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159779942690, "endTime": 652159781702286, "totalTime": 1675480}, "additional": {"logType": "info", "children": [], "durationId": "7245e7fb-04a2-4afe-8f24-c9e0351091ca"}}, {"head": {"id": "e2a1e30f-585b-4cb8-b345-70669d2a8dc1", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159784602479, "endTime": 652159785212332}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "364ff926-5f7d-476b-9e55-37a6b3d0c8b2", "logId": "53bbc73c-5148-4fd1-ad7f-1a1721184c54"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "364ff926-5f7d-476b-9e55-37a6b3d0c8b2", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159783599294}, "additional": {"logType": "detail", "children": [], "durationId": "e2a1e30f-585b-4cb8-b345-70669d2a8dc1"}}, {"head": {"id": "0137fd13-389a-41e4-a88c-c2674938e763", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159783911897}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76b6365a-92ab-4a74-8e84-a3bc71dea458", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159783997814}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a091dd8-9c9f-4cdb-8bc0-ee6dd98c694b", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159784615596}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b0f0253-4085-4891-8637-8b33fb945e58", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159784846550}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58682199-11e0-44eb-91e7-4ebf1129c531", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159784921012}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "203081e6-8e9e-4a7c-8b4a-d9dca997abdb", "name": "entry : default@BuildNativeWithCmake cost memory 0.04064178466796875", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159785052334}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3941bee0-cc22-4c63-8693-8b06a825343b", "name": "runTaskFromQueue task cost before running: 3 s 170 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159785154509}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53bbc73c-5148-4fd1-ad7f-1a1721184c54", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159784602479, "endTime": 652159785212332, "totalTime": 522234}, "additional": {"logType": "info", "children": [], "durationId": "e2a1e30f-585b-4cb8-b345-70669d2a8dc1"}}, {"head": {"id": "8b1a130a-d7fc-4b8e-ba7c-450d0c00eae5", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159788236727, "endTime": 652159792821376}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The input file '/Users/<USER>/github/LingXia/examples/harmony/AppScope/app.json5' has been changed."], "detailId": "6b563f5f-c362-4c58-947a-0a611f4a7a21", "logId": "5146ce4a-6ae1-4c8a-8f1b-167272a919d4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6b563f5f-c362-4c58-947a-0a611f4a7a21", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159786912523}, "additional": {"logType": "detail", "children": [], "durationId": "8b1a130a-d7fc-4b8e-ba7c-450d0c00eae5"}}, {"head": {"id": "9880401e-a963-420d-9058-82cb2b462090", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159787291726}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e3b6cab-d701-4be8-a47d-6dd152ea4bca", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159787412175}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68832663-7b66-4b70-861d-a1d042c116e1", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159788252168}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "373f4d95-f680-4913-ac2e-2585b1498dce", "name": "entry:default@MakePackInfo is not up-to-date, since the input file '/Users/<USER>/github/LingXia/examples/harmony/AppScope/app.json5' has been changed.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159789764527}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b18de0e0-71cc-4af5-b59b-f0e75ec92527", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159789866718}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d114bb2e-34a9-41a8-a5c9-0bda39bb7ca9", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159789975188}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d980e7b7-d04b-4a4d-a62c-358009c9ef3e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159790033808}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3fd9cca9-449c-40c2-b705-36a1cc375fec", "name": "Module Pack Info:  {\n  summary: {\n    app: { bundleName: 'a***e', bundleType: 'app', version: [Object] },\n    modules: [ [Object] ]\n  },\n  packages: [\n    {\n      deviceType: [Array],\n      moduleType: 'entry',\n      deliveryWithInstall: true,\n      name: 'entry-default'\n    }\n  ]\n}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159792075644}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c19904af-5249-46dd-b20a-37eceba64d69", "name": "entry : default@MakePackInfo cost memory 0.1821441650390625", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159792603881}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7cc7599a-9628-4288-9ad5-dda3acf6ccc3", "name": "runTaskFromQueue task cost before running: 3 s 177 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159792749307}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5146ce4a-6ae1-4c8a-8f1b-167272a919d4", "name": "Finished :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159788236727, "endTime": 652159792821376, "totalTime": 4479024}, "additional": {"logType": "info", "children": [], "durationId": "8b1a130a-d7fc-4b8e-ba7c-450d0c00eae5"}}, {"head": {"id": "2b33dd7a-738f-4ed2-85af-130a358ab90f", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159796216071, "endTime": 652159814071999}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/syscap/default/rpcid.sc' does not exist."], "detailId": "2b440d52-e524-4504-b2ea-d771c0e560ef", "logId": "d4833286-35a7-4fc4-b82b-950e70d2f6e4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2b440d52-e524-4504-b2ea-d771c0e560ef", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159794874849}, "additional": {"logType": "detail", "children": [], "durationId": "2b33dd7a-738f-4ed2-85af-130a358ab90f"}}, {"head": {"id": "f3de7038-2e77-4de8-ade4-add835639093", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159795163331}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5e67c9a-e105-4e8a-91a2-9f210ad236d2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159795249683}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "609efb00-3695-4f1f-aba2-91e3dec64512", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159796230219}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95eb8b8c-408f-4c50-a36b-9bb68903c426", "name": "File: '/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159796351924}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03675ced-09fd-413c-bf82-e37710b60703", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159796760053}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d3eebb0-fde6-4f4f-8762-ad67b211297a", "name": "entry:default@SyscapTransform is not up-to-date, since the output file '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/syscap/default/rpcid.sc' does not exist.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159813134576}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c35125d0-3961-45ae-acc4-faee6f456754", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 17 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159813314076}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23c34aa2-c577-4a3a-a048-e46244356b19", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159813440973}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9a2325e-1221-44cf-895e-101f37d2cb71", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159813515304}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b97f5b0-47a6-4b58-9020-cdbd883733bb", "name": "entry : default@SyscapTransform cost memory 0.135406494140625", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159813876708}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a34db6c1-a007-4fa2-8697-2e020e529be5", "name": "runTaskFromQueue task cost before running: 3 s 199 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159814005960}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4833286-35a7-4fc4-b82b-950e70d2f6e4", "name": "Finished :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159796216071, "endTime": 652159814071999, "totalTime": 17760816}, "additional": {"logType": "info", "children": [], "durationId": "2b33dd7a-738f-4ed2-85af-130a358ab90f"}}, {"head": {"id": "3045c6c6-6413-4474-9ad7-51d13884421d", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159817863522, "endTime": 652160004917501}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": ["The input file '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/merge_profile/default/module.json' has been changed."], "detailId": "bb261d6b-01e2-47c6-8e55-40c920a176ae", "logId": "c914d1f3-3bfb-4b1b-a7a4-828b4a7c9a36"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bb261d6b-01e2-47c6-8e55-40c920a176ae", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159815847173}, "additional": {"logType": "detail", "children": [], "durationId": "3045c6c6-6413-4474-9ad7-51d13884421d"}}, {"head": {"id": "b40bd0dd-a83d-4492-9ff1-0e89eafb83a2", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159816163385}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "591e0b52-4e5e-4e5b-a763-c79bdd38cc96", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159816252636}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3bc0f125-faf1-47a8-a7e9-4502db2a94cd", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159817880502}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5fd87c7a-8b13-48c2-b538-9f0ecc5f42ee", "name": "entry:default@ProcessProfile is not up-to-date, since the input file '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/merge_profile/default/module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159818945335}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94eb033c-e739-434f-a773-6cb8c9019e9f", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159819058324}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b98e6eb-f048-4511-a254-eccd23eb9df6", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159819152229}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75413d67-616a-4478-9626-a00737a23da3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159819207550}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "677ec9ab-82fa-4417-90f4-a4d0f91ab5b0", "name": "********", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160002148288}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d77b116b-876c-4a33-8b5b-2d6b346d80df", "name": "entry : default@ProcessProfile cost memory 0.3391876220703125", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160004669948}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "132873e8-c976-4ec0-8a0e-4bb34c0c6f62", "name": "runTaskFromQueue task cost before running: 3 s 389 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160004843156}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c914d1f3-3bfb-4b1b-a7a4-828b4a7c9a36", "name": "Finished :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652159817863522, "endTime": 652160004917501, "totalTime": 186943233}, "additional": {"logType": "info", "children": [], "durationId": "3045c6c6-6413-4474-9ad7-51d13884421d"}}, {"head": {"id": "f7d1c865-a7a7-4f3e-9ef9-3daffc7b3f7d", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160008410450, "endTime": 652160013236038}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The input file '/Users/<USER>/github/LingXia/examples/harmony/entry/oh-package.json5' has been changed."], "detailId": "fb7b0ec1-da02-4cae-bcb0-aa71caaece79", "logId": "249c28f9-9859-42e0-884a-b141b6e75f8c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fb7b0ec1-da02-4cae-bcb0-aa71caaece79", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160006572480}, "additional": {"logType": "detail", "children": [], "durationId": "f7d1c865-a7a7-4f3e-9ef9-3daffc7b3f7d"}}, {"head": {"id": "21de06c5-7310-431d-91ef-c6840cab1689", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160006920197}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b570ff9-5765-44ad-8b3b-8b9b04e04fb4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160007017296}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "111c452f-d25f-44bb-bf5f-e17463808ce5", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160008426591}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc993c3e-e2a6-438d-badd-506a1982b678", "name": "entry:default@ProcessRouterMap is not up-to-date, since the input file '/Users/<USER>/github/LingXia/examples/harmony/entry/oh-package.json5' has been changed.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160010964877}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a609c70a-155a-4109-8909-0b2adccc2620", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160011118211}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18fa7907-01aa-459f-852e-3953d9bd1c25", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160011222700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ce600ce-22c3-4394-a384-e1e5e6373612", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160011281162}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3b5422b-3b2b-419b-8a02-380f310964b0", "name": "entry : default@ProcessRouterMap cost memory 0.19568634033203125", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160012996724}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc4259e9-bf8e-48ec-8aa3-50b31c2db82b", "name": "runTaskFromQueue task cost before running: 3 s 398 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160013159094}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "249c28f9-9859-42e0-884a-b141b6e75f8c", "name": "Finished :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160008410450, "endTime": 652160013236038, "totalTime": 4713783}, "additional": {"logType": "info", "children": [], "durationId": "f7d1c865-a7a7-4f3e-9ef9-3daffc7b3f7d"}}, {"head": {"id": "292aae24-918e-4bd6-ae7a-ea4b281b5f24", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160017101542, "endTime": 652160019310738}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "927836ac-2ebd-4fec-a5ac-0b161eb01143", "logId": "7c9465f1-7698-4a7a-b762-dea85944eb2d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "927836ac-2ebd-4fec-a5ac-0b161eb01143", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160016086584}, "additional": {"logType": "detail", "children": [], "durationId": "292aae24-918e-4bd6-ae7a-ea4b281b5f24"}}, {"head": {"id": "8c43cc1d-71e3-4399-87e8-88d776d05cd3", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160016369715}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a9129ee-b722-44f0-8f78-0cde729c3932", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160016475819}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8f58ebf-67b1-4809-89b7-1b86e76ab557", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160017115187}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b8ba364-6fe6-440a-a883-4927a919ffe8", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160017380055}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc484ac3-a1de-4dc3-95d3-23026e94439b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160017456760}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d76ba146-28ba-4408-8f29-1a9e1752c2bc", "name": "entry : default@BuildNativeWithNinja cost memory 0.056884765625", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160018979482}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7e5aa11-8c58-4e83-bfb0-485e8d4e2249", "name": "runTaskFromQueue task cost before running: 3 s 404 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160019200383}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c9465f1-7698-4a7a-b762-dea85944eb2d", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160017101542, "endTime": 652160019310738, "totalTime": 2047679}, "additional": {"logType": "info", "children": [], "durationId": "292aae24-918e-4bd6-ae7a-ea4b281b5f24"}}, {"head": {"id": "55e38054-cf04-4a0f-9890-a442a577bf45", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160024018036, "endTime": 652160030562928}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": ["The output file '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resConfig.json' has been changed."], "detailId": "564e43d9-26c7-4e33-b1e8-59a8df3efb70", "logId": "b1acfab1-e8aa-4f6c-802a-66e68af32d98"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "564e43d9-26c7-4e33-b1e8-59a8df3efb70", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160021487065}, "additional": {"logType": "detail", "children": [], "durationId": "55e38054-cf04-4a0f-9890-a442a577bf45"}}, {"head": {"id": "a3065fe7-358d-4f03-83ce-74159d6764a2", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160021751537}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e9454c8-37e3-4003-b0ad-46869d040977", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160021837210}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "190d57a1-4526-4dc8-ae54-db7a42565fc5", "name": "restool module names: entry,lingxia; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160022761456}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "daefb817-22d9-4a3d-b164-2b39bf60d248", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160026194264}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "336d0753-501a-43bf-aace-1863a0d6378a", "name": "entry:default@ProcessResource is not up-to-date, since the output file '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resConfig.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160027345277}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "afeeb140-74ab-4d78-b734-fc06e6691d0e", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160027459473}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8bb7ca40-fc94-484d-b1c2-975e92526045", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160027632265}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e958279-68fd-4925-97fd-d95c0ade2c8b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160027708208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a81a5ecc-4774-42f9-b50d-eda4d4b6fae3", "name": "entry : default@ProcessResource cost memory 0.19754791259765625", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160029433657}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3d41a9c-d094-4f13-8f0c-4049e1a19708", "name": "runTaskFromQueue task cost before running: 3 s 415 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160030468653}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1acfab1-e8aa-4f6c-802a-66e68af32d98", "name": "Finished :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160024018036, "endTime": 652160030562928, "totalTime": 5540303}, "additional": {"logType": "info", "children": [], "durationId": "55e38054-cf04-4a0f-9890-a442a577bf45"}}, {"head": {"id": "dba07061-31fd-4ddd-83d2-bd17ad22c390", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160036995396, "endTime": 652160050767549}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The input file '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default/pkgContextInfo.json' has been changed."], "detailId": "cc3e963f-1d30-46a4-aab9-807be2b3fb11", "logId": "8a5c8ea0-20d2-456a-8fa8-e15485dc4ae3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cc3e963f-1d30-46a4-aab9-807be2b3fb11", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160033267423}, "additional": {"logType": "detail", "children": [], "durationId": "dba07061-31fd-4ddd-83d2-bd17ad22c390"}}, {"head": {"id": "48f7cef1-87fe-42f7-9556-4df91c7a5492", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160033540019}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5999ebe5-c48d-454e-a4b6-5ea22e42535f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160033638847}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29c836f2-cc9d-4928-adfd-1373db714985", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160037022242}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32ae4d97-11b8-43c4-b0a8-7f1c4953862f", "name": "entry:default@GenerateLoaderJson is not up-to-date, since the input file '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default/pkgContextInfo.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160046754187}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80711991-edfd-4161-b2c7-5a53586136d4", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160046924067}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26e6c9e3-fd34-4d62-a5cc-b0a6f47df664", "name": "entry : default@GenerateLoaderJson cost memory 0.575164794921875", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160050515710}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b330e631-6243-4df5-bcf9-5dfd4bbb3acd", "name": "runTaskFromQueue task cost before running: 3 s 435 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160050693263}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a5c8ea0-20d2-456a-8fa8-e15485dc4ae3", "name": "Finished :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160036995396, "endTime": 652160050767549, "totalTime": 13663854}, "additional": {"logType": "info", "children": [], "durationId": "dba07061-31fd-4ddd-83d2-bd17ad22c390"}}, {"head": {"id": "d0cf4f0b-573c-40ff-987e-499561fa4d6b", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160055728914, "endTime": 652161166060005}, "additional": {"children": ["f5c47b40-6550-419a-b5b1-0b561bbd0269"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": ["The input file '/Users/<USER>/github/LingXia/examples/harmony/entry/libs' has been changed."], "detailId": "c1f6c70a-e0b2-4643-8d91-aadcc63aa4b2", "logId": "3b7c8e01-4cba-4237-9085-61656d135b09"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c1f6c70a-e0b2-4643-8d91-aadcc63aa4b2", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160054783430}, "additional": {"logType": "detail", "children": [], "durationId": "d0cf4f0b-573c-40ff-987e-499561fa4d6b"}}, {"head": {"id": "1458eaba-b5e5-4ff0-a046-27f84cf11659", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160055052483}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ad008cb-3832-4726-8017-2139f313ce2b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160055159059}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15e61f31-5ade-4717-839f-54095a14c577", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160055739967}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24d3254c-da78-4d32-8bab-875a4a54c8be", "name": "entry:default@ProcessLibs is not up-to-date, since the input file '/Users/<USER>/github/LingXia/examples/harmony/entry/libs' has been changed.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160057851987}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "947c3604-79df-4ac1-aeba-b536b7e935ca", "name": "Incremental task entry:default@ProcessLibs pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160057967346}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a709ca01-1f03-4d5f-890b-dcba55690132", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160058054882}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10e3f8d4-840b-492f-a432-f471fa12ecce", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160058111085}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "727f5acc-26f9-4cd3-9839-7e36f3760792", "name": "default@ProcessLibs work[0] is submitted.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160061518974}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5c47b40-6550-419a-b5b1-0b561bbd0269", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Worker6", "startTime": 652161143244813, "endTime": 652161163913718}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "d0cf4f0b-573c-40ff-987e-499561fa4d6b", "logId": "d239cfca-1ee5-4814-8f75-7fafb1cedc02"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "62bee155-89fd-4204-ba87-39da8e876310", "name": "default@ProcessLibs work[0] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160062816677}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83b0f8d9-a478-4a49-b18e-4f1e9a0e9c4a", "name": "A work dispatched to worker[7] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160063114133}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b764045-98c0-4760-b4e7-3084212fdc09", "name": "Create  resident worker with id: 6.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160063451610}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f36edf4-801f-423a-b87c-ac835a10b507", "name": "default@ProcessLibs work[0] has been dispatched to worker[6].", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160065951943}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58baae14-a0f0-4b9c-8e57-5198036b8dcb", "name": "default@ProcessLibs work[0] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160066145398}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55eaab74-24dc-48ec-b489-c09b351ff16e", "name": "entry : default@ProcessLibs cost memory 0.7865676879882812", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160066297562}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d175e8c2-5f09-4d58-8dda-a05105d40aa6", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160071083774, "endTime": 652160625279521}, "additional": {"children": ["d0387791-2f03-4072-b903-a2e6ddd58192", "e3cc968e-ffbb-4b38-abb7-7791c53d9dba"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": ["The input file '/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources' has been changed."], "detailId": "7d872af4-81b3-457d-a25a-b6720e8fb8a2", "logId": "3f00b5db-fd5e-4ccf-b08b-b6f2057a1adb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7d872af4-81b3-457d-a25a-b6720e8fb8a2", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160068076851}, "additional": {"logType": "detail", "children": [], "durationId": "d175e8c2-5f09-4d58-8dda-a05105d40aa6"}}, {"head": {"id": "de01d9ea-0178-49c2-b734-c8a940f1f82a", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160068385235}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23a919c5-67c2-4121-8b61-f2fa7fb79aa1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160068490476}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64c7929a-ab17-482e-b46c-c53246fba541", "name": "restool module names: entry,lingxia; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160069000105}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69d5dcef-482d-4bde-999b-c4b3c17afd2f", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160071213714}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a60df0a-69ea-4b63-9004-d9d4a3b7ed84", "name": "entry:default@CompileResource is not up-to-date, since the input file '/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources' has been changed.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160076261531}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef6f0f18-97be-49fd-97b9-00f1b695058e", "name": "Incremental task entry:default@CompileResource pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160076499849}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0387791-2f03-4072-b903-a2e6ddd58192", "name": "create intermediate resource category", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160077495818, "endTime": 652160288909405}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d175e8c2-5f09-4d58-8dda-a05105d40aa6", "logId": "4de6a693-6dcc-4d96-883f-bcddcd309f23"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4de6a693-6dcc-4d96-883f-bcddcd309f23", "name": "create intermediate resource category", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160077495818, "endTime": 652160288909405}, "additional": {"logType": "info", "children": [], "durationId": "d0387791-2f03-4072-b903-a2e6ddd58192", "parent": "3f00b5db-fd5e-4ccf-b08b-b6f2057a1adb"}}, {"head": {"id": "79e7311c-e204-4788-bdf4-75c57b5b577e", "name": "Use tool [/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/toolchains/restool]\n [\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/toolchains/restool',\n  '-l',\n  '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resConfig.json'\n]", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160289918565}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3cc968e-ffbb-4b38-abb7-7791c53d9dba", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160291323018, "endTime": 652160623587282}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d175e8c2-5f09-4d58-8dda-a05105d40aa6", "logId": "a2f70187-9b5d-4f71-a77f-5da2ef23d8d7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "59ee375f-2faa-4a55-a7f9-3f271d85513e", "name": "current process  memoryUsage: {\n  rss: 267026432,\n  heapTotal: 166748160,\n  heapUsed: 140444152,\n  external: 2972553,\n  arrayBuffers: 1107923\n} os memoryUsage :14.856311798095703", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160294216540}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "974a633c-ab3f-41f7-8566-5e9a326dcb84", "name": "Info: thread pool is started\n", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160468116014}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56f82c53-aa8f-4520-a6fb-2084a7a4fb24", "name": "Info: GenericCompiler::CompileFiles\nInfo: thread pool is started\n", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160469613403}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32f49f10-4ec8-430f-ac9a-409a4f7c3ce7", "name": "Info: thread pool is stopped\n", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160474084735}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "240e799f-d97d-4034-ae34-138c37901886", "name": "Info: GenericCompiler::CompileFiles\nInfo: thread pool is started\n", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160476750844}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e327428e-b1aa-4ba4-a1f7-324c141ffe3c", "name": "Info: thread pool is stopped\n", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160478740142}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c487d83f-f5ba-41ae-a8d1-f0edf3f7b6c4", "name": "Info: GenericCompiler::CompileFiles\nInfo: thread pool is started\n", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160479207737}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "356c4b8a-ea6f-46e9-9919-1b91422e9771", "name": "Info: thread pool is stopped\n", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160480525533}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea1fa220-eeb3-4451-b6ea-5a20161cd3df", "name": "07-31 21:32:52.145 52174 8741636 E C01400/ImageTranscoderUtils: DecodeToRGBA IN\n", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160483925250}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b905133-5c7d-4573-99fa-553ae1733070", "name": "07-31 21:32:52.146 52174 8741636 E C01400/ImageTranscoderUtils: ImageFilter IN /Users/<USER>/github/LingXia/examples/harmony/AppScope/resources/base/media/background.png\n", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160484531848}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eecf42c4-dccd-49e1-b0d7-31f4acdff2f6", "name": "07-31 21:32:52.224 52174 8741636 E C01400/ImageTranscoderUtils: DecodeToRGBA IN\n07-31 21:32:52.224 52174 8741636 E C01400/ImageTranscoderUtils: ImageFilter IN /Users/<USER>/github/LingXia/examples/harmony/AppScope/resources/base/media/foreground.png\n", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160561486571}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88f8cf56-354c-4f99-8c41-1e8efbaacbfe", "name": "Info: can't scale media json file.\n", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160614639550}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06219981-4f78-4c17-9d2f-fd05db3d1b17", "name": "Warning: /Users/<USER>/github/LingXia/examples/harmony/AppScope/resources/base/media/layered_image.json is not png format\n", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160615343706}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36ca4d8a-cd81-49c7-9bac-ac6e955d5731", "name": "Info: thread pool is stopped\nInfo: restool resources compile success.\n", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160616186895}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2dac01bb-72a3-407c-8f9c-0599383af437", "name": "astcenc customized so is not be opened when dlclose!\n", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160618603986}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2f70187-9b5d-4f71-a77f-5da2ef23d8d7", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160291323018, "endTime": 652160623587282}, "additional": {"logType": "info", "children": [], "durationId": "e3cc968e-ffbb-4b38-abb7-7791c53d9dba", "parent": "3f00b5db-fd5e-4ccf-b08b-b6f2057a1adb"}}, {"head": {"id": "16846108-3a03-4e74-8987-a611ac103d40", "name": "entry : default@CompileResource cost memory -22.451576232910156", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160624931264}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b82e697b-b946-435d-8a86-411255843b01", "name": "runTaskFromQueue task cost before running: 4 s 10 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160625151924}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f00b5db-fd5e-4ccf-b08b-b6f2057a1adb", "name": "Finished :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160071083774, "endTime": 652160625279521, "totalTime": 553996025}, "additional": {"logType": "info", "children": ["4de6a693-6dcc-4d96-883f-bcddcd309f23", "a2f70187-9b5d-4f71-a77f-5da2ef23d8d7"], "durationId": "d175e8c2-5f09-4d58-8dda-a05105d40aa6"}}, {"head": {"id": "dd2b1d41-89e0-4aa4-8783-c06aedf5b3fd", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160633129602, "endTime": 652169762047038}, "additional": {"children": ["589fed72-f0d3-42ca-bd38-a19d7b5fbeb7", "576241ca-226a-4894-848b-20d919ab3fc3"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": ["The input file '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default' has been changed."], "detailId": "35149108-233b-4b16-bdf0-d40b435eeb4d", "logId": "110dd841-bc22-4a48-abfc-983cf31701b9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "35149108-233b-4b16-bdf0-d40b435eeb4d", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160628727274}, "additional": {"logType": "detail", "children": [], "durationId": "dd2b1d41-89e0-4aa4-8783-c06aedf5b3fd"}}, {"head": {"id": "3f9c746f-fe3e-4488-b614-2b38b4b293e7", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160629035516}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79875edd-4c52-4647-9157-9174248baf44", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160629161138}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4830ccd8-1936-4fe2-aa3e-879c1298e7e5", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160633154132}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d22388e-7281-4b21-ade8-9c6ec1844f86", "name": "Please confirm the symbolic link is valid:  /Users/<USER>/github/LingXia/examples/harmony/entry/oh_modules/lingxia", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160638094883}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "e516d19a-5a14-410c-81d9-3dc04bf3f634", "name": "The current module 'entry' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160638646472}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "01b92a62-d5dd-465b-a013-9081d6e67909", "name": "entry:default@CompileArkTS is not up-to-date, since the input file '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default' has been changed.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160644130929}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16b1bab5-a0b5-4681-9fd1-280549fa1417", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160644313062}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0929b8d9-4f19-464e-ae55-66638a988187", "name": "Please confirm the symbolic link is valid:  /Users/<USER>/github/LingXia/examples/harmony/entry/oh_modules/lingxia", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160653502755}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "62b70841-c890-451c-92ba-23869d957eaf", "name": "The current module 'entry' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160654472544}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "d097fdfa-85c5-419f-b144-ea6678782c6b", "name": "Please confirm the symbolic link is valid:  /Users/<USER>/github/LingXia/examples/harmony/entry/oh_modules/lingxia", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160657752390}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "d706104b-51df-44ac-8489-7c9f0b75db63", "name": "The current module 'entry' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160658548022}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "69a8a701-244a-4e53-ba67-6b83ab69fe2e", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160662188992}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a67afce-0b9d-41d2-b6ad-aafd20fed8f2", "name": "Compile arkts with external api path: /Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/hms/ets", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160663187381}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b767310-c736-46ea-a150-bd3f6380d0f7", "name": "default@CompileArkTS work[1] is submitted.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160664063191}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "589fed72-f0d3-42ca-bd38-a19d7b5fbeb7", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Worker4", "startTime": 652160666924199, "endTime": 652169761686025}, "additional": {"children": [], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "dd2b1d41-89e0-4aa4-8783-c06aedf5b3fd", "logId": "50abcc55-9296-43db-9379-5e3132433ef6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3cfca168-6773-40b8-ba78-ff3d094ee9d9", "name": "default@CompileArkTS work[1] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160665046059}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11f0999d-db3a-4ede-ba20-ad6ffcca1141", "name": "A work dispatched to worker[7] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160665187165}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "030b0be2-411a-4045-8405-e2d321371ae3", "name": "A work dispatched to worker[6] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160665271054}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cecf14b0-b6fc-4a48-9d50-ee324d5eb415", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160665352235}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c0ef0ec-aebc-4857-98f7-009e94f7d593", "name": "Create  resident worker with id: 4.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160665445071}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ec9e36d-546b-421c-9b34-6a76b51b84bb", "name": "default@CompileArkTS work[1] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160666943557}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c868e4eb-90ef-44ed-a445-67b35f066faa", "name": "default@CompileArkTS work[1] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160667141659}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b82c2697-7a14-4092-97d1-6acee3bf367b", "name": "CopyResources startTime: 652160667405108", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160667414651}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6158d10c-ef6d-4e91-88af-52a48a9215ba", "name": "default@CompileArkTS work[2] is submitted.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160667570720}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "576241ca-226a-4894-848b-20d919ab3fc3", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Worker5", "startTime": 652161652658916, "endTime": 652161665599769}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "dd2b1d41-89e0-4aa4-8783-c06aedf5b3fd", "logId": "b0d5c513-0c3a-46bd-b2f4-907106b55cda"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "51393ffe-f393-4f24-82aa-4023a28805bb", "name": "default@CompileArkTS work[2] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160668868227}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7110715d-11f8-46ce-b5a8-7ca6e7c87a5b", "name": "A work dispatched to worker[7] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160669001529}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12c5a8f3-da9f-49e7-accc-4d222f7f78ec", "name": "A work dispatched to worker[6] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160669078815}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be13e51a-b8d5-4f6d-80ec-7a8824a814d3", "name": "Create  resident worker with id: 5.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160669160386}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "000ffb35-1d7a-4aa6-b133-cdc4abf496fc", "name": "default@CompileArkTS work[2] has been dispatched to worker[5].", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160670457569}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0cddd94-0549-4f25-85b5-520e3bd019d6", "name": "default@CompileArkTS work[2] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160670614044}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bec51aa1-e67f-4923-93cf-c09dba7e030e", "name": "entry : default@CompileArkTS cost memory 1.749420166015625", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160670870629}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e133cd13-0e0f-4588-bd35-950d37124947", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160678344563, "endTime": 652160682675298}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "a68b98ba-a03a-42c2-9960-230785102c22", "logId": "10469a65-f21f-4714-858f-b4c0d57f2fe0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a68b98ba-a03a-42c2-9960-230785102c22", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160673207908}, "additional": {"logType": "detail", "children": [], "durationId": "e133cd13-0e0f-4588-bd35-950d37124947"}}, {"head": {"id": "0add1086-b78b-43b2-9205-6ae365a40bc8", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160673749309}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39166a03-0c89-459b-a350-434f5da74eb0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160673879094}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "364d805a-3c5b-4afc-a552-287c48aed8ef", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160678367047}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de91cbd1-3383-458b-a9b6-4aad1f3d754f", "name": "entry : default@BuildJS cost memory 0.1332550048828125", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160682318597}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2258e36-5865-40af-9b48-3b4ad26a5e07", "name": "runTaskFromQueue task cost before running: 4 s 67 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160682556519}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10469a65-f21f-4714-858f-b4c0d57f2fe0", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160678344563, "endTime": 652160682675298, "totalTime": 4164532}, "additional": {"logType": "info", "children": [], "durationId": "e133cd13-0e0f-4588-bd35-950d37124947"}}, {"head": {"id": "65e135c1-58dc-4468-9e3c-1727187ea0e4", "name": "worker[6] has one work done.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652161164543256}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb32c5a2-d937-4216-8bd8-456d6847184c", "name": "default@ProcessLibs work[0] done.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652161165102838}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d239cfca-1ee5-4814-8f75-7fafb1cedc02", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Worker6", "startTime": 652161143244813, "endTime": 652161163913718}, "additional": {"logType": "info", "children": [], "durationId": "f5c47b40-6550-419a-b5b1-0b561bbd0269", "parent": "3b7c8e01-4cba-4237-9085-61656d135b09"}}, {"head": {"id": "a4d07a99-aa67-422c-8f3a-ef13977688e4", "name": "A work dispatched to worker[6] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652161165801168}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b7c8e01-4cba-4237-9085-61656d135b09", "name": "Finished :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160055728914, "endTime": 652161166060005, "totalTime": 31319447}, "additional": {"logType": "info", "children": ["d239cfca-1ee5-4814-8f75-7fafb1cedc02"], "durationId": "d0cf4f0b-573c-40ff-987e-499561fa4d6b"}}, {"head": {"id": "61d405c7-444d-4a85-a34f-8ccf40b41067", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652161174229289, "endTime": 652161347577277}, "additional": {"children": ["02216c54-4171-47b5-a2c2-068f964e6872"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": ["The input file '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/libs/default' has been changed."], "detailId": "a60652f2-e337-4fbe-9f94-11476eaa0a39", "logId": "6c91a866-2ab0-4159-a1f8-9cc9d042286b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a60652f2-e337-4fbe-9f94-11476eaa0a39", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652161169556057}, "additional": {"logType": "detail", "children": [], "durationId": "61d405c7-444d-4a85-a34f-8ccf40b41067"}}, {"head": {"id": "5a335042-2440-4179-b143-831ead88b5e3", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652161170162216}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "635d02a1-df89-4d96-be1c-336dc38c3886", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652161170352873}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "552e47cd-4b5b-4da5-982c-698285bfa933", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652161174259246}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "199825e6-335d-498b-8d65-4998a6a52862", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652161174701532}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2131ddc3-cfaa-4c43-8835-34461017c3db", "name": "entry:default@DoNativeStrip is not up-to-date, since the input file '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/libs/default' has been changed.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652161175633159}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27001e4c-bc0c-4ca0-9aae-5414263818bc", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652161175817908}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "859d6932-e775-4327-b0b8-93d6f376fac5", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652161175963080}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c45a1f1-de7f-4d45-ba58-f0fed56fb46f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652161176058241}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af5d79f3-338e-4e04-b507-4c0318caff75", "name": "default@DoNativeStrip work[3] is submitted.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652161177317221}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02216c54-4171-47b5-a2c2-068f964e6872", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 50614, "tid": "Worker6", "startTime": 652161187480752, "endTime": 652161346830112}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "61d405c7-444d-4a85-a34f-8ccf40b41067", "logId": "dc7259b3-475d-4bcc-a36e-6b7b7fad7ae7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "52bb6bbb-6db3-46f3-909a-db3a05f3bd17", "name": "default@DoNativeStrip work[3] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652161178611715}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a8b118b-26a9-46f3-9f05-18cd5c9dadb6", "name": "A work dispatched to worker[7] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652161178806658}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9560461e-e1f4-4a42-9f6d-1fff1755b790", "name": "default@DoNativeStrip work[3] has been dispatched to worker[6].", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652161179063711}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70a830a9-f481-4493-b37e-e3646010bee5", "name": "default@DoNativeStrip work[3] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652161179185558}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41290cb5-1bba-4dfe-b9e0-66633ed97ae9", "name": "entry : default@DoNativeStrip cost memory 0.22979736328125", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652161179337430}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5cd5c0d1-5a6e-4a0d-8f0c-397ee989b420", "name": "worker[6] has one work done.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652161347030468}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2dcc38d9-4067-4ff4-98b1-9ffee0f2bea1", "name": "default@DoNativeStrip work[3] done.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652161347311127}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc7259b3-475d-4bcc-a36e-6b7b7fad7ae7", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 50614, "tid": "Worker6", "startTime": 652161187480752, "endTime": 652161346830112}, "additional": {"logType": "info", "children": [], "durationId": "02216c54-4171-47b5-a2c2-068f964e6872", "parent": "6c91a866-2ab0-4159-a1f8-9cc9d042286b"}}, {"head": {"id": "6c91a866-2ab0-4159-a1f8-9cc9d042286b", "name": "Finished :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652161174229289, "endTime": 652161347577277, "totalTime": 164579262}, "additional": {"logType": "info", "children": ["dc7259b3-475d-4bcc-a36e-6b7b7fad7ae7"], "durationId": "61d405c7-444d-4a85-a34f-8ccf40b41067"}}, {"head": {"id": "2efa714a-1f66-47bb-89e7-630638d5019c", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652161353113820, "endTime": 652162465300679}, "additional": {"children": ["b101dd9c-0e59-40af-b4dc-566fc7c98ee2"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": ["The input file '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/stripped_native_libs/default' has been changed."], "detailId": "ad3ab5fc-3537-4e75-a599-d20a8b202adb", "logId": "cb3682c7-16e4-4938-b524-04e884f8cf15"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ad3ab5fc-3537-4e75-a599-d20a8b202adb", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652161350085327}, "additional": {"logType": "detail", "children": [], "durationId": "2efa714a-1f66-47bb-89e7-630638d5019c"}}, {"head": {"id": "798ce372-50a0-4502-abfe-10b87d0c1d9a", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652161350524462}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8501132e-e5c6-4975-a6de-cf96256947df", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652161350711688}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81f72cec-2bf7-4822-8e6c-5c14cd968b07", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652161353134599}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6772011-c3f5-4f7e-abc4-71dacb1786cd", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652161353522410}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d354e53-9857-4c6e-9db4-6d095845428b", "name": "entry:default@CacheNativeLibs is not up-to-date, since the input file '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/stripped_native_libs/default' has been changed.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652161354245084}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34784ec3-0d15-4731-b63b-ec2e2f608116", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652161354385217}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f989f32-81ca-4308-9746-9a5966b84457", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652161354484101}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01649bd7-8cff-48a5-aa0b-a8347b2898da", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652161354546702}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19185ed6-eea9-41c5-b741-f75d60f93b83", "name": "default@CacheNativeLibs work[4] is submitted.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652161354815674}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b101dd9c-0e59-40af-b4dc-566fc7c98ee2", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 50614, "tid": "Worker6", "startTime": 652162460874895, "endTime": 652162464647451}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "2efa714a-1f66-47bb-89e7-630638d5019c", "logId": "5db7bfd5-9c30-4fa6-af73-0efe261ce2f7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "22e92236-c23d-4b6f-bca5-73924a56a75d", "name": "default@CacheNativeLibs work[4] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652161355933887}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1c9f026-f654-4241-9fcf-f50737d2e716", "name": "default@CacheNativeLibs work[4] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652161356060022}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc0cb1d7-aa89-4a80-8ae5-3977a3b0a615", "name": "entry : default@CacheNativeLibs cost memory 0.226226806640625", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652161356165524}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ff688cf-3b19-4590-bbb7-42d38e777a5b", "name": "default@CacheNativeLibs work[4] has been dispatched to worker[6].", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652161448478583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d5722bc-31f3-4979-bbee-bf857bf32939", "name": "A work dispatched to worker[7] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652161456990630}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6543451-a61e-4ed8-9353-836dc6de0e97", "name": "A work dispatched to worker[6] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652161457133369}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7c8f763-06ab-48df-be2f-22adce0cc86d", "name": "A work dispatched to worker[5] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652161457210243}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "615d7466-75a8-43b9-9dc9-3a4b821f0d01", "name": "A work dispatched to worker[4] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652161457254381}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ab63d77-9a12-4823-8aea-6e4269ad14b1", "name": "A work dispatched to worker[3] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652161457359631}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "297bbf10-4a5b-41c2-8c68-41e9a7d37a8a", "name": "A work dispatched to worker[2] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652161457419971}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00c053f1-4a3c-4711-a636-b37a6e927357", "name": "A work dispatched to worker[1] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652161457477441}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f790c08-a93a-4611-a574-cd5759cc5f78", "name": "A work dispatched to worker[0] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652161457528381}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52c38cfd-0c9f-4482-a822-a21de5e0abce", "name": "worker[5] has one work done.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652161665863775}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03db96f4-e05a-443a-b8a1-71f01e9cfa07", "name": "CopyResources is end, endTime: 652161666054590", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652161666062897}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26a22fce-f6e2-4e56-9cff-d252ea9eb37c", "name": "default@CompileArkTS work[2] done.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652161666190968}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0d5c513-0c3a-46bd-b2f4-907106b55cda", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Worker5", "startTime": 652161652658916, "endTime": 652161665599769}, "additional": {"logType": "info", "children": [], "durationId": "576241ca-226a-4894-848b-20d919ab3fc3", "parent": "110dd841-bc22-4a48-abfc-983cf31701b9"}}, {"head": {"id": "61d04f32-81f4-42a7-afe2-b6b96ffad4c3", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652161666342153}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31b8c41f-a6a3-4db3-a187-c8a7196f0921", "name": "worker[6] has one work done.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652162464807475}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43fe340f-6da1-4752-a1a2-07ad2d49304c", "name": "default@CacheNativeLibs work[4] done.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652162464984834}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5db7bfd5-9c30-4fa6-af73-0efe261ce2f7", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 50614, "tid": "Worker6", "startTime": 652162460874895, "endTime": 652162464647451}, "additional": {"logType": "info", "children": [], "durationId": "b101dd9c-0e59-40af-b4dc-566fc7c98ee2", "parent": "cb3682c7-16e4-4938-b524-04e884f8cf15"}}, {"head": {"id": "9d42cf54-3913-43cd-b0cd-8efd256ea51e", "name": "A work dispatched to worker[6] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652162465101464}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb3682c7-16e4-4938-b524-04e884f8cf15", "name": "Finished :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652161353113820, "endTime": 652162465300679, "totalTime": 6898321}, "additional": {"logType": "info", "children": ["5db7bfd5-9c30-4fa6-af73-0efe261ce2f7"], "durationId": "2efa714a-1f66-47bb-89e7-630638d5019c"}}, {"head": {"id": "6c3edf6a-d9a4-4cef-9007-19e67a8aef79", "name": "worker[4] has one work error.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169761421379}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81b4dbf7-2c52-45d8-a76d-cf414fbfad92", "name": "default@CompileArkTS work[1] failed.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169761826112}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50abcc55-9296-43db-9379-5e3132433ef6", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Worker4", "startTime": 652160666924199, "endTime": 652169761686025}, "additional": {"logType": "error", "children": [], "durationId": "589fed72-f0d3-42ca-bd38-a19d7b5fbeb7", "parent": "110dd841-bc22-4a48-abfc-983cf31701b9"}}, {"head": {"id": "110dd841-bc22-4a48-abfc-983cf31701b9", "name": "Failed :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652160633129602, "endTime": 652169762047038}, "additional": {"logType": "error", "children": ["50abcc55-9296-43db-9379-5e3132433ef6", "b0d5c513-0c3a-46bd-b2f4-907106b55cda"], "durationId": "dd2b1d41-89e0-4aa4-8783-c06aedf5b3fd"}}, {"head": {"id": "e17eece8-5faf-4dab-8eca-1a15b295b1ef", "name": "hvigor build process will be closed with an error.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169762324459}, "additional": {"logType": "debug", "children": [], "durationId": "dd2b1d41-89e0-4aa4-8783-c06aedf5b3fd"}}, {"head": {"id": "d15e1bbb-aa02-493c-aff4-5d374b679caf", "name": "ERROR: stacktrace = Error: ENOENT: no such file or directory, stat '/Users/<USER>/github/LingXia/examples/harmony/entry/oh_modules/lingxia'\n\u001b[31m1 ERROR: \u001b[31mArkTS:ERROR File: /Users/<USER>/github/LingXia/examples/harmony/entry/src/main/ets/entryability/EntryAbility.ets:4:23\n Cannot find module 'lingxia' or its corresponding type declarations.\n\n\u001b[31m> More Info: https://developer.huawei.com/consumer/en/doc/harmonyos-faqs-V5/faqs-compiling-and-building-4-V5\n\n\u001b[39m\n\u001b[31m2 ERROR: \u001b[31mArkTS:ERROR File: /Users/<USER>/github/LingXia/examples/harmony/entry/src/main/ets/pages/Index.ets:1:40\n Cannot find module 'lingxia' or its corresponding type declarations.\n\n\u001b[31m> More Info: https://developer.huawei.com/consumer/en/doc/harmonyos-faqs-V5/faqs-compiling-and-building-4-V5\n\n\u001b[39m\n\u001b[31mCOMPILE RESULT:FAIL {ERROR:3}\u001b[39m\n    at runArkPack (/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor-ohos-plugin/node_modules/@ohos/hvigor-arkts-compose/dist/src/arkts-pack.js:1:5438)\nError: ENOENT: no such file or directory, stat '/Users/<USER>/github/LingXia/examples/harmony/entry/oh_modules/lingxia'\n    at Object.statSync (node:fs:1688:3)\n    at resolveOhModules (/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor-ohos-plugin/node_modules/@ohos/hvigor-arkts-compose/dist/src/plugins/node-resolve/resolveOhModules.js:1:1531)\n    at resolveProjectOhModules (/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor-ohos-plugin/node_modules/@ohos/hvigor-arkts-compose/dist/src/plugins/node-resolve/resolveOhModules.js:1:1050)\n    at resolveAllOhModules (/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor-ohos-plugin/node_modules/@ohos/hvigor-arkts-compose/dist/src/plugins/node-resolve/resolveOhModules.js:1:760)\n    at Object.buildStart (/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor-ohos-plugin/node_modules/@ohos/hvigor-arkts-compose/dist/src/plugins/node-resolve/index.js:1:9657)\n    at /Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor-ohos-plugin/node_modules/@ohos/hvigor-arkts-base/node_modules/rollup/dist/shared/rollup.js:24828:40\n    at async Promise.all (index 2)\n    at async PluginDriver.hookParallel (/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor-ohos-plugin/node_modules/@ohos/hvigor-arkts-base/node_modules/rollup/dist/shared/rollup.js:24756:9)\n    at async /Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor-ohos-plugin/node_modules/@ohos/hvigor-arkts-base/node_modules/rollup/dist/shared/rollup.js:26031:13\n    at async catchUnfinishedHookActions (/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor-ohos-plugin/node_modules/@ohos/hvigor-arkts-base/node_modules/rollup/dist/shared/rollup.js:25197:24)", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169764139503}, "additional": {"logType": "debug", "children": [], "durationId": "dd2b1d41-89e0-4aa4-8783-c06aedf5b3fd"}}, {"head": {"id": "c4938797-b3e8-42d0-bc45-e74510eec949", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169767349579, "endTime": 652169767723600}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a7ac4438-615f-4a9c-a70a-3460c189330d", "logId": "25fceaa9-7812-4546-86d7-5f5ea242ee46"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "25fceaa9-7812-4546-86d7-5f5ea242ee46", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169767349579, "endTime": 652169767723600}, "additional": {"logType": "info", "children": [], "durationId": "c4938797-b3e8-42d0-bc45-e74510eec949"}}, {"head": {"id": "e78f4b29-c7db-4c4b-8dc6-49dbfbf73775", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652156615836038, "endTime": 652169768652145}, "additional": {"time": {"year": 2025, "month": 7, "day": 31, "hour": 21, "minute": 33}, "completeCommand": "{\"prop\":[],\"_\":[\"assembleHap\"]};assembleHap", "hvigorVersion": "5.17.2", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "failed"}}, {"head": {"id": "010aeb76-129f-44b1-b6ec-f053c3ba158d", "name": "BUILD FAILED in 13 s 152 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169768766871}, "additional": {"logType": "error", "children": []}}, {"head": {"id": "2893d211-f8da-4deb-98a1-0886cb00dce4", "name": "Update task entry:default@PreBuild input file:/Users/<USER>/github/LingXia/examples/harmony/AppScope/app.json5 cache from map.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169769617336}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9bf3e686-9d61-4265-aaaf-5acf44f6f968", "name": "Update task entry:default@PreBuild input file:/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/module.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169769863828}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab2adea5-9259-4657-ba1f-96cdd083c449", "name": "Update task entry:default@PreBuild input file:/Users/<USER>/github/LingXia/examples/harmony/build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169770273237}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93d8dada-698d-4243-8c13-fc4ff5bdab72", "name": "Update task entry:default@PreBuild input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169770481927}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "935dff1f-5567-419a-bb63-332c0894f038", "name": "Update task entry:default@PreBuild input file:/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources/base/profile/main_pages.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169770652391}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0ed379c-7892-4998-92b9-b523af6c2584", "name": "Update task entry:default@PreBuild input file:/Users/<USER>/github/LingXia/examples/harmony/hvigor/hvigor-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169770863555}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c05ae96-37e6-42cc-bb4a-39c4da01e87e", "name": "Update task entry:default@PreBuild input file:/Users/<USER>/github/LingXia/examples/harmony/entry/oh-package.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169771017603}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5978325-0bb1-4427-84a2-477c985a5e0e", "name": "Update task entry:default@PreBuild input file:/Users/<USER>/github/LingXia/examples/harmony/oh-package.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169771169488}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0288426b-574f-4e09-81e0-eb68d24a3b45", "name": "Incremental task entry:default@PreBuild post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169771764820}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "197b4941-df47-4b42-b1bd-d33af7e9cd06", "name": "Update task entry:default@CreateModuleInfo output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/generated/profile/default/ModuleInfo.ts cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169771956773}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2de4fcda-1b24-49a9-831c-03daf2b75224", "name": "Incremental task entry:default@CreateModuleInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169772372051}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d635b9b8-bca6-4570-aa13-57a4f36d7451", "name": "Update task entry:default@GenerateMetadata input file:/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/module.json5 cache from map.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169772472662}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "abfce54b-ae4f-4721-acea-6fd6019d3937", "name": "Update task entry:default@GenerateMetadata output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/hap_metadata/default/output_metadata.json cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169772541694}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "def921f7-cf4d-4b63-8f61-f4ee20a33a89", "name": "Incremental task entry:default@GenerateMetadata post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169772733232}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d1123c9-66ae-42a3-a381-7736e5dcf18f", "name": "Update task entry:default@MergeProfile input file:/Users/<USER>/github/LingXia/examples/harmony/AppScope/app.json5 cache from map.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169772821038}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "afc467c2-5744-44da-968f-88cf355c0175", "name": "Update task entry:default@MergeProfile input file:/Users/<USER>/github/LingXia/examples/harmony/build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169772883739}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14604ae1-121f-481a-91ec-6bfa380a7d0d", "name": "Update task entry:default@MergeProfile input file:/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/module.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169772989930}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c509b9d-ba12-4369-aaf8-e16c9b9dfbac", "name": "Update task entry:default@MergeProfile output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/merge_profile/default/module.json cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169773103305}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b95e7a0-2776-49d5-bba1-956dfa3c0733", "name": "Incremental task entry:default@MergeProfile post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169773245776}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41bafa74-5688-4d7e-a788-55ea036540ad", "name": "Update task entry:default@CreateBuildProfile input file:/Users/<USER>/github/LingXia/examples/harmony/AppScope/app.json5 cache from map.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169773321085}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f95fd855-7f3f-4daf-b0bc-6b7564e93207", "name": "Update task entry:default@CreateBuildProfile input file:/Users/<USER>/github/LingXia/examples/harmony/build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169773377587}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b7af325-f7a6-491a-9781-fd38d5bca29b", "name": "Update task entry:default@CreateBuildProfile output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/generated/profile/default/BuildProfile.ets cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169773529210}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43f709f6-81a5-4de9-ab37-d7a9fcca3f96", "name": "Incremental task entry:default@CreateBuildProfile post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169773805634}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af9f74ed-7b19-42a0-ab35-907c96b4fa27", "name": "Update task entry:default@GeneratePkgContextInfo output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default/pkgContextInfo.json cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169773911744}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "365b7447-eca5-483f-973a-69a819dfdd5a", "name": "Incremental task entry:default@GeneratePkgContextInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169774078475}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "777bf231-9303-4a8a-bbd4-6a153f3be7b8", "name": "Update task entry:default@ProcessIntegratedHsp output file:/Users/<USER>/github/LingXia/examples/harmony/build/cache/default/integrated_hsp/integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169774563676}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eec7261e-4e3f-4998-a8f2-6c70e228f15f", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169774760489}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "982cd163-ea86-4037-bbe9-b1f1b3eff7b2", "name": "Update task entry:default@MakePackInfo input file:/Users/<USER>/github/LingXia/examples/harmony/AppScope/app.json5 cache from map.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169774856957}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b67503d4-4c19-40f0-a53a-66e578f22c70", "name": "Update task entry:default@MakePackInfo input file:/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/module.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169774930530}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1079221e-10bc-4d7e-8a5b-ce9948d7abb8", "name": "Update task entry:default@MakePackInfo input file:/Users/<USER>/github/LingXia/examples/harmony/build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169775061313}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f7014d5-e5d8-45c5-b00b-cb0e32930d66", "name": "Update task entry:default@MakePackInfo output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/outputs/default/pack.info cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169775553315}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19ed3fcb-f8a1-4bb1-9c9a-98ec2d015ff0", "name": "Incremental task entry:default@MakePackInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169775741591}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15a581bd-92ae-4af1-b5dd-580802f482b6", "name": "Update task entry:default@SyscapTransform input file:/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/toolchains/syscap_tool cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169775832354}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99a2f24c-80db-41fe-af14-2fc8321be060", "name": "Update task entry:default@SyscapTransform input file:/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/device-define cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169775984441}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84473148-3ac1-4606-a9df-b56e50162eef", "name": "Update task entry:default@SyscapTransform output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/syscap/default/rpcid.sc cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169776398157}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a2bba0a-edc6-4107-9614-a754147e84e7", "name": "Incremental task entry:default@SyscapTransform post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169776747466}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d6efeb2-bbc5-4068-b332-3c7c3514836b", "name": "Update task entry:default@ProcessProfile input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/merge_profile/default/module.json cache from map.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169776913250}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ee9897b-3710-41ae-921c-6dea1f6ede3a", "name": "Update task entry:default@ProcessProfile output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/process_profile/default/module.json cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169777067546}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c106c31-3185-44a5-a8ab-a63f16f57d40", "name": "Incremental task entry:default@ProcessProfile post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169777323090}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5632fcd-dddd-477c-8c13-fd7569d759d4", "name": "Update task entry:default@ProcessRouterMap input file:/Users/<USER>/github/LingXia/examples/harmony/entry/oh-package.json5 cache from map.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169778968186}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6de2223-124a-4cc9-8bed-ad3b7a9aecf3", "name": "Update task entry:default@ProcessRouterMap input file:/Users/<USER>/github/LingXia/examples/harmony/oh-package.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169779101014}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efeaad1d-6b1f-4885-8aff-367b8857314d", "name": "Update task entry:default@ProcessRouterMap input file:/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/module.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169779248110}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8580a472-701a-4813-93dc-62bfb4de0e0c", "name": "Update task entry:default@ProcessRouterMap input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default/pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169779509286}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "340538a8-0709-4dae-8236-d2f1579619ff", "name": "Update task entry:default@ProcessRouterMap output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/router_map/default/temp-router-map.json cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169779692328}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5866d3a-1c96-4a3e-88db-f0c7634f3de2", "name": "Update task entry:default@ProcessRouterMap output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/router_map/default/loader-router-map.json cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169779833843}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "985c88fe-b8fe-4b3c-b57a-281cc8944b3f", "name": "Incremental task entry:default@ProcessRouterMap post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169780047311}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "010e8724-6de8-4092-a875-57733e7973f5", "name": "Update task entry:default@ProcessResource output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resConfig.json cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169780162302}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a181423-33b2-42bb-a5f7-d9aaf880646b", "name": "Update task entry:default@ProcessResource output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/opt-compression.json cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169780290000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d01aa75-c25c-482a-bb8b-d1ce4aad999d", "name": "Incremental task entry:default@ProcessResource post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169780441900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99e492a9-a03c-4017-b1cb-1bdc6fec359e", "name": "Update task entry:default@GenerateLoaderJson input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default/pkgContextInfo.json cache from map.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169782674604}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "098f0a7b-5468-418a-8173-bdeb5cf9f1c8", "name": "Update task entry:default@GenerateLoaderJson input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/router_map/default/temp-router-map.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169782824129}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "260a0002-dee4-4606-a900-03920a6f6b87", "name": "Update task entry:default@GenerateLoaderJson output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default/loader.json cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169783014457}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e40bb627-d313-41e4-9a09-c570c8959971", "name": "Incremental task entry:default@GenerateLoaderJson post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169783216821}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f70c46f-2f6b-4bc7-926e-301e3c2dca7e", "name": "Update task entry:default@ProcessLibs input file:/Users/<USER>/github/LingXia/examples/harmony/entry/libs cache from map.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169783353710}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a155c879-f420-444e-ae31-ea66ec6d6020", "name": "Update task entry:default@ProcessLibs input file:/Users/<USER>/github/LingXia/examples/harmony/build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169783424997}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e9e569d-846c-4b21-a375-335f7212f7b6", "name": "Update task entry:default@ProcessLibs input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169783532326}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "517589bb-3253-4829-91ee-be12e3f8b7bb", "name": "Update task entry:default@ProcessLibs output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/libs/default cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169783648043}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3655290-efc3-417f-8667-9f9feb31a726", "name": "Incremental task entry:default@ProcessLibs post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169784028612}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd007401-310e-47d8-928b-bb6fc0108f83", "name": "Update task entry:default@CompileResource input file:/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources cache from map.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169784642046}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7c81330-d649-46a9-bfd9-88a7be565d65", "name": "Update task entry:default@CompileResource input file:/Users/<USER>/github/LingXia/examples/harmony/AppScope/resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169784736146}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84832cc4-1cfe-4483-870d-5c3e6655fc71", "name": "Update task entry:default@CompileResource input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/process_profile/default/module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169785385591}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dda549cd-7ef9-4fb2-a9ef-5d8e5d75ffec", "name": "Update task entry:default@CompileResource input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resConfig.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169785514044}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac6fb1a7-6915-4ab3-afff-a62e2766dea4", "name": "Update task entry:default@CompileResource output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169785674653}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d2310e7c-abb1-4b8c-ba76-05ee112e4fd6", "name": "Update task entry:default@CompileResource output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/generated/r/default/ResourceTable.h cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169788541778}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9162d86-6355-4e3a-ad24-83bca1c41db8", "name": "Update task entry:default@CompileResource output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/generated/r/default cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169788693820}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16906ab4-3960-41bf-9874-d924c1ba0df1", "name": "Incremental task entry:default@CompileResource post-execution cost:5 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169789024343}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2aa96fbd-936b-4277-a9f5-bf58ad204d51", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default cache from map.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169791085040}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ada37d72-913a-4b7e-a49c-3ea0e9f7af9e", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169791200957}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca81f18d-bda2-4831-9c49-d4daf793191a", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169792651680}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2703f2ec-98b6-42b6-8dfc-5516eeda49e8", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169792792346}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "081a4307-d763-4f8b-a491-e5395e9e72f3", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/base/profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169792907595}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e6f996a-a3d8-4db9-b78e-2c565ada0cb9", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169793098675}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3693c9c3-12e4-4761-b926-a1e696a361bd", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default/pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169793467390}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "abd0911f-e140-46be-a1cb-dd1b38abd945", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/generated/profile/default/BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169793578325}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d973b636-5faa-4622-be0f-da8004b561d6", "name": "Update task entry:default@CompileArkTS output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader_out/default/ets cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169793719858}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d6373db-9723-4f0d-99c0-878473b277a8", "name": "Incremental task entry:default@CompileArkTS post-execution cost:5 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169793924294}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66cab0ba-6dd8-4dc8-bc07-e304911c1aa6", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169794939610}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85f51be5-3fd6-403d-b01a-9a65e962b63b", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169795252313}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "deb88fbf-810f-4b1f-90cc-d168bd76ed86", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169796651535}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb5f7d97-2d45-41c0-bc60-5f1b5378a0fe", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169796774044}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d961693-4a01-44fb-82bd-8bdbc2de8556", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/base/profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169796870212}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b760a4f3-2d0f-4361-bfad-302e21f96588", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default/pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169797048253}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc05d775-a111-4bc5-822e-fe3c229ddeff", "name": "Update task entry:default@BuildJS output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader_out/default/js cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169797186474}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "359827d6-6857-483e-aea1-c87041298916", "name": "Incremental task entry:default@BuildJS post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169797383589}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c5daf2e-ce02-4689-8885-422a5c817fcc", "name": "Update task entry:default@DoNativeStrip input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/libs/default cache from map.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169797461734}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe2b9db3-c508-49ea-89bc-7100fd022b0a", "name": "Update task entry:default@DoNativeStrip output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/stripped_native_libs/default cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169797515581}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "780566a9-0487-4bfd-8723-a38bab29aa7a", "name": "Incremental task entry:default@DoNativeStrip post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169797832374}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b20e0f36-2cf0-4e09-b0fa-a2ec410449b3", "name": "Update task entry:default@CacheNativeLibs input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/stripped_native_libs/default cache from map.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169797910547}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d889358a-6bf4-4f02-ae9b-608f821fc71c", "name": "Update task entry:default@CacheNativeLibs input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/libs/default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169797961797}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "509e4e7f-5f2c-45a0-befb-afcf11fdcd51", "name": "Update task entry:default@CacheNativeLibs output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/patch/default/base_native_libs.json cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169798236884}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d5464f6-813f-474e-bd82-5a42270522b6", "name": "Incremental task entry:default@CacheNativeLibs post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169798380942}, "additional": {"logType": "debug", "children": []}}], "workLog": []}