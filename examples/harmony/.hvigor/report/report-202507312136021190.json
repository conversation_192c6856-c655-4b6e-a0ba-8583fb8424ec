{"version": "2.0", "ppid": 50563, "events": [{"head": {"id": "91d1e6cc-5a84-4814-b004-8cfcd223c3e8", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169821335037}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6cf990e-a54a-4649-bb60-60b62c904537", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169825692721}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c703e12-bcb1-422e-a650-fd60b1e403fc", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169826166154}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e59fff42-a492-4e75-878e-77933601528e", "name": "worker[5] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169833669925}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c6b0410-df69-452e-b989-68323225bc86", "name": "worker[6] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652169839940276}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ce16887-8a30-46b9-8683-4324fbc0bfbe", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652350450944631}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80c65ffe-aceb-4093-8bb2-20869d77ca37", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652350462823744}, "additional": {"children": ["16eb4e06-3ee3-41cb-b911-f5abf3af3d8a", "7f3e30c9-5af0-443f-8402-2719a38646f3", "4fb257f5-0682-40cc-bc0c-6709ebb2d193", "ec4c7010-f922-4995-a9ad-eef80b09f427"], "state": "running", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": []}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "16eb4e06-3ee3-41cb-b911-f5abf3af3d8a", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652350462827253}, "additional": {"children": [], "state": "running", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "80c65ffe-aceb-4093-8bb2-20869d77ca37"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a98f5b68-68b3-475d-91bb-f083cb4d58e0", "name": "hvigor build process will be closed with an error.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652350468482975}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "418de685-286e-4a6c-a420-a378aaa575a7", "name": "ERROR: stacktrace = Error: Path not found: '/Users/<USER>/github/LingXia/examples/lingxia-sdk/harmony/lingxia'. Please check field: 'modules' in file: '/Users/<USER>/github/LingXia/examples/harmony/build-profile.json5'.\n    at HvigorLogger.errorMessageExit (/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/src/base/log/hvigor-log.js:1:3224)\n    at exitIfNotExists (/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/src/base/util/file-util.js:1:1265)\n    at /Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/src/base/external/core/hvigor-config.js:1:1304\n    at Array.forEach (<anonymous>)\n    at new HvigorConfig (/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/src/base/external/core/hvigor-config.js:1:1091)\n    at hvigorConfigInit (/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/src/base/external/core/hvigor-config.js:1:3418)\n    at init (/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/src/base/internal/lifecycle/init.js:1:2867)\n    at start (/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/src/base/boot/index.js:1:2462)\n    at boot (/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/src/base/boot/index.js:1:1762)\n    at Worker.<anonymous> (/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/src/base/daemon/cluster/worker-process-lifecycle.js:1:3340)", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652350470434523}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7432c552-004b-447b-bf6d-850e2e435116", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652350473682960, "endTime": 652350473751246}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6b2f0b8f-58fa-4e81-9091-f836dc39a54d", "logId": "949e67d4-6aec-446c-ab0d-8a6879af71f5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "949e67d4-6aec-446c-ab0d-8a6879af71f5", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652350473682960, "endTime": 652350473751246}, "additional": {"logType": "info", "children": [], "durationId": "7432c552-004b-447b-bf6d-850e2e435116"}}, {"head": {"id": "fa93ebac-139f-46cc-b46e-befb0a1cdbeb", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652350456606181, "endTime": 652350474334785}, "additional": {"time": {"year": 2025, "month": 7, "day": 31, "hour": 21, "minute": 36}, "completeCommand": "{\"prop\":[],\"_\":[\"assembleHap\"]};assembleHap", "hvigorVersion": "5.17.2", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "failed"}}, {"head": {"id": "d1d6e203-99f4-41b2-88f7-d5ae67990f9a", "name": "BUILD FAILED in 18 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652350474442910}, "additional": {"logType": "error", "children": []}}], "workLog": []}