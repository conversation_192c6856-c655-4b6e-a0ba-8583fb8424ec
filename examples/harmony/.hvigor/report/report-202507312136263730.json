{"version": "2.0", "ppid": 50563, "events": [{"head": {"id": "88c95e3a-d052-4311-b887-32210ba81d7f", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652350482684748}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69aa41fa-4af1-4e8f-a6f3-edbd3cce0e60", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652350491522951}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6492fd7-f7ca-4fa6-8b25-bbf27e207ceb", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652350491988034}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35e32565-00f9-4680-817c-e9def240d130", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374706008979}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f8b4948-8abf-4a04-8711-615634f660de", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374717306842, "endTime": 652375096163377}, "additional": {"children": ["77fe6521-cad9-4d0f-8f84-7e130424a3dd", "e291434f-97db-4fb1-bc2a-3d878420c382", "a46acf3a-cb85-4bc0-b785-efc1812ebc0e", "09abd05c-4f20-41a7-8094-29471d9de18c", "beccbb50-b360-4057-96e5-08edfa55fbe7", "238aaaf0-07e8-4eee-a5ba-3bb48aba707a", "b9301185-9f10-45b9-b257-2d4d03cb4431"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "eec6d833-aeb1-4303-a576-166ccda6e884"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "77fe6521-cad9-4d0f-8f84-7e130424a3dd", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374717308227, "endTime": 652374757755410}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8f8b4948-8abf-4a04-8711-615634f660de", "logId": "8598d041-2b02-42a1-b8c0-2c45af248394"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e291434f-97db-4fb1-bc2a-3d878420c382", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374757785463, "endTime": 652375092707109}, "additional": {"children": ["fbf399d8-732e-4980-bbca-9c8c29539987", "b7967d3d-08b1-46f9-8a52-aa1bbdda88fe", "84195608-62a2-4d5c-a74a-5aad0c6db1cb", "521314f4-3f09-4bb5-95fb-82e44bd05a5b", "5e5c808f-4954-41d6-a4fb-e4c6f230d65a", "528ff122-9e42-4272-813d-64661f608e38", "4df08cf6-69c2-482b-8a6e-25c93d187ac5", "afe9f004-7551-4190-9c4e-544fdb39fdcd", "5d15af59-c1be-4215-98f3-9af66b8f314b"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8f8b4948-8abf-4a04-8711-615634f660de", "logId": "c838c4d3-bca5-413a-97d2-4b2ce78bf567"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a46acf3a-cb85-4bc0-b785-efc1812ebc0e", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375092767184, "endTime": 652375096094125}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8f8b4948-8abf-4a04-8711-615634f660de", "logId": "bf588560-8c6a-4770-b579-5bcd7c70f4d2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "09abd05c-4f20-41a7-8094-29471d9de18c", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375096125570, "endTime": 652375096156642}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8f8b4948-8abf-4a04-8711-615634f660de", "logId": "df8237ca-3c74-4174-beed-da56fbd6f1c2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "beccbb50-b360-4057-96e5-08edfa55fbe7", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374722032554, "endTime": 652374722091390}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8f8b4948-8abf-4a04-8711-615634f660de", "logId": "c96f4246-076f-4525-8a59-2e6183b90af1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c96f4246-076f-4525-8a59-2e6183b90af1", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374722032554, "endTime": 652374722091390}, "additional": {"logType": "info", "children": [], "durationId": "beccbb50-b360-4057-96e5-08edfa55fbe7", "parent": "eec6d833-aeb1-4303-a576-166ccda6e884"}}, {"head": {"id": "238aaaf0-07e8-4eee-a5ba-3bb48aba707a", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374733257908, "endTime": 652374733278320}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8f8b4948-8abf-4a04-8711-615634f660de", "logId": "415ebb1d-744a-4626-ac41-0b602a9d808f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "415ebb1d-744a-4626-ac41-0b602a9d808f", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374733257908, "endTime": 652374733278320}, "additional": {"logType": "info", "children": [], "durationId": "238aaaf0-07e8-4eee-a5ba-3bb48aba707a", "parent": "eec6d833-aeb1-4303-a576-166ccda6e884"}}, {"head": {"id": "0783625f-5f87-4663-8c3c-1758d4a2644e", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374733440538}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa1a0d82-0d7e-4ac1-a728-4e383f9c3608", "name": "Cache service initialization finished in 24 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374757435904}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8598d041-2b02-42a1-b8c0-2c45af248394", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374717308227, "endTime": 652374757755410}, "additional": {"logType": "info", "children": [], "durationId": "77fe6521-cad9-4d0f-8f84-7e130424a3dd", "parent": "eec6d833-aeb1-4303-a576-166ccda6e884"}}, {"head": {"id": "fbf399d8-732e-4980-bbca-9c8c29539987", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374762278675, "endTime": 652374762290961}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e291434f-97db-4fb1-bc2a-3d878420c382", "logId": "92445368-1c98-4ce9-8953-edcf19b076c2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b7967d3d-08b1-46f9-8a52-aa1bbdda88fe", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374762394910, "endTime": 652374769732369}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e291434f-97db-4fb1-bc2a-3d878420c382", "logId": "c9b65947-0daa-4a7f-a704-9c8b4bf3fc09"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "84195608-62a2-4d5c-a74a-5aad0c6db1cb", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374769750416, "endTime": 652374896330385}, "additional": {"children": ["85bf3862-5489-4078-bef0-04f32c403884", "c7015c20-2d26-4e45-8e57-0f39a92f2f92", "8c1a1f3a-d60a-4d83-9028-18c3da182336"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e291434f-97db-4fb1-bc2a-3d878420c382", "logId": "45b956db-16d5-4b94-bfcb-c91c6a149d5d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "521314f4-3f09-4bb5-95fb-82e44bd05a5b", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374896348553, "endTime": 652374944289313}, "additional": {"children": ["3a01468b-464c-490f-8692-bd6e0f1e3b42", "8ed66f8e-7001-49f5-9a73-92bb04a4e8c4"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e291434f-97db-4fb1-bc2a-3d878420c382", "logId": "ca5d8e05-e539-43f1-92ba-9b88df816a8e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5e5c808f-4954-41d6-a4fb-e4c6f230d65a", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374944302580, "endTime": 652375070688593}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e291434f-97db-4fb1-bc2a-3d878420c382", "logId": "deccab5d-3582-42e2-b50a-1a0ecea448d3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "528ff122-9e42-4272-813d-64661f608e38", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375071921377, "endTime": 652375081174789}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e291434f-97db-4fb1-bc2a-3d878420c382", "logId": "a0ecc46a-3db6-4c6f-a44f-d1cbda6fdec9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4df08cf6-69c2-482b-8a6e-25c93d187ac5", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375081208413, "endTime": 652375092429262}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e291434f-97db-4fb1-bc2a-3d878420c382", "logId": "7f7344b0-625a-4f7b-b22a-4dd04bf9c763"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "afe9f004-7551-4190-9c4e-544fdb39fdcd", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375092471804, "endTime": 652375092687409}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e291434f-97db-4fb1-bc2a-3d878420c382", "logId": "e3364e24-05e3-41ea-bf14-b4a23c75fe44"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "92445368-1c98-4ce9-8953-edcf19b076c2", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374762278675, "endTime": 652374762290961}, "additional": {"logType": "info", "children": [], "durationId": "fbf399d8-732e-4980-bbca-9c8c29539987", "parent": "c838c4d3-bca5-413a-97d2-4b2ce78bf567"}}, {"head": {"id": "c9b65947-0daa-4a7f-a704-9c8b4bf3fc09", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374762394910, "endTime": 652374769732369}, "additional": {"logType": "info", "children": [], "durationId": "b7967d3d-08b1-46f9-8a52-aa1bbdda88fe", "parent": "c838c4d3-bca5-413a-97d2-4b2ce78bf567"}}, {"head": {"id": "85bf3862-5489-4078-bef0-04f32c403884", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374770342239, "endTime": 652374770360754}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "84195608-62a2-4d5c-a74a-5aad0c6db1cb", "logId": "b9416a42-a542-4520-ba68-7993f7c0903e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b9416a42-a542-4520-ba68-7993f7c0903e", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374770342239, "endTime": 652374770360754}, "additional": {"logType": "info", "children": [], "durationId": "85bf3862-5489-4078-bef0-04f32c403884", "parent": "45b956db-16d5-4b94-bfcb-c91c6a149d5d"}}, {"head": {"id": "c7015c20-2d26-4e45-8e57-0f39a92f2f92", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374772052161, "endTime": 652374895774532}, "additional": {"children": ["0126805b-1401-43d8-9365-9d83b4b0322a", "d3acb78e-2be0-44a2-9ad4-10b547373395"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "84195608-62a2-4d5c-a74a-5aad0c6db1cb", "logId": "14f7a1a4-45fb-42d3-9d0d-ccf41ee3cddd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0126805b-1401-43d8-9365-9d83b4b0322a", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374772053503, "endTime": 652374792570226}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c7015c20-2d26-4e45-8e57-0f39a92f2f92", "logId": "df0c828d-cb6a-44cc-82c5-6b7f165f3792"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d3acb78e-2be0-44a2-9ad4-10b547373395", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374792600481, "endTime": 652374895759010}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c7015c20-2d26-4e45-8e57-0f39a92f2f92", "logId": "14ccc63b-6df4-4d1f-8636-e9710db966b7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a35a3715-c11c-44f7-bc68-1e8798345be0", "name": "hvigorfile, resolving /Users/<USER>/github/Ling<PERSON>ia/examples/harmony/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374772059562}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a42181e4-42cc-4e28-ad09-92a62f28a824", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374791686178}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "df0c828d-cb6a-44cc-82c5-6b7f165f3792", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374772053503, "endTime": 652374792570226}, "additional": {"logType": "info", "children": [], "durationId": "0126805b-1401-43d8-9365-9d83b4b0322a", "parent": "14f7a1a4-45fb-42d3-9d0d-ccf41ee3cddd"}}, {"head": {"id": "e4f41cfb-3498-4ae0-a7de-70245644071e", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374792632181}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad1f4d0f-65e6-4c87-9adc-6cb062754e94", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374808150619}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9489b2a-bd03-4d84-aae6-be2244896075", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374808304634}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e6b77a2-55f2-4b7f-bdae-378aaf80907c", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374808622168}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8436942a-ed71-406a-90c3-749c2610fe3c", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374809778045}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9f66e2c-77d2-4043-bfa3-3ffafa432d77", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374813177479}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d164fc3-4348-4644-91a6-0cf6e3605a4a", "name": "not found resModel json file in : /Users/<USER>/github/LingXia/examples/harmony/entry/src/ohosTest/module.json5", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374815323660}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfec9584-2109-49ed-8ea1-96a283d67f13", "name": "not found resModel json file in : /Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/ohosTest/module.json5", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374822331107}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08960eaa-1519-4e8b-bbfc-da05992462ae", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374836206107}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "826c03a8-314a-48e1-b51a-bbde9d3188fa", "name": "Sdk init in 50 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374876915574}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "574f8976-4fb9-4ac0-bc75-4fd9c984ddca", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374877080830}, "additional": {"time": {"year": 2025, "month": 7, "day": 31, "hour": 21, "minute": 36}, "markType": "other"}}, {"head": {"id": "7f8924ff-9065-41d7-b97c-d7b6180f060f", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374877100650}, "additional": {"time": {"year": 2025, "month": 7, "day": 31, "hour": 21, "minute": 36}, "markType": "other"}}, {"head": {"id": "3612f873-633e-40c6-bb2c-32c04bb9d738", "name": "Project task initialization takes 18 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374895386167}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a8da227-b3f1-4fb5-ba6a-501fba0fab4d", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374895584565}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eec1aa6b-db20-4dba-99b3-86a500423100", "name": "hvigorfile, no custom plugins were found in /Users/<USER>/github/LingXia/examples/harmony/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374895660462}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d75771d1-29ff-439c-8fe0-1e60b41aeb75", "name": "hvigorfile, resolve finished /Users/<USER>/github/Ling<PERSON>ia/examples/harmony/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374895712795}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14ccc63b-6df4-4d1f-8636-e9710db966b7", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374792600481, "endTime": 652374895759010}, "additional": {"logType": "info", "children": [], "durationId": "d3acb78e-2be0-44a2-9ad4-10b547373395", "parent": "14f7a1a4-45fb-42d3-9d0d-ccf41ee3cddd"}}, {"head": {"id": "14f7a1a4-45fb-42d3-9d0d-ccf41ee3cddd", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374772052161, "endTime": 652374895774532}, "additional": {"logType": "info", "children": ["df0c828d-cb6a-44cc-82c5-6b7f165f3792", "14ccc63b-6df4-4d1f-8636-e9710db966b7"], "durationId": "c7015c20-2d26-4e45-8e57-0f39a92f2f92", "parent": "45b956db-16d5-4b94-bfcb-c91c6a149d5d"}}, {"head": {"id": "8c1a1f3a-d60a-4d83-9028-18c3da182336", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374896288110, "endTime": 652374896306143}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "84195608-62a2-4d5c-a74a-5aad0c6db1cb", "logId": "f27b7969-d0af-4d77-8e3b-a498da301f39"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f27b7969-d0af-4d77-8e3b-a498da301f39", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374896288110, "endTime": 652374896306143}, "additional": {"logType": "info", "children": [], "durationId": "8c1a1f3a-d60a-4d83-9028-18c3da182336", "parent": "45b956db-16d5-4b94-bfcb-c91c6a149d5d"}}, {"head": {"id": "45b956db-16d5-4b94-bfcb-c91c6a149d5d", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374769750416, "endTime": 652374896330385}, "additional": {"logType": "info", "children": ["b9416a42-a542-4520-ba68-7993f7c0903e", "14f7a1a4-45fb-42d3-9d0d-ccf41ee3cddd", "f27b7969-d0af-4d77-8e3b-a498da301f39"], "durationId": "84195608-62a2-4d5c-a74a-5aad0c6db1cb", "parent": "c838c4d3-bca5-413a-97d2-4b2ce78bf567"}}, {"head": {"id": "3a01468b-464c-490f-8692-bd6e0f1e3b42", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374896846955, "endTime": 652374921890227}, "additional": {"children": ["120e65eb-93f8-4900-ac90-d63483e4ee33", "61b3a97f-e95d-405b-96ab-4d7b9ffbd308", "a07a990b-9830-4e26-a6ab-c8ebb1349800"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "521314f4-3f09-4bb5-95fb-82e44bd05a5b", "logId": "f19394c1-b2f1-4773-95a2-a966d2758f38"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "120e65eb-93f8-4900-ac90-d63483e4ee33", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374898958574, "endTime": 652374898974670}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3a01468b-464c-490f-8692-bd6e0f1e3b42", "logId": "4774a87c-4b74-4e6a-ad3b-acf6114786d3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4774a87c-4b74-4e6a-ad3b-acf6114786d3", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374898958574, "endTime": 652374898974670}, "additional": {"logType": "info", "children": [], "durationId": "120e65eb-93f8-4900-ac90-d63483e4ee33", "parent": "f19394c1-b2f1-4773-95a2-a966d2758f38"}}, {"head": {"id": "61b3a97f-e95d-405b-96ab-4d7b9ffbd308", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374900204809, "endTime": 652374920695119}, "additional": {"children": ["fba2a965-7fc5-473f-a975-4947b3633e09", "2b2266e7-d3e1-4f69-8f08-e20c895b90a8"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3a01468b-464c-490f-8692-bd6e0f1e3b42", "logId": "963b7015-57bb-46c8-9c6f-5f13ba930bef"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fba2a965-7fc5-473f-a975-4947b3633e09", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374900205877, "endTime": 652374907311738}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "61b3a97f-e95d-405b-96ab-4d7b9ffbd308", "logId": "bbb893a0-09e2-4317-ac60-79c31d16f500"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2b2266e7-d3e1-4f69-8f08-e20c895b90a8", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374907332436, "endTime": 652374920677667}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "61b3a97f-e95d-405b-96ab-4d7b9ffbd308", "logId": "42c5a601-a571-483e-a27f-ce67db285683"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6a77baad-6684-4919-9ba9-760870be53df", "name": "hvigorfile, resolving /Users/<USER>/github/Ling<PERSON>ia/examples/harmony/entry/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374900211607}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "266e0b2a-3a52-4425-ba0d-dc1fac9bc832", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374907163136}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbb893a0-09e2-4317-ac60-79c31d16f500", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374900205877, "endTime": 652374907311738}, "additional": {"logType": "info", "children": [], "durationId": "fba2a965-7fc5-473f-a975-4947b3633e09", "parent": "963b7015-57bb-46c8-9c6f-5f13ba930bef"}}, {"head": {"id": "09c40cc3-2202-4d0c-aa45-31d23f161584", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374907348282}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab6c3d84-ad8f-4ce0-9fe7-746a9edb4bee", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374914652445}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b9b41dc-2696-4285-b1ff-ac0530bfc1e4", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374914828123}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a29f73b2-d52f-4b31-9d4b-4fcc5002b6fa", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374915364535}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64cf7751-4c5f-45c8-b660-c359698ea2ea", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374915627192}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15852b76-260d-4123-9d38-0b9dcca670bf", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374915709087}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "054a1aab-9a27-4dca-a26a-0fd01d005f38", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374915768446}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18e91ee2-9688-480d-8f51-6678f17fd5ec", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374915868597}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b1dc280-f7fb-4d56-bd84-81012a514d05", "name": "Module entry task initialization takes 2 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374920188646}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de722230-38bd-4fda-a3ee-63627decaec7", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374920395047}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7dd62e08-45c7-4649-8ff4-c7a899cdb92e", "name": "hvigorfile, no custom plugins were found in /Users/<USER>/github/LingXia/examples/harmony/entry/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374920475219}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb1bef4c-72a8-4e9f-a515-96a73da8c2d8", "name": "hvigorfile, resolve finished /Users/<USER>/github/LingXia/examples/harmony/entry/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374920526906}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42c5a601-a571-483e-a27f-ce67db285683", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374907332436, "endTime": 652374920677667}, "additional": {"logType": "info", "children": [], "durationId": "2b2266e7-d3e1-4f69-8f08-e20c895b90a8", "parent": "963b7015-57bb-46c8-9c6f-5f13ba930bef"}}, {"head": {"id": "963b7015-57bb-46c8-9c6f-5f13ba930bef", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374900204809, "endTime": 652374920695119}, "additional": {"logType": "info", "children": ["bbb893a0-09e2-4317-ac60-79c31d16f500", "42c5a601-a571-483e-a27f-ce67db285683"], "durationId": "61b3a97f-e95d-405b-96ab-4d7b9ffbd308", "parent": "f19394c1-b2f1-4773-95a2-a966d2758f38"}}, {"head": {"id": "a07a990b-9830-4e26-a6ab-c8ebb1349800", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374921855774, "endTime": 652374921870470}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3a01468b-464c-490f-8692-bd6e0f1e3b42", "logId": "f800a44f-d1d3-462d-8e45-047bd4ce09fc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f800a44f-d1d3-462d-8e45-047bd4ce09fc", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374921855774, "endTime": 652374921870470}, "additional": {"logType": "info", "children": [], "durationId": "a07a990b-9830-4e26-a6ab-c8ebb1349800", "parent": "f19394c1-b2f1-4773-95a2-a966d2758f38"}}, {"head": {"id": "f19394c1-b2f1-4773-95a2-a966d2758f38", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374896846955, "endTime": 652374921890227}, "additional": {"logType": "info", "children": ["4774a87c-4b74-4e6a-ad3b-acf6114786d3", "963b7015-57bb-46c8-9c6f-5f13ba930bef", "f800a44f-d1d3-462d-8e45-047bd4ce09fc"], "durationId": "3a01468b-464c-490f-8692-bd6e0f1e3b42", "parent": "ca5d8e05-e539-43f1-92ba-9b88df816a8e"}}, {"head": {"id": "8ed66f8e-7001-49f5-9a73-92bb04a4e8c4", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374922343187, "endTime": 652374944270922}, "additional": {"children": ["37bd2c1e-8bda-468a-8a49-7093dbe90ea7", "f4902374-4420-4197-8644-8cfb8e7941b0", "6e66d789-a6af-4483-bb88-031207aee6ea"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "521314f4-3f09-4bb5-95fb-82e44bd05a5b", "logId": "d973370a-0208-4f60-8090-0f10f7c8b7e9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "37bd2c1e-8bda-468a-8a49-7093dbe90ea7", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374925151110, "endTime": 652374925171684}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8ed66f8e-7001-49f5-9a73-92bb04a4e8c4", "logId": "6b1c9819-9068-4158-b0c2-efd4862966a7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6b1c9819-9068-4158-b0c2-efd4862966a7", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374925151110, "endTime": 652374925171684}, "additional": {"logType": "info", "children": [], "durationId": "37bd2c1e-8bda-468a-8a49-7093dbe90ea7", "parent": "d973370a-0208-4f60-8090-0f10f7c8b7e9"}}, {"head": {"id": "f4902374-4420-4197-8644-8cfb8e7941b0", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374926898985, "endTime": 652374943009294}, "additional": {"children": ["7237756a-6227-48b3-bb51-c15c5b34da60", "4abb82fb-7647-4e4f-897e-b64fcffed218"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8ed66f8e-7001-49f5-9a73-92bb04a4e8c4", "logId": "d9486bf4-c8c5-419f-a743-325527657479"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7237756a-6227-48b3-bb51-c15c5b34da60", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374926901115, "endTime": 652374933574980}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f4902374-4420-4197-8644-8cfb8e7941b0", "logId": "6a88e867-bf9c-47d4-8865-85b06571d99f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4abb82fb-7647-4e4f-897e-b64fcffed218", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374933593559, "endTime": 652374942994964}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "f4902374-4420-4197-8644-8cfb8e7941b0", "logId": "c62579e5-1ef5-4ce5-8c09-2f42367a4bf6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "07a3076d-c18b-4f9d-ae4c-a68bd01c2b99", "name": "hvigorfile, resolving /Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374926912444}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8f9fc29-9b61-4c5b-ae66-33fff574165a", "name": "hvigorfile, require result:  { default: { system: [Function: harTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374933424621}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a88e867-bf9c-47d4-8865-85b06571d99f", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374926901115, "endTime": 652374933574980}, "additional": {"logType": "info", "children": [], "durationId": "7237756a-6227-48b3-bb51-c15c5b34da60", "parent": "d9486bf4-c8c5-419f-a743-325527657479"}}, {"head": {"id": "227fe63b-c75e-4363-85fa-8cf24533cc74", "name": "hvigorfile, binding system plugins [Function: harTasks]", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374933611839}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7639a89-52b3-4c3d-bd30-9530d036d3b9", "name": "Start initialize module-target build option map, moduleName=lingxia, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374939337343}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e3ac2f8-bf78-403c-8154-a082a94dc215", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374939531447}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0407a8c8-e98d-4f8e-95a7-42cb4ba4b4f9", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374939815329}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23aa4dc9-12a6-4331-ab77-4cf4b79fdd9b", "name": "Module 'lingxia' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374939980343}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0862acd0-e7f8-4b5d-bb22-4cbbef02646f", "name": "Module 'lingxia' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374940047982}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29cebd7c-3045-4660-b1e0-a34f0087dd48", "name": "End initialize module-target build option map, moduleName=lingxia", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374940096817}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "776b7e67-cbd2-435f-8dc2-6bdcd54cbce9", "name": "Module 'lingxia' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374940158799}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06b52b9b-f520-4461-a02e-ac5ad2847d1a", "name": "Module lingxia task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374942653633}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfa052cd-c175-47eb-86b3-352d8600a843", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374942829191}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77519a2d-069d-4ed8-91e2-5e14ae1f6157", "name": "hvigorfile, no custom plugins were found in /Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374942900955}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c892024e-34fa-4586-a63f-c0d13ed23cba", "name": "hvigorfile, resolve finished /Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374942950330}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c62579e5-1ef5-4ce5-8c09-2f42367a4bf6", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374933593559, "endTime": 652374942994964}, "additional": {"logType": "info", "children": [], "durationId": "4abb82fb-7647-4e4f-897e-b64fcffed218", "parent": "d9486bf4-c8c5-419f-a743-325527657479"}}, {"head": {"id": "d9486bf4-c8c5-419f-a743-325527657479", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374926898985, "endTime": 652374943009294}, "additional": {"logType": "info", "children": ["6a88e867-bf9c-47d4-8865-85b06571d99f", "c62579e5-1ef5-4ce5-8c09-2f42367a4bf6"], "durationId": "f4902374-4420-4197-8644-8cfb8e7941b0", "parent": "d973370a-0208-4f60-8090-0f10f7c8b7e9"}}, {"head": {"id": "6e66d789-a6af-4483-bb88-031207aee6ea", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374944230761, "endTime": 652374944248013}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8ed66f8e-7001-49f5-9a73-92bb04a4e8c4", "logId": "fefa2187-72e3-440e-9bcb-0320179ce06c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fefa2187-72e3-440e-9bcb-0320179ce06c", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374944230761, "endTime": 652374944248013}, "additional": {"logType": "info", "children": [], "durationId": "6e66d789-a6af-4483-bb88-031207aee6ea", "parent": "d973370a-0208-4f60-8090-0f10f7c8b7e9"}}, {"head": {"id": "d973370a-0208-4f60-8090-0f10f7c8b7e9", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374922343187, "endTime": 652374944270922}, "additional": {"logType": "info", "children": ["6b1c9819-9068-4158-b0c2-efd4862966a7", "d9486bf4-c8c5-419f-a743-325527657479", "fefa2187-72e3-440e-9bcb-0320179ce06c"], "durationId": "8ed66f8e-7001-49f5-9a73-92bb04a4e8c4", "parent": "ca5d8e05-e539-43f1-92ba-9b88df816a8e"}}, {"head": {"id": "ca5d8e05-e539-43f1-92ba-9b88df816a8e", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374896348553, "endTime": 652374944289313}, "additional": {"logType": "info", "children": ["f19394c1-b2f1-4773-95a2-a966d2758f38", "d973370a-0208-4f60-8090-0f10f7c8b7e9"], "durationId": "521314f4-3f09-4bb5-95fb-82e44bd05a5b", "parent": "c838c4d3-bca5-413a-97d2-4b2ce78bf567"}}, {"head": {"id": "0da422d3-04b6-4673-9c6e-d1b5434ba2fd", "name": "watch files: [\n  '/Users/<USER>/github/LingXia/examples/harmony/hvigorfile.ts',\n  '/Users/<USER>/github/LingXia/examples/harmony/entry/hvigorfile.ts',\n  '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/hvigorfile.ts',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor-ohos-plugin/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor-ohos-plugin/src/plugin/factory/plugin-factory.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/log4js.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/debug/src/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/debug/src/node.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/debug/src/common.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/ms/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/rfdc/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/configuration.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/layouts.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/date-format/lib/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/levels.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/clustering.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/LoggingEvent.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/flatted/cjs/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/adapters.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/console.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/stdout.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/stderr.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/logLevelFilter.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/categoryFilter.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/noLogFilter.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/file.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/lib/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/lib/RollingFileWriteStream.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/fs/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/universalify/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/graceful-fs/graceful-fs.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/graceful-fs/polyfills.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/graceful-fs/legacy-streams.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/graceful-fs/clone.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/copy-sync/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/copy-sync/copy-sync.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/mkdirs/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/mkdirs/mkdirs.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/mkdirs/win32.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/mkdirs/mkdirs-sync.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/util/utimes.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/util/stat.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/copy/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/copy/copy.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/path-exists/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/empty/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/remove/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/remove/rimraf.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/file.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/link.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/symlink.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/symlink-paths.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/symlink-type.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/json/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/json/jsonfile.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/jsonfile/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/json/output-json.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/json/output-json-sync.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/move-sync/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/move-sync/move-sync.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/move/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/move/move.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/output/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/lib/now.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/lib/fileNameFormatter.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/lib/fileNameParser.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/lib/moveAndMaybeCompressFile.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/lib/RollingFileStream.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/lib/DateRollingFileStream.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/dateFile.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/fileSync.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/tcp.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/categories.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/logger.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/connect-logger.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/recording.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/src/common/util/local-file-writer.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/fs/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/universalify/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/copy/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/copy/copy.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/mkdirs/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/mkdirs/make-dir.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/mkdirs/utils.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/path-exists/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/util/utimes.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/util/stat.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/copy/copy-sync.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/empty/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/remove/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/ensure/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/ensure/file.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/ensure/link.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/ensure/symlink.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/ensure/symlink-paths.js',\n  ... 1869 more items\n]", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374977829518}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94ee336d-820c-41ac-8bb1-80ff807dced9", "name": "hvigorfile, resolve hvigorfile dependencies in 127 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375070517791}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "deccab5d-3582-42e2-b50a-1a0ecea448d3", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374944302580, "endTime": 652375070688593}, "additional": {"logType": "info", "children": [], "durationId": "5e5c808f-4954-41d6-a4fb-e4c6f230d65a", "parent": "c838c4d3-bca5-413a-97d2-4b2ce78bf567"}}, {"head": {"id": "5d15af59-c1be-4215-98f3-9af66b8f314b", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375071529376, "endTime": 652375071906251}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e291434f-97db-4fb1-bc2a-3d878420c382", "logId": "ad52673d-96f5-487a-86fd-6aa2c2e2f308"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5d226048-8494-4fe3-b9de-71779df21e35", "name": "project has submodules:entry,lingxia", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375071564980}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e044049a-878b-4e3c-82b1-bea2a0a7645e", "name": "module:lingxia no need to execute packageHap", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375071811897}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad52673d-96f5-487a-86fd-6aa2c2e2f308", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375071529376, "endTime": 652375071906251}, "additional": {"logType": "info", "children": [], "durationId": "5d15af59-c1be-4215-98f3-9af66b8f314b", "parent": "c838c4d3-bca5-413a-97d2-4b2ce78bf567"}}, {"head": {"id": "0a32b741-547d-4d1c-b198-1af56ad1e9dd", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375073648811}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "79ef0a37-b5a8-44a4-91e5-7b732a918875", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375080678003}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0ecc46a-3db6-4c6f-a44f-d1cbda6fdec9", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375071921377, "endTime": 652375081174789}, "additional": {"logType": "info", "children": [], "durationId": "528ff122-9e42-4272-813d-64661f608e38", "parent": "c838c4d3-bca5-413a-97d2-4b2ce78bf567"}}, {"head": {"id": "4067d4ff-dd42-4333-a5f9-598154782b22", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375081229936}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d74a1f1-850b-4841-83ef-6bb2970781e6", "name": "Module harmony Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375083228441}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0838924-89fd-4a1f-abc2-62bbf495df14", "name": "Module harmony's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375083350537}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "338f63b2-8484-49b1-a07a-e8194a5f7aaa", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375083802852}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30ea9e06-1bfa-4cc9-956e-f80350d572b7", "name": "Please confirm the symbolic link is valid:  /Users/<USER>/github/LingXia/examples/harmony/entry/oh_modules/lingxia", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375084239374}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "a16a24bd-d8b2-412d-81d5-4346b64ba1d4", "name": "The current module 'entry' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375084881606}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "9e9afa86-447d-481a-b863-023210741e5c", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375085472315}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb0a99a7-470c-4b88-8294-023d702a7f2c", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375085576178}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e03c13b-828c-4c53-bd55-e3ce845eb30e", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375088234288}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4c74566-c2b1-4f4c-8ab6-bf8af37c11be", "name": "Module ling<PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375089228166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e18ce48-e3fd-4b01-8102-27e2808925b9", "name": "Module lingxia's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375089353334}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f7344b0-625a-4f7b-b22a-4dd04bf9c763", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375081208413, "endTime": 652375092429262}, "additional": {"logType": "info", "children": [], "durationId": "4df08cf6-69c2-482b-8a6e-25c93d187ac5", "parent": "c838c4d3-bca5-413a-97d2-4b2ce78bf567"}}, {"head": {"id": "a44a1f38-661b-4738-9aa9-30e4540f905a", "name": "Configuration phase cost:331 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375092524366}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3364e24-05e3-41ea-bf14-b4a23c75fe44", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375092471804, "endTime": 652375092687409}, "additional": {"logType": "info", "children": [], "durationId": "afe9f004-7551-4190-9c4e-544fdb39fdcd", "parent": "c838c4d3-bca5-413a-97d2-4b2ce78bf567"}}, {"head": {"id": "c838c4d3-bca5-413a-97d2-4b2ce78bf567", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374757785463, "endTime": 652375092707109}, "additional": {"logType": "info", "children": ["92445368-1c98-4ce9-8953-edcf19b076c2", "c9b65947-0daa-4a7f-a704-9c8b4bf3fc09", "45b956db-16d5-4b94-bfcb-c91c6a149d5d", "ca5d8e05-e539-43f1-92ba-9b88df816a8e", "deccab5d-3582-42e2-b50a-1a0ecea448d3", "a0ecc46a-3db6-4c6f-a44f-d1cbda6fdec9", "7f7344b0-625a-4f7b-b22a-4dd04bf9c763", "e3364e24-05e3-41ea-bf14-b4a23c75fe44", "ad52673d-96f5-487a-86fd-6aa2c2e2f308"], "durationId": "e291434f-97db-4fb1-bc2a-3d878420c382", "parent": "eec6d833-aeb1-4303-a576-166ccda6e884"}}, {"head": {"id": "b9301185-9f10-45b9-b257-2d4d03cb4431", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375096050548, "endTime": 652375096073581}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8f8b4948-8abf-4a04-8711-615634f660de", "logId": "f40da1ac-20ab-4d8b-9360-b780a0150f33"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f40da1ac-20ab-4d8b-9360-b780a0150f33", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375096050548, "endTime": 652375096073581}, "additional": {"logType": "info", "children": [], "durationId": "b9301185-9f10-45b9-b257-2d4d03cb4431", "parent": "eec6d833-aeb1-4303-a576-166ccda6e884"}}, {"head": {"id": "bf588560-8c6a-4770-b579-5bcd7c70f4d2", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375092767184, "endTime": 652375096094125}, "additional": {"logType": "info", "children": [], "durationId": "a46acf3a-cb85-4bc0-b785-efc1812ebc0e", "parent": "eec6d833-aeb1-4303-a576-166ccda6e884"}}, {"head": {"id": "df8237ca-3c74-4174-beed-da56fbd6f1c2", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375096125570, "endTime": 652375096156642}, "additional": {"logType": "info", "children": [], "durationId": "09abd05c-4f20-41a7-8094-29471d9de18c", "parent": "eec6d833-aeb1-4303-a576-166ccda6e884"}}, {"head": {"id": "eec6d833-aeb1-4303-a576-166ccda6e884", "name": "init", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374717306842, "endTime": 652375096163377}, "additional": {"logType": "info", "children": ["8598d041-2b02-42a1-b8c0-2c45af248394", "c838c4d3-bca5-413a-97d2-4b2ce78bf567", "bf588560-8c6a-4770-b579-5bcd7c70f4d2", "df8237ca-3c74-4174-beed-da56fbd6f1c2", "c96f4246-076f-4525-8a59-2e6183b90af1", "415ebb1d-744a-4626-ac41-0b602a9d808f", "f40da1ac-20ab-4d8b-9360-b780a0150f33"], "durationId": "8f8b4948-8abf-4a04-8711-615634f660de"}}, {"head": {"id": "765fa292-aa61-4a33-9cb4-b301e160b5d5", "name": "Configuration task cost before running: 387 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375096678964}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea68f122-4e75-4621-87ab-305840d7366b", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375100863449, "endTime": 652375108650356}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": ["The input file '/Users/<USER>/github/LingXia/examples/harmony/build-profile.json5' has been changed."], "detailId": "d5c687b6-b3c5-45bc-8f8b-558b94551a4f", "logId": "8f6d8682-bda3-4c81-85ae-4c61ea2cfc46"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d5c687b6-b3c5-45bc-8f8b-558b94551a4f", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375098512713}, "additional": {"logType": "detail", "children": [], "durationId": "ea68f122-4e75-4621-87ab-305840d7366b"}}, {"head": {"id": "8c3681de-de45-4ad3-8d09-b3cd6da1551e", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375098816463}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d280fcc1-f2b4-4404-99cf-eb1be001f37f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375098944329}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2aa6e2f1-c7fa-4e8d-b02e-4aeb419d7fee", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375100886090}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b864f7fc-457f-48f5-89d6-9d26040b1672", "name": "entry:default@PreBuild is not up-to-date, since the input file '/Users/<USER>/github/LingXia/examples/harmony/build-profile.json5' has been changed.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375103079131}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a133e2ad-5b39-4982-9e86-24a1250dc71a", "name": "Incremental task entry:default@PreBuild pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375103238933}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13b7e2b6-3b85-4638-b6cf-6ad5199de26d", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375103380444}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d767c0de-3486-47d4-ae66-476af8ee7652", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375103466889}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f02257b-f96d-4bbe-898a-399ff0095e35", "name": "current product is not Atomic service.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375107712824}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46ee234d-5944-49cd-a7bd-8a1942ccaa46", "name": "Use tool [darwin: JAVA_HOME, CLASSPATH]\n [\n  {\n    JAVA_HOME: '/Applications/Android Studio.app/Contents/jbr/Contents/Home'\n  },\n  { CLASSPATH: undefined }\n]", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375107959909}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b7fb0b2-a8fb-4183-ad7c-efcbd3df7967", "name": "Use tool [darwin: NODE_HOME]\n [ { NODE_HOME: undefined } ]", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375108154579}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91498ec4-a088-472e-82d0-8e532edd78f7", "name": "entry : default@PreBuild cost memory 0.35736083984375", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375108402415}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1bb9db6b-f710-4eb1-a5a1-89ffec1ab1bb", "name": "runTaskFromQueue task cost before running: 399 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375108539878}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f6d8682-bda3-4c81-85ae-4c61ea2cfc46", "name": "Finished :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375100863449, "endTime": 652375108650356, "totalTime": 7648395}, "additional": {"logType": "info", "children": [], "durationId": "ea68f122-4e75-4621-87ab-305840d7366b"}}, {"head": {"id": "a48de501-31a5-47fa-b799-89805df7cf7d", "name": "entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375112358579, "endTime": 652375113806122}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "02436cc5-d163-4b8b-b137-02abdb4d1d9d", "logId": "8e7900e9-f8be-4033-bace-2d75e4251498"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "02436cc5-d163-4b8b-b137-02abdb4d1d9d", "name": "create entry:default@CreateModuleInfo task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375110992014}, "additional": {"logType": "detail", "children": [], "durationId": "a48de501-31a5-47fa-b799-89805df7cf7d"}}, {"head": {"id": "85fbc6d2-86cd-4939-b08d-2544b07c0716", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375111418083}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3357e30-fe71-4a4f-b96c-380a0f985a9e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375111571601}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "816e2322-5d0e-442e-811d-ea7be23f2a5b", "name": "Executing task :entry:default@CreateModuleInfo", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375112373712}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e06e418-93f0-4d18-852a-e7104bcc646c", "name": "Task 'entry:default@CreateModuleInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375112973371}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea4a4185-e6c0-46ff-b604-85260ddb2339", "name": "entry : default@CreateModuleInfo cost memory 0.05258941650390625", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375113584929}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8928a53-04b2-4c6d-abcc-55057aa25fa2", "name": "runTaskFromQueue task cost before running: 404 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375113718917}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e7900e9-f8be-4033-bace-2d75e4251498", "name": "Finished :entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375112358579, "endTime": 652375113806122, "totalTime": 1339331}, "additional": {"logType": "info", "children": [], "durationId": "a48de501-31a5-47fa-b799-89805df7cf7d"}}, {"head": {"id": "5b47ee4e-863f-40f2-9e23-cfb899a6785f", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375119423530, "endTime": 652375125867928}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "93cb1036-ed2e-418b-8511-596440b1052a", "logId": "59080617-06c2-4606-aa41-51df184ffafe"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "93cb1036-ed2e-418b-8511-596440b1052a", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375115740949}, "additional": {"logType": "detail", "children": [], "durationId": "5b47ee4e-863f-40f2-9e23-cfb899a6785f"}}, {"head": {"id": "f12be73f-d3c7-4d1e-a3fe-817168727895", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375116080162}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "faa88e40-65fc-4b6b-9189-43d373004fff", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375116240760}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8dce9cca-b0b7-4446-b261-c8ad2f08aaef", "name": "Please confirm the symbolic link is valid:  /Users/<USER>/github/LingXia/examples/harmony/entry/oh_modules/lingxia", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375117323501}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "17cd6ff0-91ec-4308-8e80-ee5374b9998f", "name": "The current module 'entry' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375117929688}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "af8a8079-9094-4d82-9043-938215f38d21", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375119439953}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15a6d241-3b1f-4cc1-80e7-c7ebf5bd88a9", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375120296279}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f4ffa1a-c6fc-4a75-b7e5-4b1beb791563", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375125248479}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "666eb10f-6cfa-4b98-9b5c-5e5ec866a64d", "name": "entry : default@GenerateMetadata cost memory 0.08867645263671875", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375125572078}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59080617-06c2-4606-aa41-51df184ffafe", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375119423530, "endTime": 652375125867928}, "additional": {"logType": "info", "children": [], "durationId": "5b47ee4e-863f-40f2-9e23-cfb899a6785f"}}, {"head": {"id": "2d507dd2-6529-4cba-8ee4-7ce39dd0573e", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375128665074, "endTime": 652375129263443}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "3257495f-11c5-43a4-9866-5a05d8d4800b", "logId": "c9e658e8-f840-4d18-a5e1-9446fc5148db"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3257495f-11c5-43a4-9866-5a05d8d4800b", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375127741150}, "additional": {"logType": "detail", "children": [], "durationId": "2d507dd2-6529-4cba-8ee4-7ce39dd0573e"}}, {"head": {"id": "e15aed97-ded2-4c10-92b8-a5219df88923", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375128277899}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f06f4ccb-a15e-439e-953e-053f65c1f17c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375128443231}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1fb25786-be21-4f7f-a30a-ba1d48478bd2", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375128676694}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "983c6743-5e2d-428b-b084-e5734890157f", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375128824520}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57579616-013f-4290-b6e7-669dc68b7440", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375128906478}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eec64e33-233e-4f71-9de1-7ba88fc95000", "name": "entry : default@ConfigureCmake cost memory 0.03914642333984375", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375129054592}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60ec2fb5-348d-4655-8088-9a925ad66a96", "name": "runTaskFromQueue task cost before running: 419 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375129173659}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9e658e8-f840-4d18-a5e1-9446fc5148db", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375128665074, "endTime": 652375129263443, "totalTime": 475893}, "additional": {"logType": "info", "children": [], "durationId": "2d507dd2-6529-4cba-8ee4-7ce39dd0573e"}}, {"head": {"id": "9c84a5fc-798e-4520-b8be-473f95f9f613", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375132769738, "endTime": 652375141293795}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": ["The input file '/Users/<USER>/github/LingXia/examples/harmony/build-profile.json5' has been changed."], "detailId": "0520ded0-60a7-4722-bb6e-fb5a9114bcc8", "logId": "356c4a8d-9673-401f-8678-8f973838aa43"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0520ded0-60a7-4722-bb6e-fb5a9114bcc8", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375131545784}, "additional": {"logType": "detail", "children": [], "durationId": "9c84a5fc-798e-4520-b8be-473f95f9f613"}}, {"head": {"id": "9f61fcdd-c9fb-4f75-9100-fc256c992bdb", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375131919903}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70810367-1412-4184-a613-22a2b28668e7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375132074553}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe727d86-79d1-423f-8442-0e89571883a7", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375132793281}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c1b6963-95c7-431a-96ef-5478ff33aa23", "name": "entry:default@MergeProfile is not up-to-date, since the input file '/Users/<USER>/github/LingXia/examples/harmony/build-profile.json5' has been changed.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375133400512}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98e31aa5-301f-41a3-a0dc-5b4e6c9566e5", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375133526187}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bcb2a040-62f0-4fd6-861f-b6a49b04d3d2", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375133657144}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88c3ae80-1e90-4ed7-8475-897fd8840982", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375133735299}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7cd458ba-2933-4a38-870a-c890f711d5cb", "name": "Change app api release type with 'Release'", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375133911055}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "996c7eca-7861-4fc8-9059-c4a13a0ebe54", "name": "Change app compile API version with '*********'", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375134042747}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7caf762c-6711-499d-b5bc-b304e6654e99", "name": "Change app target API version with '50005017'", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375134116498}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73d258e0-bcda-4e75-8468-e04ff9d887ea", "name": "Change app minimum API version with '50005017'", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375134195770}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f0334ab-6404-4fe2-8a16-b4348cc6c622", "name": "Use cli appEnvironment", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375134264060}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db12a244-c60b-4ce3-86ae-602d023926e3", "name": "entry : default@MergeProfile cost memory -3.608978271484375", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375140994841}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0659691-c518-4cf0-a46a-51f3fee45c11", "name": "runTaskFromQueue task cost before running: 431 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375141206156}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "356c4a8d-9673-401f-8678-8f973838aa43", "name": "Finished :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375132769738, "endTime": 652375141293795, "totalTime": 8397930}, "additional": {"logType": "info", "children": [], "durationId": "9c84a5fc-798e-4520-b8be-473f95f9f613"}}, {"head": {"id": "2e762f9e-b89f-422c-bb67-799db01e1561", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375143830655, "endTime": 652375146092710}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The input file '/Users/<USER>/github/LingXia/examples/harmony/build-profile.json5' has been changed."], "detailId": "2781ad44-046d-49ec-a64c-b62d1e1876ac", "logId": "e764768b-f651-443f-ae39-7367d63d779b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2781ad44-046d-49ec-a64c-b62d1e1876ac", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375142922216}, "additional": {"logType": "detail", "children": [], "durationId": "2e762f9e-b89f-422c-bb67-799db01e1561"}}, {"head": {"id": "973dadb2-b718-4745-a992-1b0c1b14ff7d", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375143166504}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90008e80-93b9-4dd5-beeb-0b3d26ed8aec", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375143255351}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2bfd9e1-0659-44c3-a850-42acd51e2fab", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375143842081}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd005776-ddbe-4ab4-9e5f-125cfac9eb2f", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375144447487}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd39f8ab-9c07-45ec-8336-036a68ab5bab", "name": "entry:default@CreateBuildProfile is not up-to-date, since the input file '/Users/<USER>/github/LingXia/examples/harmony/build-profile.json5' has been changed.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375144820306}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "663aeae6-7a02-4769-8615-fd8ac8e58dbe", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375144940043}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8868d9bb-5f13-4df7-9269-fcb1e69fb531", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375145049086}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "acf4f088-2145-4f9d-b64e-e0e450bb3cea", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375145115137}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0a7f7c8-41ed-424d-961d-bcb926063d98", "name": "entry : default@CreateBuildProfile cost memory 0.13452911376953125", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375145902402}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d903ea1-951f-44d1-bdf5-a83e4efc58c8", "name": "runTaskFromQueue task cost before running: 436 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375146034469}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e764768b-f651-443f-ae39-7367d63d779b", "name": "Finished :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375143830655, "endTime": 652375146092710, "totalTime": 2178113}, "additional": {"logType": "info", "children": [], "durationId": "2e762f9e-b89f-422c-bb67-799db01e1561"}}, {"head": {"id": "f9dd92b8-aaeb-4000-a42c-47d66e3d0358", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375148184252, "endTime": 652375148620065}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "08fc1183-e532-4e68-9695-cce232577dce", "logId": "fbb1a6af-337b-497b-9b40-9575a5e741d6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "08fc1183-e532-4e68-9695-cce232577dce", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375147290936}, "additional": {"logType": "detail", "children": [], "durationId": "f9dd92b8-aaeb-4000-a42c-47d66e3d0358"}}, {"head": {"id": "2d3fc4aa-d4da-49af-9c3a-af5108f94270", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375147506542}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9f9d5f9-4d30-4f4a-961c-790c4860c8d3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375147579744}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82302217-f8cb-430b-a479-4ea9dda8ff6e", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375148195593}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2056fe29-b7d9-4dac-9632-3c5527c6a364", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375148336879}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3cd6ae64-de8a-4dcc-91a1-421e39a03b53", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375148396273}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60565cdf-267d-4cb5-8502-779a6e9e0402", "name": "entry : default@PreCheckSyscap cost memory 0.039459228515625", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375148477722}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb9a74b3-3175-4368-baa8-19ce027ce874", "name": "runTaskFromQueue task cost before running: 439 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375148560444}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fbb1a6af-337b-497b-9b40-9575a5e741d6", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375148184252, "endTime": 652375148620065, "totalTime": 354672}, "additional": {"logType": "info", "children": [], "durationId": "f9dd92b8-aaeb-4000-a42c-47d66e3d0358"}}, {"head": {"id": "fced6171-2ba4-4e3e-8283-192b8a0ef1c1", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375154528781, "endTime": 652375156938930}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "f6e8fe79-6e3c-4107-aab9-fc997ef653bd", "logId": "bf1d0d81-0db9-43cb-b0d3-55ff0edc43e8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f6e8fe79-6e3c-4107-aab9-fc997ef653bd", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375149914245}, "additional": {"logType": "detail", "children": [], "durationId": "fced6171-2ba4-4e3e-8283-192b8a0ef1c1"}}, {"head": {"id": "0f0b809c-8f34-43c0-a97b-d07be7383147", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375150160785}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c01bca0f-c526-4bd1-8ede-1b8b406f80c8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375150255254}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24a70ca7-8133-42bc-972a-94481e8676f1", "name": "Please confirm the symbolic link is valid:  /Users/<USER>/github/LingXia/examples/harmony/entry/oh_modules/lingxia", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375151331124}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "8f348c31-4538-4517-b0db-860864851366", "name": "The current module 'entry' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375151951756}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "8ed6af81-c5d5-4cbf-b55c-2eca39ea4e2b", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375154547861}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "279cc608-eecc-4f33-8f4b-6340ee130762", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375154936713}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2677dd07-f15f-4abd-a319-47430d0a34bc", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375156568565}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ec1c0ce-8ff3-497a-a5ff-9335bf8d64b6", "name": "entry : default@GeneratePkgContextInfo cost memory 0.0667724609375", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375156800986}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf1d0d81-0db9-43cb-b0d3-55ff0edc43e8", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375154528781, "endTime": 652375156938930}, "additional": {"logType": "info", "children": [], "durationId": "fced6171-2ba4-4e3e-8283-192b8a0ef1c1"}}, {"head": {"id": "1013d8e5-9ee9-48ee-9805-986d7faef1aa", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375160644186, "endTime": 652375164278346}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file '/Users/<USER>/github/LingXia/examples/harmony/build/cache/default/integrated_hsp/integratedHspCache.json' does not exist."], "detailId": "11381e85-f6e3-4762-90ae-415e523410aa", "logId": "d24070b9-ab45-4976-b1ce-2bbb5188b6ee"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "11381e85-f6e3-4762-90ae-415e523410aa", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375158701488}, "additional": {"logType": "detail", "children": [], "durationId": "1013d8e5-9ee9-48ee-9805-986d7faef1aa"}}, {"head": {"id": "29be89f3-f48b-40fc-9f79-48b289863e9c", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375159024507}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd338c0e-8df0-4294-904a-a678e957691e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375159168045}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "274834bb-8e08-4b91-bbb2-10317ca5bb91", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375160663477}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0383ed2f-c0d9-4554-988a-2bf22ef9de82", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file '/Users/<USER>/github/LingXia/examples/harmony/build/cache/default/integrated_hsp/integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375162095671}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f3753c1-b9c1-447f-9383-d100a7da1d7e", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375162293669}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6e5704c-23fe-47f6-8db4-0b25c44ea126", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375162420923}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ce9a435-c590-4f9a-a560-cdf3b3323991", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375162510344}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "190d9c54-58b9-4d5f-a554-995717542369", "name": "entry : default@ProcessIntegratedHsp cost memory 0.11145782470703125", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375163889178}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8af70f8-d369-44b5-9e5d-b6d288bc3fa8", "name": "runTaskFromQueue task cost before running: 454 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375164151044}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d24070b9-ab45-4976-b1ce-2bbb5188b6ee", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375160644186, "endTime": 652375164278346, "totalTime": 3457852}, "additional": {"logType": "info", "children": [], "durationId": "1013d8e5-9ee9-48ee-9805-986d7faef1aa"}}, {"head": {"id": "bb0cfb3a-c77d-454a-b3a8-076f75ed17b5", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375167968689, "endTime": 652375168534301}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "9c0cd847-9a70-401b-9f76-56539d9e912a", "logId": "f4081544-9dc1-4347-840e-a4def32d76e6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9c0cd847-9a70-401b-9f76-56539d9e912a", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375166810291}, "additional": {"logType": "detail", "children": [], "durationId": "bb0cfb3a-c77d-454a-b3a8-076f75ed17b5"}}, {"head": {"id": "9c5f8810-22f7-4d27-be1a-22587481e8fc", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375167108860}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86edde27-c039-4ccc-9974-c08309c7a19b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375167240630}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35d9b1de-d7f5-45d5-91e4-d9e47f2fbf2f", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375167980096}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09b3ca39-0d88-435e-a66c-4c993ec997e2", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375168127217}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4385eb5-097d-4d2c-859e-ce6fc10dd7d5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375168207836}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8166132-f286-450c-bf9d-9cf26a4f2744", "name": "entry : default@BuildNativeWithCmake cost memory 0.04022979736328125", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375168314861}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f267a5f-cf62-419d-b2d8-dc4f41f6c64e", "name": "runTaskFromQueue task cost before running: 459 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375168455465}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4081544-9dc1-4347-840e-a4def32d76e6", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375167968689, "endTime": 652375168534301, "totalTime": 452208}, "additional": {"logType": "info", "children": [], "durationId": "bb0cfb3a-c77d-454a-b3a8-076f75ed17b5"}}, {"head": {"id": "428f798d-e9a5-476d-b159-7ee5a7fd7f96", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375170998084, "endTime": 652375174892277}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The input file '/Users/<USER>/github/LingXia/examples/harmony/build-profile.json5' has been changed."], "detailId": "da155fd3-eedb-45df-aa42-284aa8ef06c5", "logId": "acaaa3d7-afb5-40b1-891c-e4ede0179489"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "da155fd3-eedb-45df-aa42-284aa8ef06c5", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375169997098}, "additional": {"logType": "detail", "children": [], "durationId": "428f798d-e9a5-476d-b159-7ee5a7fd7f96"}}, {"head": {"id": "5b2c9248-2fc6-4bf5-a20c-d083ab45c0a0", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375170230000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dfcf61be-8db1-4f4f-8709-ff67d58313a5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375170337693}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0fb6814d-e7e9-40c2-bd72-00f0d797d1a2", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375171007760}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0872f882-db3a-428c-969d-f41a52386872", "name": "entry:default@MakePackInfo is not up-to-date, since the input file '/Users/<USER>/github/LingXia/examples/harmony/build-profile.json5' has been changed.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375172275681}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf7be9db-7a7a-4de7-abe9-44291f7bf0b3", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375172424766}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f47b02d4-1a87-41f5-93ae-0cd71bd70245", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375172546656}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d039c89-3573-46b5-99d5-74a8da9091cc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375172635337}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d085d9bc-c925-4dc4-be3c-a4530612f975", "name": "Module Pack Info:  {\n  summary: {\n    app: { bundleName: 'a***e', bundleType: 'app', version: [Object] },\n    modules: [ [Object] ]\n  },\n  packages: [\n    {\n      deviceType: [Array],\n      moduleType: 'entry',\n      deliveryWithInstall: true,\n      name: 'entry-default'\n    }\n  ]\n}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375173796107}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d84145a-1252-45fb-a235-ccf181deb001", "name": "entry : default@MakePackInfo cost memory 0.18241119384765625", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375174561592}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69680686-f258-418e-9ee8-c33caa984790", "name": "runTaskFromQueue task cost before running: 465 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375174799337}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "acaaa3d7-afb5-40b1-891c-e4ede0179489", "name": "Finished :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375170998084, "endTime": 652375174892277, "totalTime": 3752146}, "additional": {"logType": "info", "children": [], "durationId": "428f798d-e9a5-476d-b159-7ee5a7fd7f96"}}, {"head": {"id": "c72ee38d-b749-418e-b8c3-e4fb4360d085", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375178760829, "endTime": 652375182700757}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/syscap/default/rpcid.sc' does not exist."], "detailId": "16cd3c50-445d-4856-b362-e50170077373", "logId": "c1358fe3-8562-4523-af06-da2fb512ff33"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "16cd3c50-445d-4856-b362-e50170077373", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375177101809}, "additional": {"logType": "detail", "children": [], "durationId": "c72ee38d-b749-418e-b8c3-e4fb4360d085"}}, {"head": {"id": "6b73bdb5-fe47-48d2-8133-7d8811a8f3f3", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375177389886}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4acbcfe-f5f7-4a20-b724-98ede536fd06", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375177521737}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13fb74a6-d21f-44c7-8335-535e8798a284", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375178776069}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0f7fa19-a962-4d9d-82dd-1759e29e84f5", "name": "File: '/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375178923472}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d96414b1-25cb-42c4-9e6e-0324e42f47d0", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375179146287}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7aaeadaf-9bfd-49dc-8e5e-e82f8a595ae9", "name": "entry:default@SyscapTransform is not up-to-date, since the output file '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/syscap/default/rpcid.sc' does not exist.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375181939972}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d39103d3-a16a-4512-a925-bef18dcf4be1", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375182134660}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2970f94b-8dc9-425d-82ca-5e1ac87b35d6", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375182270282}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d2e7fd0-67af-4ab7-ac4e-b80e4b5189a2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375182351341}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5b9499d-c552-4512-859d-7d125224c063", "name": "entry : default@SyscapTransform cost memory 0.12877655029296875", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375182488393}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d1b761e-55a6-43bf-b706-1f85778f3828", "name": "runTaskFromQueue task cost before running: 473 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375182612739}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1358fe3-8562-4523-af06-da2fb512ff33", "name": "Finished :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375178760829, "endTime": 652375182700757, "totalTime": 3836956}, "additional": {"logType": "info", "children": [], "durationId": "c72ee38d-b749-418e-b8c3-e4fb4360d085"}}, {"head": {"id": "f15a888a-121a-47c4-8103-07d65b411a49", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375185743479, "endTime": 652375373117427}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": ["The input file '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/merge_profile/default/module.json' has been changed."], "detailId": "7539b454-8b5f-42cb-9401-d3718154a54a", "logId": "c4a2bfb2-0bde-480d-9ab7-5dd38159440d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7539b454-8b5f-42cb-9401-d3718154a54a", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375184346004}, "additional": {"logType": "detail", "children": [], "durationId": "f15a888a-121a-47c4-8103-07d65b411a49"}}, {"head": {"id": "dee7e055-be06-446d-9b13-4edf7462d1fa", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375184641322}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90994dd9-aef2-40e2-9559-651870e51c57", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375184742501}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7c11e26-3809-40da-87f4-6d9976eed04c", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375185757433}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f17fe25b-8240-442b-9e2b-0014e033c1b8", "name": "entry:default@ProcessProfile is not up-to-date, since the input file '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/merge_profile/default/module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375186556475}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b723002-a19c-4577-9fe1-406cbf892160", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375186712023}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab3d3a80-1d23-4d51-bb9d-df649081d1cf", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375186867680}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eaf6cd96-6e21-47d5-9aad-a91a16180d28", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375186949527}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2cb21267-d5dc-4af7-95c4-bbfeb41fc431", "name": "********", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375370797470}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e73eae4-b821-491b-9a2d-b19699ed06ef", "name": "entry : default@ProcessProfile cost memory 0.3220367431640625", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375372820330}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "888b035d-2863-47c5-b9ee-c3b34a23d483", "name": "runTaskFromQueue task cost before running: 663 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375373017610}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4a2bfb2-0bde-480d-9ab7-5dd38159440d", "name": "Finished :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375185743479, "endTime": 652375373117427, "totalTime": 187243163}, "additional": {"logType": "info", "children": [], "durationId": "f15a888a-121a-47c4-8103-07d65b411a49"}}, {"head": {"id": "a79e5919-61b4-4715-a137-073dd4c26f39", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375377613936, "endTime": 652375382134856}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "20b469ca-d80e-4894-8ed7-870cd1335b5c", "logId": "1af352c2-6ef1-488c-b56c-2df20472957e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "20b469ca-d80e-4894-8ed7-870cd1335b5c", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375375580484}, "additional": {"logType": "detail", "children": [], "durationId": "a79e5919-61b4-4715-a137-073dd4c26f39"}}, {"head": {"id": "5f735bfd-760c-44d4-8088-260ff71d67c3", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375375935859}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b4f5566-f231-4d56-acf2-075482b1ff41", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375376116876}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "427e6f1c-dd51-427b-b38d-083ec5f0e524", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375377634204}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c987b377-d6e0-4229-ae63-5c5e03039511", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375381809002}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b86aef9-3ea5-40c9-b493-39d371f17c20", "name": "entry : default@ProcessRouterMap cost memory 0.18065643310546875", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375382021908}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1af352c2-6ef1-488c-b56c-2df20472957e", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375377613936, "endTime": 652375382134856}, "additional": {"logType": "info", "children": [], "durationId": "a79e5919-61b4-4715-a137-073dd4c26f39"}}, {"head": {"id": "4fa1a014-4e22-43fb-9bdb-88d5f637aee9", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375385981215, "endTime": 652375387584401}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "b79a41aa-65ac-45ec-b207-e956fe0347c0", "logId": "8650617d-a2a4-4652-a736-73127e408f24"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b79a41aa-65ac-45ec-b207-e956fe0347c0", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375384910531}, "additional": {"logType": "detail", "children": [], "durationId": "4fa1a014-4e22-43fb-9bdb-88d5f637aee9"}}, {"head": {"id": "752349a7-7bcb-4804-a72a-53314805bd2f", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375385190152}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2226938-a606-4e04-a7b5-34ac36f6230f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375385301632}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c78d063f-0645-440e-ae7c-2d50be126a7d", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375385993892}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6ccd561-5a25-47b6-9b64-2f35af855103", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375386157026}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e03dfb6f-abef-480f-bfcc-147de7020bfe", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375386241826}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "208ce486-f417-454f-8337-29d6915c18db", "name": "entry : default@BuildNativeWithNinja cost memory 0.05525970458984375", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375387266117}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b44612fe-70b3-4af4-b1c6-e4dbd28ed8bb", "name": "runTaskFromQueue task cost before running: 678 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375387481324}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8650617d-a2a4-4652-a736-73127e408f24", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375385981215, "endTime": 652375387584401, "totalTime": 1444316}, "additional": {"logType": "info", "children": [], "durationId": "4fa1a014-4e22-43fb-9bdb-88d5f637aee9"}}, {"head": {"id": "b6285e4c-11f0-402b-9906-04275084005c", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375392783472, "endTime": 652375397571742}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "af944038-201a-4e97-bb15-ddb7ab773e95", "logId": "b4514bd3-7c5d-4088-a2b7-ee2c8624ca90"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "af944038-201a-4e97-bb15-ddb7ab773e95", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375389694456}, "additional": {"logType": "detail", "children": [], "durationId": "b6285e4c-11f0-402b-9906-04275084005c"}}, {"head": {"id": "e67787c5-3d01-4e78-8e64-03bfbfc12374", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375389936936}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23b08608-72cf-42f9-a6a5-896cd3f3b0eb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375390050933}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f4c8f70-ecd3-4668-bc39-a93026e9bf84", "name": "restool module names: entry,lingxia; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375390555241}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1fc0768-c4fa-4a5e-99b9-267c2634a18b", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375394868268}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2edd7478-1124-4088-8fa7-390758c50e3a", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375396398776}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea773346-5045-4d20-89c4-c9f651f380b7", "name": "entry : default@ProcessResource cost memory 0.1389007568359375", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375396568923}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4514bd3-7c5d-4088-a2b7-ee2c8624ca90", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375392783472, "endTime": 652375397571742}, "additional": {"logType": "info", "children": [], "durationId": "b6285e4c-11f0-402b-9906-04275084005c"}}, {"head": {"id": "c0e2a0dd-9a9a-4668-b57f-37e6e7c5cc9e", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375403455639, "endTime": 652375420177260}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "fa586d67-7d9a-443c-82ef-60878b66b93d", "logId": "cd5bcd92-1ae1-4006-8b91-faf53756d65a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fa586d67-7d9a-443c-82ef-60878b66b93d", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375400467767}, "additional": {"logType": "detail", "children": [], "durationId": "c0e2a0dd-9a9a-4668-b57f-37e6e7c5cc9e"}}, {"head": {"id": "3a888934-98e3-4349-9e71-cee3cca2610a", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375400739357}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0926e457-9bf1-42b5-8c31-53b3425a3b9a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375400854444}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "385d568e-add9-4c7e-9839-fba78acc68cb", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375403484361}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "030a5e2c-b8ae-46d1-ae68-f856f4984689", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 8 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375419914260}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c39fb88-3d85-489d-82d5-7fa42c131ca0", "name": "entry : default@GenerateLoaderJson cost memory -4.5631561279296875", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375420090246}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd5bcd92-1ae1-4006-8b91-faf53756d65a", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375403455639, "endTime": 652375420177260}, "additional": {"logType": "info", "children": [], "durationId": "c0e2a0dd-9a9a-4668-b57f-37e6e7c5cc9e"}}, {"head": {"id": "c2bea804-d20e-48cd-80a8-1e90836af253", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375428789562, "endTime": 652376600394613}, "additional": {"children": ["44551e4e-2fc8-4550-b437-f2bc14f79a76"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": ["The input file '/Users/<USER>/github/LingXia/examples/harmony/build-profile.json5' has been changed."], "detailId": "18fa9979-2ba1-47d2-89d8-1b977679567e", "logId": "a01937ad-a357-47fa-b64e-6a87d029cbdc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "18fa9979-2ba1-47d2-89d8-1b977679567e", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375427154636}, "additional": {"logType": "detail", "children": [], "durationId": "c2bea804-d20e-48cd-80a8-1e90836af253"}}, {"head": {"id": "65a345c0-a96d-46a5-b1e7-5cfefe5f0633", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375427544228}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "499c0ae4-14f8-4daf-8a2f-099550c823e4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375427728313}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e87a6aba-777a-45f2-a4a9-c292c958fd1f", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375428834004}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "310728b6-a8e5-4aab-838d-438b060c5ce6", "name": "entry:default@ProcessLibs is not up-to-date, since the input file '/Users/<USER>/github/LingXia/examples/harmony/build-profile.json5' has been changed.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375431443486}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53a1139f-bda1-48a7-b6dd-db911168e06f", "name": "Incremental task entry:default@ProcessLibs pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375431652900}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe5e2d5f-bfbe-42d7-bc99-960ac607c8cd", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375431797783}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef9f48df-0215-4ad6-89a0-86f2b0672ba6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375431889199}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a87ce4f-28aa-4e30-808d-140ae985cad4", "name": "default@ProcessLibs work[5] is submitted.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375436240935}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44551e4e-2fc8-4550-b437-f2bc14f79a76", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Worker6", "startTime": 652376579534743, "endTime": 652376599368964}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "c2bea804-d20e-48cd-80a8-1e90836af253", "logId": "1b686883-64a8-4e0c-9aae-9ce09fb19423"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fb0b0d1b-7757-4685-8651-c37d7a8e4652", "name": "default@ProcessLibs work[5] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375437316980}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d64370f9-55e7-423f-85ed-0436975ea7c6", "name": "A work dispatched to worker[7] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375437434750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a212e96-8cde-45e5-a86e-b33366f9ff38", "name": "Create  resident worker with id: 6.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375437533287}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b097c8d-8176-4d58-adb3-449482f08aae", "name": "default@ProcessLibs work[5] has been dispatched to worker[6].", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375440295800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5959b3a-c1b0-434e-a328-c7523f368933", "name": "default@ProcessLibs work[5] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375440481763}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31abf877-8e63-4545-9617-7ca24448fd19", "name": "entry : default@ProcessLibs cost memory 0.5371780395507812", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375440639871}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf11d8ca-07e4-481b-8dbd-e75c5e5c86fe", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375447608430, "endTime": 652375823148595}, "additional": {"children": ["747c17a0-01a9-4052-b0bc-451a013a70ea", "e1731915-6691-4200-a542-38374fde0735"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": ["The input file '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/process_profile/default/module.json' has been changed."], "detailId": "d68f9eff-3ef5-414a-8e74-50131473346c", "logId": "4090953b-5568-4aa4-945e-5c58779cf1c5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d68f9eff-3ef5-414a-8e74-50131473346c", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375442567008}, "additional": {"logType": "detail", "children": [], "durationId": "cf11d8ca-07e4-481b-8dbd-e75c5e5c86fe"}}, {"head": {"id": "7df3120c-6215-4d8a-ad9c-6ff3620ad3bc", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375443005993}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42f1b571-5553-409f-9554-908feac4bf4b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375443168238}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f679be11-f474-4f1e-b67c-308d1666d851", "name": "restool module names: entry,lingxia; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375444145209}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2284e7d-6ae9-4b0a-bfca-6a3ea803378a", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375447654835}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8582621c-bfe1-4b4b-b927-e0114449a6c8", "name": "entry:default@CompileResource is not up-to-date, since the input file '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/process_profile/default/module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375482183086}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "727904e1-4825-4b6b-8d33-44e86d6520bc", "name": "Incremental task entry:default@CompileResource pre-execution cost: 34 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375482337553}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "747c17a0-01a9-4052-b0bc-451a013a70ea", "name": "create intermediate resource category", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375483232135, "endTime": 652375508001147}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cf11d8ca-07e4-481b-8dbd-e75c5e5c86fe", "logId": "dd56a93c-2bfc-4eca-8abf-df8db90fbac5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dd56a93c-2bfc-4eca-8abf-df8db90fbac5", "name": "create intermediate resource category", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375483232135, "endTime": 652375508001147}, "additional": {"logType": "info", "children": [], "durationId": "747c17a0-01a9-4052-b0bc-451a013a70ea", "parent": "4090953b-5568-4aa4-945e-5c58779cf1c5"}}, {"head": {"id": "33b45c42-16f9-49ac-b1f8-e2644d4351c3", "name": "Use tool [/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/toolchains/restool]\n [\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/toolchains/restool',\n  '-l',\n  '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resConfig.json'\n]", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375508453183}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1731915-6691-4200-a542-38374fde0735", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375509223103, "endTime": 652375822100397}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cf11d8ca-07e4-481b-8dbd-e75c5e5c86fe", "logId": "26ff9c20-cfad-4562-bc86-13f805f80a2d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "89fe14a0-c0a7-4c5e-b9b7-e4e417de735e", "name": "current process  memoryUsage: {\n  rss: 307609600,\n  heapTotal: 161841152,\n  heapUsed: 127112584,\n  external: 3079468,\n  arrayBuffers: 1034311\n} os memoryUsage :11.993282318115234", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375510400037}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65bb9063-4b79-4e42-99c2-209bcd3b553e", "name": "Info: thread pool is started\n", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375670562584}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80d7a292-bfda-4209-a554-07fb0d82f58a", "name": "Info: GenericCompiler::CompileFiles\n", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375672004050}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b56efd4b-1ada-40fe-8013-84538be22713", "name": "Info: thread pool is started\n", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375672370460}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a886fd1-16c6-4f9e-a688-348f63c4f214", "name": "Info: thread pool is stopped\n", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375676276980}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84340f29-eb6f-4d2a-b2e3-9cc80a1274fb", "name": "Info: GenericCompiler::CompileFiles\nInfo: thread pool is started\n", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375679026940}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a93bb235-d848-4975-bfa1-c1876f948de4", "name": "Info: thread pool is stopped\n", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375681089261}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fb47c52-7117-46e1-94de-4522e6e80cf3", "name": "Info: GenericCompiler::CompileFiles\nInfo: thread pool is started\n", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375681467580}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ecb5e1d-8016-44e5-9a52-53facecce46f", "name": "Info: thread pool is stopped\n", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375682325223}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af7c60ea-f4fe-4bdd-9130-9a48267e9ba5", "name": "07-31 21:36:27.348 55974 8753003 E C01400/ImageTranscoderUtils: DecodeToRGBA IN\n07-31 21:36:27.349 55974 8753003 E C01400/ImageTranscoderUtils: ImageFilter IN /Users/<USER>/github/LingXia/examples/harmony/AppScope/resources/base/media/background.png\n", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375686004263}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88241ff4-5836-4cfd-b973-142a18131ee1", "name": "07-31 21:36:27.429 55974 8753003 E C01400/ImageTranscoderUtils: DecodeToRGBA IN\n07-31 21:36:27.429 55974 8753003 E C01400/ImageTranscoderUtils: ImageFilter IN /Users/<USER>/github/LingXia/examples/harmony/AppScope/resources/base/media/foreground.png\n", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375765510007}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3cf9582-937a-4407-941c-bf63200fb8cb", "name": "Info: can't scale media json file.\n", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375816730937}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c22c1554-aa15-4dc2-9af8-023833cc2ebf", "name": "Warning: /Users/<USER>/github/LingXia/examples/harmony/AppScope/resources/base/media/layered_image.json is not png format\n", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375816971017}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c9ae8aa-5725-4807-837c-cca4c12f4e04", "name": "Info: thread pool is stopped\n", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375817798625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "140f6212-5028-41bd-9e72-7c827b1d658d", "name": "Info: restool resources compile success.\n", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375817976895}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1721fb34-b15e-484c-8ed5-3dd276c0a0a5", "name": "astcenc customized so is not be opened when dlclose!\n", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375820046423}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26ff9c20-cfad-4562-bc86-13f805f80a2d", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375509223103, "endTime": 652375822100397}, "additional": {"logType": "info", "children": [], "durationId": "e1731915-6691-4200-a542-38374fde0735", "parent": "4090953b-5568-4aa4-945e-5c58779cf1c5"}}, {"head": {"id": "761a1397-524a-4763-99fd-4ffd45d02713", "name": "entry : default@CompileResource cost memory 0.846954345703125", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375822821982}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db07f4fc-3356-4ddc-9a6d-441531ea8f5d", "name": "runTaskFromQueue task cost before running: 1 s 113 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375823051386}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4090953b-5568-4aa4-945e-5c58779cf1c5", "name": "Finished :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375447608430, "endTime": 652375823148595, "totalTime": 375377117}, "additional": {"logType": "info", "children": ["dd56a93c-2bfc-4eca-8abf-df8db90fbac5", "26ff9c20-cfad-4562-bc86-13f805f80a2d"], "durationId": "cf11d8ca-07e4-481b-8dbd-e75c5e5c86fe"}}, {"head": {"id": "6d197769-7a73-49bf-bd08-bd9929cc52e1", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375830748055, "endTime": 652376957768114}, "additional": {"children": ["3ead26b2-0064-4050-81b1-246134aa78fa", "db985ac6-4d47-479a-b546-668b96198a3f"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "0b3a22db-6ac5-4b84-ad84-326a652f51ff", "logId": "eb675cd6-76b5-4c9c-b9ff-dc876ee7af9a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0b3a22db-6ac5-4b84-ad84-326a652f51ff", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375825684527}, "additional": {"logType": "detail", "children": [], "durationId": "6d197769-7a73-49bf-bd08-bd9929cc52e1"}}, {"head": {"id": "b0c406ae-856c-4b8e-a353-5e293abe2cf2", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375826033658}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a6353fb-166c-458e-9578-abed6525877a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375826171348}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f1ed979-d877-47c8-b412-ee1bb1373043", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375830773501}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af0179f8-3718-4528-b786-7a69cb7c356f", "name": "Please confirm the symbolic link is valid:  /Users/<USER>/github/LingXia/examples/harmony/entry/oh_modules/lingxia", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375835338789}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "b3b33fde-bb7c-4fbb-af93-f527672a24d4", "name": "The current module 'entry' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375836477971}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "e3e3533f-e3af-4730-bc2f-85a15c0a48c1", "name": "Please confirm the symbolic link is valid:  /Users/<USER>/github/LingXia/examples/harmony/entry/oh_modules/lingxia", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375846947511}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "b3681117-76a2-4f01-9e1c-466a4793eb47", "name": "The current module 'entry' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375847777863}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "c6f9d080-3c13-41e1-9314-2fe7a210eb5e", "name": "Please confirm the symbolic link is valid:  /Users/<USER>/github/LingXia/examples/harmony/entry/oh_modules/lingxia", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375850816719}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "1867d47b-39c7-42dd-8088-b895b22c5d60", "name": "The current module 'entry' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375851631666}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "5c8c582c-a0dd-4c6a-8588-423dc8ab54b1", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375855564508}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e33b87b2-3fba-4091-ba7c-9016ebbbdd97", "name": "Compile arkts with external api path: /Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/hms/ets", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375856715070}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc9b834d-2a75-47e1-beb4-bf0932d8d5ed", "name": "default@CompileArkTS work[6] is submitted.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375857395433}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ead26b2-0064-4050-81b1-246134aa78fa", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Worker4", "startTime": 652375858820431, "endTime": 652376957383443}, "additional": {"children": [], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "6d197769-7a73-49bf-bd08-bd9929cc52e1", "logId": "0399c257-a5b4-471c-bd84-c8e3d4ee2242"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c5ad7b53-26b2-4ee8-88c3-385822a24c10", "name": "default@CompileArkTS work[6] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375858331197}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aeb11cfe-0a4c-429f-80a8-822a63c28e50", "name": "A work dispatched to worker[7] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375858461419}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f69d1fc-d544-4ed0-b113-caeb0a6d5142", "name": "A work dispatched to worker[6] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375858536776}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "196aa08b-1a6a-436e-8009-83ea31cb1ff3", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375858629521}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5beec5e1-22d7-4e37-a332-2ca9ee97046a", "name": "default@CompileArkTS work[6] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375858836317}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6143cde-14f3-45b0-bc80-5f50d6e67957", "name": "default@CompileArkTS work[6] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375858963660}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "701c77d5-7afb-4e03-83ca-8abb63b5b0f7", "name": "CopyResources startTime: 652375859043079", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375859047916}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13cdd3a4-6dd4-4885-9bdb-57247dc66ef8", "name": "default@CompileArkTS work[7] is submitted.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375859172349}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db985ac6-4d47-479a-b546-668b96198a3f", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Worker5", "startTime": 652376846631666, "endTime": 652376867119747}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "6d197769-7a73-49bf-bd08-bd9929cc52e1", "logId": "18bb07ef-54f0-4c1d-868e-00351b95d86f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "16e71da2-5179-4bfc-a518-cc3f428b1b27", "name": "default@CompileArkTS work[7] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375860485667}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88425b26-f4d6-4772-8a66-3bbbe8f1ff52", "name": "A work dispatched to worker[7] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375860667463}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7be9718d-bf53-4f88-995a-3020e132c205", "name": "A work dispatched to worker[6] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375860826273}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63f0155d-3a88-4fc3-a837-a89c3a9525da", "name": "Create  resident worker with id: 5.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375860972926}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41eb3dbe-5400-49f8-abc8-6da312022126", "name": "default@CompileArkTS work[7] has been dispatched to worker[5].", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375862923865}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "116905ec-89f5-4ed0-ad48-55b5c104ff57", "name": "default@CompileArkTS work[7] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375863123968}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "110cdb93-453d-4a6d-b411-8df0c967bfe8", "name": "entry : default@CompileArkTS cost memory 1.7557449340820312", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375863421107}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e083fe56-e929-4fb8-bddf-7d78944ba7f8", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375871667703, "endTime": 652375877693081}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "8b212686-c56f-4c06-a22a-a1c89806d8ba", "logId": "a68d9ac2-79aa-4361-99ac-8eae82320fc0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8b212686-c56f-4c06-a22a-a1c89806d8ba", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375865895817}, "additional": {"logType": "detail", "children": [], "durationId": "e083fe56-e929-4fb8-bddf-7d78944ba7f8"}}, {"head": {"id": "27b2ddd9-5856-4cc7-a34a-a11b3c3ceee2", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375866319902}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a33b4ae9-b902-45aa-9d28-4a7f25dfd554", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375866527126}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e2a39bc-5292-4165-9b38-d4cf2ac9fb58", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375871693249}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8012f532-7397-48a7-969a-c59a5b1bb8ab", "name": "entry : default@BuildJS cost memory 0.1255950927734375", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375876927689}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "716168ef-5efb-4028-978c-6881d363de3b", "name": "runTaskFromQueue task cost before running: 1 s 167 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375877366776}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a68d9ac2-79aa-4361-99ac-8eae82320fc0", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375871667703, "endTime": 652375877693081, "totalTime": 5499721}, "additional": {"logType": "info", "children": [], "durationId": "e083fe56-e929-4fb8-bddf-7d78944ba7f8"}}, {"head": {"id": "23640cb3-3fbe-4b37-9b5e-cbf164f1c686", "name": "worker[6] has one work done.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376599749329}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52f3dbdc-c83c-473a-9034-969772748b74", "name": "default@ProcessLibs work[5] done.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376600028535}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b686883-64a8-4e0c-9aae-9ce09fb19423", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Worker6", "startTime": 652376579534743, "endTime": 652376599368964}, "additional": {"logType": "info", "children": [], "durationId": "44551e4e-2fc8-4550-b437-f2bc14f79a76", "parent": "a01937ad-a357-47fa-b64e-6a87d029cbdc"}}, {"head": {"id": "7705c789-17d3-4459-9a74-5ac32eef9b9a", "name": "A work dispatched to worker[6] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376600249852}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a01937ad-a357-47fa-b64e-6a87d029cbdc", "name": "Finished :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375428789562, "endTime": 652376600394613, "totalTime": 31801867}, "additional": {"logType": "info", "children": ["1b686883-64a8-4e0c-9aae-9ce09fb19423"], "durationId": "c2bea804-d20e-48cd-80a8-1e90836af253"}}, {"head": {"id": "7f573e58-1b39-47a3-acb4-4646bab896ae", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376607446810, "endTime": 652376610394070}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "1ae6ab55-32ff-4b16-8394-57b64d34f6e9", "logId": "fdc067da-fdf7-4f52-941a-f7cadb4a914a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1ae6ab55-32ff-4b16-8394-57b64d34f6e9", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376603463741}, "additional": {"logType": "detail", "children": [], "durationId": "7f573e58-1b39-47a3-acb4-4646bab896ae"}}, {"head": {"id": "8e8fbb2d-35c2-4cb1-a58c-728854247b89", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376603949092}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c38ec76-76cd-4f82-92c1-3072fcbd7d9a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376604185384}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7332cc50-e41f-43fe-8466-8010dddbde38", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376607544543}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ffffa5fb-d8f3-479e-b922-520c974b94e3", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376607916845}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43d4625b-0552-420b-b7be-cd79df379963", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376609901094}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "933e50d3-f020-42c0-9c32-b12a9c91588c", "name": "entry : default@DoNativeStrip cost memory 0.0856781005859375", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376610171390}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fdc067da-fdf7-4f52-941a-f7cadb4a914a", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376607446810, "endTime": 652376610394070}, "additional": {"logType": "info", "children": [], "durationId": "7f573e58-1b39-47a3-acb4-4646bab896ae"}}, {"head": {"id": "5bd3a099-8015-4011-854e-2039fd28d4f4", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376616286858, "endTime": 652376618190695}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "eeaf357d-2d88-4c5f-b49e-b98503ca9eb8", "logId": "57a21c27-351a-4d3f-8901-d35b375ccbc2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eeaf357d-2d88-4c5f-b49e-b98503ca9eb8", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376613006906}, "additional": {"logType": "detail", "children": [], "durationId": "5bd3a099-8015-4011-854e-2039fd28d4f4"}}, {"head": {"id": "27af7798-57bc-4073-a666-08c68092c4f9", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376613342330}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "653fb28f-c0cd-4b11-bf20-6e86cb335f2a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376613489029}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27c80a59-e4bd-48a2-9a03-edd2e9136550", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376616306247}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09acfbea-cd15-474f-a1c2-c4370fc77397", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376616677335}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "758ce4e1-a8e4-4863-a40f-cf53cf9224a9", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376617880477}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09796885-ba71-4340-a904-cf4f975bf1ad", "name": "entry : default@CacheNativeLibs cost memory 0.09278106689453125", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376618051000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57a21c27-351a-4d3f-8901-d35b375ccbc2", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376616286858, "endTime": 652376618190695}, "additional": {"logType": "info", "children": [], "durationId": "5bd3a099-8015-4011-854e-2039fd28d4f4"}}, {"head": {"id": "0bab4c39-4198-45af-b2f2-1870fc93852a", "name": "worker[5] has one work done.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376867452800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e62a204c-5e0a-4e85-a400-4937171bde7d", "name": "CopyResources is end, endTime: 652376867670613", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376867681319}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd409412-1d1a-42b0-a504-c98fcb023e08", "name": "default@CompileArkTS work[7] done.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376867843820}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18bb07ef-54f0-4c1d-868e-00351b95d86f", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Worker5", "startTime": 652376846631666, "endTime": 652376867119747}, "additional": {"logType": "info", "children": [], "durationId": "db985ac6-4d47-479a-b546-668b96198a3f", "parent": "eb675cd6-76b5-4c9c-b9ff-dc876ee7af9a"}}, {"head": {"id": "1572d6ce-08a1-4864-8a6a-8ad8ca567bf9", "name": "worker[4] has one work error.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376957156381}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d6f5962-5c51-4e16-b973-febf15ca31de", "name": "default@CompileArkTS work[6] failed.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376957498664}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0399c257-a5b4-471c-bd84-c8e3d4ee2242", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Worker4", "startTime": 652375858820431, "endTime": 652376957383443}, "additional": {"logType": "error", "children": [], "durationId": "3ead26b2-0064-4050-81b1-246134aa78fa", "parent": "eb675cd6-76b5-4c9c-b9ff-dc876ee7af9a"}}, {"head": {"id": "eb675cd6-76b5-4c9c-b9ff-dc876ee7af9a", "name": "Failed :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652375830748055, "endTime": 652376957768114}, "additional": {"logType": "error", "children": ["0399c257-a5b4-471c-bd84-c8e3d4ee2242", "18bb07ef-54f0-4c1d-868e-00351b95d86f"], "durationId": "6d197769-7a73-49bf-bd08-bd9929cc52e1"}}, {"head": {"id": "a067bf3f-6cc5-4078-ab50-ab8d83324bef", "name": "hvigor build process will be closed with an error.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376958081043}, "additional": {"logType": "debug", "children": [], "durationId": "6d197769-7a73-49bf-bd08-bd9929cc52e1"}}, {"head": {"id": "9e0f5632-bc56-4ae9-af70-6aee9eb4e93f", "name": "ERROR: stacktrace = Error: ENOENT: no such file or directory, stat '/Users/<USER>/github/LingXia/examples/harmony/entry/oh_modules/lingxia'\n\u001b[31m1 ERROR: \u001b[31mArkTS:ERROR File: /Users/<USER>/github/LingXia/examples/harmony/entry/src/main/ets/entryability/EntryAbility.ets:4:23\n Cannot find module 'lingxia' or its corresponding type declarations.\n\n\u001b[31m> More Info: https://developer.huawei.com/consumer/en/doc/harmonyos-faqs-V5/faqs-compiling-and-building-4-V5\n\n\u001b[39m\n\u001b[31m2 ERROR: \u001b[31mArkTS:ERROR File: /Users/<USER>/github/LingXia/examples/harmony/entry/src/main/ets/pages/Index.ets:1:40\n Cannot find module 'lingxia' or its corresponding type declarations.\n\n\u001b[31m> More Info: https://developer.huawei.com/consumer/en/doc/harmonyos-faqs-V5/faqs-compiling-and-building-4-V5\n\n\u001b[39m\n\u001b[31mCOMPILE RESULT:FAIL {ERROR:3}\u001b[39m\n    at runArkPack (/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor-ohos-plugin/node_modules/@ohos/hvigor-arkts-compose/dist/src/arkts-pack.js:1:5438)\nError: ENOENT: no such file or directory, stat '/Users/<USER>/github/LingXia/examples/harmony/entry/oh_modules/lingxia'\n    at Object.statSync (node:fs:1688:3)\n    at resolveOhModules (/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor-ohos-plugin/node_modules/@ohos/hvigor-arkts-compose/dist/src/plugins/node-resolve/resolveOhModules.js:1:1531)\n    at resolveProjectOhModules (/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor-ohos-plugin/node_modules/@ohos/hvigor-arkts-compose/dist/src/plugins/node-resolve/resolveOhModules.js:1:1050)\n    at resolveAllOhModules (/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor-ohos-plugin/node_modules/@ohos/hvigor-arkts-compose/dist/src/plugins/node-resolve/resolveOhModules.js:1:760)\n    at Object.buildStart (/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor-ohos-plugin/node_modules/@ohos/hvigor-arkts-compose/dist/src/plugins/node-resolve/index.js:1:9657)\n    at /Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor-ohos-plugin/node_modules/@ohos/hvigor-arkts-base/node_modules/rollup/dist/shared/rollup.js:24828:40\n    at async Promise.all (index 2)\n    at async PluginDriver.hookParallel (/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor-ohos-plugin/node_modules/@ohos/hvigor-arkts-base/node_modules/rollup/dist/shared/rollup.js:24756:9)\n    at async /Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor-ohos-plugin/node_modules/@ohos/hvigor-arkts-base/node_modules/rollup/dist/shared/rollup.js:26031:13\n    at async catchUnfinishedHookActions (/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor-ohos-plugin/node_modules/@ohos/hvigor-arkts-base/node_modules/rollup/dist/shared/rollup.js:25197:24)", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376959047035}, "additional": {"logType": "debug", "children": [], "durationId": "6d197769-7a73-49bf-bd08-bd9929cc52e1"}}, {"head": {"id": "84685754-6ebc-4e69-89df-5305eb47c95f", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376962857322, "endTime": 652376962928104}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "81de23fa-78ee-4b93-b81f-579354465e4f", "logId": "91f937a1-5ea9-43a7-bb50-d36ed0b4f42c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "91f937a1-5ea9-43a7-bb50-d36ed0b4f42c", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376962857322, "endTime": 652376962928104}, "additional": {"logType": "info", "children": [], "durationId": "84685754-6ebc-4e69-89df-5305eb47c95f"}}, {"head": {"id": "293cdebc-56a4-4a3a-84dc-216de55223f6", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652374710391694, "endTime": 652376963275176}, "additional": {"time": {"year": 2025, "month": 7, "day": 31, "hour": 21, "minute": 36}, "completeCommand": "{\"prop\":[],\"_\":[\"assembleHap\"]};assembleHap", "hvigorVersion": "5.17.2", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "failed"}}, {"head": {"id": "88951185-a936-42b9-87fa-9da80ae9be84", "name": "BUILD FAILED in 2 s 253 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376963330660}, "additional": {"logType": "error", "children": []}}, {"head": {"id": "46c2d047-1517-4257-a8da-6ec84b3007f1", "name": "Update task entry:default@PreBuild input file:/Users/<USER>/github/LingXia/examples/harmony/AppScope/app.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376963916144}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9455cb4-dab9-4e28-80df-cdfa275d7701", "name": "Update task entry:default@PreBuild input file:/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/module.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376964284574}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d574c7e-fa85-4974-98bd-2aba8b6d231d", "name": "Update task entry:default@PreBuild input file:/Users/<USER>/github/LingXia/examples/harmony/build-profile.json5 cache from map.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376964709746}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "302d3a04-4975-430f-83fb-0ef9478e8b44", "name": "Update task entry:default@PreBuild input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376964917020}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1105fa52-c9a7-4f24-9e04-6c290fe7b46f", "name": "Update task entry:default@PreBuild input file:/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources/base/profile/main_pages.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376965253189}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae5cccbd-e390-4cc8-ae02-0848aae0b87a", "name": "Update task entry:default@PreBuild input file:/Users/<USER>/github/LingXia/examples/harmony/hvigor/hvigor-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376965503221}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ace9b09-d531-4607-861f-e3cb465817be", "name": "Update task entry:default@PreBuild input file:/Users/<USER>/github/LingXia/examples/harmony/entry/oh-package.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376965854191}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75840e92-0c89-43d4-bf84-2d8e5e4e79fd", "name": "Update task entry:default@PreBuild input file:/Users/<USER>/github/LingXia/examples/harmony/oh-package.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376966204350}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40b8284d-3463-4abf-bd70-2da876cbfd57", "name": "Incremental task entry:default@PreBuild post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376966636504}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb7883a6-d71f-4130-9f5a-25a141ff9d16", "name": "Update task entry:default@CreateModuleInfo output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/generated/profile/default/ModuleInfo.ts cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376966793329}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9f7c109-4ff2-4195-aeff-a61bc938f8d2", "name": "Incremental task entry:default@CreateModuleInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376967086895}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d27a70aa-9d52-4d75-a9a7-277e197b9d5c", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376967194239}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "143cbd4a-bf2c-415d-8e16-ae06dd390079", "name": "Update task entry:default@MergeProfile input file:/Users/<USER>/github/LingXia/examples/harmony/AppScope/app.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376967315399}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c7387da-e17b-408d-805d-0f49402ee019", "name": "Update task entry:default@MergeProfile input file:/Users/<USER>/github/LingXia/examples/harmony/build-profile.json5 cache from map.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376967496776}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91731357-5433-453d-9bec-8469fd1e1913", "name": "Update task entry:default@MergeProfile input file:/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/module.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376967578307}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ff924bf-3c8f-4b2b-a0dd-f129af564a42", "name": "Update task entry:default@MergeProfile output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/merge_profile/default/module.json cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376967816585}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51f95f00-c9d7-4043-8069-19b8093c714f", "name": "Incremental task entry:default@MergeProfile post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376968044044}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01ca847f-c138-4774-a214-8faf8ba504a5", "name": "Update task entry:default@CreateBuildProfile input file:/Users/<USER>/github/LingXia/examples/harmony/AppScope/app.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376968176927}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fed3645d-7156-4742-b72a-c4e82ec9cea9", "name": "Update task entry:default@CreateBuildProfile input file:/Users/<USER>/github/LingXia/examples/harmony/build-profile.json5 cache from map.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376968477293}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21af05be-e466-4562-a609-1f9e0faa75d4", "name": "Update task entry:default@CreateBuildProfile output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/generated/profile/default/BuildProfile.ets cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376968636274}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03e2619c-efb5-425a-a88f-7edd34b64ab0", "name": "Incremental task entry:default@CreateBuildProfile post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376968862341}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "970e7847-c302-4938-b5f0-8aee998073b0", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376968967725}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b3a89c7-aeaa-46f6-9c1b-41640c7ac099", "name": "Update task entry:default@ProcessIntegratedHsp output file:/Users/<USER>/github/LingXia/examples/harmony/build/cache/default/integrated_hsp/integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376969478480}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab8c4637-1daf-49ea-98e9-75eb50c582eb", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376969702327}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15962433-7c8b-49d1-9067-c76ec938e175", "name": "Update task entry:default@MakePackInfo input file:/Users/<USER>/github/LingXia/examples/harmony/AppScope/app.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376969844208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1cad5b23-64b9-4281-a6ca-a2f7376fda74", "name": "Update task entry:default@MakePackInfo input file:/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/module.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376970016156}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "739568f4-1a64-4665-be78-b41521887994", "name": "Update task entry:default@MakePackInfo input file:/Users/<USER>/github/LingXia/examples/harmony/build-profile.json5 cache from map.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376970157216}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ac8b817-17b4-4fab-a2f1-19997bbaa8d0", "name": "Update task entry:default@MakePackInfo output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/outputs/default/pack.info cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376970633023}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b99f3e58-95a0-42e7-8b4b-27db127940a7", "name": "Incremental task entry:default@MakePackInfo post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376970847872}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24e61acb-a371-46af-96ff-a72e966af1c9", "name": "Update task entry:default@SyscapTransform input file:/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/toolchains/syscap_tool cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376970956169}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dace0acb-d935-485e-8ed0-2209e826b2a9", "name": "Update task entry:default@SyscapTransform input file:/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/device-define cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376971093579}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4635d09-9282-43b3-8b36-2062c7fb0042", "name": "Update task entry:default@SyscapTransform output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/syscap/default/rpcid.sc cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376971696477}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "063ddf0c-bd20-4a11-9c3f-f199281d5b88", "name": "Incremental task entry:default@SyscapTransform post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376971962934}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5a9eeb9-147a-417f-8be4-a6092845729f", "name": "Update task entry:default@ProcessProfile input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/merge_profile/default/module.json cache from map.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376972074260}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d3996bf-5f1e-4ff1-851b-79fd1dbd7bc7", "name": "Update task entry:default@ProcessProfile output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/process_profile/default/module.json cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376972155153}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db7ab825-7115-4ca0-a2aa-daece06355d6", "name": "Incremental task entry:default@ProcessProfile post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376972347779}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49d1f8b6-60dc-483e-9237-74cc538e03e1", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376972443280}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e0ab234-5355-474d-bd4c-6a7402dc683a", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376972518693}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "865d4459-41a2-41e7-8f9b-8243837688ca", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376972604974}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "615e2b9f-3703-4ae5-9452-c6935c2b832c", "name": "Update task entry:default@ProcessLibs input file:/Users/<USER>/github/LingXia/examples/harmony/entry/libs cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376972739770}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cce16035-8834-40ec-b40d-cf55f39a2be4", "name": "Update task entry:default@ProcessLibs input file:/Users/<USER>/github/LingXia/examples/harmony/build-profile.json5 cache from map.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376973166728}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb981bdf-4c7e-4b25-8f42-36317eb739ad", "name": "Update task entry:default@ProcessLibs input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376973262855}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b8fa2df-aa73-4b28-bd7a-d66564004f0c", "name": "Update task entry:default@ProcessLibs output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/libs/default cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376973420188}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d12ef7f7-f700-4f56-bc71-deab9a33859c", "name": "Incremental task entry:default@ProcessLibs post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376973966068}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93107231-265d-45c0-b862-123cbafa875a", "name": "Update task entry:default@CompileResource input file:/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376974802651}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c7ebfde-3d8c-4694-aacd-3a3be401cd6e", "name": "Update task entry:default@CompileResource input file:/Users/<USER>/github/LingXia/examples/harmony/AppScope/resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376978626388}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13bd4289-8450-4659-808e-c24eccd00199", "name": "Update task entry:default@CompileResource input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/process_profile/default/module.json cache from map.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376979538231}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd185a0f-ae24-4ad7-ba1d-a24949bfdbfa", "name": "Update task entry:default@CompileResource input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resConfig.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376979674945}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03555323-bf18-4e83-ac89-1ac2a1035016", "name": "Update task entry:default@CompileResource output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376979838010}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43dfdd81-270d-4c39-b7a2-e2484c296aaf", "name": "Update task entry:default@CompileResource output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/generated/r/default/ResourceTable.h cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376983284662}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d3306ef-9a53-4cb2-80e1-dd33b2e59de5", "name": "Update task entry:default@CompileResource output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/generated/r/default cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376983483800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "393139df-ed01-4371-9601-fa07bb579acf", "name": "Incremental task entry:default@CompileResource post-execution cost:10 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376983837886}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "445b3b53-6414-4f46-ad13-03a4ddb51ab5", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376985370434}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3f62ddb-aa68-4ef0-afa4-66e886362d79", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376986300759}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3cab09d8-cfb7-4009-9a3d-c759a3707bac", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376988361160}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1c2e7f6-34bc-4de2-8f2f-8088321a487f", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376988540344}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43897b69-8f2b-4a65-8734-ad8c977656b3", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/base/profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376988685539}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f206235-e71e-4818-b985-4af91fe6cf00", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376988967735}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f97773c-f250-4e41-9197-9ea9ae9edc4b", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default/pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376989470813}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e78a7947-9ee6-4e1e-b958-1a53f037b3b2", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/generated/profile/default/BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376989616634}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34c170fd-efe7-48da-9a08-fa6c721da95c", "name": "Update task entry:default@CompileArkTS output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader_out/default/ets cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376989781860}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86850f2e-36e6-4056-83e2-14655b438c90", "name": "Incremental task entry:default@CompileArkTS post-execution cost:7 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376990107888}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "003d9881-a2f4-4c13-b061-cf931731b647", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376991361038}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26c62327-9c31-4011-b859-3182bf70ee20", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376991898397}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "482959d0-0d90-442e-9d1f-39d713e44961", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376994914823}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd5297e0-770f-445a-90fd-28b95b32ad3d", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376996196557}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19e6011a-3666-46d8-87b4-7dbd97bb2713", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/base/profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376996626035}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbbd698e-1da2-45a6-9615-5b5292833f30", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default/pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376997165965}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bda2f2fb-4b6d-48ec-a492-9208c09c3bbc", "name": "Update task entry:default@BuildJS output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader_out/default/js cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376997544033}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75078287-d548-4500-9256-f31217bf7195", "name": "Incremental task entry:default@BuildJS post-execution cost:8 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376997894346}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f13ddcf4-7da0-4e19-bd59-c0594b0b6cb2", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376998027452}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c1668ee-382f-4ac2-8b02-8bd6be118f40", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652376998119936}, "additional": {"logType": "debug", "children": []}}], "workLog": []}