{"version": "2.0", "ppid": 50563, "events": [{"head": {"id": "337749dc-edb7-4442-a784-388a954655e9", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652377026795442}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "995b84bf-abc0-4d50-9448-885c22557c46", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652377029072372}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13efede9-15e7-43df-8696-899e2028afd0", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652377031042156}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ffc8639-341d-4abc-93d5-36071bb6051d", "name": "worker[6] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652377047488013}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6c2c0ef-a6e0-4452-8045-6b107879b1c8", "name": "worker[5] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652377049072976}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5e916cf-85f1-4c92-afcf-6832f11eff00", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424786344319}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "130efb25-7e1c-4c6c-b866-c4b346532396", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424790579928, "endTime": 652425052933583}, "additional": {"children": ["4bea9c69-fbc6-44d4-9cc7-a55fb18aea57", "4b23c430-5823-4e1d-a681-6255c7233545", "4c7e3744-62fc-44fd-bcbc-44c9bab4e481", "b2d69add-5333-4a88-bc2f-433399c1db4c", "e03b6b4b-3cdf-4f66-ace5-373fc8324bba", "6d027fe2-d22f-4483-9d83-01cf64aae9cb", "63ce2fc4-4f42-450b-b81d-4784caa046f5"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "c0fd1e5e-08ef-4bb6-913b-4d6f729e931a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4bea9c69-fbc6-44d4-9cc7-a55fb18aea57", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424790581154, "endTime": 652424802576876}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "130efb25-7e1c-4c6c-b866-c4b346532396", "logId": "a8e01456-c16f-4eb9-9818-c00be59557ec"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4b23c430-5823-4e1d-a681-6255c7233545", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424802596542, "endTime": 652425047469721}, "additional": {"children": ["4fd6312c-daec-4651-a269-84a2895e3b8e", "401cffae-ccb8-4ac4-b337-dc2e3fc8e628", "e773e0ec-66c8-4938-a48b-cb3f0c25c9a3", "b8c52fff-ae5c-406b-b3fc-e81ba8025007", "dbafb235-b16c-4b2d-b558-b7a61ac075c4", "3f86c9a9-92b6-4a82-87a2-45ec928fc8a9", "c72e1d5c-8306-4112-85ca-ba06c7329b0c", "b2a76fba-c8e5-4093-a05d-b62848efe3ec", "fd9fae33-9996-471c-bf31-069592f0cdf9"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "130efb25-7e1c-4c6c-b866-c4b346532396", "logId": "4428d4ac-2038-4618-a6f8-d9ef23ee464b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4c7e3744-62fc-44fd-bcbc-44c9bab4e481", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425047505788, "endTime": 652425052911876}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "130efb25-7e1c-4c6c-b866-c4b346532396", "logId": "a646ab29-fff3-4c5d-b9b4-af169487a0e1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b2d69add-5333-4a88-bc2f-433399c1db4c", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425052918754, "endTime": 652425052928387}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "130efb25-7e1c-4c6c-b866-c4b346532396", "logId": "c05c9cb9-abb7-4416-9ef1-da507c93fa56"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e03b6b4b-3cdf-4f66-ace5-373fc8324bba", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424792714406, "endTime": 652424792755430}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "130efb25-7e1c-4c6c-b866-c4b346532396", "logId": "9a0909e0-897b-4078-81aa-7160e38b1c23"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9a0909e0-897b-4078-81aa-7160e38b1c23", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424792714406, "endTime": 652424792755430}, "additional": {"logType": "info", "children": [], "durationId": "e03b6b4b-3cdf-4f66-ace5-373fc8324bba", "parent": "c0fd1e5e-08ef-4bb6-913b-4d6f729e931a"}}, {"head": {"id": "6d027fe2-d22f-4483-9d83-01cf64aae9cb", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424797268195, "endTime": 652424797285713}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "130efb25-7e1c-4c6c-b866-c4b346532396", "logId": "a6645bab-505b-472e-b3c8-3e19e19bb973"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a6645bab-505b-472e-b3c8-3e19e19bb973", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424797268195, "endTime": 652424797285713}, "additional": {"logType": "info", "children": [], "durationId": "6d027fe2-d22f-4483-9d83-01cf64aae9cb", "parent": "c0fd1e5e-08ef-4bb6-913b-4d6f729e931a"}}, {"head": {"id": "daabd7f4-3594-4564-8da3-833ef3f356a6", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424797339419}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ef4dcb4-a4d6-462a-9f88-1691106e79ce", "name": "Cache service initialization finished in 5 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424802447496}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8e01456-c16f-4eb9-9818-c00be59557ec", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424790581154, "endTime": 652424802576876}, "additional": {"logType": "info", "children": [], "durationId": "4bea9c69-fbc6-44d4-9cc7-a55fb18aea57", "parent": "c0fd1e5e-08ef-4bb6-913b-4d6f729e931a"}}, {"head": {"id": "4fd6312c-daec-4651-a269-84a2895e3b8e", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424806039402, "endTime": 652424806050857}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4b23c430-5823-4e1d-a681-6255c7233545", "logId": "08ca56e7-b619-438f-9846-770420a7fa2b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "401cffae-ccb8-4ac4-b337-dc2e3fc8e628", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424806070801, "endTime": 652424808962407}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4b23c430-5823-4e1d-a681-6255c7233545", "logId": "55a1dfcb-8cd5-4d0e-8d04-467157222ecd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e773e0ec-66c8-4938-a48b-cb3f0c25c9a3", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424808977706, "endTime": 652424887848530}, "additional": {"children": ["093f88d3-69b9-4752-9d6a-fbad02a61144", "8a4efe6c-ac7e-4854-a61f-d451ebfe29a2", "0b26c8ee-efa4-46df-8107-ee8e648bca0d"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4b23c430-5823-4e1d-a681-6255c7233545", "logId": "8ee3f680-f582-4f11-ae28-4b450b0738fa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b8c52fff-ae5c-406b-b3fc-e81ba8025007", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424887877791, "endTime": 652424939401153}, "additional": {"children": ["7a13561c-2f14-4456-8a24-5c277d61c77d", "6a2a16df-1804-4d4f-b819-1e00201c0bf4"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4b23c430-5823-4e1d-a681-6255c7233545", "logId": "1b973118-6fa7-4a69-a351-7c3f2bcb1d74"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dbafb235-b16c-4b2d-b558-b7a61ac075c4", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424939417143, "endTime": 652425033272529}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4b23c430-5823-4e1d-a681-6255c7233545", "logId": "aab77105-ea32-4e6a-91f7-4766e6df39af"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3f86c9a9-92b6-4a82-87a2-45ec928fc8a9", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425034225298, "endTime": 652425039041712}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4b23c430-5823-4e1d-a681-6255c7233545", "logId": "3b3a94dd-b667-4f4f-9ada-09e30704b910"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c72e1d5c-8306-4112-85ca-ba06c7329b0c", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425039061804, "endTime": 652425047237367}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4b23c430-5823-4e1d-a681-6255c7233545", "logId": "a55da1dc-c6c3-4e9c-b886-7c3339fc0490"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b2a76fba-c8e5-4093-a05d-b62848efe3ec", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425047271652, "endTime": 652425047447918}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4b23c430-5823-4e1d-a681-6255c7233545", "logId": "60f5cbd5-87e5-400b-bbef-d33eecb0af76"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "08ca56e7-b619-438f-9846-770420a7fa2b", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424806039402, "endTime": 652424806050857}, "additional": {"logType": "info", "children": [], "durationId": "4fd6312c-daec-4651-a269-84a2895e3b8e", "parent": "4428d4ac-2038-4618-a6f8-d9ef23ee464b"}}, {"head": {"id": "55a1dfcb-8cd5-4d0e-8d04-467157222ecd", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424806070801, "endTime": 652424808962407}, "additional": {"logType": "info", "children": [], "durationId": "401cffae-ccb8-4ac4-b337-dc2e3fc8e628", "parent": "4428d4ac-2038-4618-a6f8-d9ef23ee464b"}}, {"head": {"id": "093f88d3-69b9-4752-9d6a-fbad02a61144", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424809370201, "endTime": 652424809388350}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e773e0ec-66c8-4938-a48b-cb3f0c25c9a3", "logId": "df4f8c28-cdce-428d-b9af-ebe737c52b69"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "df4f8c28-cdce-428d-b9af-ebe737c52b69", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424809370201, "endTime": 652424809388350}, "additional": {"logType": "info", "children": [], "durationId": "093f88d3-69b9-4752-9d6a-fbad02a61144", "parent": "8ee3f680-f582-4f11-ae28-4b450b0738fa"}}, {"head": {"id": "8a4efe6c-ac7e-4854-a61f-d451ebfe29a2", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424810500032, "endTime": 652424887205359}, "additional": {"children": ["9ddccc76-90f2-4e40-a4cf-89326839b15c", "828ddf63-716e-4728-8082-8be0355713c7"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e773e0ec-66c8-4938-a48b-cb3f0c25c9a3", "logId": "50372377-8fe0-4df8-8daa-e455ee882590"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9ddccc76-90f2-4e40-a4cf-89326839b15c", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424810501195, "endTime": 652424816845919}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8a4efe6c-ac7e-4854-a61f-d451ebfe29a2", "logId": "a76a1564-69f8-4229-879f-3b69548bcdb0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "828ddf63-716e-4728-8082-8be0355713c7", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424816866145, "endTime": 652424887190559}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8a4efe6c-ac7e-4854-a61f-d451ebfe29a2", "logId": "aba2aae1-105a-47f4-8fda-ab886f7b5a90"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "096de000-61c2-47ab-b771-0bfb930cba3e", "name": "hvigorfile, resolving /Users/<USER>/github/Ling<PERSON>ia/examples/harmony/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424810507114}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12230592-4705-4c71-a062-398714a7ca2d", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424816710326}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a76a1564-69f8-4229-879f-3b69548bcdb0", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424810501195, "endTime": 652424816845919}, "additional": {"logType": "info", "children": [], "durationId": "9ddccc76-90f2-4e40-a4cf-89326839b15c", "parent": "50372377-8fe0-4df8-8daa-e455ee882590"}}, {"head": {"id": "9c26d8df-f439-4f6f-a0cc-3df75021f215", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424816880732}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d99cc67-f3ad-4b5a-bb39-0704bb66adf4", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424821813205}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5047285-081e-44de-adb4-38898317754b", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424821940326}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f7582ee-ee81-4d01-9ff4-a1837ca3ec53", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424822104448}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8225b5e8-287e-4a0e-850a-e91451292cd7", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424822202320}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65f0b821-90d8-4b76-92dd-87d5e5cb6b3b", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424823274822}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "be817a50-68fe-4e96-827e-2eeb5f19a081", "name": "not found resModel json file in : /Users/<USER>/github/LingXia/examples/harmony/entry/src/ohosTest/module.json5", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424823696634}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02a609b9-1820-4ad8-978e-b5a96b6da0b7", "name": "not found resModel json file in : /Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/ohosTest/module.json5", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424825577820}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37a7fe5f-8e03-4f43-9257-e0257799afbd", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424832814243}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f508a144-d79a-4cff-9762-dde024e81299", "name": "Sdk init in 43 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424870061724}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f16e945c-1d70-499a-9dde-e2026d4f67b6", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424870219225}, "additional": {"time": {"year": 2025, "month": 7, "day": 31, "hour": 21, "minute": 37}, "markType": "other"}}, {"head": {"id": "e63e11ad-1fa1-4885-bdaa-5a832fb5f886", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424870238543}, "additional": {"time": {"year": 2025, "month": 7, "day": 31, "hour": 21, "minute": 37}, "markType": "other"}}, {"head": {"id": "f0cc9dba-d512-4314-bb36-ecfac8e1b89d", "name": "Project task initialization takes 16 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424886820157}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "158b73d5-0ce4-4e06-9636-99a71cf5ca6b", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424886971289}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e6b8678-d24a-4345-9c7f-441914b9c1c1", "name": "hvigorfile, no custom plugins were found in /Users/<USER>/github/LingXia/examples/harmony/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424887066097}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7968b3e-3c07-43ba-ac97-5b0e9d3f2a12", "name": "hvigorfile, resolve finished /Users/<USER>/github/Ling<PERSON>ia/examples/harmony/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424887130574}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aba2aae1-105a-47f4-8fda-ab886f7b5a90", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424816866145, "endTime": 652424887190559}, "additional": {"logType": "info", "children": [], "durationId": "828ddf63-716e-4728-8082-8be0355713c7", "parent": "50372377-8fe0-4df8-8daa-e455ee882590"}}, {"head": {"id": "50372377-8fe0-4df8-8daa-e455ee882590", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424810500032, "endTime": 652424887205359}, "additional": {"logType": "info", "children": ["a76a1564-69f8-4229-879f-3b69548bcdb0", "aba2aae1-105a-47f4-8fda-ab886f7b5a90"], "durationId": "8a4efe6c-ac7e-4854-a61f-d451ebfe29a2", "parent": "8ee3f680-f582-4f11-ae28-4b450b0738fa"}}, {"head": {"id": "0b26c8ee-efa4-46df-8107-ee8e648bca0d", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424887810323, "endTime": 652424887829315}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e773e0ec-66c8-4938-a48b-cb3f0c25c9a3", "logId": "d83f0183-7521-4d42-b395-c9f423e04903"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d83f0183-7521-4d42-b395-c9f423e04903", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424887810323, "endTime": 652424887829315}, "additional": {"logType": "info", "children": [], "durationId": "0b26c8ee-efa4-46df-8107-ee8e648bca0d", "parent": "8ee3f680-f582-4f11-ae28-4b450b0738fa"}}, {"head": {"id": "8ee3f680-f582-4f11-ae28-4b450b0738fa", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424808977706, "endTime": 652424887848530}, "additional": {"logType": "info", "children": ["df4f8c28-cdce-428d-b9af-ebe737c52b69", "50372377-8fe0-4df8-8daa-e455ee882590", "d83f0183-7521-4d42-b395-c9f423e04903"], "durationId": "e773e0ec-66c8-4938-a48b-cb3f0c25c9a3", "parent": "4428d4ac-2038-4618-a6f8-d9ef23ee464b"}}, {"head": {"id": "7a13561c-2f14-4456-8a24-5c277d61c77d", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424888418427, "endTime": 652424911374550}, "additional": {"children": ["306adf1a-9428-42b4-87c0-d01bfac87734", "ca4cca45-7fff-4e4e-b2d0-07d8d7c8585c", "883a87a2-1752-4b11-84a9-3facf5a94411"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b8c52fff-ae5c-406b-b3fc-e81ba8025007", "logId": "92ab352f-d2c8-4697-9900-9c21763f588e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "306adf1a-9428-42b4-87c0-d01bfac87734", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424891016471, "endTime": 652424891033205}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7a13561c-2f14-4456-8a24-5c277d61c77d", "logId": "aa5807fd-b112-445e-a214-bbb1813221a5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aa5807fd-b112-445e-a214-bbb1813221a5", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424891016471, "endTime": 652424891033205}, "additional": {"logType": "info", "children": [], "durationId": "306adf1a-9428-42b4-87c0-d01bfac87734", "parent": "92ab352f-d2c8-4697-9900-9c21763f588e"}}, {"head": {"id": "ca4cca45-7fff-4e4e-b2d0-07d8d7c8585c", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424892543111, "endTime": 652424910123411}, "additional": {"children": ["121d13d9-7c87-4800-8d14-170356d22dff", "d61eda18-e313-4d0b-aedc-c323e933786c"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7a13561c-2f14-4456-8a24-5c277d61c77d", "logId": "2eb6bfb6-8058-4a5b-a870-1056c9a501b1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "121d13d9-7c87-4800-8d14-170356d22dff", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424892544399, "endTime": 652424901897538}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ca4cca45-7fff-4e4e-b2d0-07d8d7c8585c", "logId": "82520c46-9df6-4c0d-ac55-63e8149c32bd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d61eda18-e313-4d0b-aedc-c323e933786c", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424901919306, "endTime": 652424910107919}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ca4cca45-7fff-4e4e-b2d0-07d8d7c8585c", "logId": "bd989d24-5114-44ad-8f27-17299abf6144"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ba947e19-789c-47aa-aba6-40da4e1d819a", "name": "hvigorfile, resolving /Users/<USER>/github/Ling<PERSON>ia/examples/harmony/entry/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424892550593}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd76be71-110f-4138-861c-f0385e8c1903", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424901765138}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82520c46-9df6-4c0d-ac55-63e8149c32bd", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424892544399, "endTime": 652424901897538}, "additional": {"logType": "info", "children": [], "durationId": "121d13d9-7c87-4800-8d14-170356d22dff", "parent": "2eb6bfb6-8058-4a5b-a870-1056c9a501b1"}}, {"head": {"id": "1fb42d34-7f12-495c-a9ac-99fc1e2b4ae5", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424901932375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d7d3763-ce61-4341-b302-7c6571b2eefc", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424906354525}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b0bda90-5f25-4a63-8e90-d8e4f5bfd68f", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424906479594}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6e3a538-1691-4df5-ad37-48f0fc852c91", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424906912336}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fdf5dfec-c826-4e7c-b777-0f2fb3e81dbd", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424907123698}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7baa653e-a76c-4533-bf20-911fdf5d0e44", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424907199343}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "446e8398-c00e-4aa6-b85d-81b0acbeb0a9", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424907246426}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b59dd4e8-844e-4724-adf6-f495530d4439", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424907298321}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9082397f-1e03-4379-87c3-0e551445a85f", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424909795086}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4336780-0107-4220-b382-3e9a6dabe614", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424909961862}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af41b2bf-9667-4a1d-a670-084639d4467d", "name": "hvigorfile, no custom plugins were found in /Users/<USER>/github/LingXia/examples/harmony/entry/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424910025158}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d616b4ed-d058-4037-aef6-e03611f63c36", "name": "hvigorfile, resolve finished /Users/<USER>/github/LingXia/examples/harmony/entry/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424910068462}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd989d24-5114-44ad-8f27-17299abf6144", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424901919306, "endTime": 652424910107919}, "additional": {"logType": "info", "children": [], "durationId": "d61eda18-e313-4d0b-aedc-c323e933786c", "parent": "2eb6bfb6-8058-4a5b-a870-1056c9a501b1"}}, {"head": {"id": "2eb6bfb6-8058-4a5b-a870-1056c9a501b1", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424892543111, "endTime": 652424910123411}, "additional": {"logType": "info", "children": ["82520c46-9df6-4c0d-ac55-63e8149c32bd", "bd989d24-5114-44ad-8f27-17299abf6144"], "durationId": "ca4cca45-7fff-4e4e-b2d0-07d8d7c8585c", "parent": "92ab352f-d2c8-4697-9900-9c21763f588e"}}, {"head": {"id": "883a87a2-1752-4b11-84a9-3facf5a94411", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424911330950, "endTime": 652424911350013}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7a13561c-2f14-4456-8a24-5c277d61c77d", "logId": "638e4d5a-b87d-423f-b568-14a26cb185a0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "638e4d5a-b87d-423f-b568-14a26cb185a0", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424911330950, "endTime": 652424911350013}, "additional": {"logType": "info", "children": [], "durationId": "883a87a2-1752-4b11-84a9-3facf5a94411", "parent": "92ab352f-d2c8-4697-9900-9c21763f588e"}}, {"head": {"id": "92ab352f-d2c8-4697-9900-9c21763f588e", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424888418427, "endTime": 652424911374550}, "additional": {"logType": "info", "children": ["aa5807fd-b112-445e-a214-bbb1813221a5", "2eb6bfb6-8058-4a5b-a870-1056c9a501b1", "638e4d5a-b87d-423f-b568-14a26cb185a0"], "durationId": "7a13561c-2f14-4456-8a24-5c277d61c77d", "parent": "1b973118-6fa7-4a69-a351-7c3f2bcb1d74"}}, {"head": {"id": "6a2a16df-1804-4d4f-b819-1e00201c0bf4", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424911844179, "endTime": 652424939382877}, "additional": {"children": ["5eb5b507-395e-4c34-b648-9cf598e04278", "cbdaa9ae-a43f-4f58-a290-4f4190082bc6", "05468f8f-0fe1-4d36-9622-21c6e19b76cd"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b8c52fff-ae5c-406b-b3fc-e81ba8025007", "logId": "3982158f-549b-40a3-86a4-c3e7dcb4a2e0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5eb5b507-395e-4c34-b648-9cf598e04278", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424914048295, "endTime": 652424914062773}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6a2a16df-1804-4d4f-b819-1e00201c0bf4", "logId": "e61eed84-49d6-4152-abfb-ab0c0c0abbdb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e61eed84-49d6-4152-abfb-ab0c0c0abbdb", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424914048295, "endTime": 652424914062773}, "additional": {"logType": "info", "children": [], "durationId": "5eb5b507-395e-4c34-b648-9cf598e04278", "parent": "3982158f-549b-40a3-86a4-c3e7dcb4a2e0"}}, {"head": {"id": "cbdaa9ae-a43f-4f58-a290-4f4190082bc6", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424915246449, "endTime": 652424938114079}, "additional": {"children": ["a117a326-fb39-4a58-832d-a587b8d0371f", "1c2207a3-0276-4fa6-a200-8a34ba990f32"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6a2a16df-1804-4d4f-b819-1e00201c0bf4", "logId": "c7a98723-a074-4349-a984-ace165840391"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a117a326-fb39-4a58-832d-a587b8d0371f", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424915247661, "endTime": 652424924202140}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cbdaa9ae-a43f-4f58-a290-4f4190082bc6", "logId": "22590e3f-72a3-4c57-ac6a-46e8f71792f5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1c2207a3-0276-4fa6-a200-8a34ba990f32", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424924221315, "endTime": 652424938095696}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cbdaa9ae-a43f-4f58-a290-4f4190082bc6", "logId": "4670e34e-7cae-4a1c-9fe9-eac5e572289f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "02527156-585c-416d-8283-c0f4c5c74414", "name": "hvigorfile, resolving /Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424915254063}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0b6cca1-846b-45d8-9a52-03f7d44f76c6", "name": "hvigorfile, require result:  { default: { system: [Function: harTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424924077532}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22590e3f-72a3-4c57-ac6a-46e8f71792f5", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424915247661, "endTime": 652424924202140}, "additional": {"logType": "info", "children": [], "durationId": "a117a326-fb39-4a58-832d-a587b8d0371f", "parent": "c7a98723-a074-4349-a984-ace165840391"}}, {"head": {"id": "8db8d0d6-54e3-4633-bf9d-a1ecaa13b566", "name": "hvigorfile, binding system plugins [Function: harTasks]", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424924234134}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba39d0a3-f00b-40a5-97f7-66ed4e1e539f", "name": "Start initialize module-target build option map, moduleName=lingxia, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424932824664}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98b6f4ee-6136-4c0b-a737-6d78b950a22b", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424932968456}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9e8608f-4944-4049-bf19-3f5d474fd3b9", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424933192935}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff4cc16c-30fa-43ee-8246-8cd99b655bce", "name": "Module 'lingxia' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424933372810}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "513743da-0441-44ad-9bb8-547bac79adc4", "name": "Module 'lingxia' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424933443502}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff2b15d2-525b-4a0b-a9b3-d70d6b6d0c25", "name": "End initialize module-target build option map, moduleName=lingxia", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424933496219}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2c33930-0e6f-445a-91a2-ab75cc7cbf9a", "name": "Module 'lingxia' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424933558468}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ef8b5a1-b9ce-41d0-8945-5744c51bf09a", "name": "Module lingxia task initialization takes 3 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424937673709}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25954403-3875-4723-8cc2-a92edfb22e00", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424937912441}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c9d52d7-a7f2-4aec-9051-75116302004b", "name": "hvigorfile, no custom plugins were found in /Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424937994220}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82843baa-ea8a-40a6-b5fc-026eed48d3d4", "name": "hvigorfile, resolve finished /Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424938047668}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4670e34e-7cae-4a1c-9fe9-eac5e572289f", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424924221315, "endTime": 652424938095696}, "additional": {"logType": "info", "children": [], "durationId": "1c2207a3-0276-4fa6-a200-8a34ba990f32", "parent": "c7a98723-a074-4349-a984-ace165840391"}}, {"head": {"id": "c7a98723-a074-4349-a984-ace165840391", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424915246449, "endTime": 652424938114079}, "additional": {"logType": "info", "children": ["22590e3f-72a3-4c57-ac6a-46e8f71792f5", "4670e34e-7cae-4a1c-9fe9-eac5e572289f"], "durationId": "cbdaa9ae-a43f-4f58-a290-4f4190082bc6", "parent": "3982158f-549b-40a3-86a4-c3e7dcb4a2e0"}}, {"head": {"id": "05468f8f-0fe1-4d36-9622-21c6e19b76cd", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424939338360, "endTime": 652424939357257}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6a2a16df-1804-4d4f-b819-1e00201c0bf4", "logId": "4ac58daf-f937-4096-84bc-627c71fba178"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4ac58daf-f937-4096-84bc-627c71fba178", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424939338360, "endTime": 652424939357257}, "additional": {"logType": "info", "children": [], "durationId": "05468f8f-0fe1-4d36-9622-21c6e19b76cd", "parent": "3982158f-549b-40a3-86a4-c3e7dcb4a2e0"}}, {"head": {"id": "3982158f-549b-40a3-86a4-c3e7dcb4a2e0", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424911844179, "endTime": 652424939382877}, "additional": {"logType": "info", "children": ["e61eed84-49d6-4152-abfb-ab0c0c0abbdb", "c7a98723-a074-4349-a984-ace165840391", "4ac58daf-f937-4096-84bc-627c71fba178"], "durationId": "6a2a16df-1804-4d4f-b819-1e00201c0bf4", "parent": "1b973118-6fa7-4a69-a351-7c3f2bcb1d74"}}, {"head": {"id": "1b973118-6fa7-4a69-a351-7c3f2bcb1d74", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424887877791, "endTime": 652424939401153}, "additional": {"logType": "info", "children": ["92ab352f-d2c8-4697-9900-9c21763f588e", "3982158f-549b-40a3-86a4-c3e7dcb4a2e0"], "durationId": "b8c52fff-ae5c-406b-b3fc-e81ba8025007", "parent": "4428d4ac-2038-4618-a6f8-d9ef23ee464b"}}, {"head": {"id": "4b16a31b-bf60-4a77-8ce3-9dcc8b6ea158", "name": "watch files: [\n  '/Users/<USER>/github/LingXia/examples/harmony/hvigorfile.ts',\n  '/Users/<USER>/github/LingXia/examples/harmony/entry/hvigorfile.ts',\n  '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/hvigorfile.ts',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor-ohos-plugin/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor-ohos-plugin/src/plugin/factory/plugin-factory.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/log4js.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/debug/src/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/debug/src/node.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/debug/src/common.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/ms/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/rfdc/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/configuration.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/layouts.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/date-format/lib/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/levels.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/clustering.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/LoggingEvent.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/flatted/cjs/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/adapters.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/console.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/stdout.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/stderr.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/logLevelFilter.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/categoryFilter.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/noLogFilter.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/file.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/lib/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/lib/RollingFileWriteStream.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/fs/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/universalify/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/graceful-fs/graceful-fs.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/graceful-fs/polyfills.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/graceful-fs/legacy-streams.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/graceful-fs/clone.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/copy-sync/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/copy-sync/copy-sync.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/mkdirs/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/mkdirs/mkdirs.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/mkdirs/win32.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/mkdirs/mkdirs-sync.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/util/utimes.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/util/stat.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/copy/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/copy/copy.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/path-exists/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/empty/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/remove/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/remove/rimraf.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/file.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/link.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/symlink.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/symlink-paths.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/symlink-type.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/json/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/json/jsonfile.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/jsonfile/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/json/output-json.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/json/output-json-sync.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/move-sync/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/move-sync/move-sync.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/move/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/move/move.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/output/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/lib/now.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/lib/fileNameFormatter.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/lib/fileNameParser.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/lib/moveAndMaybeCompressFile.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/lib/RollingFileStream.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/lib/DateRollingFileStream.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/dateFile.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/fileSync.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/tcp.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/categories.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/logger.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/connect-logger.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/recording.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/src/common/util/local-file-writer.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/fs/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/universalify/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/copy/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/copy/copy.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/mkdirs/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/mkdirs/make-dir.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/mkdirs/utils.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/path-exists/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/util/utimes.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/util/stat.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/copy/copy-sync.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/empty/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/remove/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/ensure/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/ensure/file.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/ensure/link.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/ensure/symlink.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/ensure/symlink-paths.js',\n  ... 1869 more items\n]", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424967668206}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4612b6aa-b0a6-4571-82db-e6293de4789e", "name": "hvigorfile, resolve hvigorfile dependencies in 94 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425033135702}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aab77105-ea32-4e6a-91f7-4766e6df39af", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424939417143, "endTime": 652425033272529}, "additional": {"logType": "info", "children": [], "durationId": "dbafb235-b16c-4b2d-b558-b7a61ac075c4", "parent": "4428d4ac-2038-4618-a6f8-d9ef23ee464b"}}, {"head": {"id": "fd9fae33-9996-471c-bf31-069592f0cdf9", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425033912043, "endTime": 652425034212674}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4b23c430-5823-4e1d-a681-6255c7233545", "logId": "c93acc47-386f-48b9-bb5c-d1d4ade3c2fe"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8876a566-83ec-44bb-b37e-b9302f42ccc7", "name": "project has submodules:entry,lingxia", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425033942020}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7aa65431-2e62-4869-96f4-4557fd01c8e1", "name": "module:lingxia no need to execute packageHap", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425034138860}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c93acc47-386f-48b9-bb5c-d1d4ade3c2fe", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425033912043, "endTime": 652425034212674}, "additional": {"logType": "info", "children": [], "durationId": "fd9fae33-9996-471c-bf31-069592f0cdf9", "parent": "4428d4ac-2038-4618-a6f8-d9ef23ee464b"}}, {"head": {"id": "e38e564b-7739-467a-b799-22f1972a0e57", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425034831413}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a08e9067-13cc-4b55-a297-1688451f62ab", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425038616057}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b3a94dd-b667-4f4f-9ada-09e30704b910", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425034225298, "endTime": 652425039041712}, "additional": {"logType": "info", "children": [], "durationId": "3f86c9a9-92b6-4a82-87a2-45ec928fc8a9", "parent": "4428d4ac-2038-4618-a6f8-d9ef23ee464b"}}, {"head": {"id": "9f784876-f055-4187-b924-ce8950661676", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425039081080}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5110acb-763d-400d-a912-021d74008aad", "name": "Module harmony Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425040355748}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6efb777-0fcf-4b4f-970e-88b74d9ca030", "name": "Module harmony's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425040440479}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a47eb026-0188-4e2e-9db5-9fde422627e8", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425040691189}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "388626da-5582-4e80-b9e8-8c2eae146c59", "name": "Please confirm the symbolic link is valid:  /Users/<USER>/github/LingXia/examples/harmony/entry/oh_modules/lingxia", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425041319689}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "05f15c81-92a8-4f50-8da6-cdf995f3965b", "name": "The current module 'entry' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425041841931}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "6cf5ac53-5a02-4814-91e8-ec5f14ec7930", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425042370012}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7323cfed-856b-4930-aa57-42e90ece01ee", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425042449985}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d15145f4-87d9-41a7-b72d-df2063335159", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425044402046}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8774501-9832-4727-8a5b-3f697c671889", "name": "Module ling<PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425044951011}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1cc5164-bf55-4c57-abb9-9f185a5b37c2", "name": "Module lingxia's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425045042707}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a55da1dc-c6c3-4e9c-b886-7c3339fc0490", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425039061804, "endTime": 652425047237367}, "additional": {"logType": "info", "children": [], "durationId": "c72e1d5c-8306-4112-85ca-ba06c7329b0c", "parent": "4428d4ac-2038-4618-a6f8-d9ef23ee464b"}}, {"head": {"id": "3c8ede9c-13d5-4581-bde5-081cf93ebfa1", "name": "Configuration phase cost:242 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425047312661}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60f5cbd5-87e5-400b-bbef-d33eecb0af76", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425047271652, "endTime": 652425047447918}, "additional": {"logType": "info", "children": [], "durationId": "b2a76fba-c8e5-4093-a05d-b62848efe3ec", "parent": "4428d4ac-2038-4618-a6f8-d9ef23ee464b"}}, {"head": {"id": "4428d4ac-2038-4618-a6f8-d9ef23ee464b", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424802596542, "endTime": 652425047469721}, "additional": {"logType": "info", "children": ["08ca56e7-b619-438f-9846-770420a7fa2b", "55a1dfcb-8cd5-4d0e-8d04-467157222ecd", "8ee3f680-f582-4f11-ae28-4b450b0738fa", "1b973118-6fa7-4a69-a351-7c3f2bcb1d74", "aab77105-ea32-4e6a-91f7-4766e6df39af", "3b3a94dd-b667-4f4f-9ada-09e30704b910", "a55da1dc-c6c3-4e9c-b886-7c3339fc0490", "60f5cbd5-87e5-400b-bbef-d33eecb0af76", "c93acc47-386f-48b9-bb5c-d1d4ade3c2fe"], "durationId": "4b23c430-5823-4e1d-a681-6255c7233545", "parent": "c0fd1e5e-08ef-4bb6-913b-4d6f729e931a"}}, {"head": {"id": "63ce2fc4-4f42-450b-b81d-4784caa046f5", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425052863835, "endTime": 652425052891773}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "130efb25-7e1c-4c6c-b866-c4b346532396", "logId": "1e200b3e-9789-4f57-8f44-0c501a7f0dd6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1e200b3e-9789-4f57-8f44-0c501a7f0dd6", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425052863835, "endTime": 652425052891773}, "additional": {"logType": "info", "children": [], "durationId": "63ce2fc4-4f42-450b-b81d-4784caa046f5", "parent": "c0fd1e5e-08ef-4bb6-913b-4d6f729e931a"}}, {"head": {"id": "a646ab29-fff3-4c5d-b9b4-af169487a0e1", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425047505788, "endTime": 652425052911876}, "additional": {"logType": "info", "children": [], "durationId": "4c7e3744-62fc-44fd-bcbc-44c9bab4e481", "parent": "c0fd1e5e-08ef-4bb6-913b-4d6f729e931a"}}, {"head": {"id": "c05c9cb9-abb7-4416-9ef1-da507c93fa56", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425052918754, "endTime": 652425052928387}, "additional": {"logType": "info", "children": [], "durationId": "b2d69add-5333-4a88-bc2f-433399c1db4c", "parent": "c0fd1e5e-08ef-4bb6-913b-4d6f729e931a"}}, {"head": {"id": "c0fd1e5e-08ef-4bb6-913b-4d6f729e931a", "name": "init", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424790579928, "endTime": 652425052933583}, "additional": {"logType": "info", "children": ["a8e01456-c16f-4eb9-9818-c00be59557ec", "4428d4ac-2038-4618-a6f8-d9ef23ee464b", "a646ab29-fff3-4c5d-b9b4-af169487a0e1", "c05c9cb9-abb7-4416-9ef1-da507c93fa56", "9a0909e0-897b-4078-81aa-7160e38b1c23", "a6645bab-505b-472e-b3c8-3e19e19bb973", "1e200b3e-9789-4f57-8f44-0c501a7f0dd6"], "durationId": "130efb25-7e1c-4c6c-b866-c4b346532396"}}, {"head": {"id": "6439fc5f-6dab-4889-8e2d-0da92f44c6a8", "name": "Configuration task cost before running: 265 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425053158553}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70d976a4-120b-4684-89a7-aceaef43dc27", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425055859533, "endTime": 652425060739998}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": ["The input file '/Users/<USER>/github/LingXia/examples/harmony/entry/oh-package.json5' has been changed."], "detailId": "37bf7d61-e777-45ad-8b9b-41dd87bfd00c", "logId": "acb6f913-3ff8-4571-8dfa-9da8df3e6794"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "37bf7d61-e777-45ad-8b9b-41dd87bfd00c", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425054326979}, "additional": {"logType": "detail", "children": [], "durationId": "70d976a4-120b-4684-89a7-aceaef43dc27"}}, {"head": {"id": "0a39c58e-bc31-4094-9161-8ee5be082a42", "name": "jsonObjWithoutParam {\"lingxia\":\"file:./../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425054561610}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b04397a-a4a2-4c80-ada5-fa967d03bdef", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:./../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425054643691}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90a076aa-91ed-4e67-9dee-a430eb63b570", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425055870047}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51dd7ce5-b7d0-4265-b7f6-4f1aa2693028", "name": "entry:default@PreBuild is not up-to-date, since the input file '/Users/<USER>/github/LingXia/examples/harmony/entry/oh-package.json5' has been changed.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425057747580}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "789df95c-d1cc-443d-989b-1e4826fd4ed5", "name": "Incremental task entry:default@PreBuild pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425057850527}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "868fad85-7338-4ab7-b459-e4dbf5c1a9c6", "name": "jsonObjWithoutParam {\"lingxia\":\"file:./../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425057927508}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb3f6ccf-f1fc-4b7b-8fab-de98c40a6ea0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:./../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425057975601}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73bd106f-35c3-46fb-895a-bf0cfe2a824c", "name": "current product is not Atomic service.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425060175295}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2a9f345-db5f-4aa2-8758-051ce073bcea", "name": "Use tool [darwin: JAVA_HOME, CLASSPATH]\n [\n  {\n    JAVA_HOME: '/Applications/Android Studio.app/Contents/jbr/Contents/Home'\n  },\n  { CLASSPATH: undefined }\n]", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425060303080}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5057142-8923-4230-8ec7-ba0ba411ec00", "name": "Use tool [darwin: NODE_HOME]\n [ { NODE_HOME: undefined } ]", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425060401169}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4db6f03a-b50c-46dc-ac6d-9e139d3fd767", "name": "entry : default@PreBuild cost memory 0.37064361572265625", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425060585940}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "825355ee-f686-429c-a42e-46b38a5abe48", "name": "runTaskFromQueue task cost before running: 273 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425060677036}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "acb6f913-3ff8-4571-8dfa-9da8df3e6794", "name": "Finished :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425055859533, "endTime": 652425060739998, "totalTime": 4792987}, "additional": {"logType": "info", "children": [], "durationId": "70d976a4-120b-4684-89a7-aceaef43dc27"}}, {"head": {"id": "2b41e421-9049-42a9-b3e6-eeb3e86cc431", "name": "entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425062951979, "endTime": 652425063897183}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "fcf7a052-cd02-41a7-a571-ab0106457d3d", "logId": "c8c9500d-875b-445b-9e73-4bea79ba5eee"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fcf7a052-cd02-41a7-a571-ab0106457d3d", "name": "create entry:default@CreateModuleInfo task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425062298814}, "additional": {"logType": "detail", "children": [], "durationId": "2b41e421-9049-42a9-b3e6-eeb3e86cc431"}}, {"head": {"id": "93e7da84-4829-4205-9915-052e71f124a0", "name": "jsonObjWithoutParam {\"lingxia\":\"file:./../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425062508563}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f32e10a-2a77-456f-b79f-126308260bb5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:./../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425062583891}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9488c1e1-3f05-48c6-9c6e-903f963c827c", "name": "Executing task :entry:default@CreateModuleInfo", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425062962236}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb19da30-0937-465b-a9ef-edce0ea87159", "name": "Task 'entry:default@CreateModuleInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425063341978}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "412aefa1-21d5-4535-809b-c840a9903f94", "name": "entry : default@CreateModuleInfo cost memory 0.05162811279296875", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425063754821}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70298944-3d50-4ab9-a8f7-b0d993423a3b", "name": "runTaskFromQueue task cost before running: 276 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425063840486}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8c9500d-875b-445b-9e73-4bea79ba5eee", "name": "Finished :entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425062951979, "endTime": 652425063897183, "totalTime": 869359}, "additional": {"logType": "info", "children": [], "durationId": "2b41e421-9049-42a9-b3e6-eeb3e86cc431"}}, {"head": {"id": "a6fb9e11-e795-4661-969f-4c7102c0ae49", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425067335183, "endTime": 652425068818481}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "33efd702-4e76-4352-b959-0b46d8264bed", "logId": "bbd4b3e8-fb0e-4181-9c1a-a2acaba97b32"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "33efd702-4e76-4352-b959-0b46d8264bed", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425065293863}, "additional": {"logType": "detail", "children": [], "durationId": "a6fb9e11-e795-4661-969f-4c7102c0ae49"}}, {"head": {"id": "3fa96714-bf12-405e-8e02-152b6221e004", "name": "jsonObjWithoutParam {\"lingxia\":\"file:./../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425065522679}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61fe4498-d6c5-485a-9031-c60a3cd16ea4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:./../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425065610567}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c0f24e2-a3e5-4d5d-8e55-061a29252589", "name": "Please confirm the symbolic link is valid:  /Users/<USER>/github/LingXia/examples/harmony/entry/oh_modules/lingxia", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425066399757}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "a19de815-a4ab-48ff-ada6-266cf7f74703", "name": "The current module 'entry' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425066880283}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "29dac1e1-cc2a-4663-82da-1b423496c03b", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425067346969}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9288bbed-68ce-47f2-89b8-1a9faa4e4642", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425067916451}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b41e4c6-314f-43eb-b4fa-7c4cb822e562", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425068636810}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e89bc11-be67-4b0a-b8e7-d85dd8f6f31b", "name": "entry : default@GenerateMetadata cost memory 0.087646484375", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425068750447}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbd4b3e8-fb0e-4181-9c1a-a2acaba97b32", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425067335183, "endTime": 652425068818481}, "additional": {"logType": "info", "children": [], "durationId": "a6fb9e11-e795-4661-969f-4c7102c0ae49"}}, {"head": {"id": "01c48fd8-87c0-4bb8-90b6-b8c9c2e20e71", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425070391325, "endTime": 652425070721176}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "f1f5ea17-335e-4776-87fc-65702fc7750f", "logId": "a479478f-557d-4db4-9204-8e2aa725fba2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f1f5ea17-335e-4776-87fc-65702fc7750f", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425069956626}, "additional": {"logType": "detail", "children": [], "durationId": "01c48fd8-87c0-4bb8-90b6-b8c9c2e20e71"}}, {"head": {"id": "3fcc58bd-9cf8-4421-8272-f147f5f2a628", "name": "jsonObjWithoutParam {\"lingxia\":\"file:./../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425070179423}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b000a183-4704-4f20-aac6-a6633bd4fd15", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:./../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425070254511}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4084ed0e-7ba3-4c6e-8f19-9ddc2932f8fc", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425070400175}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe95feec-b629-4941-9b59-1f7bbab7135b", "name": "jsonObjWithoutParam {\"lingxia\":\"file:./../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425070485386}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d29ff29b-a956-4734-91a0-96b7bc8747bb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:./../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425070530388}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9aa5674c-36a6-48d2-843e-f93d1aeb37a6", "name": "entry : default@ConfigureCmake cost memory 0.04010772705078125", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425070598600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dea99262-3a69-4759-80be-decc8090abd3", "name": "runTaskFromQueue task cost before running: 283 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425070675576}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a479478f-557d-4db4-9204-8e2aa725fba2", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425070391325, "endTime": 652425070721176, "totalTime": 261633}, "additional": {"logType": "info", "children": [], "durationId": "01c48fd8-87c0-4bb8-90b6-b8c9c2e20e71"}}, {"head": {"id": "56311320-80da-455c-8c4e-6242b42c44c6", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425072471542, "endTime": 652425073747783}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "53d47814-ce9d-4d1b-94b8-53752f0fd8f7", "logId": "1da7e80d-f27f-4ea9-8fbd-3dd6c1f38e57"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "53d47814-ce9d-4d1b-94b8-53752f0fd8f7", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425071748314}, "additional": {"logType": "detail", "children": [], "durationId": "56311320-80da-455c-8c4e-6242b42c44c6"}}, {"head": {"id": "c0e1bbc7-fc00-4617-92d9-2d9d6763a7d4", "name": "jsonObjWithoutParam {\"lingxia\":\"file:./../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425071966114}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26a2e4bf-31f3-4846-8565-cec6e9aa0eb4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:./../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425072043903}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2eba857e-3222-4160-9c5d-34adae31e6ba", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425072481655}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64abe4c8-5360-48ab-9b6e-940076e1ec4e", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425073548202}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe8e82d0-53b1-4abd-9852-6cda5b4b0be5", "name": "entry : default@MergeProfile cost memory 0.093719482421875", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425073673288}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1da7e80d-f27f-4ea9-8fbd-3dd6c1f38e57", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425072471542, "endTime": 652425073747783}, "additional": {"logType": "info", "children": [], "durationId": "56311320-80da-455c-8c4e-6242b42c44c6"}}, {"head": {"id": "ca4ce7f9-9852-41ce-9c77-3c7e8305844c", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425075943880, "endTime": 652425077035410}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "5c9d727b-d544-4657-84d2-136bef8f8469", "logId": "1e846158-18de-4e66-ab7a-a2b4b6e425fb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5c9d727b-d544-4657-84d2-136bef8f8469", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425075131677}, "additional": {"logType": "detail", "children": [], "durationId": "ca4ce7f9-9852-41ce-9c77-3c7e8305844c"}}, {"head": {"id": "3b0b95f3-bf22-49eb-8c61-5aa78fc6bd3c", "name": "jsonObjWithoutParam {\"lingxia\":\"file:./../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425075350213}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99866b4c-ee6f-49cf-8f0b-84223f4a17fb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:./../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425075420744}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5ae4e91-02f2-4ca1-b165-8aad0e0fdea8", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425075953919}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "607f1f66-3528-4fbe-bc7f-450dff565bc9", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425076405350}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97ec5c31-ebc1-4328-9158-35f9e8d7e040", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425076876674}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5db70091-6df1-4d6a-9797-3333f2551ee6", "name": "entry : default@CreateBuildProfile cost memory 0.08905029296875", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425076969862}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e846158-18de-4e66-ab7a-a2b4b6e425fb", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425075943880, "endTime": 652425077035410}, "additional": {"logType": "info", "children": [], "durationId": "ca4ce7f9-9852-41ce-9c77-3c7e8305844c"}}, {"head": {"id": "85397064-175c-41cb-b916-9ea392d8da04", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425079187773, "endTime": 652425079604157}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "e027c12f-3384-47cf-9bc7-428c482ac38e", "logId": "c8834a4e-e80a-4a3c-af53-c3603df4ed84"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e027c12f-3384-47cf-9bc7-428c482ac38e", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425078035006}, "additional": {"logType": "detail", "children": [], "durationId": "85397064-175c-41cb-b916-9ea392d8da04"}}, {"head": {"id": "0dcb3273-daaa-44de-85d4-9f6916165f99", "name": "jsonObjWithoutParam {\"lingxia\":\"file:./../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425078351663}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d52e254-b514-4292-880f-fdfffd692884", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:./../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425078452421}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca859261-6d38-482d-8724-a5627ffe9036", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425079202676}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ded271a-5f76-45fd-abae-5fdcaa9e1feb", "name": "jsonObjWithoutParam {\"lingxia\":\"file:./../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425079334380}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3b72cbf-69e3-4a3d-83c1-4b09fa299d9f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:./../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425079392353}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "736f0a52-5131-43b3-a63d-0c3f28b928dc", "name": "entry : default@PreCheckSyscap cost memory 0.03934478759765625", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425079473166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db1df483-1947-47b6-be4a-485976b8ea1a", "name": "runTaskFromQueue task cost before running: 291 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425079555097}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8834a4e-e80a-4a3c-af53-c3603df4ed84", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425079187773, "endTime": 652425079604157, "totalTime": 343297}, "additional": {"logType": "info", "children": [], "durationId": "85397064-175c-41cb-b916-9ea392d8da04"}}, {"head": {"id": "2e4a5e0e-ea45-49dd-9bb9-5811b0a0c3fd", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425084564580, "endTime": 652425085372031}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "60ff4263-09b2-43e9-b63e-f949ad9f22aa", "logId": "9c358f69-5c70-4a85-ae91-94f21b696df1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "60ff4263-09b2-43e9-b63e-f949ad9f22aa", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425080854007}, "additional": {"logType": "detail", "children": [], "durationId": "2e4a5e0e-ea45-49dd-9bb9-5811b0a0c3fd"}}, {"head": {"id": "b4559aa9-f2c5-4c76-8611-e60e13c3daa6", "name": "jsonObjWithoutParam {\"lingxia\":\"file:./../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425081173609}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12f81d11-39d4-4e31-9fc4-598e6022159f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:./../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425081284247}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd511ae9-1ee1-4cf5-a618-68c2fbaa82d6", "name": "Please confirm the symbolic link is valid:  /Users/<USER>/github/LingXia/examples/harmony/entry/oh_modules/lingxia", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425082434480}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "585298f3-a619-4697-bfc9-3ddef3b4810a", "name": "The current module 'entry' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425082907179}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "e93a70aa-361e-46d9-97ba-3b5597b01762", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425084579820}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23027932-2d5d-4e7d-97fd-bdd144b3067a", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425084790533}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "761b3920-a9e5-4773-8f4e-1427594704bb", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425085207775}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f106108-0bce-4c1f-a96b-e75f82581b2a", "name": "entry : default@GeneratePkgContextInfo cost memory 0.066131591796875", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425085300139}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c358f69-5c70-4a85-ae91-94f21b696df1", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425084564580, "endTime": 652425085372031}, "additional": {"logType": "info", "children": [], "durationId": "2e4a5e0e-ea45-49dd-9bb9-5811b0a0c3fd"}}, {"head": {"id": "de6fdd17-aeab-4bd8-9382-29f540154539", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425087993391, "endTime": 652425089399724}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file '/Users/<USER>/github/LingXia/examples/harmony/build/cache/default/integrated_hsp/integratedHspCache.json' does not exist."], "detailId": "dc862c81-ee88-4419-a543-00f1439f72d0", "logId": "93b6ce41-d578-47c7-ac98-e1b4ae7b803c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dc862c81-ee88-4419-a543-00f1439f72d0", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425086578668}, "additional": {"logType": "detail", "children": [], "durationId": "de6fdd17-aeab-4bd8-9382-29f540154539"}}, {"head": {"id": "5f0d1e38-43f4-4386-8675-30f140e96f0c", "name": "jsonObjWithoutParam {\"lingxia\":\"file:./../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425086804864}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc33aaff-bef0-4bc1-84dc-4e961780b4bf", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:./../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425086931685}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1857a767-d838-4780-a16a-7928b8e31dd6", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425088005136}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94e96f22-2c54-40b4-86a5-01ed1fb01606", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file '/Users/<USER>/github/LingXia/examples/harmony/build/cache/default/integrated_hsp/integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425088965469}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2965f1d-7b2e-4c75-8ab7-e18447f96c84", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425089052892}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a18f2f4c-f6dd-429c-a7ae-88cc7846fed2", "name": "jsonObjWithoutParam {\"lingxia\":\"file:./../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425089146254}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33d26e80-ea6c-4412-b98e-8792faa026a8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:./../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425089194620}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b5cd01c-5e17-4d3d-bbef-2d46af9944a6", "name": "entry : default@ProcessIntegratedHsp cost memory 0.11048126220703125", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425089269897}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e40c2fdf-2e4f-4914-a01a-b59213f32cb8", "name": "runTaskFromQueue task cost before running: 301 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425089351815}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93b6ce41-d578-47c7-ac98-e1b4ae7b803c", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425087993391, "endTime": 652425089399724, "totalTime": 1340108}, "additional": {"logType": "info", "children": [], "durationId": "de6fdd17-aeab-4bd8-9382-29f540154539"}}, {"head": {"id": "c26028b7-fcd1-4c34-bdea-27bc9083937e", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425091485264, "endTime": 652425091825922}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "3ff8f771-182b-4d10-994c-e9a8beccf00d", "logId": "b804f1c0-f57a-44f2-b223-82f595a799aa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3ff8f771-182b-4d10-994c-e9a8beccf00d", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425090694980}, "additional": {"logType": "detail", "children": [], "durationId": "c26028b7-fcd1-4c34-bdea-27bc9083937e"}}, {"head": {"id": "862e7771-84ff-44ef-96f1-88862afccf47", "name": "jsonObjWithoutParam {\"lingxia\":\"file:./../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425090903630}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1bfa5f1-054a-4e68-a0d2-084f72c1d1b9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:./../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425090974372}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0bf5e0ff-b019-412b-8897-c3d2cd2c4d81", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425091494999}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b90ef9c-8a55-47ef-97e9-eea74a3533a9", "name": "jsonObjWithoutParam {\"lingxia\":\"file:./../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425091587731}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9314883e-69ef-4096-b57a-eb4afe8d7449", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:./../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425091636437}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0d6cab2-effc-4ebe-8f98-dd030a9c13f4", "name": "entry : default@BuildNativeWithCmake cost memory 0.0401763916015625", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425091707207}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc510fce-7f7e-4e08-b477-cbadab267d7f", "name": "runTaskFromQueue task cost before running: 304 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425091780903}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b804f1c0-f57a-44f2-b223-82f595a799aa", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425091485264, "endTime": 652425091825922, "totalTime": 277545}, "additional": {"logType": "info", "children": [], "durationId": "c26028b7-fcd1-4c34-bdea-27bc9083937e"}}, {"head": {"id": "071b3f22-8615-4696-8f57-50926d3ab418", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425093645956, "endTime": 652425095726582}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "f4e1b6a2-6a93-492f-8245-3744649710b7", "logId": "c4075e91-8c54-4a4d-86ed-5121583a76a5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f4e1b6a2-6a93-492f-8245-3744649710b7", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425092850830}, "additional": {"logType": "detail", "children": [], "durationId": "071b3f22-8615-4696-8f57-50926d3ab418"}}, {"head": {"id": "67d917be-49c8-4fb0-8e6d-31bf6b3c779d", "name": "jsonObjWithoutParam {\"lingxia\":\"file:./../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425093064394}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "347d9492-bc5b-47ef-aea5-39f384fe5a16", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:./../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425093152361}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cffd4dbb-09dd-4881-8044-e135115346d5", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425093654184}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d2af1a0-e6ac-47d0-8587-cf833bfac6c3", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425095518402}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a619db2c-7407-46d2-9cf2-ff61ae56708b", "name": "entry : default@MakePackInfo cost memory 0.11539459228515625", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425095646585}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4075e91-8c54-4a4d-86ed-5121583a76a5", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425093645956, "endTime": 652425095726582}, "additional": {"logType": "info", "children": [], "durationId": "071b3f22-8615-4696-8f57-50926d3ab418"}}, {"head": {"id": "202d11f4-b387-4a3e-95a5-5c455f0ac73f", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425098801538, "endTime": 652425100161568}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/syscap/default/rpcid.sc' does not exist."], "detailId": "2373f232-1357-4396-98d1-92f2914961f3", "logId": "ac4f834c-4f13-469f-96d7-e16165600dcc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2373f232-1357-4396-98d1-92f2914961f3", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425097450857}, "additional": {"logType": "detail", "children": [], "durationId": "202d11f4-b387-4a3e-95a5-5c455f0ac73f"}}, {"head": {"id": "bf6ad101-b002-47b9-a723-679e0e3596f0", "name": "jsonObjWithoutParam {\"lingxia\":\"file:./../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425097692246}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55347d9f-b4bc-47ee-94fb-2b3c99f7e016", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:./../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425097795516}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d0ea6e1-c93a-4e7c-aebc-0be62b3dd457", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425098815068}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c7b285a-9c7e-475e-806c-822ebd5c42e2", "name": "File: '/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425098941538}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7399ea6-e45c-495a-9880-146754bd0312", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425099125288}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ff61692-2c7e-4f4b-9568-805d13e40919", "name": "entry:default@SyscapTransform is not up-to-date, since the output file '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/syscap/default/rpcid.sc' does not exist.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425099696242}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59bd1af1-9be4-41bf-a7d4-b136e4b45e7e", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425099784449}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "398c87f1-8bf3-4a6d-a7c8-b44b5a9507e9", "name": "jsonObjWithoutParam {\"lingxia\":\"file:./../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425099867772}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ba47904-dc7b-4c91-b6ce-cc6c3543bbb1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:./../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425099924160}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91957fed-09c5-41f5-8b34-7584b448b41d", "name": "entry : default@SyscapTransform cost memory 0.128082275390625", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425100020661}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e02bf440-433e-4ad3-b0a2-80717e35ac3c", "name": "runTaskFromQueue task cost before running: 312 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425100106101}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac4f834c-4f13-469f-96d7-e16165600dcc", "name": "Finished :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425098801538, "endTime": 652425100161568, "totalTime": 1284663}, "additional": {"logType": "info", "children": [], "durationId": "202d11f4-b387-4a3e-95a5-5c455f0ac73f"}}, {"head": {"id": "fe6b36e5-e9a4-4590-9741-17a187bf8a02", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425102341487, "endTime": 652425103418450}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "67d3f7d8-de3c-4949-b45a-4d5c17dbfdac", "logId": "e6718c5a-e5f1-4efa-83c5-273e40420d17"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "67d3f7d8-de3c-4949-b45a-4d5c17dbfdac", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425101312640}, "additional": {"logType": "detail", "children": [], "durationId": "fe6b36e5-e9a4-4590-9741-17a187bf8a02"}}, {"head": {"id": "7e7919d0-a00b-4719-92a0-6066787a6056", "name": "jsonObjWithoutParam {\"lingxia\":\"file:./../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425101530076}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f83893e-c8ea-4823-991d-9a9c6b26202c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:./../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425101610409}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b4f63dc-6e6f-44c0-82e4-f24c14916b54", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425102354442}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "462886c4-0d90-4004-820f-1c614ddfd704", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425103244529}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1495ac00-f149-4bc0-a825-b60d7cf95807", "name": "entry : default@ProcessProfile cost memory 0.08466339111328125", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425103345331}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6718c5a-e5f1-4efa-83c5-273e40420d17", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425102341487, "endTime": 652425103418450}, "additional": {"logType": "info", "children": [], "durationId": "fe6b36e5-e9a4-4590-9741-17a187bf8a02"}}, {"head": {"id": "7e4733af-f221-4011-a385-bf474f8697aa", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425106171025, "endTime": 652425109788906}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The input file '/Users/<USER>/github/LingXia/examples/harmony/entry/oh-package.json5' has been changed."], "detailId": "0e4b17ff-bd19-49a4-8f59-a8c690481ec5", "logId": "2e5a4e25-be9c-4a36-9058-2e93cc61a102"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0e4b17ff-bd19-49a4-8f59-a8c690481ec5", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425104605855}, "additional": {"logType": "detail", "children": [], "durationId": "7e4733af-f221-4011-a385-bf474f8697aa"}}, {"head": {"id": "d02b54b7-fa67-42c4-a59d-98766fc41e80", "name": "jsonObjWithoutParam {\"lingxia\":\"file:./../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425104816370}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca8bb20f-d17a-44fc-bd42-1bd2a5a7cf83", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:./../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425104977507}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eda8d424-9375-42c3-af79-8eaa1f81cf6d", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425106184817}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de5ee0b8-6ba4-4233-ae48-33290aab9c75", "name": "entry:default@ProcessRouterMap is not up-to-date, since the input file '/Users/<USER>/github/LingXia/examples/harmony/entry/oh-package.json5' has been changed.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425108246779}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b14e3b65-dd4f-4ad0-84e6-dc7af4e21fea", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425108370738}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a012297a-6285-42e4-b940-485385caa696", "name": "jsonObjWithoutParam {\"lingxia\":\"file:./../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425108461434}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbc77dd1-f707-44aa-b626-a745daa860af", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:./../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425108517855}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "055fa916-a536-4f08-864b-488e0d5c3d2b", "name": "entry : default@ProcessRouterMap cost memory 0.187164306640625", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425109602515}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6dfd6547-59bc-4736-bb0e-943575ce9a92", "name": "runTaskFromQueue task cost before running: 322 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425109722374}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e5a4e25-be9c-4a36-9058-2e93cc61a102", "name": "Finished :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425106171025, "endTime": 652425109788906, "totalTime": 3524346}, "additional": {"logType": "info", "children": [], "durationId": "7e4733af-f221-4011-a385-bf474f8697aa"}}, {"head": {"id": "1858dd3f-f6c2-4437-acdb-7bd31770758b", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425113214481, "endTime": 652425114288738}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "2e9d7334-9bc6-44fc-846e-e499b7fd64ce", "logId": "8893a8cf-b0a5-4630-85f9-56923627956e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2e9d7334-9bc6-44fc-846e-e499b7fd64ce", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425112078656}, "additional": {"logType": "detail", "children": [], "durationId": "1858dd3f-f6c2-4437-acdb-7bd31770758b"}}, {"head": {"id": "4786a16a-de09-42fd-b1ab-d0aa4225780a", "name": "jsonObjWithoutParam {\"lingxia\":\"file:./../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425112392904}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1264396e-2cb1-4f3f-8015-a0450e57e9cd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:./../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425112516163}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fba7e833-ecdc-4f9c-a17a-06b24afdad4d", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425113228820}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5b5e4da-2da9-4bf8-9944-7368154aafd5", "name": "jsonObjWithoutParam {\"lingxia\":\"file:./../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425113399353}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3952aa68-20f4-43a7-9a15-b4a1bb0bbf98", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:./../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425113466187}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76c77f69-fb59-4b14-af9e-0214bebd26e9", "name": "entry : default@BuildNativeWithNinja cost memory 0.05487823486328125", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425114113972}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d30d9808-17b2-4391-8abb-be2455b08b84", "name": "runTaskFromQueue task cost before running: 326 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425114231351}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8893a8cf-b0a5-4630-85f9-56923627956e", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425113214481, "endTime": 652425114288738, "totalTime": 988965}, "additional": {"logType": "info", "children": [], "durationId": "1858dd3f-f6c2-4437-acdb-7bd31770758b"}}, {"head": {"id": "0d8389fd-1d09-4806-bed0-d35b5185add9", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425118180348, "endTime": 652425121279319}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "4091ffbd-cd85-4d3c-99ba-f46caa7c34a3", "logId": "8cb4379a-6cb7-4172-ba9e-10b2c2563385"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4091ffbd-cd85-4d3c-99ba-f46caa7c34a3", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425116358638}, "additional": {"logType": "detail", "children": [], "durationId": "0d8389fd-1d09-4806-bed0-d35b5185add9"}}, {"head": {"id": "1615a89b-278a-4133-8bb6-c38df9dc0434", "name": "jsonObjWithoutParam {\"lingxia\":\"file:./../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425116717651}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9475d67c-3ddd-408f-aa35-022fc633e8d8", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:./../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425116845749}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af460298-328e-4d60-b085-c4708d4ce8d9", "name": "restool module names: entry,lingxia; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425117371971}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c13b1fed-eb5a-4175-926f-0b8352f8ed7c", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425118956204}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e38c737-37ff-4c78-9ead-cb325307c11f", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425120185058}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52647463-3963-4351-a726-e2d0f223d507", "name": "entry : default@ProcessResource cost memory 0.137115478515625", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425120339359}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8cb4379a-6cb7-4172-ba9e-10b2c2563385", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425118180348, "endTime": 652425121279319}, "additional": {"logType": "info", "children": [], "durationId": "0d8389fd-1d09-4806-bed0-d35b5185add9"}}, {"head": {"id": "489a3332-939a-46b2-8d9f-53e1e0155c0e", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425126275021, "endTime": 652425138284096}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The input file '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/router_map/default/temp-router-map.json' has been changed."], "detailId": "be5b1036-f341-450d-a681-871cc6a16a9d", "logId": "27eb16c1-0b78-4aa6-b526-45d33315d85f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "be5b1036-f341-450d-a681-871cc6a16a9d", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425123834319}, "additional": {"logType": "detail", "children": [], "durationId": "489a3332-939a-46b2-8d9f-53e1e0155c0e"}}, {"head": {"id": "e9ade370-87f7-456f-a665-fbe8a593b23b", "name": "jsonObjWithoutParam {\"lingxia\":\"file:./../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425124072652}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a798e9b-2dad-4136-a872-0cd90856dbb1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:./../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425124161262}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06884598-d275-40e3-899a-71ff245b0628", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425126290727}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40bec61e-d35f-43a3-95b6-6179965e9012", "name": "entry:default@GenerateLoaderJson is not up-to-date, since the input file '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/router_map/default/temp-router-map.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425134282490}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9acc820f-21ec-4d45-b936-ab339daf99a0", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425134439945}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e9dcfb8-10e4-4255-99bb-4743ef3637b1", "name": "entry : default@GenerateLoaderJson cost memory 0.544769287109375", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425138050340}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b650ced9-7cae-4102-b761-6afdf8757d55", "name": "runTaskFromQueue task cost before running: 350 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425138197088}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27eb16c1-0b78-4aa6-b526-45d33315d85f", "name": "Finished :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425126275021, "endTime": 652425138284096, "totalTime": 11896566}, "additional": {"logType": "info", "children": [], "durationId": "489a3332-939a-46b2-8d9f-53e1e0155c0e"}}, {"head": {"id": "de796ddf-cd3e-41cf-a800-15c921a540a8", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425142846346, "endTime": 652425145773929}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "1dbe7086-190b-409b-8dec-dc2040e6d84c", "logId": "6cd0c345-09ca-43ab-9f2f-4f07b02d68b9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1dbe7086-190b-409b-8dec-dc2040e6d84c", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425141999842}, "additional": {"logType": "detail", "children": [], "durationId": "de796ddf-cd3e-41cf-a800-15c921a540a8"}}, {"head": {"id": "dd08ba7c-efdb-4192-9f50-d085462b039f", "name": "jsonObjWithoutParam {\"lingxia\":\"file:./../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425142232058}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c89d454c-6b03-4287-855a-73266ca4baf2", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:./../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425142320015}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6fdf746-6c65-43bb-a164-27720b9400b0", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425142856229}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5fd48833-8086-463c-a965-185e9a35d001", "name": "Incremental task entry:default@ProcessLibs pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425145519427}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86f73b0a-eca9-4320-a3ac-530d351dd934", "name": "entry : default@ProcessLibs cost memory 0.1286773681640625", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425145688957}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6cd0c345-09ca-43ab-9f2f-4f07b02d68b9", "name": "UP-TO-DATE :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425142846346, "endTime": 652425145773929}, "additional": {"logType": "info", "children": [], "durationId": "de796ddf-cd3e-41cf-a800-15c921a540a8"}}, {"head": {"id": "4467ece7-a230-44c0-a3c7-8d52253c3c7a", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425150256261, "endTime": 652425158479258}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "5fd99e75-5f36-437f-a562-34f31d37f5d4", "logId": "8dc81c28-13dd-44a6-91f6-7008d275aca1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5fd99e75-5f36-437f-a562-34f31d37f5d4", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425147614543}, "additional": {"logType": "detail", "children": [], "durationId": "4467ece7-a230-44c0-a3c7-8d52253c3c7a"}}, {"head": {"id": "8dfce108-ac4f-4c03-871d-c8fa4992862c", "name": "jsonObjWithoutParam {\"lingxia\":\"file:./../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425147919585}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3b36873-2bae-4300-82d2-8629f75fa256", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:./../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425148048758}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c589fd9-12ea-4660-b5a3-6b20a136c879", "name": "restool module names: entry,lingxia; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425148579558}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f63dc25-9590-4d66-878e-0556ad32bb95", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425150282365}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "009c032d-4ea3-45fd-98e9-8263109cc3e1", "name": "Incremental task entry:default@CompileResource pre-execution cost: 8 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425158190263}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9bde6f3b-6991-4147-8405-48fcc2679606", "name": "entry : default@CompileResource cost memory 0.70953369140625", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425158369938}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8dc81c28-13dd-44a6-91f6-7008d275aca1", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425150256261, "endTime": 652425158479258}, "additional": {"logType": "info", "children": [], "durationId": "4467ece7-a230-44c0-a3c7-8d52253c3c7a"}}, {"head": {"id": "b9ca5da7-1af2-43d8-b10b-5f802443fe63", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425164483794, "endTime": 652425165614663}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "822b99be-067f-43ab-a2d6-fb446377604a", "logId": "19a716e2-1d55-49dc-bf7e-bc978f1c8265"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "822b99be-067f-43ab-a2d6-fb446377604a", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425160158481}, "additional": {"logType": "detail", "children": [], "durationId": "b9ca5da7-1af2-43d8-b10b-5f802443fe63"}}, {"head": {"id": "20187410-3c8e-4f2a-bf53-0f112bf90e61", "name": "jsonObjWithoutParam {\"lingxia\":\"file:./../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425162705368}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48293c1b-60be-4f08-bd22-8924e6d0e402", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:./../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425162837205}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0a8bc71-70f0-4274-acf7-5420e6ae9d5d", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425164496121}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1e6eb75-5231-4e1f-8811-ea9c563e3421", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425164647286}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2140fce0-cc20-46ae-81ec-7fc82ffae79c", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425165446549}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f4fb9667-aeb6-4eeb-bf5d-706050dddb81", "name": "entry : default@DoNativeStrip cost memory 0.0850830078125", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425165548385}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19a716e2-1d55-49dc-bf7e-bc978f1c8265", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425164483794, "endTime": 652425165614663}, "additional": {"logType": "info", "children": [], "durationId": "b9ca5da7-1af2-43d8-b10b-5f802443fe63"}}, {"head": {"id": "105ef6af-fe7d-4a59-8b1e-24f61c5a5bbe", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425170065532, "endTime": 652425855054259}, "additional": {"children": ["e2f65885-0060-4142-a538-8cc1930d485d", "19dd6ec2-d85e-46f3-9d64-b571df5d04fe"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "802362a4-f948-4efb-bb95-0ec72fee37e2", "logId": "ef8ceccb-4a98-4d72-afee-42a4ebb8f486"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "802362a4-f948-4efb-bb95-0ec72fee37e2", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425166862376}, "additional": {"logType": "detail", "children": [], "durationId": "105ef6af-fe7d-4a59-8b1e-24f61c5a5bbe"}}, {"head": {"id": "cc46a872-f8c2-48ea-8439-5e85bce74511", "name": "jsonObjWithoutParam {\"lingxia\":\"file:./../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425167230070}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e57fb109-6ae2-4383-a05a-fc40a1586fe9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:./../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425167397278}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c39389f7-9413-4e08-9787-fe92b3c4ebb0", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425170082392}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30c1903e-26bf-4427-ba44-734c6cdfe406", "name": "Please confirm the symbolic link is valid:  /Users/<USER>/github/LingXia/examples/harmony/entry/oh_modules/lingxia", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425172118915}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "1d5fdcb7-d50d-4944-9945-4ce13cdbc7f2", "name": "The current module 'entry' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425172573338}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "8e61e27c-7d1b-46d8-9173-3c73afd45825", "name": "Please confirm the symbolic link is valid:  /Users/<USER>/github/LingXia/examples/harmony/entry/oh_modules/lingxia", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425177013012}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "339328c1-88f5-4049-b51b-52e890e86121", "name": "The current module 'entry' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425177487833}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "eb96ca9c-875a-4b6c-b4cc-24d68bec535f", "name": "Please confirm the symbolic link is valid:  /Users/<USER>/github/LingXia/examples/harmony/entry/oh_modules/lingxia", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425179353156}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "f4e3a077-3d22-4a1b-9887-7fef86da9271", "name": "The current module 'entry' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425179864429}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "e1d04d81-dda9-4506-a888-a0abfc010175", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425181732278}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e752d97-f867-4ab9-a174-fc03ea5192a3", "name": "Compile arkts with external api path: /Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/hms/ets", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425182210850}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c8fe530-747c-4acc-bf5d-e041f47c9fb4", "name": "default@CompileArkTS work[8] is submitted.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425182781493}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2f65885-0060-4142-a538-8cc1930d485d", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Worker4", "startTime": 652425183979410, "endTime": 652425854752524}, "additional": {"children": [], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "105ef6af-fe7d-4a59-8b1e-24f61c5a5bbe", "logId": "fe36a010-d1d0-44ae-ad99-605c951d8fb0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b1c058b3-2714-4fec-87e2-1ddf01d59652", "name": "default@CompileArkTS work[8] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425183627111}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6fd53933-dd59-4aab-925d-2780c19f2f31", "name": "A work dispatched to worker[7] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425183717066}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ab1c5ee-5f2c-499b-af06-39d0c0daae0f", "name": "A work dispatched to worker[6] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425183760615}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97b693eb-5a25-43d8-a62f-1c60fdb94f59", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425183795303}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9441ed6-6339-49f4-b385-9ac100ef865e", "name": "default@CompileArkTS work[8] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425183990587}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "156f9075-2870-4855-9252-36367f8a1bb4", "name": "default@CompileArkTS work[8] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425184165262}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "decb151f-b319-4624-bd91-2f3eb882e16a", "name": "CopyResources startTime: 652425184250825", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425184254967}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "813dcc62-8d73-4f3f-a793-5a139a204a9d", "name": "default@CompileArkTS work[9] is submitted.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425184439761}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19dd6ec2-d85e-46f3-9d64-b571df5d04fe", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425184598057, "endTime": 652425855058871}, "additional": {"children": [], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "105ef6af-fe7d-4a59-8b1e-24f61c5a5bbe"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "565ae691-48c3-42a3-ad21-9045861ec088", "name": "default@CompileArkTS work[9] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425185575305}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6206e90a-59df-4be5-8d10-beba6f92aa96", "name": "A work dispatched to worker[7] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425185692422}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d46d305b-eb71-4aea-a3be-3d5edfcf411b", "name": "Create  resident worker with id: 6.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425185765321}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a19daad2-996d-4585-92bb-1ddab70f27f1", "name": "default@CompileArkTS work[9] has been dispatched to worker[6].", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425187041233}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f5a5cad-4ad0-4cb8-9c92-8ca644af012b", "name": "default@CompileArkTS work[9] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425187196817}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e0a9ebe-b1ea-4020-aa07-f578f060d56f", "name": "entry : default@CompileArkTS cost memory 1.3932342529296875", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425187347150}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf8e2c32-ced7-448e-ba7c-3d5dad5d195d", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425192813561, "endTime": 652425196104938}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "f0f877e5-cb69-48e4-9404-655e08864cb5", "logId": "3c244e7e-1dc0-48b6-ba78-3bb240e9dc96"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f0f877e5-cb69-48e4-9404-655e08864cb5", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425189092445}, "additional": {"logType": "detail", "children": [], "durationId": "bf8e2c32-ced7-448e-ba7c-3d5dad5d195d"}}, {"head": {"id": "5fb7b5a8-bca7-43eb-a17c-d00588cfb56c", "name": "jsonObjWithoutParam {\"lingxia\":\"file:./../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425189427190}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff950eae-d510-4bff-bcf9-32d466521494", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:./../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425189543014}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b67617f-ae9b-43ba-849b-8d6d38528987", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425192832311}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d28c279-d1af-4171-b07a-165b590c7066", "name": "entry : default@BuildJS cost memory 0.1259613037109375", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425195879491}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c1c6d7a-09bc-4d55-80f2-cf490a34f1f4", "name": "runTaskFromQueue task cost before running: 408 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425196039554}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c244e7e-1dc0-48b6-ba78-3bb240e9dc96", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425192813561, "endTime": 652425196104938, "totalTime": 3197299}, "additional": {"logType": "info", "children": [], "durationId": "bf8e2c32-ced7-448e-ba7c-3d5dad5d195d"}}, {"head": {"id": "56548c9a-8d28-4802-85da-615124c8412c", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425200453449, "endTime": 652425202441593}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "cbfb9170-0a9d-4842-adf2-40c9f4c69e78", "logId": "432339ab-e614-4fba-a49c-b8318ab88c9f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cbfb9170-0a9d-4842-adf2-40c9f4c69e78", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425197333651}, "additional": {"logType": "detail", "children": [], "durationId": "56548c9a-8d28-4802-85da-615124c8412c"}}, {"head": {"id": "af394260-41c1-4d37-965c-c8a3895c6121", "name": "jsonObjWithoutParam {\"lingxia\":\"file:./../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425197608927}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56d4f36b-103d-4249-97cf-960d0b68d323", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:./../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425197720836}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9dc4580e-013c-45d1-9e6e-600f613d49bd", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425200475967}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efda290c-38cc-48ba-9046-5746d85e741e", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425200821614}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b875b551-cee7-40af-8c79-90fab39a9e8e", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425202203464}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55540377-671d-4a1a-ba18-1c9a89af6a4e", "name": "entry : default@CacheNativeLibs cost memory 0.09279632568359375", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425202359557}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "432339ab-e614-4fba-a49c-b8318ab88c9f", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425200453449, "endTime": 652425202441593}, "additional": {"logType": "info", "children": [], "durationId": "56548c9a-8d28-4802-85da-615124c8412c"}}, {"head": {"id": "6f99dea0-c59f-4674-b9d0-33414d92de05", "name": "worker[4] has one work error.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425854517784}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7346b343-f438-43b0-91b8-a91c95697529", "name": "default@CompileArkTS work[8] failed.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425854852719}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe36a010-d1d0-44ae-ad99-605c951d8fb0", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Worker4", "startTime": 652425183979410, "endTime": 652425854752524}, "additional": {"logType": "error", "children": [], "durationId": "e2f65885-0060-4142-a538-8cc1930d485d", "parent": "ef8ceccb-4a98-4d72-afee-42a4ebb8f486"}}, {"head": {"id": "ef8ceccb-4a98-4d72-afee-42a4ebb8f486", "name": "Failed :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425170065532, "endTime": 652425855054259}, "additional": {"logType": "error", "children": ["fe36a010-d1d0-44ae-ad99-605c951d8fb0"], "durationId": "105ef6af-fe7d-4a59-8b1e-24f61c5a5bbe"}}, {"head": {"id": "0ec9d336-ab63-4f62-9af3-c2073ee9372b", "name": "hvigor build process will be closed with an error.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425855285236}, "additional": {"logType": "debug", "children": [], "durationId": "105ef6af-fe7d-4a59-8b1e-24f61c5a5bbe"}}, {"head": {"id": "93c82f50-c8c5-4822-95f1-fecd09099669", "name": "ERROR: stacktrace = Error: ENOENT: no such file or directory, stat '/Users/<USER>/github/LingXia/examples/harmony/entry/oh_modules/lingxia'\n\u001b[31m1 ERROR: \u001b[31mArkTS:ERROR File: /Users/<USER>/github/LingXia/examples/harmony/entry/src/main/ets/entryability/EntryAbility.ets:4:23\n Cannot find module 'lingxia' or its corresponding type declarations.\n\n\u001b[31m> More Info: https://developer.huawei.com/consumer/en/doc/harmonyos-faqs-V5/faqs-compiling-and-building-4-V5\n\n\u001b[39m\n\u001b[31m2 ERROR: \u001b[31mArkTS:ERROR File: /Users/<USER>/github/LingXia/examples/harmony/entry/src/main/ets/pages/Index.ets:1:40\n Cannot find module 'lingxia' or its corresponding type declarations.\n\n\u001b[31m> More Info: https://developer.huawei.com/consumer/en/doc/harmonyos-faqs-V5/faqs-compiling-and-building-4-V5\n\n\u001b[39m\n\u001b[31mCOMPILE RESULT:FAIL {ERROR:3}\u001b[39m\n    at runArkPack (/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor-ohos-plugin/node_modules/@ohos/hvigor-arkts-compose/dist/src/arkts-pack.js:1:5438)\nError: ENOENT: no such file or directory, stat '/Users/<USER>/github/LingXia/examples/harmony/entry/oh_modules/lingxia'\n    at Object.statSync (node:fs:1688:3)\n    at resolveOhModules (/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor-ohos-plugin/node_modules/@ohos/hvigor-arkts-compose/dist/src/plugins/node-resolve/resolveOhModules.js:1:1531)\n    at resolveProjectOhModules (/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor-ohos-plugin/node_modules/@ohos/hvigor-arkts-compose/dist/src/plugins/node-resolve/resolveOhModules.js:1:1050)\n    at resolveAllOhModules (/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor-ohos-plugin/node_modules/@ohos/hvigor-arkts-compose/dist/src/plugins/node-resolve/resolveOhModules.js:1:760)\n    at Object.buildStart (/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor-ohos-plugin/node_modules/@ohos/hvigor-arkts-compose/dist/src/plugins/node-resolve/index.js:1:9657)\n    at /Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor-ohos-plugin/node_modules/@ohos/hvigor-arkts-base/node_modules/rollup/dist/shared/rollup.js:24828:40\n    at async Promise.all (index 2)\n    at async PluginDriver.hookParallel (/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor-ohos-plugin/node_modules/@ohos/hvigor-arkts-base/node_modules/rollup/dist/shared/rollup.js:24756:9)\n    at async /Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor-ohos-plugin/node_modules/@ohos/hvigor-arkts-base/node_modules/rollup/dist/shared/rollup.js:26031:13\n    at async catchUnfinishedHookActions (/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor-ohos-plugin/node_modules/@ohos/hvigor-arkts-base/node_modules/rollup/dist/shared/rollup.js:25197:24)", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425856325149}, "additional": {"logType": "debug", "children": [], "durationId": "105ef6af-fe7d-4a59-8b1e-24f61c5a5bbe"}}, {"head": {"id": "07b90333-015d-4984-b8f6-6ca8c49258b0", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425858898999, "endTime": 652425858995099}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7dd245fa-ab4d-42a0-a950-fd0a7ca0c31b", "logId": "f4272cc3-fd8a-4581-8b66-46803451933c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f4272cc3-fd8a-4581-8b66-46803451933c", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425858898999, "endTime": 652425858995099}, "additional": {"logType": "info", "children": [], "durationId": "07b90333-015d-4984-b8f6-6ca8c49258b0"}}, {"head": {"id": "6567b40f-2c5e-475c-bec2-bcf36e0aa328", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652424788592039, "endTime": 652425859361932}, "additional": {"time": {"year": 2025, "month": 7, "day": 31, "hour": 21, "minute": 37}, "completeCommand": "{\"prop\":[],\"_\":[\"assembleHap\"]};assembleHap", "hvigorVersion": "5.17.2", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "failed"}}, {"head": {"id": "3cc8ccc2-d7be-473d-a7a0-002f5e947c2a", "name": "BUILD FAILED in 1 s 71 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425859416426}, "additional": {"logType": "error", "children": []}}, {"head": {"id": "b74aec77-73f7-4bf8-a428-436da9acacc1", "name": "Update task entry:default@PreBuild input file:/Users/<USER>/github/LingXia/examples/harmony/AppScope/app.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425859871256}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2521f06a-2c71-44fa-8b1c-3ff92f387a57", "name": "Update task entry:default@PreBuild input file:/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/module.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425860119489}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d052fcb-7615-4f49-8213-91ce0d603f09", "name": "Update task entry:default@PreBuild input file:/Users/<USER>/github/LingXia/examples/harmony/build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425860277574}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc5a40e6-0efe-4584-9feb-43695363bacc", "name": "Update task entry:default@PreBuild input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425860422715}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5789802-0f3b-48dd-9d98-36dca6adfd1e", "name": "Update task entry:default@PreBuild input file:/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources/base/profile/main_pages.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425860554580}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe18cb90-f987-4bd2-8d3e-05384634846e", "name": "Update task entry:default@PreBuild input file:/Users/<USER>/github/LingXia/examples/harmony/hvigor/hvigor-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425860689745}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f493124-f9da-48e8-9b6a-e7c19ff956cb", "name": "Update task entry:default@PreBuild input file:/Users/<USER>/github/LingXia/examples/harmony/entry/oh-package.json5 cache from map.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425860823484}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "106de535-bdb1-4eca-a61f-884e229c6fd4", "name": "Update task entry:default@PreBuild input file:/Users/<USER>/github/LingXia/examples/harmony/oh-package.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425860947751}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e232bcb-137d-4e37-85dd-e06da5c0fcdf", "name": "Incremental task entry:default@PreBuild post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425861290291}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a15a509-ed71-4dbc-9b4c-c8446b1fc6c7", "name": "Update task entry:default@CreateModuleInfo output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/generated/profile/default/ModuleInfo.ts cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425861432276}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86f53270-32d3-4cc3-8c62-f18d9760ea0a", "name": "Incremental task entry:default@CreateModuleInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425861770314}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e248d351-0bd1-4bfb-a6e6-1d6b02acc127", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425861881970}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "040f5c12-a58d-4a36-9a54-fb942827fe1a", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425861975956}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54b8fa6a-3281-4f98-8421-34d13b043989", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425862124802}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "559c997b-7039-4f3c-9ab7-aad95045ac4e", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425862217809}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eecea7c2-8102-4af2-b8fb-2b5e3aaa3835", "name": "Update task entry:default@ProcessIntegratedHsp output file:/Users/<USER>/github/LingXia/examples/harmony/build/cache/default/integrated_hsp/integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425862668730}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19f53957-88d3-4d00-9ea3-108b6d2ef13d", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425862894743}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b9eaf38-9dfa-4bb9-aa06-89b0c7bb54bd", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425862985990}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4cbb6ccf-b138-4a08-ac2a-32bc2c964624", "name": "Update task entry:default@SyscapTransform input file:/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/toolchains/syscap_tool cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425863065652}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3929e6fe-8bed-45cf-8bf1-76b6b29111d8", "name": "Update task entry:default@SyscapTransform input file:/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/device-define cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425863216805}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f899343-cee9-478d-9d61-0876ecbcbb6a", "name": "Update task entry:default@SyscapTransform output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/syscap/default/rpcid.sc cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425863715048}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "952a4695-e02e-4112-ba9d-f8a54636973a", "name": "Incremental task entry:default@SyscapTransform post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425863923056}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fed8a29a-8e9e-44a9-bc31-880d55c8081a", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425863997592}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "347022ed-842d-4c93-aab4-1ed8d7323752", "name": "Update task entry:default@ProcessRouterMap input file:/Users/<USER>/github/LingXia/examples/harmony/entry/oh-package.json5 cache from map.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425864798929}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af830e85-a52f-4abf-9533-c9e156b500f2", "name": "Update task entry:default@ProcessRouterMap input file:/Users/<USER>/github/LingXia/examples/harmony/oh-package.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425864904763}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bacb1861-489d-418e-8d76-671d67531ca3", "name": "Update task entry:default@ProcessRouterMap input file:/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/module.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425865046593}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a132c79-be6d-488d-9aab-708bcb5d961e", "name": "Update task entry:default@ProcessRouterMap input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default/pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425865154205}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62ee89b2-7e95-4707-ae1d-6502ceec355a", "name": "Update task entry:default@ProcessRouterMap output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/router_map/default/temp-router-map.json cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425865262601}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3aa5a1d1-4747-45ee-84b7-1c0e3f993ba4", "name": "Update task entry:default@ProcessRouterMap output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/router_map/default/loader-router-map.json cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425865358230}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62ef59b2-3787-4e53-b316-1c23c0988581", "name": "Incremental task entry:default@ProcessRouterMap post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425865490132}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "adc57c05-2ba3-4c68-b330-88f6073e12c3", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425865553842}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "920f4381-3515-4825-b8f6-35fcc5774bfa", "name": "Update task entry:default@GenerateLoaderJson input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default/pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425867198942}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "937d16cf-ad51-426b-a7a0-3590f5fa6d98", "name": "Update task entry:default@GenerateLoaderJson input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/router_map/default/temp-router-map.json cache from map.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425867376415}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aff9d3ad-8df8-4672-b552-130d7e963d7d", "name": "Update task entry:default@GenerateLoaderJson output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default/loader.json cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425867484807}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0eb55cb7-d147-42b8-95c7-21ba8bcca162", "name": "Incremental task entry:default@GenerateLoaderJson post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425867632998}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59cc4b52-1c5c-494c-b3bb-b91fe97a7154", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425867698151}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "156207ea-69b3-444a-9112-4b10c949e0c0", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425867747685}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f648de4-b9c5-4c86-95a7-3d8beb520913", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425867793553}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59f51cca-aa04-4267-9794-810a1dcd7ba1", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425868961202}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c6c0b24-c7a7-43e1-b436-6fd6d26141d3", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425869308158}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b36e1aea-9c6f-4f2c-8d9f-2dc4cb0fb874", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425870668023}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "763e1fdd-2261-4778-a8f5-33aac727d376", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425870800224}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "072a8d10-bdc8-42c9-9e11-1e3a9f58bc58", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/base/profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425870894411}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "056be23d-239d-4ad8-99d5-86c20e1922f9", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425871101202}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f977e7c4-5574-456e-84fc-8aafad6d4eb6", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default/pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425871486401}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d0121ce-4735-47e7-885c-5c56a3b7d164", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/generated/profile/default/BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425871590988}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11bf3682-26de-4769-827f-3b1e2c781dd1", "name": "Update task entry:default@CompileArkTS output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader_out/default/ets cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425871723860}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0091cf3d-e57d-417c-a821-7c6e50248654", "name": "Incremental task entry:default@CompileArkTS post-execution cost:5 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425871912449}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbfefed2-33e8-4283-aae3-85a4e7c3ef29", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425872909654}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33acc679-a811-4664-981b-75b2a7216d18", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425873205445}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab13af54-6548-48ea-994d-250f044d4077", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425874504034}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eec09736-4a42-432e-ab36-81109abe2e0b", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425874624142}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f53c837-9f21-4961-90ef-f9594830454c", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/base/profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425874721012}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e63f30f-5555-48f6-b174-8db5edb3d3fb", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default/pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425874911091}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "799a954b-71d2-480a-a448-668dd91d9526", "name": "Update task entry:default@BuildJS output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader_out/default/js cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425875048850}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54cf73e2-35f3-4218-8288-1387730bed67", "name": "Incremental task entry:default@BuildJS post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425875362034}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55bb329d-794b-45ca-b0d9-69a4002bb86a", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425875531016}, "additional": {"logType": "debug", "children": []}}], "workLog": []}