{"version": "2.0", "ppid": 50563, "events": [{"head": {"id": "b3871f17-f4c0-488e-81a3-31989297a157", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425888914269}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ae1b65c-25b5-4684-be06-d45e17da0c3e", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425894159345}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6fbc141e-41b9-40fe-9085-2bdebcbef908", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425894448239}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60208656-053a-4a61-8d66-778b6fef1b9f", "name": "worker[6] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652425981813637}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d59b2aa8-3c9a-41e4-9df2-cd9a5998ca32", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487222569047}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2802119e-7fef-4bed-9cf6-4a4cb9422799", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487226884360, "endTime": 652487471390772}, "additional": {"children": ["95911927-a18f-4d28-b952-05b85a040b86", "994d153b-430c-4076-9684-b4511dc529c9", "cf317f6f-1bd2-48c6-a431-4c0c87187797", "7c92130a-4361-4d39-9809-153fc7fae863", "324c495b-c359-4ffc-8599-488832dcc4e4", "d7a2b22a-3abd-4313-ac7d-301e9b9c8a0c", "1b82651f-68bf-4c5a-846a-c51148c7e6cc"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "a62949a1-8f6e-4a66-8505-6cccfbbc07f5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "95911927-a18f-4d28-b952-05b85a040b86", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487226885696, "endTime": 652487244387287}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2802119e-7fef-4bed-9cf6-4a4cb9422799", "logId": "12051f1a-af0e-4a18-922e-f78f98b43074"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "994d153b-430c-4076-9684-b4511dc529c9", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487244411331, "endTime": 652487469012429}, "additional": {"children": ["1311ea24-ec25-4f4b-91ab-03160293097f", "537f0c25-7941-4dc3-810b-a4c845ab33a9", "e7a1f38a-f05e-404b-85bf-1d34ffdf1326", "712b7d0e-544f-4179-9d09-a0def58c405d", "0249fef5-9b9e-425e-a8fb-5ec012020ded", "73827ff7-84fe-4725-b79a-81f89e3f1245", "aa8acb94-0d77-40d3-b459-63f789e5c53b", "56925832-8722-4744-9658-0e1340c1b49e", "654f71c9-6bcb-460e-8a9f-62cee051200f"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2802119e-7fef-4bed-9cf6-4a4cb9422799", "logId": "734a7e25-490e-4dae-830b-1388ea5f9cc6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cf317f6f-1bd2-48c6-a431-4c0c87187797", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487469051575, "endTime": 652487471342376}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2802119e-7fef-4bed-9cf6-4a4cb9422799", "logId": "80471099-b623-4976-8fc7-d4fc039e8507"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7c92130a-4361-4d39-9809-153fc7fae863", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487471353813, "endTime": 652487471384286}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2802119e-7fef-4bed-9cf6-4a4cb9422799", "logId": "9f8842e4-6a2a-4984-b5bb-1b76906aad7e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "324c495b-c359-4ffc-8599-488832dcc4e4", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487229340425, "endTime": 652487229423924}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2802119e-7fef-4bed-9cf6-4a4cb9422799", "logId": "8b5f5f21-2817-4dcb-b8f1-854b83ecd9c6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8b5f5f21-2817-4dcb-b8f1-854b83ecd9c6", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487229340425, "endTime": 652487229423924}, "additional": {"logType": "info", "children": [], "durationId": "324c495b-c359-4ffc-8599-488832dcc4e4", "parent": "a62949a1-8f6e-4a66-8505-6cccfbbc07f5"}}, {"head": {"id": "d7a2b22a-3abd-4313-ac7d-301e9b9c8a0c", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487237076323, "endTime": 652487237108525}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2802119e-7fef-4bed-9cf6-4a4cb9422799", "logId": "e4073364-5039-4514-be3b-b22a02e303b8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e4073364-5039-4514-be3b-b22a02e303b8", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487237076323, "endTime": 652487237108525}, "additional": {"logType": "info", "children": [], "durationId": "d7a2b22a-3abd-4313-ac7d-301e9b9c8a0c", "parent": "a62949a1-8f6e-4a66-8505-6cccfbbc07f5"}}, {"head": {"id": "6ab9ec0c-2869-41d9-b52c-abd68f5b2716", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487237166874}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2b4650b-e908-48b8-ac13-f1cc09ac6c71", "name": "Cache service initialization finished in 7 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487244249452}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12051f1a-af0e-4a18-922e-f78f98b43074", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487226885696, "endTime": 652487244387287}, "additional": {"logType": "info", "children": [], "durationId": "95911927-a18f-4d28-b952-05b85a040b86", "parent": "a62949a1-8f6e-4a66-8505-6cccfbbc07f5"}}, {"head": {"id": "1311ea24-ec25-4f4b-91ab-03160293097f", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487250178671, "endTime": 652487250193018}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "994d153b-430c-4076-9684-b4511dc529c9", "logId": "3e6cea58-9663-4462-9f72-d817baf7e3d2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "537f0c25-7941-4dc3-810b-a4c845ab33a9", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487250219354, "endTime": 652487253468858}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "994d153b-430c-4076-9684-b4511dc529c9", "logId": "116f9270-337c-4141-a07f-f797f1a6e70c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e7a1f38a-f05e-404b-85bf-1d34ffdf1326", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487253485679, "endTime": 652487337341642}, "additional": {"children": ["3b094d84-5f42-4d1b-abbd-e861e1eb1213", "92263771-a1fb-49a4-95d1-86d48303b902", "0ea45f5e-f766-4727-a849-ab3adfc03562"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "994d153b-430c-4076-9684-b4511dc529c9", "logId": "0d242885-2d7b-4d26-8200-f1e548b0f937"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "712b7d0e-544f-4179-9d09-a0def58c405d", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487337362096, "endTime": 652487372875691}, "additional": {"children": ["de4916d3-caa5-4574-b857-2d728e099b1d", "b477b365-1e5c-453d-993f-ab927e10f287"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "994d153b-430c-4076-9684-b4511dc529c9", "logId": "468a46d0-cd19-47d1-85c5-dc310c2e8254"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0249fef5-9b9e-425e-a8fb-5ec012020ded", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487372889975, "endTime": 652487454061994}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "994d153b-430c-4076-9684-b4511dc529c9", "logId": "c87737b8-04f0-4a63-ba1b-c41562185dde"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "73827ff7-84fe-4725-b79a-81f89e3f1245", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487455080841, "endTime": 652487460341584}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "994d153b-430c-4076-9684-b4511dc529c9", "logId": "9c55a099-91db-4596-b4a9-ef751af2c1db"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aa8acb94-0d77-40d3-b459-63f789e5c53b", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487460371419, "endTime": 652487468657204}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "994d153b-430c-4076-9684-b4511dc529c9", "logId": "44296951-a14f-48dd-b4c6-c79c7d6b0e89"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "56925832-8722-4744-9658-0e1340c1b49e", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487468688000, "endTime": 652487468991509}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "994d153b-430c-4076-9684-b4511dc529c9", "logId": "de4ffc60-b88f-472e-af89-a785b8a39dc1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3e6cea58-9663-4462-9f72-d817baf7e3d2", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487250178671, "endTime": 652487250193018}, "additional": {"logType": "info", "children": [], "durationId": "1311ea24-ec25-4f4b-91ab-03160293097f", "parent": "734a7e25-490e-4dae-830b-1388ea5f9cc6"}}, {"head": {"id": "116f9270-337c-4141-a07f-f797f1a6e70c", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487250219354, "endTime": 652487253468858}, "additional": {"logType": "info", "children": [], "durationId": "537f0c25-7941-4dc3-810b-a4c845ab33a9", "parent": "734a7e25-490e-4dae-830b-1388ea5f9cc6"}}, {"head": {"id": "3b094d84-5f42-4d1b-abbd-e861e1eb1213", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487253985980, "endTime": 652487254005035}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e7a1f38a-f05e-404b-85bf-1d34ffdf1326", "logId": "1b5a309b-5b5e-4982-afe3-059ee35e1598"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1b5a309b-5b5e-4982-afe3-059ee35e1598", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487253985980, "endTime": 652487254005035}, "additional": {"logType": "info", "children": [], "durationId": "3b094d84-5f42-4d1b-abbd-e861e1eb1213", "parent": "0d242885-2d7b-4d26-8200-f1e548b0f937"}}, {"head": {"id": "92263771-a1fb-49a4-95d1-86d48303b902", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487255286699, "endTime": 652487336676354}, "additional": {"children": ["5506b6ba-d8e1-4a00-badc-d52b8a23d307", "11bd7f47-f12b-4548-8f8a-2c5138dcc6d2"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e7a1f38a-f05e-404b-85bf-1d34ffdf1326", "logId": "42ad5595-a87a-44c2-8820-ebdf11d661d0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5506b6ba-d8e1-4a00-badc-d52b8a23d307", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487255287813, "endTime": 652487263949350}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "92263771-a1fb-49a4-95d1-86d48303b902", "logId": "4de8825c-8791-4210-9950-d945ac5c4289"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "11bd7f47-f12b-4548-8f8a-2c5138dcc6d2", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487263973694, "endTime": 652487336661963}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "92263771-a1fb-49a4-95d1-86d48303b902", "logId": "3995ada0-4842-40ef-a014-a93d13ec4cd2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dff275fc-eff7-46c5-b674-be050ef458d5", "name": "hvigorfile, resolving /Users/<USER>/github/Ling<PERSON>ia/examples/harmony/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487255293158}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "239b0e3b-6a63-4eff-aa27-577ee7c3e402", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487263795359}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4de8825c-8791-4210-9950-d945ac5c4289", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487255287813, "endTime": 652487263949350}, "additional": {"logType": "info", "children": [], "durationId": "5506b6ba-d8e1-4a00-badc-d52b8a23d307", "parent": "42ad5595-a87a-44c2-8820-ebdf11d661d0"}}, {"head": {"id": "2b41670c-d471-484e-b081-c36ebb3ad7cf", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487263989185}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42943a23-4bda-4032-ae67-89a6e44f1391", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487270511016}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9b1ceea-b1fc-443d-a917-d4b6b027b65b", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487270676705}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4293fc24-9705-4e6e-95a3-aa966632af4c", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487270836829}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b563d563-678d-445b-a435-29568ccfdf06", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487270943095}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec8da7e7-fd59-47ff-a356-77f1ade85a31", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487272165622}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7b5403e-c8b3-4038-894c-a1af27a031a4", "name": "not found resModel json file in : /Users/<USER>/github/LingXia/examples/harmony/entry/src/ohosTest/module.json5", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487272601700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2909f256-5295-4c1c-91b9-e8bb1cac2e2f", "name": "not found resModel json file in : /Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/ohosTest/module.json5", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487275795173}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51d8b454-2faf-4695-874c-120957f7774b", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487285498202}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4cb0c2d-4fd2-41cf-b1bb-c113dd1a54ef", "name": "Sdk init in 42 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487321332880}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1eed66ed-ef7d-4157-8cb5-a03459305d1a", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487321474401}, "additional": {"time": {"year": 2025, "month": 7, "day": 31, "hour": 21, "minute": 38}, "markType": "other"}}, {"head": {"id": "06d5133e-c945-4000-a5ed-02bd5713066f", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487321531262}, "additional": {"time": {"year": 2025, "month": 7, "day": 31, "hour": 21, "minute": 38}, "markType": "other"}}, {"head": {"id": "e29dc677-05ab-4ebe-856e-8ca4a370f79b", "name": "Project task initialization takes 14 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487336338779}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7dc0f23-0f84-4f54-96be-043451e82dc1", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487336478685}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18c0a28a-7c19-4336-baef-84976b0d1ca6", "name": "hvigorfile, no custom plugins were found in /Users/<USER>/github/LingXia/examples/harmony/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487336548736}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b2531f8-34b5-4996-b782-c87fbb1a3727", "name": "hvigorfile, resolve finished /Users/<USER>/github/Ling<PERSON>ia/examples/harmony/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487336612397}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3995ada0-4842-40ef-a014-a93d13ec4cd2", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487263973694, "endTime": 652487336661963}, "additional": {"logType": "info", "children": [], "durationId": "11bd7f47-f12b-4548-8f8a-2c5138dcc6d2", "parent": "42ad5595-a87a-44c2-8820-ebdf11d661d0"}}, {"head": {"id": "42ad5595-a87a-44c2-8820-ebdf11d661d0", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487255286699, "endTime": 652487336676354}, "additional": {"logType": "info", "children": ["4de8825c-8791-4210-9950-d945ac5c4289", "3995ada0-4842-40ef-a014-a93d13ec4cd2"], "durationId": "92263771-a1fb-49a4-95d1-86d48303b902", "parent": "0d242885-2d7b-4d26-8200-f1e548b0f937"}}, {"head": {"id": "0ea45f5e-f766-4727-a849-ab3adfc03562", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487337296380, "endTime": 652487337316035}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e7a1f38a-f05e-404b-85bf-1d34ffdf1326", "logId": "79f0b711-05aa-4963-a5d1-689fd28026cc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "79f0b711-05aa-4963-a5d1-689fd28026cc", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487337296380, "endTime": 652487337316035}, "additional": {"logType": "info", "children": [], "durationId": "0ea45f5e-f766-4727-a849-ab3adfc03562", "parent": "0d242885-2d7b-4d26-8200-f1e548b0f937"}}, {"head": {"id": "0d242885-2d7b-4d26-8200-f1e548b0f937", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487253485679, "endTime": 652487337341642}, "additional": {"logType": "info", "children": ["1b5a309b-5b5e-4982-afe3-059ee35e1598", "42ad5595-a87a-44c2-8820-ebdf11d661d0", "79f0b711-05aa-4963-a5d1-689fd28026cc"], "durationId": "e7a1f38a-f05e-404b-85bf-1d34ffdf1326", "parent": "734a7e25-490e-4dae-830b-1388ea5f9cc6"}}, {"head": {"id": "de4916d3-caa5-4574-b857-2d728e099b1d", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487337830353, "endTime": 652487355799220}, "additional": {"children": ["1f87b2e0-77be-4e9d-ad07-3ebaadda3eb7", "ac6470a5-d81f-487b-9b1a-267f34283e07", "f390dc91-9648-4f2c-bdea-173ac234b8f6"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "712b7d0e-544f-4179-9d09-a0def58c405d", "logId": "d847189e-f979-4c80-a063-329fccc3c111"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1f87b2e0-77be-4e9d-ad07-3ebaadda3eb7", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487340186981, "endTime": 652487340210643}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "de4916d3-caa5-4574-b857-2d728e099b1d", "logId": "3e65dfd2-6c58-41a4-af5b-f58d6a63c26b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3e65dfd2-6c58-41a4-af5b-f58d6a63c26b", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487340186981, "endTime": 652487340210643}, "additional": {"logType": "info", "children": [], "durationId": "1f87b2e0-77be-4e9d-ad07-3ebaadda3eb7", "parent": "d847189e-f979-4c80-a063-329fccc3c111"}}, {"head": {"id": "ac6470a5-d81f-487b-9b1a-267f34283e07", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487341456371, "endTime": 652487354751367}, "additional": {"children": ["25e417f7-75fd-48a4-bc8e-2618db0dcaeb", "0a8b22dc-e6b7-451c-8c59-7c650e6b35c3"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "de4916d3-caa5-4574-b857-2d728e099b1d", "logId": "64d95b45-bf3b-4d02-a97b-05332c7dba3e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "25e417f7-75fd-48a4-bc8e-2618db0dcaeb", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487341457503, "endTime": 652487346997839}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ac6470a5-d81f-487b-9b1a-267f34283e07", "logId": "e3919a32-d89c-48ba-b445-3d4d405cf791"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0a8b22dc-e6b7-451c-8c59-7c650e6b35c3", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487347016230, "endTime": 652487354737909}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ac6470a5-d81f-487b-9b1a-267f34283e07", "logId": "a74ffead-2cb4-42e2-8b64-1662e67c282c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "de47570b-7f3c-4313-bf18-8fc00443c807", "name": "hvigorfile, resolving /Users/<USER>/github/Ling<PERSON>ia/examples/harmony/entry/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487341462753}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a132e72-ffef-4d7c-b0d1-4f893291949a", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487346855838}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3919a32-d89c-48ba-b445-3d4d405cf791", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487341457503, "endTime": 652487346997839}, "additional": {"logType": "info", "children": [], "durationId": "25e417f7-75fd-48a4-bc8e-2618db0dcaeb", "parent": "64d95b45-bf3b-4d02-a97b-05332c7dba3e"}}, {"head": {"id": "65e77846-f698-451e-8199-7889823d6f53", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487347039778}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b193e22-f749-4d4d-883e-b46644b67406", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487351322790}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5589a95-42c9-45cd-b855-b4565017a43b", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487351440966}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da65cbc0-e30a-4ebc-a252-f1127b9f97ac", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487351651471}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc5415a7-9382-40fb-8194-89e2bfc64730", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487351812029}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2cacf275-5337-4ff8-9cb8-f5e9b5ee72f5", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487351877349}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aef7a45c-72d2-4a98-8d66-a17c07395e7d", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487351925140}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae3902f5-5ef6-4fc4-8cc9-8cbfef3f0467", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487351982779}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10757f09-eb52-4a02-940a-c0a1dbd0a259", "name": "Module entry task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487354421731}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "243b92ff-2231-47ea-b0b3-502444610975", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487354577476}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad0f4dc5-f996-4921-96f5-73906b3d5df5", "name": "hvigorfile, no custom plugins were found in /Users/<USER>/github/LingXia/examples/harmony/entry/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487354646963}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "626ba067-80fa-4a98-865c-5b01563ab3e1", "name": "hvigorfile, resolve finished /Users/<USER>/github/LingXia/examples/harmony/entry/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487354695720}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a74ffead-2cb4-42e2-8b64-1662e67c282c", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487347016230, "endTime": 652487354737909}, "additional": {"logType": "info", "children": [], "durationId": "0a8b22dc-e6b7-451c-8c59-7c650e6b35c3", "parent": "64d95b45-bf3b-4d02-a97b-05332c7dba3e"}}, {"head": {"id": "64d95b45-bf3b-4d02-a97b-05332c7dba3e", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487341456371, "endTime": 652487354751367}, "additional": {"logType": "info", "children": ["e3919a32-d89c-48ba-b445-3d4d405cf791", "a74ffead-2cb4-42e2-8b64-1662e67c282c"], "durationId": "ac6470a5-d81f-487b-9b1a-267f34283e07", "parent": "d847189e-f979-4c80-a063-329fccc3c111"}}, {"head": {"id": "f390dc91-9648-4f2c-bdea-173ac234b8f6", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487355757657, "endTime": 652487355780285}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "de4916d3-caa5-4574-b857-2d728e099b1d", "logId": "00531d93-3e03-44e9-82d8-cef038b5181d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "00531d93-3e03-44e9-82d8-cef038b5181d", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487355757657, "endTime": 652487355780285}, "additional": {"logType": "info", "children": [], "durationId": "f390dc91-9648-4f2c-bdea-173ac234b8f6", "parent": "d847189e-f979-4c80-a063-329fccc3c111"}}, {"head": {"id": "d847189e-f979-4c80-a063-329fccc3c111", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487337830353, "endTime": 652487355799220}, "additional": {"logType": "info", "children": ["3e65dfd2-6c58-41a4-af5b-f58d6a63c26b", "64d95b45-bf3b-4d02-a97b-05332c7dba3e", "00531d93-3e03-44e9-82d8-cef038b5181d"], "durationId": "de4916d3-caa5-4574-b857-2d728e099b1d", "parent": "468a46d0-cd19-47d1-85c5-dc310c2e8254"}}, {"head": {"id": "b477b365-1e5c-453d-993f-ab927e10f287", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487356203922, "endTime": 652487372859497}, "additional": {"children": ["889482cd-4d7e-48a7-84de-80e1b30ad5fa", "6790e138-eaf0-4507-be54-9bfaa7013fa9", "1c667921-d1ae-4e8e-adc0-b441e6b87692"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "712b7d0e-544f-4179-9d09-a0def58c405d", "logId": "d47b18b2-384f-4131-a9ad-f540db24125b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "889482cd-4d7e-48a7-84de-80e1b30ad5fa", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487358137193, "endTime": 652487358153087}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b477b365-1e5c-453d-993f-ab927e10f287", "logId": "e6347ebe-1daf-4230-bb91-c9d9911c8338"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e6347ebe-1daf-4230-bb91-c9d9911c8338", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487358137193, "endTime": 652487358153087}, "additional": {"logType": "info", "children": [], "durationId": "889482cd-4d7e-48a7-84de-80e1b30ad5fa", "parent": "d47b18b2-384f-4131-a9ad-f540db24125b"}}, {"head": {"id": "6790e138-eaf0-4507-be54-9bfaa7013fa9", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487360463314, "endTime": 652487371703717}, "additional": {"children": ["f353ba68-36b0-49a8-af97-f43db66ca910", "b707c2d9-8f69-4252-ac9d-60828ada3452"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b477b365-1e5c-453d-993f-ab927e10f287", "logId": "38bcda02-d83c-4f7c-bc97-97ff3d71cbb5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f353ba68-36b0-49a8-af97-f43db66ca910", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487360464633, "endTime": 652487364921145}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6790e138-eaf0-4507-be54-9bfaa7013fa9", "logId": "a1ef6f0a-f3db-445e-98d9-a807034f856f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b707c2d9-8f69-4252-ac9d-60828ada3452", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487364940144, "endTime": 652487371690222}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6790e138-eaf0-4507-be54-9bfaa7013fa9", "logId": "10db0d2d-5879-4635-b235-abc30adc2307"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "981c36e8-1e5b-41a1-abdd-00ad245661af", "name": "hvigorfile, resolving /Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487360471622}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc763b89-94b6-450c-bd4b-52f492afadbb", "name": "hvigorfile, require result:  { default: { system: [Function: harTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487364781894}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1ef6f0a-f3db-445e-98d9-a807034f856f", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487360464633, "endTime": 652487364921145}, "additional": {"logType": "info", "children": [], "durationId": "f353ba68-36b0-49a8-af97-f43db66ca910", "parent": "38bcda02-d83c-4f7c-bc97-97ff3d71cbb5"}}, {"head": {"id": "f72980ff-c11b-4273-ad4b-3f29c600128b", "name": "hvigorfile, binding system plugins [Function: harTasks]", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487364953564}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b902a652-99d4-47a6-bbdd-1104eef212e2", "name": "Start initialize module-target build option map, moduleName=lingxia, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487368768048}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "347da4ee-8e2f-4dd8-a79f-0df519c306cd", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487368906682}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a750113c-ca70-4a0f-87f9-b64773286e84", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487369091354}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dcdee87c-675d-4b8c-8842-646974fecc9d", "name": "Module 'lingxia' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487369230676}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78ce9882-be0d-4d57-b581-cc06a8d17566", "name": "Module 'lingxia' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487369294922}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "273be6da-c184-49b3-8a09-0e06c430db3d", "name": "End initialize module-target build option map, moduleName=lingxia", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487369342553}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5204a7c1-ba18-4db9-981f-7623083ce42f", "name": "Module 'lingxia' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487369404344}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9591a646-6a11-405d-8158-318e0edd0f72", "name": "Module lingxia task initialization takes 1 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487371385807}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a22327e4-60c1-475f-ae99-d074d558c059", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487371519705}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b04be0e-a520-433c-8e26-33d511e8157f", "name": "hvigorfile, no custom plugins were found in /Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487371582767}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60ae449d-cb3d-4904-9f7a-ef9a122c5fc0", "name": "hvigorfile, resolve finished /Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487371640443}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10db0d2d-5879-4635-b235-abc30adc2307", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487364940144, "endTime": 652487371690222}, "additional": {"logType": "info", "children": [], "durationId": "b707c2d9-8f69-4252-ac9d-60828ada3452", "parent": "38bcda02-d83c-4f7c-bc97-97ff3d71cbb5"}}, {"head": {"id": "38bcda02-d83c-4f7c-bc97-97ff3d71cbb5", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487360463314, "endTime": 652487371703717}, "additional": {"logType": "info", "children": ["a1ef6f0a-f3db-445e-98d9-a807034f856f", "10db0d2d-5879-4635-b235-abc30adc2307"], "durationId": "6790e138-eaf0-4507-be54-9bfaa7013fa9", "parent": "d47b18b2-384f-4131-a9ad-f540db24125b"}}, {"head": {"id": "1c667921-d1ae-4e8e-adc0-b441e6b87692", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487372820808, "endTime": 652487372837285}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b477b365-1e5c-453d-993f-ab927e10f287", "logId": "6ebf157f-94f6-4e68-8a26-e89ad04eee7d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6ebf157f-94f6-4e68-8a26-e89ad04eee7d", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487372820808, "endTime": 652487372837285}, "additional": {"logType": "info", "children": [], "durationId": "1c667921-d1ae-4e8e-adc0-b441e6b87692", "parent": "d47b18b2-384f-4131-a9ad-f540db24125b"}}, {"head": {"id": "d47b18b2-384f-4131-a9ad-f540db24125b", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487356203922, "endTime": 652487372859497}, "additional": {"logType": "info", "children": ["e6347ebe-1daf-4230-bb91-c9d9911c8338", "38bcda02-d83c-4f7c-bc97-97ff3d71cbb5", "6ebf157f-94f6-4e68-8a26-e89ad04eee7d"], "durationId": "b477b365-1e5c-453d-993f-ab927e10f287", "parent": "468a46d0-cd19-47d1-85c5-dc310c2e8254"}}, {"head": {"id": "468a46d0-cd19-47d1-85c5-dc310c2e8254", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487337362096, "endTime": 652487372875691}, "additional": {"logType": "info", "children": ["d847189e-f979-4c80-a063-329fccc3c111", "d47b18b2-384f-4131-a9ad-f540db24125b"], "durationId": "712b7d0e-544f-4179-9d09-a0def58c405d", "parent": "734a7e25-490e-4dae-830b-1388ea5f9cc6"}}, {"head": {"id": "71b732b5-8eef-4b28-b9fc-d0d57bd9e252", "name": "watch files: [\n  '/Users/<USER>/github/LingXia/examples/harmony/hvigorfile.ts',\n  '/Users/<USER>/github/LingXia/examples/harmony/entry/hvigorfile.ts',\n  '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/hvigorfile.ts',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor-ohos-plugin/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor-ohos-plugin/src/plugin/factory/plugin-factory.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/log4js.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/debug/src/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/debug/src/node.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/debug/src/common.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/ms/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/rfdc/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/configuration.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/layouts.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/date-format/lib/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/levels.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/clustering.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/LoggingEvent.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/flatted/cjs/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/adapters.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/console.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/stdout.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/stderr.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/logLevelFilter.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/categoryFilter.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/noLogFilter.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/file.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/lib/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/lib/RollingFileWriteStream.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/fs/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/universalify/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/graceful-fs/graceful-fs.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/graceful-fs/polyfills.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/graceful-fs/legacy-streams.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/graceful-fs/clone.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/copy-sync/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/copy-sync/copy-sync.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/mkdirs/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/mkdirs/mkdirs.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/mkdirs/win32.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/mkdirs/mkdirs-sync.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/util/utimes.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/util/stat.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/copy/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/copy/copy.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/path-exists/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/empty/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/remove/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/remove/rimraf.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/file.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/link.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/symlink.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/symlink-paths.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/symlink-type.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/json/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/json/jsonfile.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/jsonfile/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/json/output-json.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/json/output-json-sync.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/move-sync/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/move-sync/move-sync.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/move/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/move/move.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/output/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/lib/now.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/lib/fileNameFormatter.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/lib/fileNameParser.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/lib/moveAndMaybeCompressFile.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/lib/RollingFileStream.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/lib/DateRollingFileStream.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/dateFile.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/fileSync.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/tcp.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/categories.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/logger.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/connect-logger.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/recording.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/src/common/util/local-file-writer.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/fs/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/universalify/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/copy/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/copy/copy.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/mkdirs/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/mkdirs/make-dir.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/mkdirs/utils.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/path-exists/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/util/utimes.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/util/stat.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/copy/copy-sync.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/empty/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/remove/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/ensure/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/ensure/file.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/ensure/link.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/ensure/symlink.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/ensure/symlink-paths.js',\n  ... 1869 more items\n]", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487398387843}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b093498c-fe74-41a1-afc1-5f178376c2dc", "name": "hvigorfile, resolve hvigorfile dependencies in 81 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487453911397}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c87737b8-04f0-4a63-ba1b-c41562185dde", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487372889975, "endTime": 652487454061994}, "additional": {"logType": "info", "children": [], "durationId": "0249fef5-9b9e-425e-a8fb-5ec012020ded", "parent": "734a7e25-490e-4dae-830b-1388ea5f9cc6"}}, {"head": {"id": "654f71c9-6bcb-460e-8a9f-62cee051200f", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487454763036, "endTime": 652487455066482}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "994d153b-430c-4076-9684-b4511dc529c9", "logId": "1a36be42-d263-4312-991f-17edf6f9e38e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "73ca1426-9a17-425b-819f-735b4b63fc14", "name": "project has submodules:entry,lingxia", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487454798225}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c4ff67a-3d97-4d6c-8649-d02c71a1cc7d", "name": "module:lingxia no need to execute packageHap", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487454991085}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a36be42-d263-4312-991f-17edf6f9e38e", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487454763036, "endTime": 652487455066482}, "additional": {"logType": "info", "children": [], "durationId": "654f71c9-6bcb-460e-8a9f-62cee051200f", "parent": "734a7e25-490e-4dae-830b-1388ea5f9cc6"}}, {"head": {"id": "740a1b7d-829c-4125-b194-d3362d63d0f9", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487455682675}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed9419b8-41ae-4d26-b30e-6ef34942a808", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487459865303}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c55a099-91db-4596-b4a9-ef751af2c1db", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487455080841, "endTime": 652487460341584}, "additional": {"logType": "info", "children": [], "durationId": "73827ff7-84fe-4725-b79a-81f89e3f1245", "parent": "734a7e25-490e-4dae-830b-1388ea5f9cc6"}}, {"head": {"id": "7e9c37bc-b6e0-4dfd-98db-b38037beedc8", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487460393604}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "00af7d75-1e87-4b08-bb01-fa1e0280f57e", "name": "Module harmony Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487461857592}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fbcdacf8-92ce-4a2a-9e14-6dd88febde97", "name": "Module harmony's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487461988251}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd441dc2-2821-4045-be7c-2120f3c085f5", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487462290662}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03d1e459-c1bf-45c7-80a3-5a0e6d28425e", "name": "Please confirm the symbolic link is valid:  /Users/<USER>/github/LingXia/examples/harmony/entry/oh_modules/lingxia", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487462872477}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "024b915a-4d48-4acd-b20d-b86de085b246", "name": "The current module 'entry' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487463413067}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "90bd470a-4167-4443-b690-3b165c4a2c0c", "name": "Module entry Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487463838087}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d2af6c4-adfd-4c1c-8fe7-a3d4511634a1", "name": "Module entry's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487463906118}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63d3244f-d39c-4795-8c48-b421de23bfe3", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487466061636}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10fa474b-8817-4563-98fe-e8579aca5835", "name": "Module ling<PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487466573510}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2037ad81-8047-49a5-84e8-47832c1d3d93", "name": "Module lingxia's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487466657087}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44296951-a14f-48dd-b4c6-c79c7d6b0e89", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487460371419, "endTime": 652487468657204}, "additional": {"logType": "info", "children": [], "durationId": "aa8acb94-0d77-40d3-b459-63f789e5c53b", "parent": "734a7e25-490e-4dae-830b-1388ea5f9cc6"}}, {"head": {"id": "df985de1-e0a4-4b5e-a984-0b9048504ba9", "name": "Configuration phase cost:219 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487468811044}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de4ffc60-b88f-472e-af89-a785b8a39dc1", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487468688000, "endTime": 652487468991509}, "additional": {"logType": "info", "children": [], "durationId": "56925832-8722-4744-9658-0e1340c1b49e", "parent": "734a7e25-490e-4dae-830b-1388ea5f9cc6"}}, {"head": {"id": "734a7e25-490e-4dae-830b-1388ea5f9cc6", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487244411331, "endTime": 652487469012429}, "additional": {"logType": "info", "children": ["3e6cea58-9663-4462-9f72-d817baf7e3d2", "116f9270-337c-4141-a07f-f797f1a6e70c", "0d242885-2d7b-4d26-8200-f1e548b0f937", "468a46d0-cd19-47d1-85c5-dc310c2e8254", "c87737b8-04f0-4a63-ba1b-c41562185dde", "9c55a099-91db-4596-b4a9-ef751af2c1db", "44296951-a14f-48dd-b4c6-c79c7d6b0e89", "de4ffc60-b88f-472e-af89-a785b8a39dc1", "1a36be42-d263-4312-991f-17edf6f9e38e"], "durationId": "994d153b-430c-4076-9684-b4511dc529c9", "parent": "a62949a1-8f6e-4a66-8505-6cccfbbc07f5"}}, {"head": {"id": "1b82651f-68bf-4c5a-846a-c51148c7e6cc", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487471299605, "endTime": 652487471320991}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2802119e-7fef-4bed-9cf6-4a4cb9422799", "logId": "c8f88e4d-1352-47da-80fe-7955109ae027"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c8f88e4d-1352-47da-80fe-7955109ae027", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487471299605, "endTime": 652487471320991}, "additional": {"logType": "info", "children": [], "durationId": "1b82651f-68bf-4c5a-846a-c51148c7e6cc", "parent": "a62949a1-8f6e-4a66-8505-6cccfbbc07f5"}}, {"head": {"id": "80471099-b623-4976-8fc7-d4fc039e8507", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487469051575, "endTime": 652487471342376}, "additional": {"logType": "info", "children": [], "durationId": "cf317f6f-1bd2-48c6-a431-4c0c87187797", "parent": "a62949a1-8f6e-4a66-8505-6cccfbbc07f5"}}, {"head": {"id": "9f8842e4-6a2a-4984-b5bb-1b76906aad7e", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487471353813, "endTime": 652487471384286}, "additional": {"logType": "info", "children": [], "durationId": "7c92130a-4361-4d39-9809-153fc7fae863", "parent": "a62949a1-8f6e-4a66-8505-6cccfbbc07f5"}}, {"head": {"id": "a62949a1-8f6e-4a66-8505-6cccfbbc07f5", "name": "init", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487226884360, "endTime": 652487471390772}, "additional": {"logType": "info", "children": ["12051f1a-af0e-4a18-922e-f78f98b43074", "734a7e25-490e-4dae-830b-1388ea5f9cc6", "80471099-b623-4976-8fc7-d4fc039e8507", "9f8842e4-6a2a-4984-b5bb-1b76906aad7e", "8b5f5f21-2817-4dcb-b8f1-854b83ecd9c6", "e4073364-5039-4514-be3b-b22a02e303b8", "c8f88e4d-1352-47da-80fe-7955109ae027"], "durationId": "2802119e-7fef-4bed-9cf6-4a4cb9422799"}}, {"head": {"id": "421be4fc-74f9-4a4f-850c-80a69308188c", "name": "Configuration task cost before running: 247 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487471657832}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec03bb9a-3236-4fe9-9a62-b0e191a5e08a", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487474898518, "endTime": 652487480779403}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": ["The input file '/Users/<USER>/github/LingXia/examples/harmony/entry/oh-package.json5' has been changed."], "detailId": "44cc244c-8b9c-403f-99c2-a3fce723363b", "logId": "87cc32dc-fa48-4cd3-8475-51fd0443b4d3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "44cc244c-8b9c-403f-99c2-a3fce723363b", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487472957117}, "additional": {"logType": "detail", "children": [], "durationId": "ec03bb9a-3236-4fe9-9a62-b0e191a5e08a"}}, {"head": {"id": "6af367ce-435a-4f3d-bf8d-f4b6e6e7b588", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487473219679}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c595b79b-ccfe-4e38-a784-377b98237e5f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487473334339}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ae5e59f-f9a9-47cd-acf6-239b12f1475b", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487474913005}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d8e6172-9930-4a4c-b1f4-99d42935d9e7", "name": "entry:default@PreBuild is not up-to-date, since the input file '/Users/<USER>/github/LingXia/examples/harmony/entry/oh-package.json5' has been changed.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487477128700}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dde7d9e2-bec2-44b0-a53c-eb4cc95f56df", "name": "Incremental task entry:default@PreBuild pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487477289342}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "621d83af-253d-4104-ac5b-c670142e2cf1", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487477395934}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a1f7a695-3d5d-415c-9241-285a471203c1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487477461097}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "955921a2-8730-4337-9fe0-816dc26280ad", "name": "current product is not Atomic service.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487479981857}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5c56ca7-4a38-4dee-bcab-9c0ebf3ddc1a", "name": "Use tool [darwin: JAVA_HOME, CLASSPATH]\n [\n  {\n    JAVA_HOME: '/Applications/Android Studio.app/Contents/jbr/Contents/Home'\n  },\n  { CLASSPATH: undefined }\n]", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487480166364}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0695a64-04e6-4289-b5f6-16016a44e85b", "name": "Use tool [darwin: NODE_HOME]\n [ { NODE_HOME: undefined } ]", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487480307382}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5ad0076-e359-4c8e-aff8-e37582d5b84a", "name": "entry : default@PreBuild cost memory 0.3872222900390625", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487480574870}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a7a78a2-1b20-4279-8f22-70b4fbc5b09d", "name": "runTaskFromQueue task cost before running: 256 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487480698484}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87cc32dc-fa48-4cd3-8475-51fd0443b4d3", "name": "Finished :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487474898518, "endTime": 652487480779403, "totalTime": 5772177}, "additional": {"logType": "info", "children": [], "durationId": "ec03bb9a-3236-4fe9-9a62-b0e191a5e08a"}}, {"head": {"id": "4d711ec7-fe4b-4652-aecd-10c132917083", "name": "entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487483403282, "endTime": 652487484612302}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "bce8c2eb-e384-4ff9-a3bb-2891141faa39", "logId": "bf78d006-b122-45ae-9b24-1e5418ba564c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bce8c2eb-e384-4ff9-a3bb-2891141faa39", "name": "create entry:default@CreateModuleInfo task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487482589853}, "additional": {"logType": "detail", "children": [], "durationId": "4d711ec7-fe4b-4652-aecd-10c132917083"}}, {"head": {"id": "7929a2b3-cc20-4b94-8b8d-0b59cfa31392", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487482803928}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd17cd40-c8ad-46af-b7d1-3ff0265b1d92", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487482891930}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "569260dd-73b0-497b-b30e-11b8002dcdb2", "name": "Executing task :entry:default@CreateModuleInfo", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487483418513}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0d12e89-73fd-4881-b48f-3713612bb91c", "name": "Task 'entry:default@CreateModuleInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487483952296}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5cfd19c-cc9c-4974-bd4f-42a5f60246b8", "name": "entry : default@CreateModuleInfo cost memory 0.0516510009765625", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487484439366}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8608b156-5926-4763-9292-a3d8f790a3b1", "name": "runTaskFromQueue task cost before running: 260 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487484549782}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf78d006-b122-45ae-9b24-1e5418ba564c", "name": "Finished :entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487483403282, "endTime": 652487484612302, "totalTime": 1124425}, "additional": {"logType": "info", "children": [], "durationId": "4d711ec7-fe4b-4652-aecd-10c132917083"}}, {"head": {"id": "ed3cde59-23f4-4730-b3f9-8749d53784f1", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487488854068, "endTime": 652487490216674}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "a4329b77-5b59-412c-96e7-915ab10b4221", "logId": "72d22ef0-299b-46f9-b200-75b73b2c7644"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a4329b77-5b59-412c-96e7-915ab10b4221", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487486690401}, "additional": {"logType": "detail", "children": [], "durationId": "ed3cde59-23f4-4730-b3f9-8749d53784f1"}}, {"head": {"id": "aa81fd37-2369-4380-bbcf-9275c8949775", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487486961276}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e22f0cc-405a-4bd4-94dc-8d1fdaa6f14e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487487087516}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9c5a134-5f47-4018-8878-64a0002aaebd", "name": "Please confirm the symbolic link is valid:  /Users/<USER>/github/LingXia/examples/harmony/entry/oh_modules/lingxia", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487487921667}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "0e0e2d0c-34d3-40e0-a6a6-d62cf3fd2e1d", "name": "The current module 'entry' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487488416141}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "62bbe924-e39a-474e-a94b-f4325579290e", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487488864997}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb11b2f2-9c0d-45c6-9b23-0290a7fb9196", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487489431141}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4dbab72c-cf4e-4000-8d95-f0ad2368cd56", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487490028662}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8be3a029-b574-4d4b-bfc6-7b53da35deb6", "name": "entry : default@GenerateMetadata cost memory 0.08737945556640625", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487490134399}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72d22ef0-299b-46f9-b200-75b73b2c7644", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487488854068, "endTime": 652487490216674}, "additional": {"logType": "info", "children": [], "durationId": "ed3cde59-23f4-4730-b3f9-8749d53784f1"}}, {"head": {"id": "821fa07f-e6a7-4371-b94d-f29b326ea39d", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487492166374, "endTime": 652487492585969}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "2b3292dc-544b-4ef3-9b99-4f1df4eab90a", "logId": "94fcf128-bc29-4f8d-943c-99a4394178dc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2b3292dc-544b-4ef3-9b99-4f1df4eab90a", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487491602055}, "additional": {"logType": "detail", "children": [], "durationId": "821fa07f-e6a7-4371-b94d-f29b326ea39d"}}, {"head": {"id": "6f47da0d-3062-4895-8c4b-42e9784de55b", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487491870984}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47ab7f9a-4a4b-48b7-b7b6-1ba219cf4368", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487491993751}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "167475d0-63de-4b1e-8918-2c9f715a2285", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487492176276}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84168b07-1463-4e4a-bbfd-d755aebbe988", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487492285064}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1fb913b2-1e02-41ce-a1fd-84f8387cc96f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487492342373}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d9da474-12bb-442a-8bb2-c31d8aad53be", "name": "entry : default@ConfigureCmake cost memory 0.0390472412109375", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487492426932}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf7abef8-f10c-4a1f-b46a-a34f5cff1d77", "name": "runTaskFromQueue task cost before running: 268 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487492526743}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94fcf128-bc29-4f8d-943c-99a4394178dc", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487492166374, "endTime": 652487492585969, "totalTime": 333743}, "additional": {"logType": "info", "children": [], "durationId": "821fa07f-e6a7-4371-b94d-f29b326ea39d"}}, {"head": {"id": "a020be65-71df-4bbf-94c8-844958cb05ae", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487494592878, "endTime": 652487495865995}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "74167b10-8f19-4ee4-b614-d3bdb30ae0e7", "logId": "fa065297-8d26-4809-becd-6d2cd2b1b313"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "74167b10-8f19-4ee4-b614-d3bdb30ae0e7", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487493781666}, "additional": {"logType": "detail", "children": [], "durationId": "a020be65-71df-4bbf-94c8-844958cb05ae"}}, {"head": {"id": "45ed3b96-cf96-4544-ab6f-c4a8ca40d828", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487494031709}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8dbf724-0aef-40e8-b6af-cd8f5267a85f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487494125256}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9221f3f3-4e7e-43ea-8a76-3d89cb58018a", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487494603538}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3ff545d-e316-4949-97c1-da60571f8100", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487495679608}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "adc33f8b-b6fc-44cd-b83f-7b61437e7b51", "name": "entry : default@MergeProfile cost memory 0.3255157470703125", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487495785894}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa065297-8d26-4809-becd-6d2cd2b1b313", "name": "UP-TO-DATE :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487494592878, "endTime": 652487495865995}, "additional": {"logType": "info", "children": [], "durationId": "a020be65-71df-4bbf-94c8-844958cb05ae"}}, {"head": {"id": "bd5a6de1-5f20-4651-9168-5cce9a5acee4", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487498383068, "endTime": 652487499624828}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "135432bb-badc-43d7-ad21-02416c7d393f", "logId": "5b71c80c-469a-499f-b46f-997111df8228"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "135432bb-badc-43d7-ad21-02416c7d393f", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487497385531}, "additional": {"logType": "detail", "children": [], "durationId": "bd5a6de1-5f20-4651-9168-5cce9a5acee4"}}, {"head": {"id": "0e5bfc83-ba5d-4c73-be5f-f2f95edaa71f", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487497644929}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52c1aaba-c2d3-4ce5-9390-cbe322eb8b93", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487497766483}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25515d86-73b5-4498-a5e0-adac9f9bba3b", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487498394238}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f383ae54-3703-45c8-b82f-334bc1810bed", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487498872015}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7643fdfb-5989-4f14-868f-58258879f19b", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487499434265}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0b3f3bf-70fc-40cb-8430-cc6353167f54", "name": "entry : default@CreateBuildProfile cost memory 0.0890045166015625", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487499542304}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b71c80c-469a-499f-b46f-997111df8228", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487498383068, "endTime": 652487499624828}, "additional": {"logType": "info", "children": [], "durationId": "bd5a6de1-5f20-4651-9168-5cce9a5acee4"}}, {"head": {"id": "210be553-795f-4e31-825d-03a775d2838f", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487505062481, "endTime": 652487505521129}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "e07bce99-6e7b-4180-8176-55d988662ff9", "logId": "5f9b9f53-3f30-43e5-9696-4bbdddfd0d94"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e07bce99-6e7b-4180-8176-55d988662ff9", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487504016897}, "additional": {"logType": "detail", "children": [], "durationId": "210be553-795f-4e31-825d-03a775d2838f"}}, {"head": {"id": "c902aa2b-0d35-454f-8683-6eb9cee667c9", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487504244866}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f1762c2-5927-42ec-8c44-aea5a7246582", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487504326623}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd7cfa93-8d63-458c-8370-7a09840cde2e", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487505075223}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5df36ef3-9a73-404c-948b-7271050607b2", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487505189709}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82e55270-4486-4ceb-ad93-2dc37637a854", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487505297366}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7d6af73-c66c-44ab-8ad5-ddfbc29260b7", "name": "entry : default@PreCheckSyscap cost memory 0.03926849365234375", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487505383290}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6e263d9-8c05-4bbd-ac5e-ecf9c628b767", "name": "runTaskFromQueue task cost before running: 281 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487505470947}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f9b9f53-3f30-43e5-9696-4bbdddfd0d94", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487505062481, "endTime": 652487505521129, "totalTime": 383911}, "additional": {"logType": "info", "children": [], "durationId": "210be553-795f-4e31-825d-03a775d2838f"}}, {"head": {"id": "6e04bcb4-2b04-45ea-a1c7-77cdd07cfce1", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487511075910, "endTime": 652487512022942}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "c470cb09-42ad-42a8-9d09-0c7634944cde", "logId": "5a6882ad-89d6-4889-be23-88c668dbe53b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c470cb09-42ad-42a8-9d09-0c7634944cde", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487506835596}, "additional": {"logType": "detail", "children": [], "durationId": "6e04bcb4-2b04-45ea-a1c7-77cdd07cfce1"}}, {"head": {"id": "266a7c71-9d05-4dd1-a842-279dd179360f", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487507105488}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ceb960b-c4ad-4bba-a4a0-3c1fb9c51454", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487507202977}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec4b9172-89e4-480a-b91d-4f699a01db03", "name": "Please confirm the symbolic link is valid:  /Users/<USER>/github/LingXia/examples/harmony/entry/oh_modules/lingxia", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487508536636}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "2be6c75f-f78f-425e-b852-8ae62fb4172e", "name": "The current module 'entry' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487509026365}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "cd82b71f-aa15-450f-b000-62b20c250fd6", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487511092445}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b723397-c06f-46da-8cec-cd08509da873", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487511342570}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a915a479-8cb8-4040-b086-166b8617b28d", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487511748289}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d217b24e-261b-4835-a70c-30c90d196227", "name": "entry : default@GeneratePkgContextInfo cost memory 0.0660858154296875", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487511863803}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a6882ad-89d6-4889-be23-88c668dbe53b", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487511075910, "endTime": 652487512022942}, "additional": {"logType": "info", "children": [], "durationId": "6e04bcb4-2b04-45ea-a1c7-77cdd07cfce1"}}, {"head": {"id": "cc2676cb-407e-4080-9486-464ae01e8ec5", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487514663632, "endTime": 652487515948128}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file '/Users/<USER>/github/LingXia/examples/harmony/build/cache/default/integrated_hsp/integratedHspCache.json' does not exist."], "detailId": "7555f4bf-4568-4302-b556-7fdf5ea11c72", "logId": "0504b64c-2173-4aeb-b2ae-181d9dcdb2ed"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7555f4bf-4568-4302-b556-7fdf5ea11c72", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487513321125}, "additional": {"logType": "detail", "children": [], "durationId": "cc2676cb-407e-4080-9486-464ae01e8ec5"}}, {"head": {"id": "d0f5bdf1-e337-4c18-b0ef-6935c77724e0", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487513550406}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7454bdae-8bc6-47e3-9cac-06c8a9cd236a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487513636965}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d43b50a-ee6b-4cc9-abd9-6735aef3a70b", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487514676072}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0e3437d-f447-4fe7-b8c7-91c5f8cad238", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file '/Users/<USER>/github/LingXia/examples/harmony/build/cache/default/integrated_hsp/integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487515555490}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1bf3b03-e70a-4723-8ebe-3b6dcbf4e844", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487515637669}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfbb6edb-804f-482e-8098-fcc4cd2b157d", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487515708102}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0776bbc-cb59-4a9d-bae4-0c40b7329141", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487515753166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c451e8e-6848-4c8b-8f1a-77c0f7d7d00a", "name": "entry : default@ProcessIntegratedHsp cost memory 0.1103668212890625", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487515829138}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "faaa8695-65b6-42e6-8505-bcfd6638c7d7", "name": "runTaskFromQueue task cost before running: 292 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487515903497}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0504b64c-2173-4aeb-b2ae-181d9dcdb2ed", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487514663632, "endTime": 652487515948128, "totalTime": 1222207}, "additional": {"logType": "info", "children": [], "durationId": "cc2676cb-407e-4080-9486-464ae01e8ec5"}}, {"head": {"id": "d930c1de-744a-4ab1-a34b-2df8fb271224", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487518694187, "endTime": 652487519124882}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "c9d07595-3bd6-4e74-8845-d584e5fa0622", "logId": "da571d1f-5ac0-4601-8f6b-d7e80a874767"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c9d07595-3bd6-4e74-8845-d584e5fa0622", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487517424663}, "additional": {"logType": "detail", "children": [], "durationId": "d930c1de-744a-4ab1-a34b-2df8fb271224"}}, {"head": {"id": "02115404-583a-46ef-bee8-ac6c41bc6385", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487517884191}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9cf13d9a-667f-479c-8003-e527b9cdb71e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487518008242}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7809ac71-4d9b-476c-a769-f229d240326e", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487518734392}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07b64b9a-5e85-45f6-97b8-7446d3ab2d63", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487518851448}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17767c4b-b205-4186-82b5-c35a36ed766a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487518902194}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "444129d9-5ad7-40cd-954a-dc5b5241f70b", "name": "entry : default@BuildNativeWithCmake cost memory 0.04019927978515625", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487518979508}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30155630-4ec3-4ba5-8186-c2eba628dc6c", "name": "runTaskFromQueue task cost before running: 295 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487519070913}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "da571d1f-5ac0-4601-8f6b-d7e80a874767", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487518694187, "endTime": 652487519124882, "totalTime": 350948}, "additional": {"logType": "info", "children": [], "durationId": "d930c1de-744a-4ab1-a34b-2df8fb271224"}}, {"head": {"id": "74039207-210e-42c3-9a11-6bdf8533c91c", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487521503314, "endTime": 652487523475698}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "11573d65-9cff-4bd8-888c-be8956bebf01", "logId": "a73ef47b-723c-4dd3-9465-9ace272140d5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "11573d65-9cff-4bd8-888c-be8956bebf01", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487520542116}, "additional": {"logType": "detail", "children": [], "durationId": "74039207-210e-42c3-9a11-6bdf8533c91c"}}, {"head": {"id": "a4c56c71-408c-4bde-8b7e-b2c010937e6e", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487520803944}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6074691-e09b-4086-b26a-8b9a212d9886", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487520907675}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0412c665-f6aa-40cd-835a-7cffcfefe384", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487521513983}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26bf170c-5576-47be-8c54-c36fbcecc699", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487523223464}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7aa9f1ba-dc9c-4af2-9bb5-419c90faed78", "name": "entry : default@MakePackInfo cost memory 0.11539459228515625", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487523369401}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a73ef47b-723c-4dd3-9465-9ace272140d5", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487521503314, "endTime": 652487523475698}, "additional": {"logType": "info", "children": [], "durationId": "74039207-210e-42c3-9a11-6bdf8533c91c"}}, {"head": {"id": "bebfc598-de1c-40b9-9cc3-3d488b1b174e", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487526257895, "endTime": 652487527604356}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/syscap/default/rpcid.sc' does not exist."], "detailId": "2eed5d2d-c1b0-4353-9971-95c2ce5b5f5e", "logId": "bb11ce30-cba2-4559-b5a6-b82b663b254a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2eed5d2d-c1b0-4353-9971-95c2ce5b5f5e", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487525099318}, "additional": {"logType": "detail", "children": [], "durationId": "bebfc598-de1c-40b9-9cc3-3d488b1b174e"}}, {"head": {"id": "225cddfa-da51-46dd-90c1-fc0f41b39b8f", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487525324896}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "673807dc-5b35-4ae0-b3a4-f5e2ccc18697", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487525413329}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0008140a-d1b9-4f1d-bd30-3acb157e5c40", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487526268826}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a5343dc-51bd-4cc2-900c-f559adc81da5", "name": "File: '/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487526385771}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5dc88c8-02d8-4f89-bcd1-81d3d8da01e6", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487526571050}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e09b798-4f93-4585-a29f-deec6fc332d1", "name": "entry:default@SyscapTransform is not up-to-date, since the output file '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/syscap/default/rpcid.sc' does not exist.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487527151744}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b63db6fe-2021-4b07-bcae-813de92afcbf", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487527241081}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40404920-fa53-4767-8ec6-08fa816a2989", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487527319932}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aebd1fa9-3b31-4841-85c9-8c08770c686a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487527374764}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "296ec354-5b64-4179-9fa5-4923d26ec197", "name": "entry : default@SyscapTransform cost memory 0.12816619873046875", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487527467897}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f3256f6-60d2-4f3e-af29-ab73b0613e01", "name": "runTaskFromQueue task cost before running: 303 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487527552735}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb11ce30-cba2-4559-b5a6-b82b663b254a", "name": "Finished :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487526257895, "endTime": 652487527604356, "totalTime": 1274410}, "additional": {"logType": "info", "children": [], "durationId": "bebfc598-de1c-40b9-9cc3-3d488b1b174e"}}, {"head": {"id": "f249d1cf-712e-44a3-909d-c52c6396369c", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487530444076, "endTime": 652487531575732}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": [], "detailId": "3d9cc7d6-02c1-43aa-b731-0c6f25e00bfe", "logId": "f51f690f-e5cd-4572-bb45-293aaf701eee"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3d9cc7d6-02c1-43aa-b731-0c6f25e00bfe", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487529275878}, "additional": {"logType": "detail", "children": [], "durationId": "f249d1cf-712e-44a3-909d-c52c6396369c"}}, {"head": {"id": "34325155-9246-408f-88b5-56c0d14aaad0", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487529563884}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98f4772f-bc9b-4fe9-8553-551dc1d64740", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487529668147}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e80f9da-4b18-45f4-b85d-8c0a9d3eab9a", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487530455509}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c667ad7-e29d-47a5-b6c0-bdfdaa74f3ce", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487531411080}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c868aff7-39b3-480f-8c84-f564fddbb61b", "name": "entry : default@ProcessProfile cost memory 0.086212158203125", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487531509088}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f51f690f-e5cd-4572-bb45-293aaf701eee", "name": "UP-TO-DATE :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487530444076, "endTime": 652487531575732}, "additional": {"logType": "info", "children": [], "durationId": "f249d1cf-712e-44a3-909d-c52c6396369c"}}, {"head": {"id": "4db58fec-9f9c-4ac2-b729-945ab000e350", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487534562379, "endTime": 652487537994014}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The input file '/Users/<USER>/github/LingXia/examples/harmony/entry/oh-package.json5' has been changed."], "detailId": "769d578c-f1a2-4aab-96c8-969a2bb77830", "logId": "b369b8c9-8f85-46f2-8635-ed1d5669b38c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "769d578c-f1a2-4aab-96c8-969a2bb77830", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487532874556}, "additional": {"logType": "detail", "children": [], "durationId": "4db58fec-9f9c-4ac2-b729-945ab000e350"}}, {"head": {"id": "90409a2f-e862-4e4d-b9ba-43ec4e812b46", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487533112964}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf1b2946-d9da-442c-a7b1-2741419535db", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487533216582}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c0b32c7-0e4a-4b47-91ee-85ae170a91af", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487534578706}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69fa8c76-83d4-4426-83b7-cbf79e4a24f8", "name": "entry:default@ProcessRouterMap is not up-to-date, since the input file '/Users/<USER>/github/LingXia/examples/harmony/entry/oh-package.json5' has been changed.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487536614765}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49a7eff5-ba59-409a-b78e-eddcf9076bb6", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487536752402}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b2e7e77-57b2-44d9-9a35-bdd084ffdb78", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487536844526}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff7be5ff-919e-462e-9d60-e2b0ce9716be", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487536994159}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4db5164-fafb-4b52-917a-cf168af6326d", "name": "entry : default@ProcessRouterMap cost memory 0.18711090087890625", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487537795892}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d894fd93-6bcf-4b62-8805-cb446d9d2cb5", "name": "runTaskFromQueue task cost before running: 314 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487537929583}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b369b8c9-8f85-46f2-8635-ed1d5669b38c", "name": "Finished :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487534562379, "endTime": 652487537994014, "totalTime": 3332023}, "additional": {"logType": "info", "children": [], "durationId": "4db58fec-9f9c-4ac2-b729-945ab000e350"}}, {"head": {"id": "0421d7a1-8644-430d-9acb-d96d4fff24ca", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487541026788, "endTime": 652487541891693}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "fda9fe15-b5ff-4aea-8b3f-3f9b78839fe2", "logId": "364980e1-e844-4d66-a604-94823674eb94"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fda9fe15-b5ff-4aea-8b3f-3f9b78839fe2", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487540036181}, "additional": {"logType": "detail", "children": [], "durationId": "0421d7a1-8644-430d-9acb-d96d4fff24ca"}}, {"head": {"id": "0184cc09-ce68-4a68-8a43-5f11c8784e71", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487540307829}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c6f1827-2b08-48f4-aa6a-6bb369440a2c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487540415167}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f52a861-d186-4ecc-b805-f6cc7d99a54a", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487541038369}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97f4b214-43de-4de4-8369-c6e4de8c8884", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487541157572}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b59d6a74-d71c-4518-bdf5-9998d5dc2286", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487541220172}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15d2f1e9-c55c-4fe8-8607-22ae8d405d3a", "name": "entry : default@BuildNativeWithNinja cost memory 0.05481719970703125", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487541715311}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e864741-69c5-432a-bc5b-ca51b6d97b47", "name": "runTaskFromQueue task cost before running: 318 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487541833672}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "364980e1-e844-4d66-a604-94823674eb94", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487541026788, "endTime": 652487541891693, "totalTime": 776907}, "additional": {"logType": "info", "children": [], "durationId": "0421d7a1-8644-430d-9acb-d96d4fff24ca"}}, {"head": {"id": "56d8fbe8-ab72-4097-9fd3-e494865babc4", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487545813246, "endTime": 652487549911120}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "36ef92b4-1e8c-4948-86c6-669f8c5e032c", "logId": "789fd015-eef3-4931-8737-f33d36a9b661"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "36ef92b4-1e8c-4948-86c6-669f8c5e032c", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487543744654}, "additional": {"logType": "detail", "children": [], "durationId": "56d8fbe8-ab72-4097-9fd3-e494865babc4"}}, {"head": {"id": "eaee7446-9511-4975-a61a-62c25e9eeb41", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487543987790}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c13d5cbc-570b-438f-830d-ff4b73e12900", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487544075685}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3372f89d-3a35-4ff2-b4dc-f9a5b97e8e3c", "name": "restool module names: entry,lingxia; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487544771814}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a59e6ff-4e7d-4725-9f3c-6ea537c727e5", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487547369673}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a581e246-7695-4635-99e6-9fa90531b773", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487548860015}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a1fe438-6f2f-49a5-8673-e549b4ccf02a", "name": "entry : default@ProcessResource cost memory 0.13724517822265625", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487549013609}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "789fd015-eef3-4931-8737-f33d36a9b661", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487545813246, "endTime": 652487549911120}, "additional": {"logType": "info", "children": [], "durationId": "56d8fbe8-ab72-4097-9fd3-e494865babc4"}}, {"head": {"id": "643c19e2-c57d-4b31-91ce-0583e15b76ad", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487554705891, "endTime": 652487564763367}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The input file '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/router_map/default/temp-router-map.json' has been changed."], "detailId": "317b9708-b589-4385-a492-7f3af696c410", "logId": "22b362da-a683-46f1-a635-dcdbdb6c52c5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "317b9708-b589-4385-a492-7f3af696c410", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487552074542}, "additional": {"logType": "detail", "children": [], "durationId": "643c19e2-c57d-4b31-91ce-0583e15b76ad"}}, {"head": {"id": "09e7a120-6535-4d5b-8b64-fd6ccecba14e", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487552302564}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83bc5cc1-ab11-4a4a-a403-d31f2e68acee", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487552382933}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5609d23-9e98-4d10-8fcf-004032f45a57", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487554727264}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "866f2c93-133c-4622-b793-42f898e9d115", "name": "entry:default@GenerateLoaderJson is not up-to-date, since the input file '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/router_map/default/temp-router-map.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487561994610}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4084b413-e901-48c9-9fc7-f7133059dd21", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487562154446}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2529c5e3-2bfa-44d2-b784-aa2fbd2b8d25", "name": "entry : default@GenerateLoaderJson cost memory 0.5445480346679688", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487564517419}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81ded535-c73f-434a-a414-44e296b6fa69", "name": "runTaskFromQueue task cost before running: 340 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487564687893}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22b362da-a683-46f1-a635-dcdbdb6c52c5", "name": "Finished :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487554705891, "endTime": 652487564763367, "totalTime": 9950125}, "additional": {"logType": "info", "children": [], "durationId": "643c19e2-c57d-4b31-91ce-0583e15b76ad"}}, {"head": {"id": "3dfcb9b3-d23a-46c4-9b47-d1455f974fca", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487569521089, "endTime": 652487571645106}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "b2c5c48c-06c3-44e1-88f8-e7bbe5909057", "logId": "02c4a060-aadb-4cc6-9090-bea61fad594e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b2c5c48c-06c3-44e1-88f8-e7bbe5909057", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487568673162}, "additional": {"logType": "detail", "children": [], "durationId": "3dfcb9b3-d23a-46c4-9b47-d1455f974fca"}}, {"head": {"id": "6f3ed23c-2553-4e6f-b92b-c556c58e033a", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487568897903}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e096d3c0-49bd-441f-a8dc-5d7f1d0c9498", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487568986716}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "59ceff28-80c6-4261-8c77-a139be9aebd1", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487569531818}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1df34a8-80f7-41e5-9230-75f5cee38367", "name": "Incremental task entry:default@ProcessLibs pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487571436193}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ce2fd8f-f667-4bb8-9ba6-6a6d7d2b74ff", "name": "entry : default@ProcessLibs cost memory 0.1288604736328125", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487571564703}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02c4a060-aadb-4cc6-9090-bea61fad594e", "name": "UP-TO-DATE :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487569521089, "endTime": 652487571645106}, "additional": {"logType": "info", "children": [], "durationId": "3dfcb9b3-d23a-46c4-9b47-d1455f974fca"}}, {"head": {"id": "b1ac1866-0eb4-45c6-855f-554a815e24c9", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487580049776, "endTime": 652487595215829}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "1c756313-abe4-4738-8f40-ba916cae25fb", "logId": "8dcfbb37-cd72-4296-ba0d-7f76817dabef"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1c756313-abe4-4738-8f40-ba916cae25fb", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487572846358}, "additional": {"logType": "detail", "children": [], "durationId": "b1ac1866-0eb4-45c6-855f-554a815e24c9"}}, {"head": {"id": "10ccbba6-5bde-40f2-b5bd-9e6790a49644", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487573064402}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae0c033b-fd1b-4c15-9b16-7ff6168f3dde", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487573139438}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5bd8545-1138-46e1-9b54-869282fa7938", "name": "restool module names: entry,lingxia; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487573561198}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "079b4005-5ecd-4430-a641-c1ab0b581d88", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487580088280}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89741b40-7421-45c3-b688-ebdb91aa9e0e", "name": "Incremental task entry:default@CompileResource pre-execution cost: 14 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487594633718}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c09b09d1-3157-4770-a20e-7f9506e9b5b1", "name": "entry : default@CompileResource cost memory 0.7096786499023438", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487594852434}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8dcfbb37-cd72-4296-ba0d-7f76817dabef", "name": "UP-TO-DATE :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487580049776, "endTime": 652487595215829}, "additional": {"logType": "info", "children": [], "durationId": "b1ac1866-0eb4-45c6-855f-554a815e24c9"}}, {"head": {"id": "32cef0a1-dc13-41bc-b545-57fe10a160a0", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487601656669, "endTime": 652487603705865}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "156c4cb1-ace8-48f5-a355-d1f7f0975097", "logId": "216b023f-fcf8-4717-a0ff-4f1862cf12a6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "156c4cb1-ace8-48f5-a355-d1f7f0975097", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487598260801}, "additional": {"logType": "detail", "children": [], "durationId": "32cef0a1-dc13-41bc-b545-57fe10a160a0"}}, {"head": {"id": "d4236d3b-f394-4849-a190-706f7c39e7fc", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487598697599}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0eae6e2-9670-484b-bd03-3a78dbe6c140", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487599039428}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "317d596d-e1bb-46a5-a44a-47b820d1b114", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487601677945}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af95f730-13ab-469b-90a9-c49d39d8918b", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487601936713}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a16520c-1b3e-4149-875c-92ccc38a3fe9", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487603377490}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b61ef19-c265-4458-b624-48a675a46e58", "name": "entry : default@DoNativeStrip cost memory 0.08530426025390625", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487603583857}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "216b023f-fcf8-4717-a0ff-4f1862cf12a6", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487601656669, "endTime": 652487603705865}, "additional": {"logType": "info", "children": [], "durationId": "32cef0a1-dc13-41bc-b545-57fe10a160a0"}}, {"head": {"id": "634c4873-31f1-474c-a92a-d60ec5ecce97", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487612478335, "endTime": 652488267973662}, "additional": {"children": ["2b4491b1-87cb-40b3-8f5c-59569909e8d1", "8b9b8988-6321-46bf-8bab-b1bf1977dbd8"], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "99e30a8d-87d1-4f19-831c-8080291e6fca", "logId": "7001fee7-5199-46b7-9df7-d83d1a12a203"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "99e30a8d-87d1-4f19-831c-8080291e6fca", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487607054531}, "additional": {"logType": "detail", "children": [], "durationId": "634c4873-31f1-474c-a92a-d60ec5ecce97"}}, {"head": {"id": "9e8e7b00-8930-4ade-8bcf-ddee75da5ebc", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487607509784}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc814fc7-ded9-4c98-9859-e088e0aac3fa", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487607802056}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07bfc626-be2b-4850-a710-67bdd73df277", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487612497203}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "425acc41-39b7-4285-930c-9ac05f3c0b54", "name": "Please confirm the symbolic link is valid:  /Users/<USER>/github/LingXia/examples/harmony/entry/oh_modules/lingxia", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487615067955}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "c07b4c05-ae91-4bac-a5cd-a7a91ec4974d", "name": "The current module 'entry' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487615733499}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "78a40664-6c44-42b8-825f-6c5e0121f500", "name": "Please confirm the symbolic link is valid:  /Users/<USER>/github/LingXia/examples/harmony/entry/oh_modules/lingxia", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487630140458}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "f657999d-28d0-46bf-b959-531af6725d25", "name": "The current module 'entry' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487630672967}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "68cf8320-0ff4-48fd-811a-7992d126a6ac", "name": "Please confirm the symbolic link is valid:  /Users/<USER>/github/LingXia/examples/harmony/entry/oh_modules/lingxia", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487633358945}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "37fd4a22-44f5-4eeb-b0b9-42fca006da10", "name": "The current module 'entry' has dependency which is not installed at its oh-package.json5.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487633866981}, "additional": {"logType": "warn", "children": []}}, {"head": {"id": "85833561-8072-4e2f-af10-9110cad55789", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487636369636}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b40e7895-858c-4d1e-b310-83e2e65d70d9", "name": "Compile arkts with external api path: /Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/hms/ets", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487637178302}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d64ef94c-bfc0-4cee-84bd-b3e7a7c0cda8", "name": "default@CompileArkTS work[10] is submitted.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487638146115}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b4491b1-87cb-40b3-8f5c-59569909e8d1", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Worker4", "startTime": 652487639387153, "endTime": 652488267661167}, "additional": {"children": [], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "634c4873-31f1-474c-a92a-d60ec5ecce97", "logId": "001af3ad-ad20-4ed8-8419-8742d3896981"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5780c730-c51e-4334-b824-75d253d63b89", "name": "default@CompileArkTS work[10] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487638895824}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e077383b-caa8-407c-bb4c-c965fde015f7", "name": "A work dispatched to worker[7] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487638983502}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a91dd9d0-47ec-4c9f-b732-537cdd32fd92", "name": "A work dispatched to worker[6] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487639027128}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8b03942-d5a2-4c49-8bdd-7b1c2fd6a0be", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487639063667}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db98a463-4e15-4242-8ab0-e1492dc28f07", "name": "default@CompileArkTS work[10] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487639406770}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebe0ece8-4a11-4966-ab4a-3c644385e9b2", "name": "default@CompileArkTS work[10] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487639501679}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de84bb9a-271b-48e3-bb36-b18a86bf7870", "name": "CopyResources startTime: 652487639547744", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487639551298}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7c8a6b2-228d-48d7-97b4-e60d7cb5de9a", "name": "default@CompileArkTS work[11] is submitted.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487639621894}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b9b8988-6321-46bf-8bab-b1bf1977dbd8", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487639680260, "endTime": 652488267979870}, "additional": {"children": [], "state": "failed", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "634c4873-31f1-474c-a92a-d60ec5ecce97"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f971f6a4-157f-41c8-89d8-7bff6c73f7c3", "name": "default@CompileArkTS work[11] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487640233180}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab203ae8-d22f-46ad-b201-ddebff8d3fa5", "name": "A work dispatched to worker[7] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487640346641}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3dc221b-aa29-41dd-82d3-e4fdf3ae9765", "name": "Create  resident worker with id: 6.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487640658257}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bbed5ed0-4edb-4d22-9044-503251094a77", "name": "default@CompileArkTS work[11] has been dispatched to worker[6].", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487644039573}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0060642f-1ff1-4e83-94ec-e85452137f16", "name": "default@CompileArkTS work[11] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487644167292}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5ff887a-d282-4c5b-8ed9-b821fb580825", "name": "entry : default@CompileArkTS cost memory -4.166023254394531", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487644358837}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc0a676b-5152-412b-b99d-f406c4499be4", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487649602055, "endTime": 652487653372491}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "821d64be-7eba-404d-9769-0e9a1da45e13", "logId": "bee5c18d-6d17-45a0-9cf1-84d40600a435"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "821d64be-7eba-404d-9769-0e9a1da45e13", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487645865228}, "additional": {"logType": "detail", "children": [], "durationId": "cc0a676b-5152-412b-b99d-f406c4499be4"}}, {"head": {"id": "68c44c6a-5c22-47bd-baa1-b33e5632174e", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487646285378}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f92c3d25-5031-402e-94e0-dcee13e78f87", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487646405012}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c27a7fa-55c4-43c0-ac68-a9c33c738894", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487649624860}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0da48ce0-55f2-4adb-b676-22bc73e71105", "name": "entry : default@BuildJS cost memory 0.1255645751953125", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487653052584}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51813fd2-4981-450b-9bea-0086f1d631cf", "name": "runTaskFromQueue task cost before running: 429 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487653263467}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bee5c18d-6d17-45a0-9cf1-84d40600a435", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487649602055, "endTime": 652487653372491, "totalTime": 3617579}, "additional": {"logType": "info", "children": [], "durationId": "cc0a676b-5152-412b-b99d-f406c4499be4"}}, {"head": {"id": "ae250afb-7111-4821-af3b-42af6f6f44a9", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487659174307, "endTime": 652487661015964}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "659b6b81-6020-4560-9831-ff66d21565a4", "logId": "737438f1-6836-41c5-ac3a-3a91a728b0e1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "659b6b81-6020-4560-9831-ff66d21565a4", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487655559096}, "additional": {"logType": "detail", "children": [], "durationId": "ae250afb-7111-4821-af3b-42af6f6f44a9"}}, {"head": {"id": "12174cb9-f9e5-47a1-9886-454f35a229be", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487655945153}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1dd1a6da-83e3-41c5-b19f-14f6fbeb913d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487656119072}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12a4d861-703f-40b9-a153-e47902dce969", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487659195922}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1fd7549d-27b7-4e78-a589-a2058ff3dc26", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487659561569}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30cd4828-03f3-460d-8cb0-ef92ab46a713", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487660787749}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2aee6d6-ada7-407a-a304-0a766d0b11a7", "name": "entry : default@CacheNativeLibs cost memory 0.0998382568359375", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487660939917}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "737438f1-6836-41c5-ac3a-3a91a728b0e1", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487659174307, "endTime": 652487661015964}, "additional": {"logType": "info", "children": [], "durationId": "ae250afb-7111-4821-af3b-42af6f6f44a9"}}, {"head": {"id": "afee0faf-a725-474e-9dde-0d23de66b6ce", "name": "worker[4] has one work error.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488267474740}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "920e8f04-539f-493d-81d2-cee8806d4dae", "name": "default@CompileArkTS work[10] failed.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488267757210}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "001af3ad-ad20-4ed8-8419-8742d3896981", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Worker4", "startTime": 652487639387153, "endTime": 652488267661167}, "additional": {"logType": "error", "children": [], "durationId": "2b4491b1-87cb-40b3-8f5c-59569909e8d1", "parent": "7001fee7-5199-46b7-9df7-d83d1a12a203"}}, {"head": {"id": "7001fee7-5199-46b7-9df7-d83d1a12a203", "name": "Failed :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487612478335, "endTime": 652488267973662}, "additional": {"logType": "error", "children": ["001af3ad-ad20-4ed8-8419-8742d3896981"], "durationId": "634c4873-31f1-474c-a92a-d60ec5ecce97"}}, {"head": {"id": "55115bea-bd8a-4326-9164-2f5d01b889ed", "name": "hvigor build process will be closed with an error.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488268226105}, "additional": {"logType": "debug", "children": [], "durationId": "634c4873-31f1-474c-a92a-d60ec5ecce97"}}, {"head": {"id": "ba7ea4ff-4112-4add-8dc8-c0c28cb15e77", "name": "ERROR: stacktrace = Error: ENOENT: no such file or directory, stat '/Users/<USER>/github/LingXia/examples/harmony/entry/oh_modules/lingxia'\n\u001b[31m1 ERROR: \u001b[31mArkTS:ERROR File: /Users/<USER>/github/LingXia/examples/harmony/entry/src/main/ets/entryability/EntryAbility.ets:4:23\n Cannot find module 'lingxia' or its corresponding type declarations.\n\n\u001b[31m> More Info: https://developer.huawei.com/consumer/en/doc/harmonyos-faqs-V5/faqs-compiling-and-building-4-V5\n\n\u001b[39m\n\u001b[31m2 ERROR: \u001b[31mArkTS:ERROR File: /Users/<USER>/github/LingXia/examples/harmony/entry/src/main/ets/pages/Index.ets:1:40\n Cannot find module 'lingxia' or its corresponding type declarations.\n\n\u001b[31m> More Info: https://developer.huawei.com/consumer/en/doc/harmonyos-faqs-V5/faqs-compiling-and-building-4-V5\n\n\u001b[39m\n\u001b[31mCOMPILE RESULT:FAIL {ERROR:3}\u001b[39m\n    at runArkPack (/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor-ohos-plugin/node_modules/@ohos/hvigor-arkts-compose/dist/src/arkts-pack.js:1:5438)\nError: ENOENT: no such file or directory, stat '/Users/<USER>/github/LingXia/examples/harmony/entry/oh_modules/lingxia'\n    at Object.statSync (node:fs:1688:3)\n    at resolveOhModules (/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor-ohos-plugin/node_modules/@ohos/hvigor-arkts-compose/dist/src/plugins/node-resolve/resolveOhModules.js:1:1531)\n    at resolveProjectOhModules (/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor-ohos-plugin/node_modules/@ohos/hvigor-arkts-compose/dist/src/plugins/node-resolve/resolveOhModules.js:1:1050)\n    at resolveAllOhModules (/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor-ohos-plugin/node_modules/@ohos/hvigor-arkts-compose/dist/src/plugins/node-resolve/resolveOhModules.js:1:760)\n    at Object.buildStart (/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor-ohos-plugin/node_modules/@ohos/hvigor-arkts-compose/dist/src/plugins/node-resolve/index.js:1:9657)\n    at /Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor-ohos-plugin/node_modules/@ohos/hvigor-arkts-base/node_modules/rollup/dist/shared/rollup.js:24828:40\n    at async Promise.all (index 2)\n    at async PluginDriver.hookParallel (/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor-ohos-plugin/node_modules/@ohos/hvigor-arkts-base/node_modules/rollup/dist/shared/rollup.js:24756:9)\n    at async /Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor-ohos-plugin/node_modules/@ohos/hvigor-arkts-base/node_modules/rollup/dist/shared/rollup.js:26031:13\n    at async catchUnfinishedHookActions (/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor-ohos-plugin/node_modules/@ohos/hvigor-arkts-base/node_modules/rollup/dist/shared/rollup.js:25197:24)", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488269107267}, "additional": {"logType": "debug", "children": [], "durationId": "634c4873-31f1-474c-a92a-d60ec5ecce97"}}, {"head": {"id": "46d64e2b-7486-42c2-ab42-4de7dc1d667e", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488271478091, "endTime": 652488271589377}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "19f9bc90-4e0e-4f9c-a05f-5464f0c9f826", "logId": "6b996e72-d230-4950-895e-ff613a989b93"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6b996e72-d230-4950-895e-ff613a989b93", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488271478091, "endTime": 652488271589377}, "additional": {"logType": "info", "children": [], "durationId": "46d64e2b-7486-42c2-ab42-4de7dc1d667e"}}, {"head": {"id": "c090e67d-d39d-4b6d-97a3-01d17e56b649", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652487224786626, "endTime": 652488271835741}, "additional": {"time": {"year": 2025, "month": 7, "day": 31, "hour": 21, "minute": 38}, "completeCommand": "{\"prop\":[],\"_\":[\"assembleHap\"]};assembleHap", "hvigorVersion": "5.17.2", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "failed"}}, {"head": {"id": "fa7279f3-7771-4b07-9d3f-c5362b33e888", "name": "BUILD FAILED in 1 s 47 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488271875064}, "additional": {"logType": "error", "children": []}}, {"head": {"id": "e186fc67-3e0b-4ed7-8f91-8dd72f6fd183", "name": "Update task entry:default@PreBuild input file:/Users/<USER>/github/LingXia/examples/harmony/AppScope/app.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488272186169}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b35527b5-24b6-4ca8-a05c-e537e2c5b71e", "name": "Update task entry:default@PreBuild input file:/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/module.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488272368227}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28458b3e-bc89-440a-9d72-35184ab31e9f", "name": "Update task entry:default@PreBuild input file:/Users/<USER>/github/LingXia/examples/harmony/build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488272516745}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec38c76e-b993-417a-8dd8-cefc72ec3aa1", "name": "Update task entry:default@PreBuild input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488272688176}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70e58ceb-82ce-4a02-8641-a60fa54a68b1", "name": "Update task entry:default@PreBuild input file:/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources/base/profile/main_pages.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488272850973}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84cf8982-2692-4558-9f82-629fa8b78f3f", "name": "Update task entry:default@PreBuild input file:/Users/<USER>/github/LingXia/examples/harmony/hvigor/hvigor-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488273074467}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8684ce2f-4d5e-487f-953a-2f772ed444ef", "name": "Update task entry:default@PreBuild input file:/Users/<USER>/github/LingXia/examples/harmony/entry/oh-package.json5 cache from map.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488273286673}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58c70159-03a8-4120-b1de-1107438a2512", "name": "Update task entry:default@PreBuild input file:/Users/<USER>/github/LingXia/examples/harmony/oh-package.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488273356907}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8a78dfa-b4a7-4988-9199-70dd3e401dea", "name": "Incremental task entry:default@PreBuild post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488273540164}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20dae12c-4e97-40ad-9eb9-cbdcea8446b1", "name": "Update task entry:default@CreateModuleInfo output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/generated/profile/default/ModuleInfo.ts cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488273620547}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0e53263-82f0-4946-9e41-c761f4cf2cc6", "name": "Incremental task entry:default@CreateModuleInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488273812230}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7bd7adc7-cd89-4c43-9ee2-7ec480a5add1", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488273881590}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc661c27-290e-471d-a539-4244666ae6b5", "name": "There is no need to refresh cache, since the incremental task entry:default@MergeProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488273954942}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de5d30d7-88ec-41b8-ab48-8bc8b2f209c3", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488274004654}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b415067-5df8-4477-8600-d4c68e17de95", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488274052915}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "097a38c1-4949-42a3-b6ac-0be409a5522f", "name": "Update task entry:default@ProcessIntegratedHsp output file:/Users/<USER>/github/LingXia/examples/harmony/build/cache/default/integrated_hsp/integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488274491128}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64db69f8-eeb9-4e88-bca6-db4b5422b820", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488274766095}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89d3cc57-1245-4200-965c-34bceb9bbbb0", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488274868166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49d1e2f2-6d40-4c0f-a074-7624959473b3", "name": "Update task entry:default@SyscapTransform input file:/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/toolchains/syscap_tool cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488274992571}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d7225c4-e40b-4808-a58d-3c787e03fc26", "name": "Update task entry:default@SyscapTransform input file:/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/device-define cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488275177834}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68b2fc3e-aef2-4624-8001-5ae9d31f6a27", "name": "Update task entry:default@SyscapTransform output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/syscap/default/rpcid.sc cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488275652874}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "153866bb-6a7c-431f-bbd3-254565200abb", "name": "Incremental task entry:default@SyscapTransform post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488275858454}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "026a0416-a0f1-4c29-b97c-2b52b7db6b36", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488275928586}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c557fa82-e4d9-4f27-b90d-f0af156af6ed", "name": "Update task entry:default@ProcessRouterMap input file:/Users/<USER>/github/LingXia/examples/harmony/entry/oh-package.json5 cache from map.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488276687603}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a2160ac-6ce2-4a7a-83d1-e76477862d66", "name": "Update task entry:default@ProcessRouterMap input file:/Users/<USER>/github/LingXia/examples/harmony/oh-package.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488276763296}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ef90174-9487-4d14-bb62-518c54166496", "name": "Update task entry:default@ProcessRouterMap input file:/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/module.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488276876236}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06539bd7-4acc-4f0a-a327-f738c4d18465", "name": "Update task entry:default@ProcessRouterMap input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default/pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488276976855}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "412107ba-ac68-448c-980e-9062ff985d48", "name": "Update task entry:default@ProcessRouterMap output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/router_map/default/temp-router-map.json cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488277081614}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cebf4a8a-d2f4-46a8-8db4-36c544dacc22", "name": "Update task entry:default@ProcessRouterMap output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/router_map/default/loader-router-map.json cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488277178428}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc8d1955-4407-44d8-9c4e-83f913496f2f", "name": "Incremental task entry:default@ProcessRouterMap post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488277317038}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64f1b728-174a-47b0-a29a-865925435491", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488277378495}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbaad183-437c-46ad-817c-4bb6d35fd522", "name": "Update task entry:default@GenerateLoaderJson input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default/pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488279381753}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78c943c8-d6c3-4224-b111-84c5f07e216a", "name": "Update task entry:default@GenerateLoaderJson input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/router_map/default/temp-router-map.json cache from map.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488279649929}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5241cebc-52e3-46ba-9910-22fdfad11799", "name": "Update task entry:default@GenerateLoaderJson output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default/loader.json cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488279737588}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "883fc92e-c469-4df1-bc25-77ae1c6d48c2", "name": "Incremental task entry:default@GenerateLoaderJson post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488279905510}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9a75ee0-6bdb-42d6-af9a-a5c42e95aac1", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488279974279}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e87ec2a-700d-450f-9395-02be06cc7924", "name": "There is no need to refresh cache, since the incremental task entry:default@CompileResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488280030465}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2262cc60-c96d-4f6c-9740-44edf2a3fdc3", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488280086948}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1a7f8df-fdec-4749-a32a-8e5cb444813a", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488281485800}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de7dc53b-ad44-4d47-84c2-7ef5adac4085", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488281952435}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0646e602-367e-4859-a7c2-616a74938764", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488283432761}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb893c60-e03f-4fc8-8bc6-1d8a967ceec1", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488283688381}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d84b3495-eb82-4eb0-9a29-e79e6ea77cf2", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/base/profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488283810143}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7c1f055-1b8f-4cd7-a05e-a7d58957043f", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488284056065}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b62d1b99-0864-48c3-a763-3e8193ec0928", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default/pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488284492218}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7020b93b-6f68-4a0a-ac4a-a124b7cd46bb", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/generated/profile/default/BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488284613163}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72bcd18e-cec4-4051-9f09-da874a967c28", "name": "Update task entry:default@CompileArkTS output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader_out/default/ets cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488284829044}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54189f13-c8b9-410d-a409-aab445556125", "name": "Incremental task entry:default@CompileArkTS post-execution cost:5 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488285096473}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c99db419-c06f-4936-9a22-26953b0f7159", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488286341267}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d39620f3-8726-4eb2-a6ed-85e92fd17b6a", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488286809326}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1c8a2e7-1320-4e4b-8288-8b427012724a", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488288946178}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd8635ac-afa5-4613-a9e3-37db04b2672d", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488289134005}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "49954de7-cfd8-412b-a4d9-f8090864ca7d", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/base/profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488289245206}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02d37e65-b897-482b-83c4-390fc63db061", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default/pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488289492350}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af751d66-dbac-4cc5-b85a-3f136a21b54b", "name": "Update task entry:default@BuildJS output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader_out/default/js cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488289641084}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ca27f43-6d9f-4ca0-886c-182ff6e217ba", "name": "Incremental task entry:default@BuildJS post-execution cost:5 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488289861734}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37b0a3b2-252c-4b9b-b2f3-c6e56aa8f126", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488290013803}, "additional": {"logType": "debug", "children": []}}], "workLog": []}