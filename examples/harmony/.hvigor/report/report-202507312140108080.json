{"version": "2.0", "ppid": 50563, "events": [{"head": {"id": "dc6f5cb9-3899-4bb2-94d7-0e9c6bf13aa3", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488304027567}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe2a51e6-a62e-400d-ac0e-4fd1fe7392e7", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488309476601}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8826d82e-d505-4ec3-9db2-003c9e05c7d1", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488309979495}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6781a5d-e78a-435f-a56d-b956bfd7be2c", "name": "worker[6] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652488467558182}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f80b6fce-0110-40c3-a9d6-b8a15b5823a2", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599148206163}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52cc938f-c2ad-422c-83ef-ac0e16d2d010", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599153706721, "endTime": 652599460971437}, "additional": {"children": ["2789a985-193a-4ad7-8d98-3262a479c7c2", "1e6d5926-2abd-4dd7-8001-1e40c569f65e", "3cb36db4-b4a6-451c-995b-bad95c6f3878", "40f87095-e5ee-4665-8737-e8f74bd8db21", "1ca1dc06-1a9a-464b-9e40-3b45968f58de", "74984ac9-4f96-4437-9eff-9a1ab956a791", "54e64a04-a35d-45ae-a7a4-bae6711118d7"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "809cd784-b832-43b9-a021-400df791330d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2789a985-193a-4ad7-8d98-3262a479c7c2", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599153707995, "endTime": 652599168755388}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "52cc938f-c2ad-422c-83ef-ac0e16d2d010", "logId": "2e966976-f0f6-4760-89ab-85182ec9966a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1e6d5926-2abd-4dd7-8001-1e40c569f65e", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599168803083, "endTime": 652599458236031}, "additional": {"children": ["a4576115-2dbf-4ec2-828e-d4aaa0b30c6d", "009672d0-b27e-4629-bebb-8d8c18bb1c6a", "2a99fd17-c283-404e-9390-0aaf7226773a", "2eafb922-defb-424d-9f3f-564845a6d8ba", "92b99bab-5747-4185-9342-c70f7ca163d6", "5ce0bf24-f840-40f7-818f-82f3d1a348ea", "fe7fbee9-44dd-41fc-b46e-20359c49d8aa", "0f41ab2e-6d4e-41a5-9264-6fa7f03aa590", "d5d63ee5-d144-4a2a-b32d-499ee3de4438"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "52cc938f-c2ad-422c-83ef-ac0e16d2d010", "logId": "74fb261e-8f72-49cd-b9e9-945e26691117"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3cb36db4-b4a6-451c-995b-bad95c6f3878", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599458270549, "endTime": 652599460943593}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "52cc938f-c2ad-422c-83ef-ac0e16d2d010", "logId": "328b8258-4814-4502-864f-777c976455f2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "40f87095-e5ee-4665-8737-e8f74bd8db21", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599460955446, "endTime": 652599460964789}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "52cc938f-c2ad-422c-83ef-ac0e16d2d010", "logId": "1e43b896-63ac-49a4-997d-d2ced4f70ec9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1ca1dc06-1a9a-464b-9e40-3b45968f58de", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599156222127, "endTime": 652599156260239}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "52cc938f-c2ad-422c-83ef-ac0e16d2d010", "logId": "0a67bc8c-6519-4ec2-a4c9-af799fcb2f6d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0a67bc8c-6519-4ec2-a4c9-af799fcb2f6d", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599156222127, "endTime": 652599156260239}, "additional": {"logType": "info", "children": [], "durationId": "1ca1dc06-1a9a-464b-9e40-3b45968f58de", "parent": "809cd784-b832-43b9-a021-400df791330d"}}, {"head": {"id": "74984ac9-4f96-4437-9eff-9a1ab956a791", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599162581122, "endTime": 652599162603104}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "52cc938f-c2ad-422c-83ef-ac0e16d2d010", "logId": "74aacb51-db33-4786-bd8c-f0926585b5d7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "74aacb51-db33-4786-bd8c-f0926585b5d7", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599162581122, "endTime": 652599162603104}, "additional": {"logType": "info", "children": [], "durationId": "74984ac9-4f96-4437-9eff-9a1ab956a791", "parent": "809cd784-b832-43b9-a021-400df791330d"}}, {"head": {"id": "dd996276-4439-4edd-a7ea-5fefbe6549f3", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599162667234}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6a467d4-2940-4b78-b355-fd338b3bd2f8", "name": "Cache service initialization finished in 6 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599168364485}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e966976-f0f6-4760-89ab-85182ec9966a", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599153707995, "endTime": 652599168755388}, "additional": {"logType": "info", "children": [], "durationId": "2789a985-193a-4ad7-8d98-3262a479c7c2", "parent": "809cd784-b832-43b9-a021-400df791330d"}}, {"head": {"id": "a4576115-2dbf-4ec2-828e-d4aaa0b30c6d", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599173428164, "endTime": 652599173442855}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1e6d5926-2abd-4dd7-8001-1e40c569f65e", "logId": "1a32b795-3397-4f60-80e5-5dfeadefe224"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "009672d0-b27e-4629-bebb-8d8c18bb1c6a", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599173466534, "endTime": 652599180711898}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1e6d5926-2abd-4dd7-8001-1e40c569f65e", "logId": "3efdca5d-7ecb-4251-b404-86658e1daae7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2a99fd17-c283-404e-9390-0aaf7226773a", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599180734088, "endTime": 652599274309945}, "additional": {"children": ["aeb4728c-21c9-4064-819f-ae7c72bc928b", "e829e610-5805-4806-a3a6-35c1c13ee505", "fcd3c4dd-5126-431a-99fb-02654e85455f"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1e6d5926-2abd-4dd7-8001-1e40c569f65e", "logId": "e5bfaf0f-e29c-4d06-b3be-a03f9f67a5c1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2eafb922-defb-424d-9f3f-564845a6d8ba", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599274330127, "endTime": 652599328587644}, "additional": {"children": ["521b065d-47ab-4d32-9b0c-415eb884625e", "0841aa85-1d34-4ce7-9423-72c9cc7e82d4"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1e6d5926-2abd-4dd7-8001-1e40c569f65e", "logId": "ba9a6eeb-3c27-4bbc-b3df-b1f5ff5d51cf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "92b99bab-5747-4185-9342-c70f7ca163d6", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599328603387, "endTime": 652599432791785}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1e6d5926-2abd-4dd7-8001-1e40c569f65e", "logId": "416718a3-d4d4-42c7-b1ce-61de918429cc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5ce0bf24-f840-40f7-818f-82f3d1a348ea", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599433785251, "endTime": 652599441480754}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1e6d5926-2abd-4dd7-8001-1e40c569f65e", "logId": "f336fde3-d069-4e72-ae61-d631562b7063"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fe7fbee9-44dd-41fc-b46e-20359c49d8aa", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599441506923, "endTime": 652599458039542}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1e6d5926-2abd-4dd7-8001-1e40c569f65e", "logId": "cdcc9e14-43ef-434d-b071-5a5130647264"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0f41ab2e-6d4e-41a5-9264-6fa7f03aa590", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599458069933, "endTime": 652599458218518}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1e6d5926-2abd-4dd7-8001-1e40c569f65e", "logId": "30e41dde-05a7-40f0-8697-40bfaa9e1b38"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1a32b795-3397-4f60-80e5-5dfeadefe224", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599173428164, "endTime": 652599173442855}, "additional": {"logType": "info", "children": [], "durationId": "a4576115-2dbf-4ec2-828e-d4aaa0b30c6d", "parent": "74fb261e-8f72-49cd-b9e9-945e26691117"}}, {"head": {"id": "3efdca5d-7ecb-4251-b404-86658e1daae7", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599173466534, "endTime": 652599180711898}, "additional": {"logType": "info", "children": [], "durationId": "009672d0-b27e-4629-bebb-8d8c18bb1c6a", "parent": "74fb261e-8f72-49cd-b9e9-945e26691117"}}, {"head": {"id": "aeb4728c-21c9-4064-819f-ae7c72bc928b", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599181917136, "endTime": 652599181944293}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2a99fd17-c283-404e-9390-0aaf7226773a", "logId": "eae5d187-f884-4250-86d7-1a6bbc9c27de"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eae5d187-f884-4250-86d7-1a6bbc9c27de", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599181917136, "endTime": 652599181944293}, "additional": {"logType": "info", "children": [], "durationId": "aeb4728c-21c9-4064-819f-ae7c72bc928b", "parent": "e5bfaf0f-e29c-4d06-b3be-a03f9f67a5c1"}}, {"head": {"id": "e829e610-5805-4806-a3a6-35c1c13ee505", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599185963459, "endTime": 652599273391214}, "additional": {"children": ["11da8420-afb9-4db7-9dcc-fe251a59f16a", "f25ab638-56c2-4e91-8af4-d9251f9e9cc1"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2a99fd17-c283-404e-9390-0aaf7226773a", "logId": "635f2247-9761-45c1-bb39-183a5f3f170f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "11da8420-afb9-4db7-9dcc-fe251a59f16a", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599185966971, "endTime": 652599192535962}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e829e610-5805-4806-a3a6-35c1c13ee505", "logId": "bc12cb90-ff6b-4a75-a8f9-29f461aa5fe7"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f25ab638-56c2-4e91-8af4-d9251f9e9cc1", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599192560617, "endTime": 652599273361988}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e829e610-5805-4806-a3a6-35c1c13ee505", "logId": "18e2b311-6154-4c6b-bdf6-cbb99ff17352"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3253c0f1-2119-4203-960a-d54627482c02", "name": "hvigorfile, resolving /Users/<USER>/github/Ling<PERSON>ia/examples/harmony/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599185979251}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8bb7876e-100c-4378-afad-d4aaf6f64a95", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599192382485}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc12cb90-ff6b-4a75-a8f9-29f461aa5fe7", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599185966971, "endTime": 652599192535962}, "additional": {"logType": "info", "children": [], "durationId": "11da8420-afb9-4db7-9dcc-fe251a59f16a", "parent": "635f2247-9761-45c1-bb39-183a5f3f170f"}}, {"head": {"id": "eee399eb-ff0e-467a-a428-776a15d5801e", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599192580445}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c787ba93-edaa-40ed-b503-94ea27e1c693", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599198315093}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad785678-b1b1-4a0f-ab87-81ec6e89688a", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599198430738}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "756af0fb-2a79-49e8-a461-a893f3a8c03a", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599198562008}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d82c408-00ff-45eb-aeb9-e0376da7a3af", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599198653364}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e2c14a8-ef7a-4fbf-96d0-3c99364b0906", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599199723170}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51fa3f29-b7cd-48cd-8cfe-711de3b16126", "name": "not found resModel json file in : /Users/<USER>/github/LingXia/examples/harmony/entry/src/ohosTest/module.json5", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599200385909}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "197f485f-c481-44d6-93ad-31d34e1084f9", "name": "not found resModel json file in : /Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/ohosTest/module.json5", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599204540087}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35dbb449-9df2-46dd-9f78-28b19ce3d39d", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599212716453}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee88f501-f671-4267-8df5-0cfef2bf4ad4", "name": "Sdk init in 48 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599254778797}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "811c2915-5abd-4dcc-b0b5-4ebd59ec1b36", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599254925589}, "additional": {"time": {"year": 2025, "month": 7, "day": 31, "hour": 21, "minute": 40}, "markType": "other"}}, {"head": {"id": "bccf7f57-307e-48cf-bf63-e8f7a92fcbe4", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599254945537}, "additional": {"time": {"year": 2025, "month": 7, "day": 31, "hour": 21, "minute": 40}, "markType": "other"}}, {"head": {"id": "25bf4b98-1d72-4636-8c48-5672d3cb35a3", "name": "Project task initialization takes 18 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599273035487}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dac5b072-b7e2-43df-9f88-a8696fe59297", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599273165823}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dfbcc14c-bc96-43b5-b15a-1f4f5534c484", "name": "hvigorfile, no custom plugins were found in /Users/<USER>/github/LingXia/examples/harmony/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599273231183}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d941e0f-069b-42d4-a707-6b85b9b18686", "name": "hvigorfile, resolve finished /Users/<USER>/github/Ling<PERSON>ia/examples/harmony/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599273288270}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "18e2b311-6154-4c6b-bdf6-cbb99ff17352", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599192560617, "endTime": 652599273361988}, "additional": {"logType": "info", "children": [], "durationId": "f25ab638-56c2-4e91-8af4-d9251f9e9cc1", "parent": "635f2247-9761-45c1-bb39-183a5f3f170f"}}, {"head": {"id": "635f2247-9761-45c1-bb39-183a5f3f170f", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599185963459, "endTime": 652599273391214}, "additional": {"logType": "info", "children": ["bc12cb90-ff6b-4a75-a8f9-29f461aa5fe7", "18e2b311-6154-4c6b-bdf6-cbb99ff17352"], "durationId": "e829e610-5805-4806-a3a6-35c1c13ee505", "parent": "e5bfaf0f-e29c-4d06-b3be-a03f9f67a5c1"}}, {"head": {"id": "fcd3c4dd-5126-431a-99fb-02654e85455f", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599274263922, "endTime": 652599274283033}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2a99fd17-c283-404e-9390-0aaf7226773a", "logId": "d40de9fe-aac5-49d4-b92f-96f89407e2dc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d40de9fe-aac5-49d4-b92f-96f89407e2dc", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599274263922, "endTime": 652599274283033}, "additional": {"logType": "info", "children": [], "durationId": "fcd3c4dd-5126-431a-99fb-02654e85455f", "parent": "e5bfaf0f-e29c-4d06-b3be-a03f9f67a5c1"}}, {"head": {"id": "e5bfaf0f-e29c-4d06-b3be-a03f9f67a5c1", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599180734088, "endTime": 652599274309945}, "additional": {"logType": "info", "children": ["eae5d187-f884-4250-86d7-1a6bbc9c27de", "635f2247-9761-45c1-bb39-183a5f3f170f", "d40de9fe-aac5-49d4-b92f-96f89407e2dc"], "durationId": "2a99fd17-c283-404e-9390-0aaf7226773a", "parent": "74fb261e-8f72-49cd-b9e9-945e26691117"}}, {"head": {"id": "521b065d-47ab-4d32-9b0c-415eb884625e", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599274817500, "endTime": 652599304417089}, "additional": {"children": ["0823f141-ff32-4a2a-a0d9-502d84073f28", "0fa09f96-5b34-476a-8280-f203006b04a6", "e857a52d-dd47-4f90-b5aa-a2b1d2591a09"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2eafb922-defb-424d-9f3f-564845a6d8ba", "logId": "aaba43c9-32a0-4dbf-8371-27fa4bfdb614"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0823f141-ff32-4a2a-a0d9-502d84073f28", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599277467797, "endTime": 652599277499292}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "521b065d-47ab-4d32-9b0c-415eb884625e", "logId": "bb5dc2df-e86a-4c8e-819d-d7f01ab12348"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bb5dc2df-e86a-4c8e-819d-d7f01ab12348", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599277467797, "endTime": 652599277499292}, "additional": {"logType": "info", "children": [], "durationId": "0823f141-ff32-4a2a-a0d9-502d84073f28", "parent": "aaba43c9-32a0-4dbf-8371-27fa4bfdb614"}}, {"head": {"id": "0fa09f96-5b34-476a-8280-f203006b04a6", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599280629815, "endTime": 652599302923418}, "additional": {"children": ["705b8f01-fea5-447b-8f03-f3a546ca5c5a", "be776823-9fad-408d-83b2-3ca452df0986"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "521b065d-47ab-4d32-9b0c-415eb884625e", "logId": "16c111ed-591d-4e42-bde6-f156839858be"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "705b8f01-fea5-447b-8f03-f3a546ca5c5a", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599280631715, "endTime": 652599286342088}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0fa09f96-5b34-476a-8280-f203006b04a6", "logId": "84f548aa-6269-4433-9d5c-5854fd01c1ff"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "be776823-9fad-408d-83b2-3ca452df0986", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599286367795, "endTime": 652599302906646}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0fa09f96-5b34-476a-8280-f203006b04a6", "logId": "40cca4c7-2ee7-4818-8c0d-c965818a1bad"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4e55d3e7-ca8b-4aaa-bc8f-5951596263e0", "name": "hvigorfile, resolving /Users/<USER>/github/Ling<PERSON>ia/examples/harmony/entry/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599280641086}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c08a6164-4e57-43c3-9a23-15c431dde8f8", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599286092307}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84f548aa-6269-4433-9d5c-5854fd01c1ff", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599280631715, "endTime": 652599286342088}, "additional": {"logType": "info", "children": [], "durationId": "705b8f01-fea5-447b-8f03-f3a546ca5c5a", "parent": "16c111ed-591d-4e42-bde6-f156839858be"}}, {"head": {"id": "35c90734-0d7e-46b2-b2ce-ce938222501a", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599286396252}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "070d5127-1968-4380-8a50-0a95311108de", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599294766367}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e1824ea4-d98b-4d96-becb-d53f8a43d960", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599295258197}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62ac5dd9-e83a-4c7c-a146-a7f9cb11b905", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599295588373}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a84d51f6-3e6c-43b6-8521-9105dc2ae1c7", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599295781070}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02513a9a-a868-4eee-8c6b-cecd7c86f9d5", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599295915860}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d38846d1-30b9-4150-a77a-267b0d40a874", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599296117980}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b6dfbca-0738-4723-93bf-d642707ac8d6", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599296377078}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9450a63-83be-4bd7-8bc5-9c2a680ed5ce", "name": "Module entry task initialization takes 3 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599302310576}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eec3f412-cab8-4e5d-8a3f-f5ecf0cb3a52", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599302589699}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "98650c16-5122-4e39-a43a-53dbe1cc1aa7", "name": "hvigorfile, no custom plugins were found in /Users/<USER>/github/LingXia/examples/harmony/entry/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599302766574}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "545e3970-8bf1-42f0-ace0-fe2f68742d64", "name": "hvigorfile, resolve finished /Users/<USER>/github/LingXia/examples/harmony/entry/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599302852310}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40cca4c7-2ee7-4818-8c0d-c965818a1bad", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599286367795, "endTime": 652599302906646}, "additional": {"logType": "info", "children": [], "durationId": "be776823-9fad-408d-83b2-3ca452df0986", "parent": "16c111ed-591d-4e42-bde6-f156839858be"}}, {"head": {"id": "16c111ed-591d-4e42-bde6-f156839858be", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599280629815, "endTime": 652599302923418}, "additional": {"logType": "info", "children": ["84f548aa-6269-4433-9d5c-5854fd01c1ff", "40cca4c7-2ee7-4818-8c0d-c965818a1bad"], "durationId": "0fa09f96-5b34-476a-8280-f203006b04a6", "parent": "aaba43c9-32a0-4dbf-8371-27fa4bfdb614"}}, {"head": {"id": "e857a52d-dd47-4f90-b5aa-a2b1d2591a09", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599304255253, "endTime": 652599304275752}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "521b065d-47ab-4d32-9b0c-415eb884625e", "logId": "de2232a2-ee89-4bd2-9e63-de27d9df0f52"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "de2232a2-ee89-4bd2-9e63-de27d9df0f52", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599304255253, "endTime": 652599304275752}, "additional": {"logType": "info", "children": [], "durationId": "e857a52d-dd47-4f90-b5aa-a2b1d2591a09", "parent": "aaba43c9-32a0-4dbf-8371-27fa4bfdb614"}}, {"head": {"id": "aaba43c9-32a0-4dbf-8371-27fa4bfdb614", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599274817500, "endTime": 652599304417089}, "additional": {"logType": "info", "children": ["bb5dc2df-e86a-4c8e-819d-d7f01ab12348", "16c111ed-591d-4e42-bde6-f156839858be", "de2232a2-ee89-4bd2-9e63-de27d9df0f52"], "durationId": "521b065d-47ab-4d32-9b0c-415eb884625e", "parent": "ba9a6eeb-3c27-4bbc-b3df-b1f5ff5d51cf"}}, {"head": {"id": "0841aa85-1d34-4ce7-9423-72c9cc7e82d4", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599305291820, "endTime": 652599328565267}, "additional": {"children": ["9907e9fc-0ac1-4844-b8b5-ee8d1b1c1d0a", "ae022cd3-32b7-4807-8cd3-4b7f6789c223", "8c035a77-a9ad-4aaf-96a6-fd2f7095f454"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "2eafb922-defb-424d-9f3f-564845a6d8ba", "logId": "bf511ac7-4fed-4087-83b1-1a4875ad4f8f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9907e9fc-0ac1-4844-b8b5-ee8d1b1c1d0a", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599307836502, "endTime": 652599307857723}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0841aa85-1d34-4ce7-9423-72c9cc7e82d4", "logId": "2cee41f6-b2f1-4b11-9085-10987b2fac1a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2cee41f6-b2f1-4b11-9085-10987b2fac1a", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599307836502, "endTime": 652599307857723}, "additional": {"logType": "info", "children": [], "durationId": "9907e9fc-0ac1-4844-b8b5-ee8d1b1c1d0a", "parent": "bf511ac7-4fed-4087-83b1-1a4875ad4f8f"}}, {"head": {"id": "ae022cd3-32b7-4807-8cd3-4b7f6789c223", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599309398972, "endTime": 652599326829481}, "additional": {"children": ["87129a67-7c58-4e58-b46b-750b37499c2e", "3a08bf74-66cc-4efe-934e-934a864310e6"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0841aa85-1d34-4ce7-9423-72c9cc7e82d4", "logId": "e0e49723-0e6f-4f9b-976e-713328e4fdb2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "87129a67-7c58-4e58-b46b-750b37499c2e", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599309400800, "endTime": 652599316014970}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ae022cd3-32b7-4807-8cd3-4b7f6789c223", "logId": "e0a191a7-4e8c-4c6a-b24f-d014da8b4fa6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3a08bf74-66cc-4efe-934e-934a864310e6", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599316037263, "endTime": 652599326806775}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ae022cd3-32b7-4807-8cd3-4b7f6789c223", "logId": "4547bafb-23ce-401e-b1a6-20cb5fbbc93d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c29c8017-8bf9-4feb-a1c2-9e1b39ef7927", "name": "hvigorfile, resolving /Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599309409302}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b61d0c11-8560-485e-8bbb-308c723812b8", "name": "hvigorfile, require result:  { default: { system: [Function: harTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599315884499}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0a191a7-4e8c-4c6a-b24f-d014da8b4fa6", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599309400800, "endTime": 652599316014970}, "additional": {"logType": "info", "children": [], "durationId": "87129a67-7c58-4e58-b46b-750b37499c2e", "parent": "e0e49723-0e6f-4f9b-976e-713328e4fdb2"}}, {"head": {"id": "70b97fb8-afc2-484a-b99b-f56f42a5f9ec", "name": "hvigorfile, binding system plugins [Function: harTasks]", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599316053568}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba5c6783-e237-4f50-b3a7-7b35ba4cef24", "name": "Start initialize module-target build option map, moduleName=lingxia, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599321829466}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c43f986-4227-402a-b897-146d23038841", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599321972827}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed1fa338-633c-4188-a665-36d38d4f4d7f", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599322156954}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac897581-e4d4-47bf-8f8f-88e067141995", "name": "Module 'lingxia' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599322536462}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36917ddd-2082-4bce-ba72-296f1c2beac4", "name": "Module 'lingxia' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599322696768}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ee8dfe5-d6e2-44d6-8b90-c40be55f2b6a", "name": "End initialize module-target build option map, moduleName=lingxia", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599322787719}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c5b59c8-3aed-4eb3-91e9-a195714bd947", "name": "Module 'lingxia' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599322973466}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2171125-d048-4b32-97fc-2a69cbc3d6b4", "name": "Module lingxia task initialization takes 2 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599326077451}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87a7dc67-dfca-4475-ae08-ed7b77de627b", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599326457960}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75751c3a-a233-458a-aa4d-58316b0de98a", "name": "hvigorfile, no custom plugins were found in /Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599326589516}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "131c3016-b8b9-4908-8d36-076277e897f0", "name": "hvigorfile, resolve finished /Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599326730060}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4547bafb-23ce-401e-b1a6-20cb5fbbc93d", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599316037263, "endTime": 652599326806775}, "additional": {"logType": "info", "children": [], "durationId": "3a08bf74-66cc-4efe-934e-934a864310e6", "parent": "e0e49723-0e6f-4f9b-976e-713328e4fdb2"}}, {"head": {"id": "e0e49723-0e6f-4f9b-976e-713328e4fdb2", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599309398972, "endTime": 652599326829481}, "additional": {"logType": "info", "children": ["e0a191a7-4e8c-4c6a-b24f-d014da8b4fa6", "4547bafb-23ce-401e-b1a6-20cb5fbbc93d"], "durationId": "ae022cd3-32b7-4807-8cd3-4b7f6789c223", "parent": "bf511ac7-4fed-4087-83b1-1a4875ad4f8f"}}, {"head": {"id": "8c035a77-a9ad-4aaf-96a6-fd2f7095f454", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599328513844, "endTime": 652599328536049}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "0841aa85-1d34-4ce7-9423-72c9cc7e82d4", "logId": "6b082457-e8a6-4017-beda-c016a1649ea9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6b082457-e8a6-4017-beda-c016a1649ea9", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599328513844, "endTime": 652599328536049}, "additional": {"logType": "info", "children": [], "durationId": "8c035a77-a9ad-4aaf-96a6-fd2f7095f454", "parent": "bf511ac7-4fed-4087-83b1-1a4875ad4f8f"}}, {"head": {"id": "bf511ac7-4fed-4087-83b1-1a4875ad4f8f", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599305291820, "endTime": 652599328565267}, "additional": {"logType": "info", "children": ["2cee41f6-b2f1-4b11-9085-10987b2fac1a", "e0e49723-0e6f-4f9b-976e-713328e4fdb2", "6b082457-e8a6-4017-beda-c016a1649ea9"], "durationId": "0841aa85-1d34-4ce7-9423-72c9cc7e82d4", "parent": "ba9a6eeb-3c27-4bbc-b3df-b1f5ff5d51cf"}}, {"head": {"id": "ba9a6eeb-3c27-4bbc-b3df-b1f5ff5d51cf", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599274330127, "endTime": 652599328587644}, "additional": {"logType": "info", "children": ["aaba43c9-32a0-4dbf-8371-27fa4bfdb614", "bf511ac7-4fed-4087-83b1-1a4875ad4f8f"], "durationId": "2eafb922-defb-424d-9f3f-564845a6d8ba", "parent": "74fb261e-8f72-49cd-b9e9-945e26691117"}}, {"head": {"id": "d0af0ed9-919b-4680-a8e7-52cb6a1f7718", "name": "watch files: [\n  '/Users/<USER>/github/LingXia/examples/harmony/hvigorfile.ts',\n  '/Users/<USER>/github/LingXia/examples/harmony/entry/hvigorfile.ts',\n  '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/hvigorfile.ts',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor-ohos-plugin/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor-ohos-plugin/src/plugin/factory/plugin-factory.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/log4js.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/debug/src/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/debug/src/node.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/debug/src/common.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/ms/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/rfdc/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/configuration.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/layouts.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/date-format/lib/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/levels.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/clustering.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/LoggingEvent.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/flatted/cjs/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/adapters.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/console.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/stdout.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/stderr.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/logLevelFilter.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/categoryFilter.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/noLogFilter.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/file.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/lib/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/lib/RollingFileWriteStream.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/fs/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/universalify/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/graceful-fs/graceful-fs.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/graceful-fs/polyfills.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/graceful-fs/legacy-streams.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/graceful-fs/clone.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/copy-sync/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/copy-sync/copy-sync.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/mkdirs/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/mkdirs/mkdirs.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/mkdirs/win32.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/mkdirs/mkdirs-sync.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/util/utimes.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/util/stat.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/copy/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/copy/copy.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/path-exists/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/empty/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/remove/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/remove/rimraf.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/file.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/link.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/symlink.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/symlink-paths.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/symlink-type.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/json/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/json/jsonfile.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/jsonfile/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/json/output-json.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/json/output-json-sync.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/move-sync/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/move-sync/move-sync.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/move/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/move/move.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/output/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/lib/now.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/lib/fileNameFormatter.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/lib/fileNameParser.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/lib/moveAndMaybeCompressFile.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/lib/RollingFileStream.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/lib/DateRollingFileStream.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/dateFile.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/fileSync.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/tcp.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/categories.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/logger.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/connect-logger.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/recording.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/src/common/util/local-file-writer.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/fs/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/universalify/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/copy/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/copy/copy.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/mkdirs/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/mkdirs/make-dir.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/mkdirs/utils.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/path-exists/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/util/utimes.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/util/stat.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/copy/copy-sync.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/empty/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/remove/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/ensure/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/ensure/file.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/ensure/link.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/ensure/symlink.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/ensure/symlink-paths.js',\n  ... 1869 more items\n]", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599359821109}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c02b796a-6a52-4ba5-b7a0-b64298c502a9", "name": "hvigorfile, resolve hvigorfile dependencies in 104 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599432650367}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "416718a3-d4d4-42c7-b1ce-61de918429cc", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599328603387, "endTime": 652599432791785}, "additional": {"logType": "info", "children": [], "durationId": "92b99bab-5747-4185-9342-c70f7ca163d6", "parent": "74fb261e-8f72-49cd-b9e9-945e26691117"}}, {"head": {"id": "d5d63ee5-d144-4a2a-b32d-499ee3de4438", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599433448820, "endTime": 652599433767991}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1e6d5926-2abd-4dd7-8001-1e40c569f65e", "logId": "47e4e5c8-cf00-4aca-98ca-cdace409ce13"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "13293166-abb3-4be2-8217-d49c8566087e", "name": "project has submodules:entry,lingxia", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599433482787}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c330795e-5b70-4b1f-9535-12310697942c", "name": "module:lingxia no need to execute packageHap", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599433692752}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47e4e5c8-cf00-4aca-98ca-cdace409ce13", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599433448820, "endTime": 652599433767991}, "additional": {"logType": "info", "children": [], "durationId": "d5d63ee5-d144-4a2a-b32d-499ee3de4438", "parent": "74fb261e-8f72-49cd-b9e9-945e26691117"}}, {"head": {"id": "bc0b6d1c-f1c0-4800-b4d0-b31fa2bc8ed6", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599434402354}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76bc93f4-36d2-4956-8de4-a8cfc845d762", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599440977925}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f336fde3-d069-4e72-ae61-d631562b7063", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599433785251, "endTime": 652599441480754}, "additional": {"logType": "info", "children": [], "durationId": "5ce0bf24-f840-40f7-818f-82f3d1a348ea", "parent": "74fb261e-8f72-49cd-b9e9-945e26691117"}}, {"head": {"id": "5f3ec44f-8ca9-49d7-9fa4-02ae67f4d48b", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599441526354}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c3997fa-8b86-47a7-ba96-b37a1a7c207a", "name": "Module harmony Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599442909090}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c6a7663-135b-40fb-88d4-c0957a657dcc", "name": "Module harmony's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599443038930}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32cf81ef-9d7a-40f3-80ad-59a9c4ce1353", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599444410823}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c15ec382-8b51-4dd5-b316-89b4f393e1bc", "name": "Module entry Collected Dependency: /Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599446794755}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93853d03-b7ff-429d-b0f5-849a8d883c67", "name": "Module entry's total dependency: 1", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599447038233}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60a41bfe-5898-4bd4-8149-7330e9c8913d", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599454406881}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e98cfb89-f96b-4d9d-bc2a-08364bcb7374", "name": "Module ling<PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599455416010}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ed47c3c-8874-4b98-84d4-894306227e6d", "name": "Module lingxia's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599455536009}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cdcc9e14-43ef-434d-b071-5a5130647264", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599441506923, "endTime": 652599458039542}, "additional": {"logType": "info", "children": [], "durationId": "fe7fbee9-44dd-41fc-b46e-20359c49d8aa", "parent": "74fb261e-8f72-49cd-b9e9-945e26691117"}}, {"head": {"id": "2b7f895e-c2aa-432e-a8fc-b56f56cc8305", "name": "Configuration phase cost:285 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599458105131}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30e41dde-05a7-40f0-8697-40bfaa9e1b38", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599458069933, "endTime": 652599458218518}, "additional": {"logType": "info", "children": [], "durationId": "0f41ab2e-6d4e-41a5-9264-6fa7f03aa590", "parent": "74fb261e-8f72-49cd-b9e9-945e26691117"}}, {"head": {"id": "74fb261e-8f72-49cd-b9e9-945e26691117", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599168803083, "endTime": 652599458236031}, "additional": {"logType": "info", "children": ["1a32b795-3397-4f60-80e5-5dfeadefe224", "3efdca5d-7ecb-4251-b404-86658e1daae7", "e5bfaf0f-e29c-4d06-b3be-a03f9f67a5c1", "ba9a6eeb-3c27-4bbc-b3df-b1f5ff5d51cf", "416718a3-d4d4-42c7-b1ce-61de918429cc", "f336fde3-d069-4e72-ae61-d631562b7063", "cdcc9e14-43ef-434d-b071-5a5130647264", "30e41dde-05a7-40f0-8697-40bfaa9e1b38", "47e4e5c8-cf00-4aca-98ca-cdace409ce13"], "durationId": "1e6d5926-2abd-4dd7-8001-1e40c569f65e", "parent": "809cd784-b832-43b9-a021-400df791330d"}}, {"head": {"id": "54e64a04-a35d-45ae-a7a4-bae6711118d7", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599460900227, "endTime": 652599460923656}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "52cc938f-c2ad-422c-83ef-ac0e16d2d010", "logId": "da3c84ab-87ca-4806-864e-5aebc58154af"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "da3c84ab-87ca-4806-864e-5aebc58154af", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599460900227, "endTime": 652599460923656}, "additional": {"logType": "info", "children": [], "durationId": "54e64a04-a35d-45ae-a7a4-bae6711118d7", "parent": "809cd784-b832-43b9-a021-400df791330d"}}, {"head": {"id": "328b8258-4814-4502-864f-777c976455f2", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599458270549, "endTime": 652599460943593}, "additional": {"logType": "info", "children": [], "durationId": "3cb36db4-b4a6-451c-995b-bad95c6f3878", "parent": "809cd784-b832-43b9-a021-400df791330d"}}, {"head": {"id": "1e43b896-63ac-49a4-997d-d2ced4f70ec9", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599460955446, "endTime": 652599460964789}, "additional": {"logType": "info", "children": [], "durationId": "40f87095-e5ee-4665-8737-e8f74bd8db21", "parent": "809cd784-b832-43b9-a021-400df791330d"}}, {"head": {"id": "809cd784-b832-43b9-a021-400df791330d", "name": "init", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599153706721, "endTime": 652599460971437}, "additional": {"logType": "info", "children": ["2e966976-f0f6-4760-89ab-85182ec9966a", "74fb261e-8f72-49cd-b9e9-945e26691117", "328b8258-4814-4502-864f-777c976455f2", "1e43b896-63ac-49a4-997d-d2ced4f70ec9", "0a67bc8c-6519-4ec2-a4c9-af799fcb2f6d", "74aacb51-db33-4786-bd8c-f0926585b5d7", "da3c84ab-87ca-4806-864e-5aebc58154af"], "durationId": "52cc938f-c2ad-422c-83ef-ac0e16d2d010"}}, {"head": {"id": "18b5d4ce-3208-4744-86a2-7a87fc743dfa", "name": "Configuration task cost before running: 311 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599461250218}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a759151-d122-4944-8cca-4c768ce26039", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599465443132, "endTime": 652599474738656}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": ["The input file '/Users/<USER>/github/LingXia/examples/harmony/entry/oh-package.json5' has been changed."], "detailId": "16ccd698-85b8-4ae8-a5a6-be5f6cb5e146", "logId": "1b1e3ed2-9c6a-471e-ac9c-811f7d836ab6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "16ccd698-85b8-4ae8-a5a6-be5f6cb5e146", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599463366160}, "additional": {"logType": "detail", "children": [], "durationId": "3a759151-d122-4944-8cca-4c768ce26039"}}, {"head": {"id": "ace65af2-4159-413b-ba5f-6ea4f1723a37", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599463703733}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c680f323-e1be-4b34-984a-9eb39d1cb954", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599463823692}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "337dbf63-4a0a-47db-89a3-f197356778f9", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599465456044}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "678f8f7d-3966-4c9f-bc1e-e24045056e88", "name": "entry:default@PreBuild is not up-to-date, since the input file '/Users/<USER>/github/LingXia/examples/harmony/entry/oh-package.json5' has been changed.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599469391521}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2188173-332f-458d-ba9e-8c9e9c92b71a", "name": "Incremental task entry:default@PreBuild pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599469549714}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6f29da8f-ddbe-4c73-a7b7-35d96721287d", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599469651209}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "abdd894a-2b89-42f0-bbac-8c856d08862c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599469709168}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc93d352-c297-41b5-8174-d33e41f7a5d7", "name": "current product is not Atomic service.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599473956134}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87fff285-4b07-424b-bab1-a15e8f4ea156", "name": "Use tool [darwin: JAVA_HOME, CLASSPATH]\n [\n  {\n    JAVA_HOME: '/Applications/Android Studio.app/Contents/jbr/Contents/Home'\n  },\n  { CLASSPATH: undefined }\n]", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599474184101}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f64ff59-62a9-4a25-813f-e566b74ade96", "name": "Use tool [darwin: NODE_HOME]\n [ { NODE_HOME: undefined } ]", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599474308803}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7c57d3d-3c14-415e-befe-85be5ac9602f", "name": "entry : default@PreBuild cost memory 0.4253082275390625", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599474525623}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3c3b83e-befe-42ec-9922-47cafc73e471", "name": "runTaskFromQueue task cost before running: 325 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599474640176}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b1e3ed2-9c6a-471e-ac9c-811f7d836ab6", "name": "Finished :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599465443132, "endTime": 652599474738656, "totalTime": 9165772}, "additional": {"logType": "info", "children": [], "durationId": "3a759151-d122-4944-8cca-4c768ce26039"}}, {"head": {"id": "266db86b-1313-402d-9ffc-bbda04ac44d8", "name": "lingxia:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599481312900, "endTime": 652599610902560}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "lingxia", "category": "Verification", "taskRunReasons": ["The task snapshots are different."], "detailId": "114ed60a-8253-460b-adf7-a23a720c7f03", "logId": "d339051f-a752-43bb-8b9e-31e9e6b262f2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "114ed60a-8253-460b-adf7-a23a720c7f03", "name": "create lingxia:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599477965797}, "additional": {"logType": "detail", "children": [], "durationId": "266db86b-1313-402d-9ffc-bbda04ac44d8"}}, {"head": {"id": "8cb5d781-2dcd-47db-a319-8b0b7ad8ac1e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599478437998}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce2fa908-ad58-4c84-9c93-e4c1fd8b7057", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599478693807}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9eb80912-28bb-43db-92fe-0fe2edbe7695", "name": "Executing task :lingxia:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599481336502}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13752e16-b295-4da7-89ac-9be9a46abf63", "name": "lingxia:default@PreBuild is not up-to-date, since the task snapshots are different.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599484475121}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1fabb9e9-fdad-47d1-9a4e-31c3efc6f3e4", "name": "Incremental task lingxia:default@PreBuild pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599484788691}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd7f9a6e-f8e9-4ad3-9cc0-733c18ca5d07", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599484958491}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2de4f5cf-8d33-41eb-9a07-e11902362aa8", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599485056189}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53433040-fffa-492e-a433-e70c5033c2db", "name": "current product is not Atomic service.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599578804118}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87b71fcb-76dd-4e05-b245-5b65fc8cc71b", "name": "Use tool [darwin: JAVA_HOME, CLASSPATH]\n [\n  {\n    JAVA_HOME: '/Applications/Android Studio.app/Contents/jbr/Contents/Home'\n  },\n  { CLASSPATH: undefined }\n]", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599579177893}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2043e4d-67ab-43a5-ba5f-c1428895ccc3", "name": "Use tool [darwin: NODE_HOME]\n [ { NODE_HOME: undefined } ]", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599579356619}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "643532b2-9876-4c06-9ba4-cb2f6407702f", "name": "lingxia : default@PreBuild cost memory 2.4668960571289062", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599610640902}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3070b10a-0ecf-4bc3-95a9-41abdb1053cf", "name": "runTaskFromQueue task cost before running: 461 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599610827087}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d339051f-a752-43bb-8b9e-31e9e6b262f2", "name": "Finished :lingxia:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599481312900, "endTime": 652599610902560, "totalTime": 129481447}, "additional": {"logType": "info", "children": [], "durationId": "266db86b-1313-402d-9ffc-bbda04ac44d8"}}, {"head": {"id": "a31b48bc-8364-4fc1-842c-69461a78c60e", "name": "entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599616437667, "endTime": 652599618793307}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "512dd746-90f6-430b-ba47-5c4f7dfb2cad", "logId": "47594df7-01c8-4d10-86a0-6bd7b32a6ab9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "512dd746-90f6-430b-ba47-5c4f7dfb2cad", "name": "create entry:default@CreateModuleInfo task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599614741855}, "additional": {"logType": "detail", "children": [], "durationId": "a31b48bc-8364-4fc1-842c-69461a78c60e"}}, {"head": {"id": "610a9a8d-2ffe-49de-a74a-2eca7d1dc5b0", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599615225601}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "540196db-2edb-4650-902a-8e2763a2a547", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599615388763}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8031567a-60db-4d1b-ac04-cc09433fde6a", "name": "Executing task :entry:default@CreateModuleInfo", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599616458547}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b35f23c-5922-4d2a-bc41-e9dd6a58fe0e", "name": "Task 'entry:default@CreateModuleInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599617262182}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97a4b04c-7a14-47da-9d6f-e01e54ec95f0", "name": "entry : default@CreateModuleInfo cost memory 0.0513458251953125", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599618218650}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1a4af96-b0ca-4ccc-9719-d2a1fcb6e742", "name": "runTaskFromQueue task cost before running: 469 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599618602015}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47594df7-01c8-4d10-86a0-6bd7b32a6ab9", "name": "Finished :entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599616437667, "endTime": 652599618793307, "totalTime": 2114151}, "additional": {"logType": "info", "children": [], "durationId": "a31b48bc-8364-4fc1-842c-69461a78c60e"}}, {"head": {"id": "86c19937-0de4-4fd1-8bd9-6cc7a4ba8467", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599627460803, "endTime": 652599630331407}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "377b0ce4-e214-4c82-a5cf-1c77e1f6d283", "logId": "fd863cc7-c1f1-43ec-a3f2-cbb73f3de787"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "377b0ce4-e214-4c82-a5cf-1c77e1f6d283", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599621925296}, "additional": {"logType": "detail", "children": [], "durationId": "86c19937-0de4-4fd1-8bd9-6cc7a4ba8467"}}, {"head": {"id": "82da1840-c61a-4485-bc2c-3e75533b0e07", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599622391204}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c13e8a9-6fcd-4e3d-99d7-4b1f2bb0b05e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599622561609}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1856d4dd-3da1-4307-bba9-b60dbda04e82", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599627485845}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7b8d538-6a0d-40e5-9c6d-3c6a5673412b", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599628629303}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "edabd4eb-fce5-4315-ba80-0398ed548f19", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599629961402}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "061045ef-c357-4217-b29a-a836d9fdc280", "name": "entry : default@GenerateMetadata cost memory 0.08736419677734375", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599630186383}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd863cc7-c1f1-43ec-a3f2-cbb73f3de787", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599627460803, "endTime": 652599630331407}, "additional": {"logType": "info", "children": [], "durationId": "86c19937-0de4-4fd1-8bd9-6cc7a4ba8467"}}, {"head": {"id": "683ec9c4-5b13-4021-b187-6a3a47301533", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599635875726, "endTime": 652599636776043}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "ff5df4c9-fab1-4514-84c7-d2e7928eec2b", "logId": "66770f19-9646-4919-8e30-d9b1df8c78dc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ff5df4c9-fab1-4514-84c7-d2e7928eec2b", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599633392005}, "additional": {"logType": "detail", "children": [], "durationId": "683ec9c4-5b13-4021-b187-6a3a47301533"}}, {"head": {"id": "91747992-b00b-4d83-8f0c-4c067d42d168", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599634065881}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e04b62d-5370-46f0-863f-a9c6908f6f37", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599634237396}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e315601f-4b92-4ddb-bfd6-52895333ff83", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599635898258}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2664b0d0-5fab-435e-9df8-b51ef02ae6aa", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599636112206}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a30471af-ff41-47a1-bcff-fbf0d358d946", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599636213439}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a36f19e7-1dc2-4790-b996-bd7082a801b5", "name": "entry : default@PreCheckSyscap cost memory 0.0393524169921875", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599636523136}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91108cf5-2a82-4656-ae9b-c20f3ea33b81", "name": "runTaskFromQueue task cost before running: 487 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599636682340}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66770f19-9646-4919-8e30-d9b1df8c78dc", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599635875726, "endTime": 652599636776043, "totalTime": 770472}, "additional": {"logType": "info", "children": [], "durationId": "683ec9c4-5b13-4021-b187-6a3a47301533"}}, {"head": {"id": "8f011a82-7647-480b-91f2-e6d694ccef4e", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599647437762, "endTime": 652599649551415}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The task snapshots are different."], "detailId": "7d21bc73-14bb-448a-8949-25888bf5034d", "logId": "5d6cccaf-4abb-4b5d-b204-bed911eafa4c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7d21bc73-14bb-448a-8949-25888bf5034d", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599639493920}, "additional": {"logType": "detail", "children": [], "durationId": "8f011a82-7647-480b-91f2-e6d694ccef4e"}}, {"head": {"id": "3ac6833f-4887-4222-827c-e1820bd270e7", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599639926562}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3eb8146-4512-42ed-ba01-a1d2ae18d422", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599640080804}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73fdc9e4-70f7-4487-82bb-1e15f0c5f94f", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599647456741}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69f059b5-40f9-49a7-9110-95a64000413f", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599647864885}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe38c954-1858-4f1f-9656-a76fce9c0fb1", "name": "entry:default@GeneratePkgContextInfo is not up-to-date, since the task snapshots are different.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599648241646}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eaf5e13c-1011-4cd8-96d0-e1dec4ada6f4", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599648329120}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a74cb1e1-8986-4386-96c7-8479724d07dc", "name": "entry : default@GeneratePkgContextInfo cost memory 0.07682037353515625", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599649140371}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72f1b693-f4a3-46ff-9db9-a3949a51a145", "name": "runTaskFromQueue task cost before running: 500 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599649392036}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d6cccaf-4abb-4b5d-b204-bed911eafa4c", "name": "Finished :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599647437762, "endTime": 652599649551415, "totalTime": 1819711}, "additional": {"logType": "info", "children": [], "durationId": "8f011a82-7647-480b-91f2-e6d694ccef4e"}}, {"head": {"id": "5b55ccab-5833-4ed6-a2e7-9e3aef445785", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599653270781, "endTime": 652599654865978}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file '/Users/<USER>/github/LingXia/examples/harmony/build/cache/default/integrated_hsp/integratedHspCache.json' does not exist."], "detailId": "160f0193-8260-4ac7-b2a5-9d6fa6edbfa3", "logId": "21a04a99-41c6-49dd-9dae-bc596e1ebf33"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "160f0193-8260-4ac7-b2a5-9d6fa6edbfa3", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599651029430}, "additional": {"logType": "detail", "children": [], "durationId": "5b55ccab-5833-4ed6-a2e7-9e3aef445785"}}, {"head": {"id": "f0a02ef1-a42b-4d96-bb77-bbe557c1986c", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599651415966}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b996d0c-a8b5-4e0c-896e-41358e3f9399", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599651619849}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d97086e9-02d4-4731-8882-d40bbd0b1244", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599653287050}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ccbe553c-4ca3-4bd6-9883-baf939d9e105", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file '/Users/<USER>/github/LingXia/examples/harmony/build/cache/default/integrated_hsp/integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599654330805}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "afc853dc-d45b-4e9d-96bb-c84b6f844054", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599654441955}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef8610c8-b68c-4d8c-a9e9-8b6af391fa99", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599654529065}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d1b884b-0b8d-4663-9a1b-35d0ac8aedcd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599654586574}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "006a3fb0-0d97-47a6-bbea-ce9020bffa01", "name": "entry : default@ProcessIntegratedHsp cost memory 0.11026763916015625", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599654725500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93767209-b6d1-4309-95a5-94f31f1eb639", "name": "runTaskFromQueue task cost before running: 505 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599654811705}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21a04a99-41c6-49dd-9dae-bc596e1ebf33", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599653270781, "endTime": 652599654865978, "totalTime": 1523702}, "additional": {"logType": "info", "children": [], "durationId": "5b55ccab-5833-4ed6-a2e7-9e3aef445785"}}, {"head": {"id": "e73fbaf1-18ff-46a3-83c9-d1cc509cf845", "name": "lingxia:default@CreateHarBuildProfile", "description": "Create the BuildProfile.ets file for the HAR package.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599658319307, "endTime": 652599660613396}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "lingxia", "category": "Generate", "taskRunReasons": ["The task snapshots are different."], "detailId": "5b0d695e-53df-4b96-a56d-58180773af83", "logId": "9418db78-208a-4c86-bc08-cb6287c681b9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5b0d695e-53df-4b96-a56d-58180773af83", "name": "create lingxia:default@CreateHarBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599656763225}, "additional": {"logType": "detail", "children": [], "durationId": "e73fbaf1-18ff-46a3-83c9-d1cc509cf845"}}, {"head": {"id": "2a6872fc-77df-4fb8-9a27-ef7f1a1b757a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599657081341}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3ee64ef-f4da-432f-9b9f-1d7bcd06e460", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599657171886}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6fe4a422-3e43-43c0-8f39-11fff9d38fa0", "name": "Executing task :lingxia:default@CreateHarBuildProfile", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599658335879}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1251f98f-b83b-44ad-8407-138768ee97d8", "name": "Task 'lingxia:default@CreateHarBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599658664365}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78926060-7af0-4af5-a22d-ba31562ecf0e", "name": "lingxia:default@CreateHarBuildProfile is not up-to-date, since the task snapshots are different.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599659248227}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24106ab6-448d-4f9c-8d1b-0f4108302eaf", "name": "Incremental task lingxia:default@CreateHarBuildProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599659334007}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aef68caa-b225-486e-acf1-3e4b09d008e9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599659414130}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9e6bebd-0748-4316-a0da-67372590ed61", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599659583695}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d13c92b-e7d2-43a4-9f64-334286cdb9de", "name": "lingxia : default@CreateHarBuildProfile cost memory 0.1110382080078125", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599660424507}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e64296b-2981-4bdc-b1e2-5ab888022a31", "name": "runTaskFromQueue task cost before running: 511 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599660552735}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9418db78-208a-4c86-bc08-cb6287c681b9", "name": "Finished :lingxia:default@CreateHarBuildProfile", "description": "Create the BuildProfile.ets file for the HAR package.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599658319307, "endTime": 652599660613396, "totalTime": 2205544}, "additional": {"logType": "info", "children": [], "durationId": "e73fbaf1-18ff-46a3-83c9-d1cc509cf845"}}, {"head": {"id": "37bec2c2-83b0-4a5a-971e-c1657574c3a3", "name": "lingxia:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599662917033, "endTime": 652599663377533}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "lingxia", "category": "Native", "taskRunReasons": [], "detailId": "3ac53218-92b5-49aa-9a3f-acf34d1317f3", "logId": "12a9fc68-23ab-475d-8444-0098900d5cb6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3ac53218-92b5-49aa-9a3f-acf34d1317f3", "name": "create lingxia:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599662302759}, "additional": {"logType": "detail", "children": [], "durationId": "37bec2c2-83b0-4a5a-971e-c1657574c3a3"}}, {"head": {"id": "5db88d0c-5ef0-464d-b4e9-f40271aadbc5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599662587202}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9875fef-aa5a-4589-9699-31ee08a3dbce", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599662753465}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8eb481f-14ed-4e86-8a28-72931f74c56b", "name": "Executing task :lingxia:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599662929670}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6313492-c64e-41e7-ae4b-5dc89ed7967d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599663032136}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c89f177a-2a47-452c-ad61-ba491fccde4e", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599663084495}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80bef72c-0a09-4d2d-84a8-972c71e9af7f", "name": "lingxia : default@ConfigureCmake cost memory 0.036376953125", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599663169886}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5afcfe51-f044-4cb4-935d-29345ac00665", "name": "runTaskFromQueue task cost before running: 514 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599663319049}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12a9fc68-23ab-475d-8444-0098900d5cb6", "name": "Finished :lingxia:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599662917033, "endTime": 652599663377533, "totalTime": 375589}, "additional": {"logType": "info", "children": [], "durationId": "37bec2c2-83b0-4a5a-971e-c1657574c3a3"}}, {"head": {"id": "71a188a4-0a2b-46e7-9732-980c6a744249", "name": "lingxia:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599665967144, "endTime": 652599841966656}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "lingxia", "category": "Config", "taskRunReasons": ["The task snapshots are different."], "detailId": "91a574a5-9aec-4e91-9f24-6a8cf937ba6d", "logId": "56e74d81-0e2f-4571-925e-5744dc962635"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "91a574a5-9aec-4e91-9f24-6a8cf937ba6d", "name": "create lingxia:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599664637814}, "additional": {"logType": "detail", "children": [], "durationId": "71a188a4-0a2b-46e7-9732-980c6a744249"}}, {"head": {"id": "dbac8f26-7841-4b65-9d65-5f9df84a0120", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599664939240}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "906fb393-712d-4291-832c-0d05d9fa47f3", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599665022593}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d89901a8-dcef-4811-996a-520ee03451aa", "name": "Executing task :lingxia:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599665988303}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6bf178be-9f5a-4d0a-8d53-673f9a9f1e5d", "name": "lingxia:default@MergeProfile is not up-to-date, since the task snapshots are different.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599789601694}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1589b1c-edcc-4aab-b5de-6ce3c41fbbb5", "name": "Incremental task lingxia:default@MergeProfile pre-execution cost: 20 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599789891138}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19a76cf4-4153-4d1d-87f5-ee7ee8722f5e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599790061748}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f57763d-5f1a-4b42-bf71-b4a0ef269b6a", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599790145736}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47794d89-d8c5-4a4b-b9a3-848e58de63a8", "name": "Change app api release type with 'Release'", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599837632984}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b839d9c9-2756-4ac8-92fc-f2102ed12a72", "name": "Change app compile API version with '*********'", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599838244576}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0db35e10-f9b4-4b20-8020-37e9e18e52aa", "name": "Change app target API version with '50005017'", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599838363405}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2546537d-bae8-47e1-bfed-74eb497a3be4", "name": "Change app minimum API version with '50005017'", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599838434243}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fdbec126-7757-414d-ae19-d929b69653c3", "name": "Use cli appEnvironment", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599838684277}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b05dcf60-cf01-4fbd-b0fc-785cfb2bb375", "name": "lingxia : default@MergeProfile cost memory -26.670501708984375", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599841687097}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40de7934-eb5c-4c72-9166-2e57b287fd12", "name": "runTaskFromQueue task cost before running: 692 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599841892529}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56e74d81-0e2f-4571-925e-5744dc962635", "name": "Finished :lingxia:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599665967144, "endTime": 652599841966656, "totalTime": 175893307}, "additional": {"logType": "info", "children": [], "durationId": "71a188a4-0a2b-46e7-9732-980c6a744249"}}, {"head": {"id": "e6f1be4f-c4e8-4913-b045-9989f4238608", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599844777501, "endTime": 652599848762426}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/syscap/default/rpcid.sc' does not exist."], "detailId": "cd42cb92-baa8-43bb-a9e7-e04dc8a8c4c3", "logId": "8edd03d2-7e72-4f5c-86e2-f98cf8f69f5c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cd42cb92-baa8-43bb-a9e7-e04dc8a8c4c3", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599843489841}, "additional": {"logType": "detail", "children": [], "durationId": "e6f1be4f-c4e8-4913-b045-9989f4238608"}}, {"head": {"id": "aedb0638-5135-408c-9605-d3dc63dbffcf", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599843800927}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d6ea32d-a8e6-4575-ab92-1bfbaf21f364", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599843900379}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce40576f-075f-439b-852c-63188dc1f620", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599844790782}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d8e320f-52e1-4b66-8e04-290ae77f0f09", "name": "File: '/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599844953381}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b95fc418-5527-4e03-8a7e-625470c4a4f5", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599845690193}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c745080-d4ac-4448-bc60-b1ad7c4e816a", "name": "entry:default@SyscapTransform is not up-to-date, since the output file '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/syscap/default/rpcid.sc' does not exist.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599848205049}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cccd3d40-0d14-4625-a5ad-bbac91da2f97", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599848345326}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4247cacb-7767-40b1-b547-5b924badbc1f", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599848440891}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8eb66bd1-70c7-44bd-91d9-ae2f58a46895", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599848495374}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a734fd79-72cc-4dec-9595-b0688dc4e26e", "name": "entry : default@SyscapTransform cost memory 0.12790679931640625", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599848585392}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51e91dce-8a7d-488c-9af8-96d3206e10eb", "name": "runTaskFromQueue task cost before running: 699 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599848684393}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8edd03d2-7e72-4f5c-86e2-f98cf8f69f5c", "name": "Finished :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599844777501, "endTime": 652599848762426, "totalTime": 3873626}, "additional": {"logType": "info", "children": [], "durationId": "e6f1be4f-c4e8-4913-b045-9989f4238608"}}, {"head": {"id": "bbbf4265-9fc6-4973-9e74-c9ed19417308", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599852215602, "endTime": 652599857130557}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The input file '/Users/<USER>/github/LingXia/examples/harmony/entry/oh-package.json5' has been changed."], "detailId": "d61e80d6-16f0-4799-8bf9-cdb05e5be577", "logId": "88833db5-0670-4799-ba25-3ae5dcb48059"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d61e80d6-16f0-4799-8bf9-cdb05e5be577", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599850017379}, "additional": {"logType": "detail", "children": [], "durationId": "bbbf4265-9fc6-4973-9e74-c9ed19417308"}}, {"head": {"id": "71e209c2-52f0-4d13-93bd-2928787a480c", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599850272189}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d0a7abe-b258-4fb5-84fe-1d57effc9af7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599850357784}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7376adf-e586-4a2c-a2d2-dbe7c443e619", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599852232707}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a62af73-f9f2-44f4-bf1e-944e90fa00bf", "name": "entry:default@ProcessRouterMap is not up-to-date, since the input file '/Users/<USER>/github/LingXia/examples/harmony/entry/oh-package.json5' has been changed.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599855726637}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "040e1374-d7e6-43f6-bab1-ebda11cef485", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599855875840}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4205dc4d-8e73-4d6e-a697-e729744de205", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599855969508}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9547fc1-e6e7-4c35-997a-e657b72f3edf", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599856021335}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e934c62d-57ce-47a3-a2a6-d9ccbd7375a8", "name": "entry : default@ProcessRouterMap cost memory 0.192108154296875", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599856959308}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b779b9c-ec7f-4a45-aefe-1c6bdb17c72a", "name": "runTaskFromQueue task cost before running: 707 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599857074978}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88833db5-0670-4799-ba25-3ae5dcb48059", "name": "Finished :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599852215602, "endTime": 652599857130557, "totalTime": 4836798}, "additional": {"logType": "info", "children": [], "durationId": "bbbf4265-9fc6-4973-9e74-c9ed19417308"}}, {"head": {"id": "673802ed-33e7-4cad-832f-adcbcdd987e7", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599859789535, "endTime": 652599860897854}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "b8cd59e9-ecfc-4c7d-80b9-6617e311f84d", "logId": "f32aaed5-403f-486b-977d-6e3731adf101"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b8cd59e9-ecfc-4c7d-80b9-6617e311f84d", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599858897342}, "additional": {"logType": "detail", "children": [], "durationId": "673802ed-33e7-4cad-832f-adcbcdd987e7"}}, {"head": {"id": "740e4fc6-1ab5-4526-a7a4-e1e637d3a412", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599859135010}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6411efa-b6d8-4cec-87fa-74418d49f076", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599859213194}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15271b69-1e77-4e69-88d6-862aa48e94c7", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599859801274}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fe9c39a6-61aa-4427-a8c6-c1406333fd85", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599860254136}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6df1aed6-4654-49e1-8fa2-d889a40d0b2b", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599860747262}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b2cc7b9-80e7-44ca-8e73-d5a12494ff8d", "name": "entry : default@CreateBuildProfile cost memory 0.08893585205078125", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599860835441}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f32aaed5-403f-486b-977d-6e3731adf101", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599859789535, "endTime": 652599860897854}, "additional": {"logType": "info", "children": [], "durationId": "673802ed-33e7-4cad-832f-adcbcdd987e7"}}, {"head": {"id": "51885606-9761-4710-91a3-42065c3449b2", "name": "lingxia:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599863608746, "endTime": 652599863993986}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "lingxia", "category": "Native", "taskRunReasons": [], "detailId": "bf7827c2-6dcf-4b8f-8a1f-d14793f539aa", "logId": "b9916e9a-9773-46df-a148-10755bbbe0e0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bf7827c2-6dcf-4b8f-8a1f-d14793f539aa", "name": "create lingxia:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599862406585}, "additional": {"logType": "detail", "children": [], "durationId": "51885606-9761-4710-91a3-42065c3449b2"}}, {"head": {"id": "8f7247e5-f98b-4d67-b93f-23f339797aa5", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599862788796}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7cb62599-3343-4e30-befc-82421d69d9c4", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599862975990}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "83883e40-a31b-48a1-bb36-2b69e7bae8d2", "name": "Executing task :lingxia:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599863620133}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "933e7830-f9f3-48bf-8be9-92d140b40d13", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599863727617}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "abfc9d15-2476-451a-9fb1-49c5e25fa5d0", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599863779071}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5bbb0afa-64a7-4743-984d-d1f9d850832e", "name": "lingxia : default@BuildNativeWithCmake cost memory 0.036468505859375", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599863863929}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9dd60e01-77fe-4110-ae05-1b1612738cf4", "name": "runTaskFromQueue task cost before running: 714 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599863946860}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9916e9a-9773-46df-a148-10755bbbe0e0", "name": "Finished :lingxia:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599863608746, "endTime": 652599863993986, "totalTime": 316367}, "additional": {"logType": "info", "children": [], "durationId": "51885606-9761-4710-91a3-42065c3449b2"}}, {"head": {"id": "c395ed28-e715-444e-a71f-8f1b24f177b6", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599865912129, "endTime": 652599871830579}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": ["The input file '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/merge_profile/default/module.json' has been changed."], "detailId": "dfeee48f-b22a-4d4f-8c34-15923ee51089", "logId": "af77847d-0ff5-4112-8604-9e0c786d4338"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dfeee48f-b22a-4d4f-8c34-15923ee51089", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599865026481}, "additional": {"logType": "detail", "children": [], "durationId": "c395ed28-e715-444e-a71f-8f1b24f177b6"}}, {"head": {"id": "4483cbdd-386d-4e75-8017-4d7751c0796e", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599865408228}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9c54c60-c105-4525-827c-d5d76d97c91c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599865478752}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2eeea570-8c65-4e33-b68d-e52ca9c199e1", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599865919726}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fa090b9-2754-4b33-a479-15544bba3904", "name": "entry:default@MergeProfile is not up-to-date, since the input file '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/merge_profile/default/module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599867745872}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "446760a7-2c85-4175-a2c8-c3ad315e77a8", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599867895109}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9165fbfc-cee4-4ed8-8b0f-7f691de7ea5b", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599867977039}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d4b7589-e1e8-4f1e-8786-dfe50869a20b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599868026449}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6d801500-87ee-4011-a217-489f9eba7dbe", "name": "Change app api release type with 'Release'", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599868142662}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f01f27a-1d73-46fe-8dac-966fdafa3084", "name": "Change app compile API version with '*********'", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599868245809}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6608e3d9-50eb-4c26-8211-a24ae5cae800", "name": "Change app target API version with '50005017'", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599868296388}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c2e9269-436b-4b07-bdbf-e3660569c6a5", "name": "Change app minimum API version with '50005017'", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599868336142}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e27eb067-9609-491c-9d60-28de24e7125c", "name": "Use cli appEnvironment", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599868379980}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c88e1cc-1e5a-4aaf-a6f9-1cec92f345a1", "name": "entry : default@MergeProfile cost memory 0.35939788818359375", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599871430011}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63ffa9c0-d2f0-48be-981b-74325d80e4e8", "name": "runTaskFromQueue task cost before running: 722 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599871750561}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "af77847d-0ff5-4112-8604-9e0c786d4338", "name": "Finished :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599865912129, "endTime": 652599871830579, "totalTime": 5787317}, "additional": {"logType": "info", "children": [], "durationId": "c395ed28-e715-444e-a71f-8f1b24f177b6"}}, {"head": {"id": "c82f07c5-9721-4e72-828a-40bff9f29e71", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599876785152, "endTime": 652599891208492}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The input file '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default/pkgContextInfo.json' has been changed."], "detailId": "980b2127-38b2-495d-8abb-b97e71477954", "logId": "65d75a8b-bee8-413a-bc57-653893c2cb43"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "980b2127-38b2-495d-8abb-b97e71477954", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599873302314}, "additional": {"logType": "detail", "children": [], "durationId": "c82f07c5-9721-4e72-828a-40bff9f29e71"}}, {"head": {"id": "84324695-3a09-4c4e-85b3-1ff5309a8550", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599873585314}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f196daf5-ff22-4f46-b845-d44039c913fd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599873679720}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2620a054-7237-4f11-b9fd-3ae8fd482cab", "name": "Module 'lingxia' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599874734955}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9060fdc-539a-4d2c-8f36-e9c179c5b9f3", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599876805543}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "278fd2fe-519b-4884-8b6f-66331766ee1d", "name": "Module 'lingxia' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599878218845}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f0b8098-6175-4c61-bd60-68c5b561c661", "name": "Module 'lingxia' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599880437985}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ebe0d40-ebc1-49ea-b504-55c9f2c3bc7d", "name": "Module 'lingxia' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599882985564}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3cbd3c7-616f-41f3-9946-a44ed2606116", "name": "Module 'lingxia' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599884746011}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c86c2ad9-510a-4bcb-9b1d-8e1cfbf7c5ca", "name": "Module 'lingxia' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599886927350}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "675de5a7-5ccb-48a6-94f8-7084e4633f41", "name": "entry:default@GenerateLoaderJson is not up-to-date, since the input file '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default/pkgContextInfo.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599887216309}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24ba91e0-453f-474e-a766-e86e9e9ebe3d", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599887370925}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de21091c-4f18-4af6-9cf6-54c0d192207a", "name": "Module 'lingxia' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599888394906}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4361bcd0-8a32-4826-bc64-aeff7ba59240", "name": "entry : default@GenerateLoaderJson cost memory 0.6502685546875", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599890792843}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f500347-f968-4f56-af1b-85bee409d86f", "name": "runTaskFromQueue task cost before running: 741 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599891126986}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06028b20-4037-49ae-9afc-3eb5a2bba073", "name": "Module 'lingxia' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599892146177}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "605af409-19bf-46b9-92a2-b35a18856530", "name": "Module 'lingxia' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599893744952}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65d75a8b-bee8-413a-bc57-653893c2cb43", "name": "Finished :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599876785152, "endTime": 652599891208492, "totalTime": 14305361}, "additional": {"logType": "info", "children": [], "durationId": "c82f07c5-9721-4e72-828a-40bff9f29e71"}}, {"head": {"id": "a6e6d95d-ed53-4433-a265-f24b8c1509bc", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599896265044, "endTime": 652599896684159}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "d4b76f93-6885-439b-b131-d17e30991555", "logId": "412f1069-d52f-4a1e-b4d3-52e74c32a886"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d4b76f93-6885-439b-b131-d17e30991555", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599895393368}, "additional": {"logType": "detail", "children": [], "durationId": "a6e6d95d-ed53-4433-a265-f24b8c1509bc"}}, {"head": {"id": "80b27084-d0c0-4047-a0f9-76a32cb36310", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599895689661}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a718d105-501b-4df3-9929-d07653829a18", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599895874429}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7cb89527-33ee-49bd-98bc-d4521ce6aa01", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599896279260}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "96a62620-254f-411a-830d-ce3695805cb0", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599896395665}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8b7b96e-1355-48bf-8dde-62f625bdf92f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599896447122}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01dfbe44-b7a4-42a8-be40-25389afee683", "name": "entry : default@ConfigureCmake cost memory 0.03913116455078125", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599896522736}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "070bd9e1-7d13-4553-a05f-13b1bd20e811", "name": "runTaskFromQueue task cost before running: 747 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599896632335}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "412f1069-d52f-4a1e-b4d3-52e74c32a886", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599896265044, "endTime": 652599896684159, "totalTime": 323369}, "additional": {"logType": "info", "children": [], "durationId": "a6e6d95d-ed53-4433-a265-f24b8c1509bc"}}, {"head": {"id": "88829a99-8b02-4197-bfd7-314f2b149ba8", "name": "lingxia:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599899184119, "endTime": 652599900226126}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "lingxia", "category": "Native", "taskRunReasons": [], "detailId": "daad51ad-7aee-4991-9d70-e870b7c873c9", "logId": "ef14d0ff-7acc-4a3d-94b7-f8ff9d51151c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "daad51ad-7aee-4991-9d70-e870b7c873c9", "name": "create lingxia:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599898083270}, "additional": {"logType": "detail", "children": [], "durationId": "88829a99-8b02-4197-bfd7-314f2b149ba8"}}, {"head": {"id": "ba7d45a4-d3da-448c-b5ed-d191374a170b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599898417146}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1379bbbf-74f6-4489-bb71-0d9d14366bd6", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599898527874}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8f024d0-242d-4561-80cf-dd5b5375e1dc", "name": "Executing task :lingxia:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599899198235}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38adf813-3f64-4678-9087-acb50da276af", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599899323336}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9b55432-05f0-4ab3-ac70-e4eafaeaf78f", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599899378318}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "13e79d14-7d85-4ad9-88e1-a080e695a461", "name": "lingxia : default@BuildNativeWithNinja cost memory 0.05200958251953125", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599900049325}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ec284a4-86e5-4f9c-8945-3c90f3fa78ce", "name": "runTaskFromQueue task cost before running: 750 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599900172886}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef14d0ff-7acc-4a3d-94b7-f8ff9d51151c", "name": "Finished :lingxia:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599899184119, "endTime": 652599900226126, "totalTime": 960452}, "additional": {"logType": "info", "children": [], "durationId": "88829a99-8b02-4197-bfd7-314f2b149ba8"}}, {"head": {"id": "9dda06c9-553d-4f58-bb8c-6fdb3f19befc", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599903776474, "endTime": 652599905776387}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "a3ea81ef-da3a-4bf1-a942-a102f7041e1f", "logId": "e9fc9e0a-7f7d-4d7d-90a5-1a80e58afb11"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a3ea81ef-da3a-4bf1-a942-a102f7041e1f", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599902311226}, "additional": {"logType": "detail", "children": [], "durationId": "9dda06c9-553d-4f58-bb8c-6fdb3f19befc"}}, {"head": {"id": "0d17af58-6464-4e8a-9f92-65b271348e46", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599902747466}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e433299-8b6e-4122-be1b-c96198982a8c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599903008502}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b05ce208-a5a3-47a7-bfb9-ecadbb69661a", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599903790907}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c3a9392-3df3-49ea-a2a9-a3b301c742bc", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599905574481}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee410864-f96b-47be-b378-1cb662494a39", "name": "entry : default@MakePackInfo cost memory 0.1149444580078125", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599905707219}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9fc9e0a-7f7d-4d7d-90a5-1a80e58afb11", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599903776474, "endTime": 652599905776387}, "additional": {"logType": "info", "children": [], "durationId": "9dda06c9-553d-4f58-bb8c-6fdb3f19befc"}}, {"head": {"id": "a3348d7a-cd18-4e62-898c-499155482063", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599908616003, "endTime": 652600049950618}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": ["The input file '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/merge_profile/default/module.json' has been changed."], "detailId": "b6cd7259-258e-40c5-9eec-b47b34708b0b", "logId": "e8906078-21a3-4f6f-92ec-26289512ad36"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b6cd7259-258e-40c5-9eec-b47b34708b0b", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599907527115}, "additional": {"logType": "detail", "children": [], "durationId": "a3348d7a-cd18-4e62-898c-499155482063"}}, {"head": {"id": "e55ec9bf-081a-436c-a571-93cacc33b76d", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599907787399}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd4f480b-3157-4722-91c4-0fb7399ab54d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599907874677}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cef79801-9cd7-43bd-a23b-bf991782234a", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599908627020}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38ab5328-a450-49f5-baae-8b84c125698d", "name": "entry:default@ProcessProfile is not up-to-date, since the input file '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/merge_profile/default/module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599909386856}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f2c1d93e-e16a-42cb-b8ae-affcc4ec6fa9", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599909476688}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43fe42f3-23d5-42a0-873a-ff8b470056f6", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599909551611}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b0a633b-8d31-4f86-a274-f7da59f8ca6d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599909599821}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2c84a50-90b1-40a0-b775-00475bf1e726", "name": "********", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600048017465}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3f8ad41-fa63-4970-98a4-e57e32020ffe", "name": "entry : default@ProcessProfile cost memory 0.32875823974609375", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600049728350}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "435409cf-f9ea-4699-8025-24c17f6ac4e1", "name": "runTaskFromQueue task cost before running: 900 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600049884977}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8906078-21a3-4f6f-92ec-26289512ad36", "name": "Finished :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599908616003, "endTime": 652600049950618, "totalTime": 141236109}, "additional": {"logType": "info", "children": [], "durationId": "a3348d7a-cd18-4e62-898c-499155482063"}}, {"head": {"id": "32c5e5d5-af52-4295-81e1-cfa6f31a8974", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600052302342, "endTime": 652600052648465}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "182a515e-c7f9-40d6-98bc-6fc51bf46a22", "logId": "4292f0df-ebfc-4249-a715-a6f5c47a862a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "182a515e-c7f9-40d6-98bc-6fc51bf46a22", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600051455279}, "additional": {"logType": "detail", "children": [], "durationId": "32c5e5d5-af52-4295-81e1-cfa6f31a8974"}}, {"head": {"id": "3ae0d1e3-888c-4f07-83c3-cc9d8d90bdaf", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600051685364}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "489adaf0-f2a4-443c-9213-b2c9ffa98f16", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600051786671}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ea2dee6-e9d2-4c35-bc9b-1b4fb32a69a2", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600052310323}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f9a4bba-e82b-470e-84a8-13f0fd0badfb", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600052406816}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71e4e2ea-6b69-4602-9e29-0d8d06d20e2f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600052455733}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "93c2c8fb-80d0-461f-bbee-436104a49259", "name": "entry : default@BuildNativeWithCmake cost memory 0.04022979736328125", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600052525524}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "473d0e6a-db1a-42a9-8121-2fc618d1b726", "name": "runTaskFromQueue task cost before running: 903 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600052604677}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4292f0df-ebfc-4249-a715-a6f5c47a862a", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600052302342, "endTime": 652600052648465, "totalTime": 279411}, "additional": {"logType": "info", "children": [], "durationId": "32c5e5d5-af52-4295-81e1-cfa6f31a8974"}}, {"head": {"id": "95812323-0d2c-4b04-9987-1e2033c3b982", "name": "lingxia:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600054486779, "endTime": 652601698563737}, "additional": {"children": ["b25efda8-6298-4043-a0e6-e259a687b849"], "state": "success", "targetName": "default", "moduleName": "lingxia", "category": "Resources", "taskRunReasons": ["The task snapshots are different."], "detailId": "5d2e2915-92fe-48d4-b796-6add19346cd3", "logId": "469adb8c-19d2-4f42-9b56-1a5cf9a7b531"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5d2e2915-92fe-48d4-b796-6add19346cd3", "name": "create lingxia:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600053705892}, "additional": {"logType": "detail", "children": [], "durationId": "95812323-0d2c-4b04-9987-1e2033c3b982"}}, {"head": {"id": "ea8c13de-1236-4250-a773-7eb71ab10fc8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600053914314}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d16f61e6-d2e3-4301-9405-099a66636438", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600053990427}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f94aa2ec-1e17-4ddc-a82b-e6e9edd3bca2", "name": "Executing task :lingxia:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600054497107}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36332442-4ae8-407a-85b4-0c10a8bdbbc7", "name": "lingxia:default@ProcessLibs is not up-to-date, since the task snapshots are different.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600056282522}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d8dd5df-6819-4043-9124-320240c319ef", "name": "Incremental task lingxia:default@ProcessLibs pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600056408434}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1a7356f-cc7c-46d1-8b69-24775fd891ac", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600056488529}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5020016a-1eb5-4fdc-9da3-3467bb61420a", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600056535808}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32811518-8707-4149-bce8-9d7e87b48876", "name": "default@ProcessLibs work[12] is submitted.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600058599747}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b25efda8-6298-4043-a0e6-e259a687b849", "name": "lingxia:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Worker6", "startTime": 652601696064837, "endTime": 652601697129227}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "lingxia", "category": "", "taskRunReasons": [], "parent": "95812323-0d2c-4b04-9987-1e2033c3b982", "logId": "bcfca751-da63-4fb5-8ac2-d76e95ff0cb0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7eaf8bb1-aa80-4f26-9e1a-9db9b66656a3", "name": "default@ProcessLibs work[12] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600059503049}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b00b6e42-b411-4a09-abbf-3ebbed764c9a", "name": "A work dispatched to worker[7] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600059654228}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "589eda8c-861e-4b96-8abc-f6737199d7a0", "name": "Create  resident worker with id: 6.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600059732628}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b6856db-2c93-4c3d-8bb2-1c457f53be38", "name": "default@ProcessLibs work[12] has been dispatched to worker[6].", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600060822037}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d607160-17a6-4fbd-8726-23363ef8a736", "name": "default@ProcessLibs work[12] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600060944724}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ebe8181-2a64-4c45-917b-00c2369fb0e7", "name": "lingxia : default@ProcessLibs cost memory 0.5157623291015625", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600061061402}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8488ae9-5273-46f5-a42d-ad12de53ab3d", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600065412486, "endTime": 652600068834287}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "5019377c-2b95-4636-baf1-d538d4077bd9", "logId": "9923a642-5dd9-4c85-b0cd-7f3ebe73b7c0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5019377c-2b95-4636-baf1-d538d4077bd9", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600062509254}, "additional": {"logType": "detail", "children": [], "durationId": "e8488ae9-5273-46f5-a42d-ad12de53ab3d"}}, {"head": {"id": "1aaa12e1-8367-4e3d-8461-97a3368d9ecd", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600062891833}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dabcf480-c889-4ccb-adc2-50f515797fbc", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600063000919}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db5c4e1b-43b0-4828-bf46-9b66855f4085", "name": "restool module names: entry,lingxia; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600063768128}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63295216-bf80-4004-b480-fa3f18dc0453", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600066286836}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0dbb667-2dc8-4177-9606-f56055d8187a", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600067795312}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ebe8d26-d890-4980-84e5-6dd6585a2fbd", "name": "entry : default@ProcessResource cost memory 0.13732147216796875", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600067957767}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9923a642-5dd9-4c85-b0cd-7f3ebe73b7c0", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600065412486, "endTime": 652600068834287}, "additional": {"logType": "info", "children": [], "durationId": "e8488ae9-5273-46f5-a42d-ad12de53ab3d"}}, {"head": {"id": "4b566531-2d49-4b23-9eae-e4812c6adbf7", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600075893035, "endTime": 652600387981509}, "additional": {"children": ["b9f67413-f8e2-4b06-a572-03cc1d7b3ab5", "c5c473c6-ed0f-4b31-a851-9bb935ec6257"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": ["The input file '/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources' has been changed."], "detailId": "9714f6ee-555e-47b3-8fbc-71cfc3ec9aa4", "logId": "1f2a68c0-b0b8-403c-a728-167c7d8f8dff"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9714f6ee-555e-47b3-8fbc-71cfc3ec9aa4", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600071685122}, "additional": {"logType": "detail", "children": [], "durationId": "4b566531-2d49-4b23-9eae-e4812c6adbf7"}}, {"head": {"id": "76cd693d-b99a-4812-8b87-7bd64d946324", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600072026959}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aac44bb8-b9d5-46a4-81a5-6e34236b0bf0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600072173552}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "316c8d2a-5e6f-4a0d-9eed-7dff038c9693", "name": "restool module names: entry,lingxia; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600072892829}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c699a35f-5a9f-4838-bf9c-b6ad56208c84", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600075958037}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "472ce308-d3ee-4227-b4be-808a1a6d6268", "name": "entry:default@CompileResource is not up-to-date, since the input file '/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources' has been changed.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600082104866}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e267ecff-7371-4713-b4a1-ec1b0edbd115", "name": "Incremental task entry:default@CompileResource pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600082327069}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9f67413-f8e2-4b06-a572-03cc1d7b3ab5", "name": "create intermediate resource category", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600083641393, "endTime": 652600084998344}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4b566531-2d49-4b23-9eae-e4812c6adbf7", "logId": "c439a190-132d-4c99-841c-83962de74e7f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c439a190-132d-4c99-841c-83962de74e7f", "name": "create intermediate resource category", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600083641393, "endTime": 652600084998344}, "additional": {"logType": "info", "children": [], "durationId": "b9f67413-f8e2-4b06-a572-03cc1d7b3ab5", "parent": "1f2a68c0-b0b8-403c-a728-167c7d8f8dff"}}, {"head": {"id": "77bbcd88-33d6-4904-885e-7990b3c11a3b", "name": "Use tool [/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/toolchains/restool]\n [\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/toolchains/restool',\n  '-l',\n  '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resConfig.json'\n]", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600085691182}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c5c473c6-ed0f-4b31-a851-9bb935ec6257", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600086729667, "endTime": 652600386602130}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4b566531-2d49-4b23-9eae-e4812c6adbf7", "logId": "0a8eb95d-19a7-4f39-be18-008b9207d862"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c36bebac-2cd9-4608-8e05-5977d767c028", "name": "current process  memoryUsage: {\n  rss: 462016512,\n  heapTotal: 166936576,\n  heapUsed: 123724608,\n  external: 3225048,\n  arrayBuffers: 1246959\n} os memoryUsage :15.314327239990234", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600088218203}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8d722e2-0b7a-4ffb-992f-e1d80e6a2443", "name": "Info: thread pool is started\n", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600234485678}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3ffd4c8-8475-4f9a-9a61-1ffcb3029a41", "name": "Info: GenericCompiler::CompileFiles\n", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600235820691}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6a93159-497f-4f4a-9029-0f81e8efa728", "name": "Info: thread pool is started\n", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600236117055}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0476843e-1e29-4f4d-ba51-1d2e34a05e1c", "name": "Info: thread pool is stopped\n", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600239504357}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "acbccfed-1593-4e6e-8814-63ed75d51b0a", "name": "Info: GenericCompiler::CompileFiles\n", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600241374543}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e57d245a-02f7-4843-b407-d1730094f0f2", "name": "Info: thread pool is started\n", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600241768625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fed9b477-e20c-4f03-bb26-d2cc0419a89b", "name": "Info: thread pool is stopped\n", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600242687819}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c303dba3-5bcf-4578-a0af-3e49449cc876", "name": "Info: GenericCompiler::CompileFiles\nInfo: thread pool is started\n", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600243136448}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c415dec7-6886-40b7-9007-c84e2a004a17", "name": "Info: thread pool is stopped\n", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600243879118}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d4ca00d-31ae-4a02-ac2a-d7efef8b2679", "name": "07-31 21:40:11.905 59573 8760986 E C01400/ImageTranscoderUtils: DecodeToRGBA IN\n07-31 21:40:11.906 59573 8760986 E C01400/ImageTranscoderUtils: ImageFilter IN /Users/<USER>/github/LingXia/examples/harmony/AppScope/resources/base/media/background.png\n", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600248387569}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1fbc6ace-2e04-432f-a32c-7e5283966f40", "name": "07-31 21:40:11.979 59573 8760986 E C01400/ImageTranscoderUtils: DecodeToRGBA IN\n07-31 21:40:11.979 59573 8760986 E C01400/ImageTranscoderUtils: ImageFilter IN /Users/<USER>/github/LingXia/examples/harmony/AppScope/resources/base/media/foreground.png\n", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600322097237}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cacad7cc-c8c9-42e2-a706-398582a9291b", "name": "Info: can't scale media json file.\n", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600379218165}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5102178-7a8a-4fee-86df-e174d4c5eb08", "name": "Warning: /Users/<USER>/github/LingXia/examples/harmony/AppScope/resources/base/media/layered_image.json is not png format\n", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600379457586}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20105996-7488-4ab0-b5d0-aa69a4ae3ab7", "name": "Info: thread pool is stopped\n", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600380627694}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e310379-68d6-456e-9e1b-01c8db3f2c78", "name": "Info: restool resources compile success.\n", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600380928693}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b0c6b09-44c7-4116-b797-d3fa9fa3c8d3", "name": "astcenc customized so is not be opened when dlclose!\n", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600382195150}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a8eb95d-19a7-4f39-be18-008b9207d862", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600086729667, "endTime": 652600386602130}, "additional": {"logType": "info", "children": [], "durationId": "c5c473c6-ed0f-4b31-a851-9bb935ec6257", "parent": "1f2a68c0-b0b8-403c-a728-167c7d8f8dff"}}, {"head": {"id": "a68f01fb-6233-4ddf-867a-c54af8763821", "name": "entry : default@CompileResource cost memory 0.9583587646484375", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600387665058}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "716d5da8-2050-4806-8011-ccda85f401d0", "name": "runTaskFromQueue task cost before running: 1 s 238 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600387867072}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f2a68c0-b0b8-403c-a728-167c7d8f8dff", "name": "Finished :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600075893035, "endTime": 652600387981509, "totalTime": 311905251}, "additional": {"logType": "info", "children": ["c439a190-132d-4c99-841c-83962de74e7f", "0a8eb95d-19a7-4f39-be18-008b9207d862"], "durationId": "4b566531-2d49-4b23-9eae-e4812c6adbf7"}}, {"head": {"id": "4800a74a-6a16-4ae7-a193-1c08c482f58f", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600398523573, "endTime": 652605807258491}, "additional": {"children": ["6a2a3774-c5cf-4c5d-8f7e-b5648c9ff34a", "6375d7d0-717a-4d4f-a672-a19da6e05068"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "cfd3e621-0bf6-4fd4-a424-fbd6a04f227f", "logId": "b319f7eb-313e-48db-9bdf-83a100e2f75a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cfd3e621-0bf6-4fd4-a424-fbd6a04f227f", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600391399386}, "additional": {"logType": "detail", "children": [], "durationId": "4800a74a-6a16-4ae7-a193-1c08c482f58f"}}, {"head": {"id": "906c243e-8b5a-4b9d-a416-37ca302c86f5", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600391809845}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58e1c69c-64ef-452c-9f8f-dd5d83e495f9", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600392002669}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a899b06-684d-4790-8d1b-9cff419459ce", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600398555719}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a7ca61b-fb52-4e28-8016-0c0a9a3a50ab", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600416296312}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90f02fd6-0961-44f0-8151-5ac221fbbef3", "name": "Compile arkts with external api path: /Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/hms/ets", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600418409645}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c0190f68-dc6d-46d7-80ef-102008f1e8fb", "name": "default@CompileArkTS work[13] is submitted.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600419031346}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a2a3774-c5cf-4c5d-8f7e-b5648c9ff34a", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Worker4", "startTime": 652600420722717, "endTime": 652605788544544}, "additional": {"children": ["91183c46-3bc6-41ca-b4be-0c2a4e0a2902", "b5cdde16-022b-43fc-8f95-09de4777f7e1", "d112a293-9e7d-497c-8335-c571ca612e6e", "da6ce6b0-00ae-40af-9302-66d1d18ab552", "b667cafa-402a-45df-a3f0-3d8972798950", "9bf7b0d3-2b7f-4404-9c43-e3d84be61ef0", "5ddc3b13-c893-4ef4-9494-736235e02232"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "4800a74a-6a16-4ae7-a193-1c08c482f58f", "logId": "8dc72840-b914-4087-8a5b-3f40b50944a9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "62a3edd9-51dd-4b60-beb8-238934<PERSON><PERSON><PERSON>", "name": "default@CompileArkTS work[13] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600419988287}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "226ce69e-9953-488d-ae42-522c560ea056", "name": "A work dispatched to worker[7] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600420115839}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1813aeaf-9074-4e6b-bf7e-8beb74c9d178", "name": "A work dispatched to worker[6] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600420178389}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9f630ee-4607-4993-b188-1c557e29a97f", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600420232973}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f58d480d-b871-4153-aabd-b27e1e9bb23b", "name": "default@CompileArkTS work[13] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600420419128}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a34fa22b-cf54-434e-be32-f1638c2f1fdc", "name": "default@CompileArkTS work[13] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600420505516}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd0857a1-fa1a-43ce-8aba-0d382e1e0dad", "name": "CopyResources startTime: 652600420559541", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600420563537}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1278cc30-644b-48b8-ba43-8bc4309578ca", "name": "default@CompileArkTS work[14] is submitted.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600420667980}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6375d7d0-717a-4d4f-a672-a19da6e05068", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Worker5", "startTime": 652601876278036, "endTime": 652601901137736}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "4800a74a-6a16-4ae7-a193-1c08c482f58f", "logId": "877f4e83-a33a-4fb1-9d76-f3198e6b28a9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "69780e6e-d8bf-406d-a664-8f662963fc20", "name": "default@CompileArkTS work[14] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600421387791}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a62a273-fc54-4c59-b223-61abbfdd5b69", "name": "A work dispatched to worker[7] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600421484946}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7841e85b-ee41-4833-a530-3613412403ac", "name": "A work dispatched to worker[6] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600421536436}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0150497d-3fa9-46f5-b153-9f9ec8b62f3a", "name": "Create  resident worker with id: 5.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600421691894}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f6b5478-58aa-47f0-aabe-01b7f7334029", "name": "default@CompileArkTS work[14] has been dispatched to worker[5].", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600423995158}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0a003b5f-cf34-4bea-a366-c9c704d6053f", "name": "default@CompileArkTS work[14] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600424405460}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee090f68-8a7f-4ab2-98fc-f9746a26a2aa", "name": "entry : default@CompileArkTS cost memory 1.3417129516601562", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600424664126}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bdc94e99-67c5-4900-9c4f-584fe9747844", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600434967241, "endTime": 652600440603173}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "81b24a2b-45cc-4f13-8cc2-d985c09e63f1", "logId": "8f01f117-7106-45b0-94a7-1fe7282a57da"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "81b24a2b-45cc-4f13-8cc2-d985c09e63f1", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600427336014}, "additional": {"logType": "detail", "children": [], "durationId": "bdc94e99-67c5-4900-9c4f-584fe9747844"}}, {"head": {"id": "587d7b02-ce08-4b7e-b2ba-08044c8bcca1", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600428073883}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8cbd01c8-4950-4670-be16-875f746bf080", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600428268076}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad66f690-ccca-4c52-919c-2e866c9ada7d", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600434993765}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75489469-6a3f-4835-9672-7ddc18f31cb1", "name": "entry : default@BuildJS cost memory 0.**********", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600439695913}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1854c31-5d7f-48bd-af02-05036e341846", "name": "runTaskFromQueue task cost before running: 1 s 290 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600439926877}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f01f117-7106-45b0-94a7-1fe7282a57da", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600434967241, "endTime": 652600440603173, "totalTime": 4911303}, "additional": {"logType": "info", "children": [], "durationId": "bdc94e99-67c5-4900-9c4f-584fe9747844"}}, {"head": {"id": "dbc46723-2cd2-40b3-89f8-9b59874c7ca0", "name": "worker[6] has one work done.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652601697444209}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e056a3cf-e468-490d-b604-9a3eca2822e6", "name": "default@ProcessLibs work[12] done.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652601697989646}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bcfca751-da63-4fb5-8ac2-d76e95ff0cb0", "name": "lingxia:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Worker6", "startTime": 652601696064837, "endTime": 652601697129227}, "additional": {"logType": "info", "children": [], "durationId": "b25efda8-6298-4043-a0e6-e259a687b849", "parent": "469adb8c-19d2-4f42-9b56-1a5cf9a7b531"}}, {"head": {"id": "b95ef6fb-0e05-40f6-a067-0fb0f20bc976", "name": "A work dispatched to worker[6] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652601698307241}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "469adb8c-19d2-4f42-9b56-1a5cf9a7b531", "name": "Finished :lingxia:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600054486779, "endTime": 652601698563737, "totalTime": 7723363}, "additional": {"logType": "info", "children": ["bcfca751-da63-4fb5-8ac2-d76e95ff0cb0"], "durationId": "95812323-0d2c-4b04-9987-1e2033c3b982"}}, {"head": {"id": "4ac177db-8169-4a93-b810-240864c0bf27", "name": "lingxia:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652601708933219, "endTime": 652601711376769}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "lingxia", "category": "Native", "taskRunReasons": [], "detailId": "94e6696d-8ba6-435d-a4e8-4f0d574601b5", "logId": "d4633edd-ea60-44be-9156-e0ed2912455c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "94e6696d-8ba6-435d-a4e8-4f0d574601b5", "name": "create lingxia:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652601702974165}, "additional": {"logType": "detail", "children": [], "durationId": "4ac177db-8169-4a93-b810-240864c0bf27"}}, {"head": {"id": "76bb1dba-6c0c-4f10-b139-39a9862786a2", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652601703423654}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e64ffa25-9816-4a9d-ada6-132590d46c0f", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652601703928816}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "edd818ca-41cc-4365-b809-be4bd0b340d6", "name": "Executing task :lingxia:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652601708961607}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce4ee33f-beec-444a-9da3-b7f93267f672", "name": "Task 'lingxia:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652601709311256}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb052805-6c33-4e5f-b704-f05940ae5670", "name": "Incremental task lingxia:default@DoNativeStrip pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652601710715943}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d59dc421-ebae-4ba7-be0b-4dcdaafddfa7", "name": "lingxia : default@DoNativeStrip cost memory 0.200958251953125", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652601711103258}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4633edd-ea60-44be-9156-e0ed2912455c", "name": "UP-TO-DATE :lingxia:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652601708933219, "endTime": 652601711376769}, "additional": {"logType": "info", "children": [], "durationId": "4ac177db-8169-4a93-b810-240864c0bf27"}}, {"head": {"id": "7a160b18-2103-48b2-8257-9c648a2ef933", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652601721181908, "endTime": 652601723634440}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "9a14b89c-d422-4351-aa9f-594aabeb0809", "logId": "b211be8a-5164-40d0-a444-a9ac0db365e0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9a14b89c-d422-4351-aa9f-594aabeb0809", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652601713694023}, "additional": {"logType": "detail", "children": [], "durationId": "7a160b18-2103-48b2-8257-9c648a2ef933"}}, {"head": {"id": "30480dec-8383-48cd-ba09-b2d616febef6", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652601719557757}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd3e432b-c2d3-4d9b-b058-d0db60760450", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652601719750513}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c95f9d61-24ce-4a82-818c-30f3a350c932", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652601721201912}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec77d7cc-d900-4558-9b12-f477b96e36fe", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652601721435992}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "60aadcf7-c385-4daa-89bd-84a559c21a9f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652601721574793}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2fb61ac-f1bb-4040-bce9-ec3aef6f7191", "name": "entry : default@BuildNativeWithNinja cost memory 0.0549774169921875", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652601723212948}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "566f09c9-ad2b-44b3-9635-c5c83d5639bf", "name": "runTaskFromQueue task cost before running: 2 s 574 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652601723501822}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b211be8a-5164-40d0-a444-a9ac0db365e0", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652601721181908, "endTime": 652601723634440, "totalTime": 2254427}, "additional": {"logType": "info", "children": [], "durationId": "7a160b18-2103-48b2-8257-9c648a2ef933"}}, {"head": {"id": "31e8551b-2127-4885-a185-2a7a0e99eae5", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652601730440056, "endTime": 652601760466007}, "additional": {"children": ["bc1d57c4-2c64-48ee-8369-b6c7928286bd"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": ["The task snapshots are different."], "detailId": "82c610d5-25ac-4471-bcc9-1f566fa76014", "logId": "47d18b5b-58dd-417b-98cf-7e5569d20ffd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "82c610d5-25ac-4471-bcc9-1f566fa76014", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652601728645146}, "additional": {"logType": "detail", "children": [], "durationId": "31e8551b-2127-4885-a185-2a7a0e99eae5"}}, {"head": {"id": "df565560-3789-49f9-a6aa-49c79532602e", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652601729121673}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30d86c17-0b30-4bc0-a423-535454cc55d3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652601729322814}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1c942be-d6db-4706-bdc1-dd46c642c46f", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652601730466518}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25646f12-3797-44de-9dce-2da14a08c73f", "name": "entry:default@ProcessLibs is not up-to-date, since the task snapshots are different.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652601738149620}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32200715-f9a1-4fde-9833-8edf7b737120", "name": "Incremental task entry:default@ProcessLibs pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652601738377922}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc0a4d05-47de-43f1-9452-cb0d24e4c39d", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652601738652630}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "956ed155-00bb-4b67-8428-4014bc5f4c1c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652601738798427}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8617a76-4923-4219-b06c-9ca1dc6b37aa", "name": "default@ProcessLibs work[15] is submitted.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652601743991000}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc1d57c4-2c64-48ee-8369-b6c7928286bd", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Worker6", "startTime": 652601749045888, "endTime": 652601759853350}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "31e8551b-2127-4885-a185-2a7a0e99eae5", "logId": "f99a0e5f-ab2d-435a-ae8a-59cd4ff39e6b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0fa9933d-b1f5-4000-a721-ccc4244b03d6", "name": "default@ProcessLibs work[15] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652601745320880}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6819675f-31a6-4a7d-970c-6750a935ab07", "name": "A work dispatched to worker[7] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652601745511622}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "840a521b-79e3-4f97-9d72-e81c7c123615", "name": "default@ProcessLibs work[15] has been dispatched to worker[6].", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652601745829169}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d966a366-a574-463a-852a-c308b765a662", "name": "default@ProcessLibs work[15] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652601745950870}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4bced4cf-bb2c-4317-9b15-a70194fdff49", "name": "entry : default@ProcessLibs cost memory 0.6111068725585938", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652601746105714}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9771a6b6-8685-4732-96b6-c90ffae9178f", "name": "worker[6] has one work done.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652601759988401}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38898425-5bf6-48ae-b507-5ae7393a4cfc", "name": "default@ProcessLibs work[15] done.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652601760227225}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f99a0e5f-ab2d-435a-ae8a-59cd4ff39e6b", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Worker6", "startTime": 652601749045888, "endTime": 652601759853350}, "additional": {"logType": "info", "children": [], "durationId": "bc1d57c4-2c64-48ee-8369-b6c7928286bd", "parent": "47d18b5b-58dd-417b-98cf-7e5569d20ffd"}}, {"head": {"id": "07812774-e9e7-4213-8c3a-384a9d929665", "name": "A work dispatched to worker[6] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652601760369882}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47d18b5b-58dd-417b-98cf-7e5569d20ffd", "name": "Finished :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652601730440056, "endTime": 652601760466007, "totalTime": 26604858}, "additional": {"logType": "info", "children": ["f99a0e5f-ab2d-435a-ae8a-59cd4ff39e6b"], "durationId": "31e8551b-2127-4885-a185-2a7a0e99eae5"}}, {"head": {"id": "940f6ed5-d628-45cf-a34b-8b6c8217041f", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652601769119757, "endTime": 652601775793805}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "d2cec573-e291-4ff6-aa06-5a7d49369069", "logId": "fbb61780-a5b1-4ecc-b16a-31f9f31a1508"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d2cec573-e291-4ff6-aa06-5a7d49369069", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652601764612261}, "additional": {"logType": "detail", "children": [], "durationId": "940f6ed5-d628-45cf-a34b-8b6c8217041f"}}, {"head": {"id": "9136ffe0-421a-4b73-bd06-c9bc8deebfc9", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652601765126412}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "803274ac-49fd-4502-b3c3-4283dcf4d0ad", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652601765389834}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a33c07da-3d4d-4baf-971a-9c7194e98e38", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652601769144571}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4afc09bd-1964-4ef6-8f6e-8e03b653c049", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652601769435233}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "abbdd74f-0025-48a7-a3f5-75b21cdc2d41", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 6 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652601775396207}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea4d52e1-5388-4fb9-97c3-e40ba2d4653d", "name": "entry : default@DoNativeStrip cost memory 0.08806610107421875", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652601775626856}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fbb61780-a5b1-4ecc-b16a-31f9f31a1508", "name": "UP-TO-DATE :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652601769119757, "endTime": 652601775793805}, "additional": {"logType": "info", "children": [], "durationId": "940f6ed5-d628-45cf-a34b-8b6c8217041f"}}, {"head": {"id": "e74d12e7-949a-48a4-ac6a-13e91d70972e", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652601783904214, "endTime": 652601787005960}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "4f590624-c091-4f67-bfb5-83a5d0c43036", "logId": "fd84447e-2fc9-49c0-ab93-64b2bf2c0fac"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4f590624-c091-4f67-bfb5-83a5d0c43036", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652601778221217}, "additional": {"logType": "detail", "children": [], "durationId": "e74d12e7-949a-48a4-ac6a-13e91d70972e"}}, {"head": {"id": "abe3cf18-e08c-4cb7-8fbc-1826351237ab", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652601778677977}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4debf1ca-56bb-4d86-a772-da70aea57a99", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652601778901945}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e23c191-e64d-47ba-95e6-081c8dc293c6", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652601783932174}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d045e0a-61bf-4f5b-b8a7-30a41b79a434", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652601784316941}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd9ff31f-d1e6-472c-a933-7bd9ef3e8b2c", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652601786513370}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "534db0c5-60da-4a18-9a28-35791a3452ef", "name": "entry : default@CacheNativeLibs cost memory 0.09212493896484375", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652601786783157}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd84447e-2fc9-49c0-ab93-64b2bf2c0fac", "name": "UP-TO-DATE :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652601783904214, "endTime": 652601787005960}, "additional": {"logType": "info", "children": [], "durationId": "e74d12e7-949a-48a4-ac6a-13e91d70972e"}}, {"head": {"id": "4b0f35bb-db24-497c-8aba-0231ab035aa4", "name": "worker[5] has one work done.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652601901447381}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "937c06b8-2d17-4eea-99df-754014c4d4b8", "name": "CopyResources is end, endTime: 652601901668524", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652601901679360}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "afbe54d4-2518-4d24-92a4-df3a7ac72e2a", "name": "default@CompileArkTS work[14] done.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652601901831611}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "877f4e83-a33a-4fb1-9d76-f3198e6b28a9", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Worker5", "startTime": 652601876278036, "endTime": 652601901137736}, "additional": {"logType": "info", "children": [], "durationId": "6375d7d0-717a-4d4f-a672-a19da6e05068", "parent": "b319f7eb-313e-48db-9bdf-83a100e2f75a"}}, {"head": {"id": "51391423-cb8e-4d25-9c33-53560fe13509", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652601901989819}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1eb3b96d-a9fe-47b9-8e9e-20df1d814a43", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652605790052734}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "91183c46-3bc6-41ca-b4be-0c2a4e0a2902", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Worker4", "startTime": 652600420942134, "endTime": 652600428141511}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "6a2a3774-c5cf-4c5d-8f7e-b5648c9ff34a", "logId": "59ad3b3a-fdb9-43ab-a55a-973f60d66e43"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "59ad3b3a-fdb9-43ab-a55a-973f60d66e43", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600420942134, "endTime": 652600428141511}, "additional": {"logType": "info", "children": [], "durationId": "91183c46-3bc6-41ca-b4be-0c2a4e0a2902", "parent": "8dc72840-b914-4087-8a5b-3f40b50944a9"}}, {"head": {"id": "b5cdde16-022b-43fc-8f95-09de4777f7e1", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Worker4", "startTime": 652600429000312, "endTime": 652600509088239}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "6a2a3774-c5cf-4c5d-8f7e-b5648c9ff34a", "logId": "9ebeec12-c052-460f-91ab-ba34e38392c2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9ebeec12-c052-460f-91ab-ba34e38392c2", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600429000312, "endTime": 652600509088239}, "additional": {"logType": "info", "children": [], "durationId": "b5cdde16-022b-43fc-8f95-09de4777f7e1", "parent": "8dc72840-b914-4087-8a5b-3f40b50944a9"}}, {"head": {"id": "d112a293-9e7d-497c-8335-c571ca612e6e", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Worker4", "startTime": 652600509111616, "endTime": 652600509164135}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "6a2a3774-c5cf-4c5d-8f7e-b5648c9ff34a", "logId": "2b226560-e05f-4bda-baea-790a5b298179"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2b226560-e05f-4bda-baea-790a5b298179", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600509111616, "endTime": 652600509164135}, "additional": {"logType": "info", "children": [], "durationId": "d112a293-9e7d-497c-8335-c571ca612e6e", "parent": "8dc72840-b914-4087-8a5b-3f40b50944a9"}}, {"head": {"id": "da6ce6b0-00ae-40af-9302-66d1d18ab552", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Worker4", "startTime": 652600509196700, "endTime": 652605628584307}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "6a2a3774-c5cf-4c5d-8f7e-b5648c9ff34a", "logId": "c305b620-94eb-4ea2-b927-42274de7d128"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c305b620-94eb-4ea2-b927-42274de7d128", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600509196700, "endTime": 652605628584307}, "additional": {"logType": "info", "children": [], "durationId": "da6ce6b0-00ae-40af-9302-66d1d18ab552", "parent": "8dc72840-b914-4087-8a5b-3f40b50944a9"}}, {"head": {"id": "b667cafa-402a-45df-a3f0-3d8972798950", "name": "write build package cache", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Worker4", "startTime": 652605628797657, "endTime": 652605715520291}, "additional": {"children": ["36e5117c-997f-4b93-b1f8-eaa17af2561c", "42ab8e1a-0f3e-4170-b419-50c36cc8b165", "1ffbdffa-8b5d-4585-9694-e5acb40aa323"], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "6a2a3774-c5cf-4c5d-8f7e-b5648c9ff34a", "logId": "54a9e813-7db5-4702-8f16-abc03cf312b3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "54a9e813-7db5-4702-8f16-abc03cf312b3", "name": "write build package cache", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652605628797657, "endTime": 652605715520291}, "additional": {"logType": "info", "children": ["d374275c-834e-419d-a9de-09ee9df5706f", "f14e9869-2582-4adb-a1e6-b42fee729374", "5deea557-c9d1-4bdf-9ebe-eaa36ab6e99d"], "durationId": "b667cafa-402a-45df-a3f0-3d8972798950", "parent": "8dc72840-b914-4087-8a5b-3f40b50944a9"}}, {"head": {"id": "36e5117c-997f-4b93-b1f8-eaa17af2561c", "name": "get final cache", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Worker4", "startTime": 652605629061968, "endTime": 652605629101875}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "b667cafa-402a-45df-a3f0-3d8972798950", "logId": "d374275c-834e-419d-a9de-09ee9df5706f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d374275c-834e-419d-a9de-09ee9df5706f", "name": "get final cache", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652605629061968, "endTime": 652605629101875}, "additional": {"logType": "info", "children": [], "durationId": "36e5117c-997f-4b93-b1f8-eaa17af2561c", "parent": "54a9e813-7db5-4702-8f16-abc03cf312b3"}}, {"head": {"id": "42ab8e1a-0f3e-4170-b419-50c36cc8b165", "name": "pack cache", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Worker4", "startTime": 652605629110870, "endTime": 652605700169078}, "additional": {"children": ["f4c0ea0e-fd93-4e45-86a3-851f75579e8f", "5c94753a-09df-4d4d-a668-69592fc977f7"], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "b667cafa-402a-45df-a3f0-3d8972798950", "logId": "f14e9869-2582-4adb-a1e6-b42fee729374"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f14e9869-2582-4adb-a1e6-b42fee729374", "name": "pack cache", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652605629110870, "endTime": 652605700169078}, "additional": {"logType": "info", "children": ["b742896b-9af4-4032-b705-05c1e8c6945e", "f7cf9b44-ed45-4b80-9560-f03da5d36450"], "durationId": "42ab8e1a-0f3e-4170-b419-50c36cc8b165", "parent": "54a9e813-7db5-4702-8f16-abc03cf312b3"}}, {"head": {"id": "f4c0ea0e-fd93-4e45-86a3-851f75579e8f", "name": "module 'LxAppNavigation.ets' pack", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Worker4", "startTime": 652605635005252, "endTime": 652605652716519}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "42ab8e1a-0f3e-4170-b419-50c36cc8b165", "logId": "b742896b-9af4-4032-b705-05c1e8c6945e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b742896b-9af4-4032-b705-05c1e8c6945e", "name": "module 'LxAppNavigation.ets' pack", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652605635005252, "endTime": 652605652716519}, "additional": {"logType": "info", "children": [], "durationId": "f4c0ea0e-fd93-4e45-86a3-851f75579e8f", "parent": "f14e9869-2582-4adb-a1e6-b42fee729374"}}, {"head": {"id": "5c94753a-09df-4d4d-a668-69592fc977f7", "name": "module 'LxAppContainer.ets' pack", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Worker4", "startTime": 652605657530688, "endTime": 652605685052896}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "42ab8e1a-0f3e-4170-b419-50c36cc8b165", "logId": "f7cf9b44-ed45-4b80-9560-f03da5d36450"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f7cf9b44-ed45-4b80-9560-f03da5d36450", "name": "module 'LxAppContainer.ets' pack", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652605657530688, "endTime": 652605685052896}, "additional": {"logType": "info", "children": [], "durationId": "5c94753a-09df-4d4d-a668-69592fc977f7", "parent": "f14e9869-2582-4adb-a1e6-b42fee729374"}}, {"head": {"id": "1ffbdffa-8b5d-4585-9694-e5acb40aa323", "name": "write cache", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Worker4", "startTime": 652605700179019, "endTime": 652605715500360}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "b667cafa-402a-45df-a3f0-3d8972798950", "logId": "5deea557-c9d1-4bdf-9ebe-eaa36ab6e99d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "5deea557-c9d1-4bdf-9ebe-eaa36ab6e99d", "name": "write cache", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652605700179019, "endTime": 652605715500360}, "additional": {"logType": "info", "children": [], "durationId": "1ffbdffa-8b5d-4585-9694-e5acb40aa323", "parent": "54a9e813-7db5-4702-8f16-abc03cf312b3"}}, {"head": {"id": "9bf7b0d3-2b7f-4404-9c43-e3d84be61ef0", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Worker4", "startTime": 652605715542156, "endTime": 652605774350927}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "6a2a3774-c5cf-4c5d-8f7e-b5648c9ff34a", "logId": "059d9a0e-2342-4b48-b641-997628cb292e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "059d9a0e-2342-4b48-b641-997628cb292e", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652605715542156, "endTime": 652605774350927}, "additional": {"logType": "info", "children": [], "durationId": "9bf7b0d3-2b7f-4404-9c43-e3d84be61ef0", "parent": "8dc72840-b914-4087-8a5b-3f40b50944a9"}}, {"head": {"id": "5ddc3b13-c893-4ef4-9494-736235e02232", "name": "load compilation dependencies", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Worker4", "startTime": 652161652573372, "endTime": 652162749385890}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "6a2a3774-c5cf-4c5d-8f7e-b5648c9ff34a", "logId": "f0c74931-0267-406f-9b44-0aaf3dfe8491"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f0c74931-0267-406f-9b44-0aaf3dfe8491", "name": "load compilation dependencies", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652161652573372, "endTime": 652162749385890}, "additional": {"logType": "info", "children": [], "durationId": "5ddc3b13-c893-4ef4-9494-736235e02232", "parent": "8dc72840-b914-4087-8a5b-3f40b50944a9"}}, {"head": {"id": "a3454135-61fd-4000-9763-9fb94a36ac8b", "name": "default@CompileArkTS work[13] done.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652605806520716}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8dc72840-b914-4087-8a5b-3f40b50944a9", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Worker4", "startTime": 652600420722717, "endTime": 652605788544544}, "additional": {"logType": "info", "children": ["59ad3b3a-fdb9-43ab-a55a-973f60d66e43", "9ebeec12-c052-460f-91ab-ba34e38392c2", "2b226560-e05f-4bda-baea-790a5b298179", "c305b620-94eb-4ea2-b927-42274de7d128", "54a9e813-7db5-4702-8f16-abc03cf312b3", "059d9a0e-2342-4b48-b641-997628cb292e", "f0c74931-0267-406f-9b44-0aaf3dfe8491"], "durationId": "6a2a3774-c5cf-4c5d-8f7e-b5648c9ff34a", "parent": "b319f7eb-313e-48db-9bdf-83a100e2f75a"}}, {"head": {"id": "b319f7eb-313e-48db-9bdf-83a100e2f75a", "name": "Finished :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652600398523573, "endTime": 652605807258491, "totalTime": 5390029526}, "additional": {"logType": "info", "children": ["8dc72840-b914-4087-8a5b-3f40b50944a9", "877f4e83-a33a-4fb1-9d76-f3198e6b28a9"], "durationId": "4800a74a-6a16-4ae7-a193-1c08c482f58f"}}, {"head": {"id": "d72e8d9a-2ee9-4fef-83dc-600d23a67b36", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652605833847796, "endTime": 652605841893505}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": ["The input file '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/module.json' has been changed."], "detailId": "7fc56258-6b55-43e0-a45f-f819d6374619", "logId": "bd950882-f892-4051-adeb-cdae00af3b3a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7fc56258-6b55-43e0-a45f-f819d6374619", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652605831025158}, "additional": {"logType": "detail", "children": [], "durationId": "d72e8d9a-2ee9-4fef-83dc-600d23a67b36"}}, {"head": {"id": "fe07a003-0ef4-4107-92f2-286e95c52dbe", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652605831653775}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ad29e4c-ee1d-47c9-a3ee-3fd2c9eed1e5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652605831848932}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40254a70-7b73-4665-b2cc-8ce6acbd3bc6", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652605833873081}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd02ac3e-24e4-47b9-9988-043f9c969f10", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652605835873810}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5902f905-c8c4-432d-8782-571ca8cf315c", "name": "entry:default@GeneratePkgModuleJson is not up-to-date, since the input file '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652605836553414}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f8b4598-8d64-4aad-8bef-ba8844259d17", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652605836774206}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc23d6c5-50a0-4eb6-8b57-a7cfd90bad11", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652605836943135}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a611ee75-8aaf-4ec8-85b5-031bd4340469", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652605837497234}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "edd2f8e2-3030-4862-9199-16e74fc93897", "name": "entry : default@GeneratePkgModuleJson cost memory 0.10556793212890625", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652605841487946}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0c85764-47f2-4963-b0fe-b6a4218533bc", "name": "runTaskFromQueue task cost before running: 6 s 692 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652605841775356}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd950882-f892-4051-adeb-cdae00af3b3a", "name": "Finished :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652605833847796, "endTime": 652605841893505, "totalTime": 7861694}, "additional": {"logType": "info", "children": [], "durationId": "d72e8d9a-2ee9-4fef-83dc-600d23a67b36"}}, {"head": {"id": "4ad85071-a622-4a12-8d18-1924e6d66400", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652605860872844, "endTime": 652606613368986}, "additional": {"children": ["d7a095c0-c2a4-484e-9939-952915699e60", "07cdd941-1029-492a-b10a-6c1bf6206abd", "f8892b9f-589c-43a8-946e-04e5aa94e7eb"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": ["The input file '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/module.json' has been changed."], "detailId": "89c0a246-c242-443a-b985-5aa400c76df2", "logId": "45c72d16-4e41-4fa8-a3b0-802be9ba5523"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "89c0a246-c242-443a-b985-5aa400c76df2", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652605847681788}, "additional": {"logType": "detail", "children": [], "durationId": "4ad85071-a622-4a12-8d18-1924e6d66400"}}, {"head": {"id": "a5e5fd13-5d45-4314-9aa2-cdd578430cdb", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652605848357966}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf7497c5-2bae-4b75-a7fd-ff3475e6d127", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652605848618999}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d5fa80f-87d2-4b36-a180-81c2d1d882b4", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652605860899908}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e9c4f110-4c2e-4f72-87af-21aa914671ce", "name": "entry:default@PackageHap is not up-to-date, since the input file '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652605866925274}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb91fcd3-c67f-4ff1-b4ea-05834a5521ce", "name": "Incremental task entry:default@PackageHap pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652605867205733}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbdedeba-eb58-4e10-ad6e-f656c378d62e", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652605867338396}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2de23073-61ba-4b14-bf87-56e0866c8dd3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652605867402797}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7a095c0-c2a4-484e-9939-952915699e60", "name": "generate HAP packaging command", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652605868467405, "endTime": 652605871342460}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4ad85071-a622-4a12-8d18-1924e6d66400", "logId": "0cb5c119-1b08-492d-8dfa-68eed216713c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d7a1353b-30dd-4cd1-9995-b8d2bd597b94", "name": "Use tool [/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/toolchains/lib/app_packing_tool.jar]\n [\n  'java',\n  '-Dfile.encoding=utf-8',\n  '-jar',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/toolchains/lib/app_packing_tool.jar',\n  '--mode',\n  'hap',\n  '--force',\n  'true',\n  '--lib-path',\n  '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/stripped_native_libs/default',\n  '--json-path',\n  '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/package/default/module.json',\n  '--resources-path',\n  '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources',\n  '--index-path',\n  '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources.index',\n  '--pack-info-path',\n  '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/outputs/default/pack.info',\n  '--out-path',\n  '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/outputs/default/entry-default-unsigned.hap',\n  '--ets-path',\n  '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader_out/default/ets',\n  '--pkg-context-path',\n  '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default/pkgContextInfo.json'\n]", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652605870349741}, "additional": {"logType": "debug", "children": [], "durationId": "4ad85071-a622-4a12-8d18-1924e6d66400"}}, {"head": {"id": "0cb5c119-1b08-492d-8dfa-68eed216713c", "name": "generate HAP packaging command", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652605868467405, "endTime": 652605871342460}, "additional": {"logType": "info", "children": [], "durationId": "d7a095c0-c2a4-484e-9939-952915699e60", "parent": "45c72d16-4e41-4fa8-a3b0-802be9ba5523"}}, {"head": {"id": "07cdd941-1029-492a-b10a-6c1bf6206abd", "name": "submit HAP packaging task to work pool", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652605872702793, "endTime": 652605875760219}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4ad85071-a622-4a12-8d18-1924e6d66400", "logId": "206aff5d-c5c2-4942-9b68-f688712a010c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d15179d4-fa3a-4bb7-8cbc-5c7a506de893", "name": "default@PackageHap work[16] is submitted.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652605873732070}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f8892b9f-589c-43a8-946e-04e5aa94e7eb", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Worker4", "startTime": 652605909608514, "endTime": 652606612500075}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "4ad85071-a622-4a12-8d18-1924e6d66400", "logId": "26838c4d-0bb4-46af-9e4f-ec9aac586d5b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c94cde77-5769-483b-804e-d88fdd18bd04", "name": "default@PackageHap work[16] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652605875528401}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "855eca36-faab-4fc0-b991-cd1dd72bb46b", "name": "default@PackageHap work[16] is not dispatched.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652605875683349}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "206aff5d-c5c2-4942-9b68-f688712a010c", "name": "submit HAP packaging task to work pool", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652605872702793, "endTime": 652605875760219}, "additional": {"logType": "info", "children": [], "durationId": "07cdd941-1029-492a-b10a-6c1bf6206abd", "parent": "45c72d16-4e41-4fa8-a3b0-802be9ba5523"}}, {"head": {"id": "c36e010a-1db7-4593-bbd4-df816e769ce6", "name": "entry : default@PackageHap cost memory 0.565704345703125", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652605880442798}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "317d3604-044c-4de3-b24b-cfc5d968cd35", "name": "default@PackageHap work[16] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652605907800236}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e802136c-f2f5-4777-92a3-226cae1d2489", "name": "A work dispatched to worker[7] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652605976443859}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78f6739e-59d1-4b76-bdaf-abd43535ebdf", "name": "A work dispatched to worker[6] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652605976691229}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0573b3e5-05d1-460d-8557-2f856b98fabe", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652605976791366}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5064559f-6640-479b-aee3-4999d9940766", "name": "A work dispatched to worker[4] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652605976857028}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e56c936-9879-455b-9dd1-9828ca9b100f", "name": "A work dispatched to worker[3] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652605976929078}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1138b8e3-d6a3-4370-b951-6fadde00462c", "name": "A work dispatched to worker[2] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652605976998272}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1f50dd9-ad07-425d-925d-1042c4986460", "name": "A work dispatched to worker[1] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652605977067055}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "130a54a7-d2c8-48b8-a7ad-3748fdfec530", "name": "A work dispatched to worker[0] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652605977135635}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e31a119-64be-4472-869f-f4f7ee2d0e1b", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652606612681330}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf42a212-9d81-4414-9c99-00d8498519de", "name": "default@PackageHap work[16] done.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652606612921963}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "26838c4d-0bb4-46af-9e4f-ec9aac586d5b", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Worker4", "startTime": 652605909608514, "endTime": 652606612500075}, "additional": {"logType": "info", "children": [], "durationId": "f8892b9f-589c-43a8-946e-04e5aa94e7eb", "parent": "45c72d16-4e41-4fa8-a3b0-802be9ba5523"}}, {"head": {"id": "c8480e47-cba8-4c74-afb7-c339fefdc2a5", "name": "A work dispatched to worker[4] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652606613118003}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45c72d16-4e41-4fa8-a3b0-802be9ba5523", "name": "Finished :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652605860872844, "endTime": 652606613368986, "totalTime": 722676582}, "additional": {"logType": "info", "children": ["0cb5c119-1b08-492d-8dfa-68eed216713c", "206aff5d-c5c2-4942-9b68-f688712a010c", "26838c4d-0bb4-46af-9e4f-ec9aac586d5b"], "durationId": "4ad85071-a622-4a12-8d18-1924e6d66400"}}, {"head": {"id": "d2a353be-91c7-4ed5-a617-13d89927867e", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652606618538701, "endTime": 652609247864834}, "additional": {"children": ["e5ff6255-1fcd-44d2-af66-7935d5889849", "b07a5fe5-4217-48aa-ac3c-53126284d8ae"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The input file '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/outputs/default/entry-default-unsigned.hap' has been changed."], "detailId": "ce597494-5408-4ce6-8fdf-280efe307552", "logId": "c7a8760d-8ecf-446d-9085-91c363ca6908"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ce597494-5408-4ce6-8fdf-280efe307552", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652606616178328}, "additional": {"logType": "detail", "children": [], "durationId": "d2a353be-91c7-4ed5-a617-13d89927867e"}}, {"head": {"id": "c5cf964a-ee6b-4f41-b6dc-2c75163243a1", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652606616449880}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0310592d-8cf3-4724-8339-1ba15c9480f4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652606616549844}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d37a25b-4e81-4f76-a24b-50046f216a31", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652606618553169}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bda3ca39-9c69-4ee3-b963-6c254da6d7a2", "name": "entry:default@SignHap is not up-to-date, since the input file '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/outputs/default/entry-default-unsigned.hap' has been changed.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652606620899472}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28172f16-fbf2-4c2e-af28-8885339df95f", "name": "Incremental task entry:default@SignHap pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652606621086144}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34700771-f099-430f-8390-8f79414ff084", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652606621215464}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b09480a9-30a4-4527-bbb9-0f38951c4163", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652606621314716}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5ff6255-1fcd-44d2-af66-7935d5889849", "name": "generate hos_hap signing command", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652606628868984, "endTime": 652608521724438}, "additional": {"children": ["2ca6cb0a-0edf-499e-9634-72da4f7fbe6a"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d2a353be-91c7-4ed5-a617-13d89927867e", "logId": "44975d6a-5f4f-4305-8c56-41f1e311a51f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2ca6cb0a-0edf-499e-9634-72da4f7fbe6a", "name": "verify signing configuration and get bundle name", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652606652862042, "endTime": 652608520281251}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e5ff6255-1fcd-44d2-af66-7935d5889849", "logId": "017ca11d-7978-43b7-a90c-60667028f832"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "40150ea2-5659-4028-8fba-b1612b1a7029", "name": "java daemon socket received message:{\"code\":0,\"message\":\"verify profile success\"}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652608518475693}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "017ca11d-7978-43b7-a90c-60667028f832", "name": "verify signing configuration and get bundle name", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652606652862042, "endTime": 652608520281251}, "additional": {"logType": "info", "children": [], "durationId": "2ca6cb0a-0edf-499e-9634-72da4f7fbe6a", "parent": "44975d6a-5f4f-4305-8c56-41f1e311a51f"}}, {"head": {"id": "44975d6a-5f4f-4305-8c56-41f1e311a51f", "name": "generate hos_hap signing command", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652606628868984, "endTime": 652608521724438}, "additional": {"logType": "info", "children": ["017ca11d-7978-43b7-a90c-60667028f832"], "durationId": "e5ff6255-1fcd-44d2-af66-7935d5889849", "parent": "c7a8760d-8ecf-446d-9085-91c363ca6908"}}, {"head": {"id": "b07a5fe5-4217-48aa-ac3c-53126284d8ae", "name": "execute hos_hap signing command", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652608522450534, "endTime": 652609247183116}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "d2a353be-91c7-4ed5-a617-13d89927867e", "logId": "21a758fc-c2df-4171-ab47-f9d87eff2ced"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eababc16-f91f-49c4-8b8d-a184baae7ade", "name": "java daemon socket close code:1000 reason:close by user", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652608528279912}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c68c51d1-3b8b-422f-a139-91f30fac4230", "name": "java daemon socket received message:{\"code\":0,\"message\":\"sign app success\"}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609246221238}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21a758fc-c2df-4171-ab47-f9d87eff2ced", "name": "execute hos_hap signing command", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652608522450534, "endTime": 652609247183116}, "additional": {"logType": "info", "children": [], "durationId": "b07a5fe5-4217-48aa-ac3c-53126284d8ae", "parent": "c7a8760d-8ecf-446d-9085-91c363ca6908"}}, {"head": {"id": "1fa4cb1d-2830-4bba-a98f-69ed8b5618a1", "name": "entry : default@SignHap cost memory -2.8312454223632812", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609247373895}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "63e41e83-64c3-45aa-95cd-8c076bbfbab9", "name": "runTaskFromQueue task cost before running: 10 s 98 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609247675804}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7a8760d-8ecf-446d-9085-91c363ca6908", "name": "Finished :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652606618538701, "endTime": 652609247864834, "totalTime": 2629072238}, "additional": {"logType": "info", "children": ["44975d6a-5f4f-4305-8c56-41f1e311a51f", "21a758fc-c2df-4171-ab47-f9d87eff2ced"], "durationId": "d2a353be-91c7-4ed5-a617-13d89927867e"}}, {"head": {"id": "3e365cce-0b63-406b-bd8c-29e8cefe27de", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609252430869, "endTime": 652609257365220}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "dde90845-9ebe-4b2e-a131-47343998421b", "logId": "06fbdd30-4cfc-4a1c-84f4-349767d660ae"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dde90845-9ebe-4b2e-a131-47343998421b", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609250733247}, "additional": {"logType": "detail", "children": [], "durationId": "3e365cce-0b63-406b-bd8c-29e8cefe27de"}}, {"head": {"id": "265be8fe-76f7-40dc-b275-f3e022b3efe7", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609251203838}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e570f85-5001-41f5-b2df-f0861f19ddda", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609251448309}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c0cdbcb-1ab5-4b85-b626-031fd09bdc77", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609252446445}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3adb1945-6113-4986-93b4-e0adc9d61526", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609256477927}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4e4de8fd-1009-4e4d-8ffc-80bd95182f5f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609256628564}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3699eddc-d613-4788-848a-b2a1e2de51f2", "name": "entry : default@CollectDebugSymbol cost memory 0.20001220703125", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609256814406}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b03badc9-b7f7-466b-8611-698813cce2f6", "name": "runTaskFromQueue task cost before running: 10 s 107 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609257182925}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06fbdd30-4cfc-4a1c-84f4-349767d660ae", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609252430869, "endTime": 652609257365220, "totalTime": 4667261}, "additional": {"logType": "info", "children": [], "durationId": "3e365cce-0b63-406b-bd8c-29e8cefe27de"}}, {"head": {"id": "75d71f1d-4584-417f-8a46-1ab7f3da6204", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609259909604, "endTime": 652609260326495}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "c64bfc03-da20-4225-a85c-c408c911651d", "logId": "57d57723-2837-4299-8d4f-f7703fdb0d24"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c64bfc03-da20-4225-a85c-c408c911651d", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609259829901}, "additional": {"logType": "detail", "children": [], "durationId": "75d71f1d-4584-417f-8a46-1ab7f3da6204"}}, {"head": {"id": "9fda31b8-954b-411c-9efe-98883f5401f4", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609259921129}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ec6073a-a0cc-4e6d-b985-d29d29fdf8af", "name": "entry : assembleHap cost memory 0.01165008544921875", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609260135515}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ea2e6918-68a5-4dc8-b191-a50ae0013fc6", "name": "runTaskFromQueue task cost before running: 10 s 110 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609260254215}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57d57723-2837-4299-8d4f-f7703fdb0d24", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609259909604, "endTime": 652609260326495, "totalTime": 314329}, "additional": {"logType": "info", "children": [], "durationId": "75d71f1d-4584-417f-8a46-1ab7f3da6204"}}, {"head": {"id": "9f2b376d-2b82-4b0d-b339-422d6fcd1892", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609263787218, "endTime": 652609263820070}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "8089d0a3-bd4a-468e-8e2e-8c1b6318b3de", "logId": "1c52985b-7374-43a5-bd25-8a33d4a06733"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1c52985b-7374-43a5-bd25-8a33d4a06733", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609263787218, "endTime": 652609263820070}, "additional": {"logType": "info", "children": [], "durationId": "9f2b376d-2b82-4b0d-b339-422d6fcd1892"}}, {"head": {"id": "63becba3-ab17-4d75-8ce5-079c5f831050", "name": "BUILD SUCCESSFUL in 10 s 114 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609263961855}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "65527b65-be13-49a9-bc04-260346f9377a", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652599150271736, "endTime": 652609264444724}, "additional": {"time": {"year": 2025, "month": 7, "day": 31, "hour": 21, "minute": 40}, "completeCommand": "{\"prop\":[],\"_\":[\"assembleHap\"]};assembleHap", "hvigorVersion": "5.17.2", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "213e9d1b-ed6c-42b5-bc30-573220e5f68d", "name": "Update task entry:default@PreBuild input file:/Users/<USER>/github/LingXia/examples/harmony/AppScope/app.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609264669479}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2b4c1b9-4b89-469b-89a6-0b27cdab35fb", "name": "Update task entry:default@PreBuild input file:/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/module.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609265234011}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c41a241d-319a-46f4-8f23-57b8f236e778", "name": "Update task entry:default@PreBuild input file:/Users/<USER>/github/LingXia/examples/harmony/build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609265519144}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0b3be9d-58d1-4e3e-bf2c-eb24b12ac277", "name": "Update task entry:default@PreBuild input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609266501469}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "02eae184-bb1c-4cfd-ab6a-689eac0fd921", "name": "Update task entry:default@PreBuild input file:/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources/base/profile/main_pages.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609266676841}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f58b64ed-4904-4612-a441-7593cc29a7d7", "name": "Update task entry:default@PreBuild input file:/Users/<USER>/github/LingXia/examples/harmony/hvigor/hvigor-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609266795827}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dfa65dc1-1cc1-461f-92a4-0935f81fb4ee", "name": "Update task entry:default@PreBuild input file:/Users/<USER>/github/LingXia/examples/harmony/entry/oh-package.json5 cache from map.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609266899549}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d323dd2-f42b-4888-9c25-d3ea9b8116ca", "name": "Update task entry:default@PreBuild input file:/Users/<USER>/github/LingXia/examples/harmony/oh-package.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609266959546}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a33e814-86a0-4731-a4a2-579d3607d339", "name": "Incremental task entry:default@PreBuild post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609267125137}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bdbf043a-a938-498d-bfe3-797213776b8d", "name": "Update task lingxia:default@PreBuild input file:/Users/<USER>/github/LingXia/examples/harmony/AppScope/app.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609267299118}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fddf541a-d888-41ac-bb89-381b505c6099", "name": "Update task lingxia:default@PreBuild input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/module.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609267409757}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57a5b1e8-2569-4cc7-ae1b-47ffc78eb2ba", "name": "Update task lingxia:default@PreBuild input file:/Users/<USER>/github/LingXia/examples/harmony/build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609267523907}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0235102-e2fb-42e3-a913-0e1eb574bb56", "name": "Update task lingxia:default@PreBuild input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609267616624}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc73e0d3-fff5-43da-b61e-916c2c2203ba", "name": "Update task lingxia:default@PreBuild input file:/Users/<USER>/github/LingXia/examples/harmony/hvigor/hvigor-config.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609267710886}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d21d4e83-0beb-41d0-a2b0-c1d1f4731080", "name": "Update task lingxia:default@PreBuild input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/oh-package.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609267803593}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ada4c5a2-d9a4-43c9-a8fd-d7c6e50b1796", "name": "Update task lingxia:default@PreBuild input file:/Users/<USER>/github/LingXia/examples/harmony/oh-package.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609267894482}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c96d7040-f53b-4c96-82a0-e6af326a0a07", "name": "Incremental task lingxia:default@PreBuild post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609268026453}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f63e5156-251e-4905-b647-49f4dcaddc9c", "name": "Update task entry:default@CreateModuleInfo output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/generated/profile/default/ModuleInfo.ts cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609268103961}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a520234-30ee-44f2-9e17-046a47615eb5", "name": "Incremental task entry:default@CreateModuleInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609268317654}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b37ca01-180b-47c5-8ee3-12cc98f84019", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609268387970}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77a5a009-c591-4479-bcd1-f5cee29c99b6", "name": "Update task entry:default@GeneratePkgContextInfo output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default/pkgContextInfo.json cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609268448849}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "276021f2-4dc4-408f-8bb0-0d35cc23016c", "name": "Incremental task entry:default@GeneratePkgContextInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609268586943}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7b8bfca-a667-409f-af96-2b912003e85d", "name": "Update task entry:default@ProcessIntegratedHsp output file:/Users/<USER>/github/LingXia/examples/harmony/build/cache/default/integrated_hsp/integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609269224653}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61212d2b-8370-448a-8115-baf3c74aec7f", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609269551951}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67461eea-b813-4c45-8431-a56677aa4275", "name": "Update task lingxia:default@CreateHarBuildProfile input file:/Users/<USER>/github/LingXia/examples/harmony/AppScope/app.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609269650470}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9521ce42-b827-4f35-bc08-e13e96101605", "name": "Update task lingxia:default@CreateHarBuildProfile input file:/Users/<USER>/github/LingXia/examples/harmony/build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609269913975}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec41542a-afb4-4ae0-9a12-c6da24fd993a", "name": "Update task lingxia:default@CreateHarBuildProfile output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/BuildProfile.ets cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609270156096}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "66b581f7-9dc0-4c30-a62a-d53ea52c187e", "name": "Incremental task lingxia:default@CreateHarBuildProfile post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609270578284}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20a5fc79-7792-43c2-8dfa-dea9f080f13d", "name": "Update task lingxia:default@MergeProfile input file:/Users/<USER>/github/LingXia/examples/harmony/AppScope/app.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609270858915}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d86941eb-c26a-4481-90ef-ca86de1c8e39", "name": "Update task lingxia:default@MergeProfile input file:/Users/<USER>/github/LingXia/examples/harmony/build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609271284469}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee659621-b1e8-4ad7-9dcc-cb4dc035c882", "name": "Update task lingxia:default@MergeProfile input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/module.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609271532539}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c159d2b-1e29-4b3f-8176-17082522d7a8", "name": "Update task lingxia:default@MergeProfile output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/merge_profile/default/module.json cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609271908489}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f1fe685-bb52-40b0-9b59-808af9b20699", "name": "Incremental task lingxia:default@MergeProfile post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609272154036}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "999c268f-a3b8-4ead-9048-df7308a41bdb", "name": "Update task entry:default@SyscapTransform input file:/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/toolchains/syscap_tool cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609272330119}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fea27920-596a-40b2-88cf-a53b77a812a0", "name": "Update task entry:default@SyscapTransform input file:/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/device-define cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609272534122}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d407302a-7817-4056-af5d-4ea4a1ed927e", "name": "Update task entry:default@SyscapTransform output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/syscap/default/rpcid.sc cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609273031363}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "703f3d3b-8806-4865-988b-04c2324cf3cd", "name": "Incremental task entry:default@SyscapTransform post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609273274500}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b77262b-ad1b-4fcc-a1a8-73b732c58926", "name": "Update task entry:default@ProcessRouterMap input file:/Users/<USER>/github/LingXia/examples/harmony/entry/oh-package.json5 cache from map.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609274063666}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4928a1b4-3300-45fd-8667-88b469011907", "name": "Update task entry:default@ProcessRouterMap input file:/Users/<USER>/github/LingXia/examples/harmony/oh-package.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609274191509}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fff06f6d-9aed-4a19-9371-7223e4dc6909", "name": "Update task entry:default@ProcessRouterMap input file:/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/module.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609274327078}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34d87de0-2940-47b3-8797-01f9f2142b4d", "name": "Update task entry:default@ProcessRouterMap input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default/pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609274437625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4b99f73-1cf9-4565-96c7-9334098f9314", "name": "Update task entry:default@ProcessRouterMap output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/router_map/default/temp-router-map.json cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609274554015}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b13fe9e-194f-4652-be80-252960f0c0fe", "name": "Update task entry:default@ProcessRouterMap output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/router_map/default/loader-router-map.json cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609274662506}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6cd55922-d955-4c40-8558-2a04d54fff29", "name": "Incremental task entry:default@ProcessRouterMap post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609274870834}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f06dfc2d-c27a-426a-9001-bd3431284e14", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609274964034}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a6babec-78bb-4840-914a-0667cd91e307", "name": "Update task entry:default@MergeProfile input file:/Users/<USER>/github/LingXia/examples/harmony/AppScope/app.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609275067556}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c458963-c146-42f5-92d0-9b635c8349c8", "name": "Update task entry:default@MergeProfile input file:/Users/<USER>/github/LingXia/examples/harmony/build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609275205391}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb4309d1-9634-4094-ac82-e4da371a6835", "name": "Update task entry:default@MergeProfile input file:/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/module.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609275310530}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0334badf-93d9-4aad-a417-3c886bcba00a", "name": "Update task entry:default@MergeProfile input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/merge_profile/default/module.json cache from map.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609275407171}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4d68397-80a7-41ed-8a00-fd9f2e8ac11e", "name": "Update task entry:default@MergeProfile output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/merge_profile/default/module.json cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609275470275}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e32689d-e3bd-4b88-af9e-870e7cbb6b55", "name": "Incremental task entry:default@MergeProfile post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609275625904}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6b74680-3b96-4738-863f-20513627b7a4", "name": "Module 'lingxia' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609277910187}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fbd4495d-b6ab-4c50-af65-4e50f6e8093d", "name": "Update task entry:default@GenerateLoaderJson input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default/pkgContextInfo.json cache from map.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609278918685}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7521cdc3-797e-45d2-92d5-d70bfd226bed", "name": "Update task entry:default@GenerateLoaderJson input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/router_map/default/temp-router-map.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609279226431}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34751e21-19b5-478d-aebb-49d5463ac07b", "name": "Update task entry:default@GenerateLoaderJson output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default/loader.json cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609279583633}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7317e67-56cd-4655-850c-7076f2e1f3f5", "name": "Incremental task entry:default@GenerateLoaderJson post-execution cost:5 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609279985589}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "187b70b0-1610-4c6e-9f08-078e487876a4", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609280087775}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1868b7ae-a464-4450-bcd1-58eb71d5fffb", "name": "Update task entry:default@ProcessProfile input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/merge_profile/default/module.json cache from map.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609280151304}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "20526453-01b2-4068-9b24-a00116377d8d", "name": "Update task entry:default@ProcessProfile output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/process_profile/default/module.json cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609280203702}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "086df231-4898-499a-82d1-2adf698b5a2b", "name": "Incremental task entry:default@ProcessProfile post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609280347234}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ddbdd40c-e509-4791-bc2c-744795a74e41", "name": "Update task lingxia:default@ProcessLibs input file:/Users/<USER>/github/LingXia/examples/harmony/build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609280459645}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b2beb34-24c7-4fb2-a89e-b725bd4f1941", "name": "Update task lingxia:default@ProcessLibs input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609280552864}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4b20af95-59eb-4d4d-a380-47f2a4c6e4e7", "name": "Update task lingxia:default@ProcessLibs output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/libs/default cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609280670444}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c821569-0e75-45ee-a096-5b62922cf808", "name": "Incremental task lingxia:default@ProcessLibs post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609280867735}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ccb05ce1-eb01-46cb-921a-ab9687130d40", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609280934714}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52fa8a34-ac9e-4e9d-b246-4b3f0b8f1534", "name": "Update task entry:default@CompileResource input file:/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources cache from map.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609281476964}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a394b8c-e9e7-4757-8f9e-17ec7602c7be", "name": "Update task entry:default@CompileResource input file:/Users/<USER>/github/LingXia/examples/harmony/AppScope/resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609281570619}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9081280a-f5c5-402a-af7d-d920ec07df43", "name": "Update task entry:default@CompileResource input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/process_profile/default/module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609282114083}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f26cd64f-7678-4fca-be58-31d189966cc2", "name": "Update task entry:default@CompileResource input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resConfig.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609282233783}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3aac75c-1230-446c-9370-a32c15e18764", "name": "Update task entry:default@CompileResource output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609282334715}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61b91ee4-fe0b-4e17-b8bd-621bd890d629", "name": "Update task entry:default@CompileResource output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/generated/r/default/ResourceTable.h cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609284938989}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9af01d06-c0a8-4625-8565-08484ea4ed24", "name": "Update task entry:default@CompileResource output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/generated/r/default cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609285075522}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9f04fcc-68d1-4f40-b688-94e177549147", "name": "Incremental task entry:default@CompileResource post-execution cost:5 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609285319816}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b08db5c-f454-4264-a2ed-442c16bb181e", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609286997298}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19b6a13a-274c-4512-afa7-116c6244a394", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/Index.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609287640971}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38bdacc8-6922-4f53-820b-2f55dbc45b7b", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609287900041}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8e54f1e-2e06-4b14-b8d7-a8e10d9d0bd1", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609288750562}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "719654a8-b4f4-4193-888e-8a2d6a1546d9", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609290963463}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "82002675-1ffa-47f1-a894-80cde555bdec", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609291128824}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "68e78822-7b52-43b8-a7c3-2c72ff217383", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/base/profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609291232545}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e765763-81e3-4ed2-bd97-f1e7b0d88d60", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609291412537}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d86cf1e5-9f65-4b5f-a3e5-18cd808e4a80", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default/pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609291771387}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "729c4437-7930-4837-bc04-a2ab77ada62f", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/generated/profile/default/BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609291880334}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09672aed-4a36-432b-bc62-5c3c416f1da6", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609291988374}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64955329-9560-476f-a412-2001b20ad25b", "name": "Update task entry:default@CompileArkTS output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader_out/default/ets cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609292110710}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e875c14-23d0-4e19-bce5-f76950124fc9", "name": "Incremental task entry:default@CompileArkTS post-execution cost:7 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609292398457}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0421fcd6-1199-4c57-8592-29ca685b2760", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609293481073}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e6b9d22-46de-4ff7-b0ba-9a885c63b04a", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/Index.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609293779708}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "413317eb-5671-4f25-803d-4d38883bfc9b", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609293890021}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa816c73-13ad-49e9-b4ab-bc8fbc7db960", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609296720157}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4a87579-763c-4ed0-98fa-08148a1e78d6", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609296911734}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8d484f4-6b7d-4df1-b758-0c4076a3a286", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/base/profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609297018563}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "677d0463-a1fb-417d-bbc9-07775ff3ecec", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default/pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609297217709}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25696953-f96e-4e2e-a311-2f1ad94e1e36", "name": "Update task entry:default@BuildJS output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader_out/default/js cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609297347842}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "125aff1c-6e4d-4a61-9c49-8b31d42f8bf8", "name": "Incremental task entry:default@BuildJS post-execution cost:6 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609297537450}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fca14fa-9e9d-4ac6-9c3f-3d820f3838c7", "name": "There is no need to refresh cache, since the incremental task lingxia:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609297603483}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a0f0829-2934-459d-8e16-f4f2e6344d71", "name": "Update task entry:default@ProcessLibs input file:/Users/<USER>/github/LingXia/examples/harmony/entry/libs cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609297708347}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "905797a5-b34e-4769-8c02-c09fc49a44e1", "name": "Update task entry:default@ProcessLibs input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/libs/default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609297982264}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c21a23aa-1ffc-4406-8477-eaed02c9645b", "name": "Update task entry:default@ProcessLibs input file:/Users/<USER>/github/LingXia/examples/harmony/build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609298114463}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd27a45e-850a-4301-9747-a5f225b13320", "name": "Update task entry:default@ProcessLibs input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609298210939}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8af29567-e477-42ac-97cf-9d6ada052ec6", "name": "Update task entry:default@ProcessLibs output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/libs/default cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609298300710}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7600fe69-b9db-4cc6-b160-aeae821c34c7", "name": "Incremental task entry:default@ProcessLibs post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609298591836}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c47eb57e-e46b-49bf-a809-f223f918d212", "name": "There is no need to refresh cache, since the incremental task entry:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609298661826}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "67d00be6-8432-441f-92df-ce6852746bdb", "name": "There is no need to refresh cache, since the incremental task entry:default@CacheNativeLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609298729082}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a6e2e31-ea92-4317-b0cb-e50375a5a72c", "name": "Update task entry:default@GeneratePkgModuleJson input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/module.json cache from map.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609298789986}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1288fd1-6e11-4feb-9ef1-315fed197aca", "name": "Update task entry:default@GeneratePkgModuleJson output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/package/default/module.json cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609298840420}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a546e11-b179-4043-b486-d91ea5efce4f", "name": "Incremental task entry:default@GeneratePkgModuleJson post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609298970439}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e7aea80-3fca-47b4-b576-c753a04a6273", "name": "Update task entry:default@PackageHap input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/stripped_native_libs/default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609299589232}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "706b6f94-eff4-4438-96e4-eba6fb50afd9", "name": "Update task entry:default@PackageHap input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/module.json cache from map.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609300575811}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6faddbc9-16a5-4986-a041-4ddc97d6917a", "name": "Update task entry:default@PackageHap input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609300694875}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09da3c79-ac7b-4e4e-a896-1de99028c4fd", "name": "Update task entry:default@PackageHap input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources.index cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609302896971}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ffafcbe-6004-478d-955d-1e67a462d51a", "name": "Update task entry:default@PackageHap input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/outputs/default/pack.info cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609303140359}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ad896ea-71d8-422f-92d3-8802a4e88f12", "name": "Update task entry:default@PackageHap input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader_out/default/ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609303380627}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "08357403-177c-4c28-971f-73d26d646bb9", "name": "Update task entry:default@PackageHap input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default/pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609303749300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cada031b-d8f1-44fb-88d7-e115603c2036", "name": "Update task entry:default@PackageHap input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader_out/default/ets/sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609304056066}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a067da8-be78-4213-8350-2090a902dc61", "name": "Update task entry:default@PackageHap input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/generated/profile/default/BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609304314652}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01481434-932a-41c7-b25a-decd4914de6f", "name": "Update task entry:default@PackageHap output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/outputs/default/entry-default-unsigned.hap cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609304833462}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ff7ff97-0965-4116-8d43-64b1d2c120d9", "name": "Update task entry:default@PackageHap output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/source_map/default/sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609304968525}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b213bab-777d-4489-93f9-1f6c215e4cdf", "name": "Update task entry:default@PackageHap output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/outputs/default/mapping/sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609305070892}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de12c3ba-750e-433c-ab9c-044d0eac4e8a", "name": "Incremental task entry:default@PackageHap post-execution cost:7 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609305225681}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2080e2c-8e7f-4c89-b4d7-01411f96f396", "name": "Update task entry:default@SignHap input file:/Users/<USER>/.ohos/config/default_harmony_Ox-3HNfvclQdbq4KOWhaXIc2hg7i7fx0_2L33NsWpQw=.cer cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609305353763}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ba20aaf9-7d25-4a62-9ce5-67eba1b00e57", "name": "Update task entry:default@SignHap input file:/Users/<USER>/.ohos/config/default_harmony_Ox-3HNfvclQdbq4KOWhaXIc2hg7i7fx0_2L33NsWpQw=.p7b cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609305458098}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e39e383-cb8f-4311-8c41-8be660285331", "name": "Update task entry:default@SignHap input file:/Users/<USER>/.ohos/config/default_harmony_Ox-3HNfvclQdbq4KOWhaXIc2hg7i7fx0_2L33NsWpQw=.p12 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609305549923}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e521a162-1436-4278-8c31-41b1c8b86866", "name": "Update task entry:default@SignHap input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/outputs/default/entry-default-unsigned.hap cache from map.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609305635089}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8389a903-685b-4164-8db2-4978ba9bd7c3", "name": "Update task entry:default@SignHap output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/outputs/default/entry-default-signed.hap cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609305692859}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ea053e4-9c59-4bcf-8a16-35a8e005e610", "name": "Incremental task entry:default@SignHap post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609305802036}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0dd7fa3a-af0d-4c14-aa5b-2d04597aafc0", "name": "Update task entry:default@CollectDebugSymbol input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader_out/default/ets/sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609307315251}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae3dc52a-3c54-4ab3-9022-4af7013aef74", "name": "Update task entry:default@CollectDebugSymbol input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/libs/default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609307486550}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "76fbc144-2356-4f90-9cbc-6980971686fa", "name": "Update task entry:default@CollectDebugSymbol output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/outputs/default/symbol cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609307815693}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4549b0ea-a06d-4a69-b7e8-e9640de0ac16", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609308014886}, "additional": {"logType": "debug", "children": []}}], "workLog": []}