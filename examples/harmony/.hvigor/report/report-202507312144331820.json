{"version": "2.0", "ppid": 50563, "events": [{"head": {"id": "d31c9336-6761-4b72-83b8-945d4b380125", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609330282778}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ccd75d88-2a0c-46fd-86d7-0c52a4e0353a", "name": "java daemon socket close code:1000 reason:close by user", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609332104835}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a32444cd-adde-46f1-b423-1b9c21401865", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609334866286}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d61d1f63-b19d-49db-84b4-351e86ab7705", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609335422815}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f352d4d-2b65-4de8-a18e-dfc47ae2474a", "name": "worker[6] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609345170183}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e74bdef-5109-4210-9159-0d4b2c1c612e", "name": "worker[5] exits with exit code 1.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652609347286468}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7d30dc9-dda2-435f-b77e-f35de17c2b66", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861487073335}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f292787-2fcc-4c4b-a49f-df6069342c1f", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861534594515, "endTime": 652862045920731}, "additional": {"children": ["585cd977-c87b-4a72-9072-39b079adb979", "1932ce57-5a5e-4f00-bb9e-5a7f20d468d8", "f44b2099-f559-4cf9-9354-8dd042a98f02", "53b60920-30d3-49fc-b07c-49ba0bf24465", "99e03117-369c-4cd7-9105-34b99b6e71c5", "25ae078a-a39b-4f35-b8ea-996b195665e5", "107bf2dd-9bc1-4263-a9db-2062e76cda15"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "9e4e0acb-e2e5-413a-8a0c-a8fd4c452589"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "585cd977-c87b-4a72-9072-39b079adb979", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861534608611, "endTime": 652861593148891}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4f292787-2fcc-4c4b-a49f-df6069342c1f", "logId": "bfe19dbe-58b4-4cf4-9abf-3d1973207640"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1932ce57-5a5e-4f00-bb9e-5a7f20d468d8", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861593191121, "endTime": 652862042522755}, "additional": {"children": ["9e0d9d90-23dc-44b4-845a-234bc669048b", "9656a7b7-1285-4953-8966-f8af6d71856c", "a9ad3ad3-8f82-4160-9b07-7cf3267b558c", "e68a532d-ae25-4749-a13e-dd209cd680a6", "eaaed2cb-e421-41c1-a2ef-8f75047b763d", "fc23e379-6ec8-454c-876f-f653a17c24e4", "aa010d6c-d599-43e5-9a3a-576e044dcbb9", "2bbebf4a-2ba2-45d1-8b72-d8da8971ef8d", "9d679ba1-1a10-4f54-a37d-34f64f237acc"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4f292787-2fcc-4c4b-a49f-df6069342c1f", "logId": "4b9bd515-db3e-44ce-81d1-a6405b2087c6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f44b2099-f559-4cf9-9354-8dd042a98f02", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862042557199, "endTime": 652862045821153}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4f292787-2fcc-4c4b-a49f-df6069342c1f", "logId": "2634f943-e8ed-45e7-92f5-d7b0bddb2140"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "53b60920-30d3-49fc-b07c-49ba0bf24465", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862045831023, "endTime": 652862045896655}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4f292787-2fcc-4c4b-a49f-df6069342c1f", "logId": "23963865-1f78-4a5d-87a6-e4db3ef463ab"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "99e03117-369c-4cd7-9105-34b99b6e71c5", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861546440714, "endTime": 652861549097868}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4f292787-2fcc-4c4b-a49f-df6069342c1f", "logId": "e1ad6379-ca26-4bd8-a24b-3352bafb501d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e1ad6379-ca26-4bd8-a24b-3352bafb501d", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861546440714, "endTime": 652861549097868}, "additional": {"logType": "info", "children": [], "durationId": "99e03117-369c-4cd7-9105-34b99b6e71c5", "parent": "9e4e0acb-e2e5-413a-8a0c-a8fd4c452589"}}, {"head": {"id": "25ae078a-a39b-4f35-b8ea-996b195665e5", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861573830618, "endTime": 652861573852286}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4f292787-2fcc-4c4b-a49f-df6069342c1f", "logId": "3cb7324a-43f7-4c7a-a696-04840c65d783"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3cb7324a-43f7-4c7a-a696-04840c65d783", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861573830618, "endTime": 652861573852286}, "additional": {"logType": "info", "children": [], "durationId": "25ae078a-a39b-4f35-b8ea-996b195665e5", "parent": "9e4e0acb-e2e5-413a-8a0c-a8fd4c452589"}}, {"head": {"id": "cdcd9aed-5cb0-4a72-983c-74f53b5a1d95", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861574118045}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1300a225-6449-42bd-8256-d88a6be0d37f", "name": "Cache service initialization finished in 18 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861592941034}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfe19dbe-58b4-4cf4-9abf-3d1973207640", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861534608611, "endTime": 652861593148891}, "additional": {"logType": "info", "children": [], "durationId": "585cd977-c87b-4a72-9072-39b079adb979", "parent": "9e4e0acb-e2e5-413a-8a0c-a8fd4c452589"}}, {"head": {"id": "9e0d9d90-23dc-44b4-845a-234bc669048b", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861610075824, "endTime": 652861610105129}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1932ce57-5a5e-4f00-bb9e-5a7f20d468d8", "logId": "ab34e8af-dd96-49a1-a450-cbf7efc35f98"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9656a7b7-1285-4953-8966-f8af6d71856c", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861610134779, "endTime": 652861615443904}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1932ce57-5a5e-4f00-bb9e-5a7f20d468d8", "logId": "0d1e46d2-655e-40f9-a296-26f419e3ec4b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a9ad3ad3-8f82-4160-9b07-7cf3267b558c", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861615462413, "endTime": 652861749576672}, "additional": {"children": ["4d8ed8da-306a-4db9-8cca-6da25997328f", "ea088701-9b03-4fbc-9f4a-83946b25342c", "0296c903-3787-408d-81c1-097079bbf473"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1932ce57-5a5e-4f00-bb9e-5a7f20d468d8", "logId": "05f6dd4a-e762-4ab2-8695-7e1bc8596de9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e68a532d-ae25-4749-a13e-dd209cd680a6", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861749595509, "endTime": 652861858294368}, "additional": {"children": ["369ae54a-8ac6-4a31-9dcf-14db33d8bea7", "81fa425a-5b15-44c1-af34-7afcf61761dc"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1932ce57-5a5e-4f00-bb9e-5a7f20d468d8", "logId": "95860eff-7246-43ab-b2a3-0946958da616"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "eaaed2cb-e421-41c1-a2ef-8f75047b763d", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861858323505, "endTime": 652862022986184}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1932ce57-5a5e-4f00-bb9e-5a7f20d468d8", "logId": "0f4d562e-a890-4371-b832-9f9d7303d70b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fc23e379-6ec8-454c-876f-f653a17c24e4", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862023995695, "endTime": 652862032183261}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1932ce57-5a5e-4f00-bb9e-5a7f20d468d8", "logId": "c6f1e038-412e-4b04-844c-36c730f089be"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aa010d6c-d599-43e5-9a3a-576e044dcbb9", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862032208585, "endTime": 652862042355996}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1932ce57-5a5e-4f00-bb9e-5a7f20d468d8", "logId": "1ed66f22-2bbb-449c-9f8c-c2c5e1392964"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2bbebf4a-2ba2-45d1-8b72-d8da8971ef8d", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862042382639, "endTime": 652862042507875}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1932ce57-5a5e-4f00-bb9e-5a7f20d468d8", "logId": "b58a4cff-bd40-4d7a-8763-43ef743c247c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ab34e8af-dd96-49a1-a450-cbf7efc35f98", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861610075824, "endTime": 652861610105129}, "additional": {"logType": "info", "children": [], "durationId": "9e0d9d90-23dc-44b4-845a-234bc669048b", "parent": "4b9bd515-db3e-44ce-81d1-a6405b2087c6"}}, {"head": {"id": "0d1e46d2-655e-40f9-a296-26f419e3ec4b", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861610134779, "endTime": 652861615443904}, "additional": {"logType": "info", "children": [], "durationId": "9656a7b7-1285-4953-8966-f8af6d71856c", "parent": "4b9bd515-db3e-44ce-81d1-a6405b2087c6"}}, {"head": {"id": "4d8ed8da-306a-4db9-8cca-6da25997328f", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861616154157, "endTime": 652861616189856}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a9ad3ad3-8f82-4160-9b07-7cf3267b558c", "logId": "e5bfb6f1-4ade-4db5-9bb2-2ef07b090d6a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e5bfb6f1-4ade-4db5-9bb2-2ef07b090d6a", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861616154157, "endTime": 652861616189856}, "additional": {"logType": "info", "children": [], "durationId": "4d8ed8da-306a-4db9-8cca-6da25997328f", "parent": "05f6dd4a-e762-4ab2-8695-7e1bc8596de9"}}, {"head": {"id": "ea088701-9b03-4fbc-9f4a-83946b25342c", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861619156932, "endTime": 652861749055870}, "additional": {"children": ["8a361165-49ff-49a6-88cb-2a7c091aabdf", "6291f3c6-7523-4835-ade8-367a3508c5a2"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a9ad3ad3-8f82-4160-9b07-7cf3267b558c", "logId": "d02cfd69-b013-4018-a7f3-969f06084f4e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8a361165-49ff-49a6-88cb-2a7c091aabdf", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861619159030, "endTime": 652861643778266}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ea088701-9b03-4fbc-9f4a-83946b25342c", "logId": "28e02108-7790-4800-978d-4080572d0446"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6291f3c6-7523-4835-ade8-367a3508c5a2", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861643836809, "endTime": 652861749039864}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ea088701-9b03-4fbc-9f4a-83946b25342c", "logId": "2a01d4c3-def2-4ca7-af17-e7e93a6e6b6b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "15fa71b1-4f24-4b70-bbdb-f60c092fa2ae", "name": "hvigorfile, resolving /Users/<USER>/github/Ling<PERSON>ia/examples/harmony/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861619167568}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5895199-2a31-4996-bbc3-3a0c4cd62852", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861643436886}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28e02108-7790-4800-978d-4080572d0446", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861619159030, "endTime": 652861643778266}, "additional": {"logType": "info", "children": [], "durationId": "8a361165-49ff-49a6-88cb-2a7c091aabdf", "parent": "d02cfd69-b013-4018-a7f3-969f06084f4e"}}, {"head": {"id": "41958925-f5dc-417b-b550-25f3448487f1", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861643871682}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b5e66bb-9337-4e4f-a2d1-7829bec29d24", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861654538559}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ddbcf61-6670-4a0d-b5b5-80afdad3bcf1", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861654724865}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a98e5fdc-28ff-4b2a-9fed-810632210198", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861655112717}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8818c2ce-6790-4aaf-87a0-71f3b6ed440b", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861662029554}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9e6c48f-1dfd-4e3c-b0d9-2dcfb255f0a6", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861663824564}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8588404f-6cd5-4d94-bc91-0a9ba0ba3952", "name": "not found resModel json file in : /Users/<USER>/github/LingXia/examples/harmony/entry/src/ohosTest/module.json5", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861665366990}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbe68657-0a1c-429e-848e-baa9ac952b8c", "name": "not found resModel json file in : /Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/ohosTest/module.json5", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861671484225}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e100a78f-841c-46f9-9809-f96a12ac4a4a", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861688125749}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d8120fd-1c83-4206-b03c-4939f5f1d4d7", "name": "Sdk init in 54 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861729730367}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cbd503dc-b0b5-4af9-9546-df69f5926a7c", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861729898604}, "additional": {"time": {"year": 2025, "month": 7, "day": 31, "hour": 21, "minute": 44}, "markType": "other"}}, {"head": {"id": "4811c8bf-707a-4e9c-8ef1-7a4b12358a29", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861729932449}, "additional": {"time": {"year": 2025, "month": 7, "day": 31, "hour": 21, "minute": 44}, "markType": "other"}}, {"head": {"id": "2735db18-c139-430d-8a75-00a154d003c0", "name": "Project task initialization takes 18 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861748695323}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10bbee18-2aac-41b6-95bb-d8a95e057c2d", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861748821840}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e6303a0-c335-4b6b-bc13-fa9efdf487d6", "name": "hvigorfile, no custom plugins were found in /Users/<USER>/github/LingXia/examples/harmony/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861748900586}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8cb4004e-6b96-43d1-a7f1-c7b95c7a3d67", "name": "hvigorfile, resolve finished /Users/<USER>/github/Ling<PERSON>ia/examples/harmony/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861748984513}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a01d4c3-def2-4ca7-af17-e7e93a6e6b6b", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861643836809, "endTime": 652861749039864}, "additional": {"logType": "info", "children": [], "durationId": "6291f3c6-7523-4835-ade8-367a3508c5a2", "parent": "d02cfd69-b013-4018-a7f3-969f06084f4e"}}, {"head": {"id": "d02cfd69-b013-4018-a7f3-969f06084f4e", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861619156932, "endTime": 652861749055870}, "additional": {"logType": "info", "children": ["28e02108-7790-4800-978d-4080572d0446", "2a01d4c3-def2-4ca7-af17-e7e93a6e6b6b"], "durationId": "ea088701-9b03-4fbc-9f4a-83946b25342c", "parent": "05f6dd4a-e762-4ab2-8695-7e1bc8596de9"}}, {"head": {"id": "0296c903-3787-408d-81c1-097079bbf473", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861749540353, "endTime": 652861749557463}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "a9ad3ad3-8f82-4160-9b07-7cf3267b558c", "logId": "d8530c8b-85f4-41b2-b319-d6d9b0dfe295"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d8530c8b-85f4-41b2-b319-d6d9b0dfe295", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861749540353, "endTime": 652861749557463}, "additional": {"logType": "info", "children": [], "durationId": "0296c903-3787-408d-81c1-097079bbf473", "parent": "05f6dd4a-e762-4ab2-8695-7e1bc8596de9"}}, {"head": {"id": "05f6dd4a-e762-4ab2-8695-7e1bc8596de9", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861615462413, "endTime": 652861749576672}, "additional": {"logType": "info", "children": ["e5bfb6f1-4ade-4db5-9bb2-2ef07b090d6a", "d02cfd69-b013-4018-a7f3-969f06084f4e", "d8530c8b-85f4-41b2-b319-d6d9b0dfe295"], "durationId": "a9ad3ad3-8f82-4160-9b07-7cf3267b558c", "parent": "4b9bd515-db3e-44ce-81d1-a6405b2087c6"}}, {"head": {"id": "369ae54a-8ac6-4a31-9dcf-14db33d8bea7", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861750023799, "endTime": 652861775221269}, "additional": {"children": ["d7796210-6757-4158-a18b-974fe1acb435", "fd13c843-f812-4936-bc76-b20e8c9fe306", "0f6a7eb4-8798-48a7-8b65-4df8d448b982"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e68a532d-ae25-4749-a13e-dd209cd680a6", "logId": "098a5d26-a177-4545-b42f-d7479a148fff"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d7796210-6757-4158-a18b-974fe1acb435", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861751908906, "endTime": 652861751923909}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "369ae54a-8ac6-4a31-9dcf-14db33d8bea7", "logId": "40434f75-ed90-4813-901d-7da4fd7f8809"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "40434f75-ed90-4813-901d-7da4fd7f8809", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861751908906, "endTime": 652861751923909}, "additional": {"logType": "info", "children": [], "durationId": "d7796210-6757-4158-a18b-974fe1acb435", "parent": "098a5d26-a177-4545-b42f-d7479a148fff"}}, {"head": {"id": "fd13c843-f812-4936-bc76-b20e8c9fe306", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861753062184, "endTime": 652861773823085}, "additional": {"children": ["736d4e43-ab94-4907-8e73-a6996d94e40d", "d9b701e1-a5c7-414b-803e-d5e61f33da1e"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "369ae54a-8ac6-4a31-9dcf-14db33d8bea7", "logId": "bd3c0450-8c41-4cb2-b806-52bd778a1429"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "736d4e43-ab94-4907-8e73-a6996d94e40d", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861753063141, "endTime": 652861759462803}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fd13c843-f812-4936-bc76-b20e8c9fe306", "logId": "52b81a18-3b83-4632-a540-45f3cbf7c4a0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d9b701e1-a5c7-414b-803e-d5e61f33da1e", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861759481608, "endTime": 652861773807766}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "fd13c843-f812-4936-bc76-b20e8c9fe306", "logId": "aa8c0685-43ec-417d-a276-81b23ae2175d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0f682bd2-2040-48a2-864e-4de09da44157", "name": "hvigorfile, resolving /Users/<USER>/github/Ling<PERSON>ia/examples/harmony/entry/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861753067706}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40f8df92-e0a1-49cf-8134-db99b3486c2b", "name": "hvigorfile, require result:  { default: { system: [Function: hapTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861759303608}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52b81a18-3b83-4632-a540-45f3cbf7c4a0", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861753063141, "endTime": 652861759462803}, "additional": {"logType": "info", "children": [], "durationId": "736d4e43-ab94-4907-8e73-a6996d94e40d", "parent": "bd3c0450-8c41-4cb2-b806-52bd778a1429"}}, {"head": {"id": "a709f7dd-5381-4115-9e1e-a9ca23edccdf", "name": "hvigorfile, binding system plugins [Function: hapTasks]", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861759495189}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f12157f-b05f-4356-b5b0-90cc3e8aa445", "name": "Start initialize module-target build option map, moduleName=entry, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861766122468}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b4c8239-c953-4f01-b0ba-0767cdf66982", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861766527972}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88ac2cbc-2c83-4585-8179-f0457b5b5d49", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861767975436}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "32ed6160-d39a-47fa-bbf0-5550ea12a037", "name": "Module 'entry' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861768174011}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b80088c3-3a01-450e-b036-d749a149d597", "name": "Module 'entry' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"default\"\n}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861768247057}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b246ebf-f4ea-4dbf-bb5f-cf8293e40b47", "name": "End initialize module-target build option map, moduleName=entry", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861768313436}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9bfe18b3-a3c2-4860-9027-d91ef13f5cf1", "name": "Module 'entry' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  },\n  \"name\": \"debug\"\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861768429231}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97210084-30fe-41e0-b98d-d6585314a19d", "name": "Module entry task initialization takes 3 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861773413308}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d60e811d-1039-4a3f-98ab-d85bc4adeba9", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861773620369}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1af0b9c7-8d29-4e8c-b192-7659fdabe8da", "name": "hvigorfile, no custom plugins were found in /Users/<USER>/github/LingXia/examples/harmony/entry/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861773697579}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c9596f4-b194-474e-b9d2-e66214b8c79c", "name": "hvigorfile, resolve finished /Users/<USER>/github/LingXia/examples/harmony/entry/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861773748114}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa8c0685-43ec-417d-a276-81b23ae2175d", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861759481608, "endTime": 652861773807766}, "additional": {"logType": "info", "children": [], "durationId": "d9b701e1-a5c7-414b-803e-d5e61f33da1e", "parent": "bd3c0450-8c41-4cb2-b806-52bd778a1429"}}, {"head": {"id": "bd3c0450-8c41-4cb2-b806-52bd778a1429", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861753062184, "endTime": 652861773823085}, "additional": {"logType": "info", "children": ["52b81a18-3b83-4632-a540-45f3cbf7c4a0", "aa8c0685-43ec-417d-a276-81b23ae2175d"], "durationId": "fd13c843-f812-4936-bc76-b20e8c9fe306", "parent": "098a5d26-a177-4545-b42f-d7479a148fff"}}, {"head": {"id": "0f6a7eb4-8798-48a7-8b65-4df8d448b982", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861775019613, "endTime": 652861775192225}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "369ae54a-8ac6-4a31-9dcf-14db33d8bea7", "logId": "f8d38cad-fd30-44f7-8e2f-c4c385f7cd99"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f8d38cad-fd30-44f7-8e2f-c4c385f7cd99", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861775019613, "endTime": 652861775192225}, "additional": {"logType": "info", "children": [], "durationId": "0f6a7eb4-8798-48a7-8b65-4df8d448b982", "parent": "098a5d26-a177-4545-b42f-d7479a148fff"}}, {"head": {"id": "098a5d26-a177-4545-b42f-d7479a148fff", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861750023799, "endTime": 652861775221269}, "additional": {"logType": "info", "children": ["40434f75-ed90-4813-901d-7da4fd7f8809", "bd3c0450-8c41-4cb2-b806-52bd778a1429", "f8d38cad-fd30-44f7-8e2f-c4c385f7cd99"], "durationId": "369ae54a-8ac6-4a31-9dcf-14db33d8bea7", "parent": "95860eff-7246-43ab-b2a3-0946958da616"}}, {"head": {"id": "81fa425a-5b15-44c1-af34-7afcf61761dc", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861775674488, "endTime": 652861858234857}, "additional": {"children": ["0e6c236f-4131-496f-b79f-4e2494b9495a", "1f69e9c1-bf2a-4fe9-b40e-fe33a68d4502", "7f97e429-258a-4947-952f-acb61b8016f5"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e68a532d-ae25-4749-a13e-dd209cd680a6", "logId": "e23f4805-6847-491d-8020-4be895be0bf9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0e6c236f-4131-496f-b79f-4e2494b9495a", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861779167555, "endTime": 652861779189913}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "81fa425a-5b15-44c1-af34-7afcf61761dc", "logId": "97bbc7cd-8202-47ea-8426-c8033a3292b6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "97bbc7cd-8202-47ea-8426-c8033a3292b6", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861779167555, "endTime": 652861779189913}, "additional": {"logType": "info", "children": [], "durationId": "0e6c236f-4131-496f-b79f-4e2494b9495a", "parent": "e23f4805-6847-491d-8020-4be895be0bf9"}}, {"head": {"id": "1f69e9c1-bf2a-4fe9-b40e-fe33a68d4502", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861780693496, "endTime": 652861853204283}, "additional": {"children": ["c96285f6-4bbe-4fa1-b7fb-def12c50768b", "abd7cfb9-bd5c-40bc-92cc-ecb0dfebbf13"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "81fa425a-5b15-44c1-af34-7afcf61761dc", "logId": "c5948545-8c77-48d7-9e8e-a007fe0ef341"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c96285f6-4bbe-4fa1-b7fb-def12c50768b", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861780694829, "endTime": 652861784419287}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1f69e9c1-bf2a-4fe9-b40e-fe33a68d4502", "logId": "d4506388-81f5-4d26-860d-e77264cc0ed9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "abd7cfb9-bd5c-40bc-92cc-ecb0dfebbf13", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861784457197, "endTime": 652861853174231}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1f69e9c1-bf2a-4fe9-b40e-fe33a68d4502", "logId": "7c675801-531e-4b60-9ed6-ac96841cff0a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "862fd8a1-99ac-4929-a165-52079163f99c", "name": "hvigorfile, resolving /Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861780702275}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29506119-c0bd-48dd-b083-2fb3e6a7ca35", "name": "hvigorfile, require result:  { default: { system: [Function: harTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861784273963}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4506388-81f5-4d26-860d-e77264cc0ed9", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861780694829, "endTime": 652861784419287}, "additional": {"logType": "info", "children": [], "durationId": "c96285f6-4bbe-4fa1-b7fb-def12c50768b", "parent": "c5948545-8c77-48d7-9e8e-a007fe0ef341"}}, {"head": {"id": "dac94eea-d4cd-4ab1-92e7-248c9f965354", "name": "hvigorfile, binding system plugins [Function: harTasks]", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861784470526}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "461104ee-af0d-4516-a93c-bf81bdf4ea2e", "name": "Start initialize module-target build option map, moduleName=lingxia, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861819995463}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a282293-7753-478e-bd4f-134536ec96ff", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861820356007}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b30511b0-18ef-4716-b009-22b999f0ff03", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861823233241}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "386cb03d-d67c-4255-84e5-499538beee07", "name": "Module 'lingxia' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861824287090}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c1f90873-8ba4-4cd7-954f-cbfae7fc6155", "name": "Module 'lingxia' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861825608039}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "452df120-7cdf-4382-ae8d-f7a7fe2f787c", "name": "End initialize module-target build option map, moduleName=lingxia", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861827342542}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a89de047-4206-4378-9f69-b86d7d99115d", "name": "Module 'lingxia' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861829569736}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27a7cb93-7dce-4694-9374-5e26d65581a5", "name": "Module lingxia task initialization takes 5 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861850454981}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "348e7c8a-45fb-4e49-bcf5-e74e3f34242c", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861851586334}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "beb9fd41-429c-461b-8805-462a49fed1e7", "name": "hvigorfile, no custom plugins were found in /Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861851759847}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8aef826-ef50-4eb2-a4de-ff7138a1d53a", "name": "hvigorfile, resolve finished /Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861851860993}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c675801-531e-4b60-9ed6-ac96841cff0a", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861784457197, "endTime": 652861853174231}, "additional": {"logType": "info", "children": [], "durationId": "abd7cfb9-bd5c-40bc-92cc-ecb0dfebbf13", "parent": "c5948545-8c77-48d7-9e8e-a007fe0ef341"}}, {"head": {"id": "c5948545-8c77-48d7-9e8e-a007fe0ef341", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861780693496, "endTime": 652861853204283}, "additional": {"logType": "info", "children": ["d4506388-81f5-4d26-860d-e77264cc0ed9", "7c675801-531e-4b60-9ed6-ac96841cff0a"], "durationId": "1f69e9c1-bf2a-4fe9-b40e-fe33a68d4502", "parent": "e23f4805-6847-491d-8020-4be895be0bf9"}}, {"head": {"id": "7f97e429-258a-4947-952f-acb61b8016f5", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861857666926, "endTime": 652861857694764}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "81fa425a-5b15-44c1-af34-7afcf61761dc", "logId": "dc38c402-93b3-4e6d-90c9-e99658d26141"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dc38c402-93b3-4e6d-90c9-e99658d26141", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861857666926, "endTime": 652861857694764}, "additional": {"logType": "info", "children": [], "durationId": "7f97e429-258a-4947-952f-acb61b8016f5", "parent": "e23f4805-6847-491d-8020-4be895be0bf9"}}, {"head": {"id": "e23f4805-6847-491d-8020-4be895be0bf9", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861775674488, "endTime": 652861858234857}, "additional": {"logType": "info", "children": ["97bbc7cd-8202-47ea-8426-c8033a3292b6", "c5948545-8c77-48d7-9e8e-a007fe0ef341", "dc38c402-93b3-4e6d-90c9-e99658d26141"], "durationId": "81fa425a-5b15-44c1-af34-7afcf61761dc", "parent": "95860eff-7246-43ab-b2a3-0946958da616"}}, {"head": {"id": "95860eff-7246-43ab-b2a3-0946958da616", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861749595509, "endTime": 652861858294368}, "additional": {"logType": "info", "children": ["098a5d26-a177-4545-b42f-d7479a148fff", "e23f4805-6847-491d-8020-4be895be0bf9"], "durationId": "e68a532d-ae25-4749-a13e-dd209cd680a6", "parent": "4b9bd515-db3e-44ce-81d1-a6405b2087c6"}}, {"head": {"id": "fc30e416-802d-4f7b-8e3a-76cebff11df3", "name": "watch files: [\n  '/Users/<USER>/github/LingXia/examples/harmony/hvigorfile.ts',\n  '/Users/<USER>/github/LingXia/examples/harmony/entry/hvigorfile.ts',\n  '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/hvigorfile.ts',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor-ohos-plugin/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor-ohos-plugin/src/plugin/factory/plugin-factory.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/log4js.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/debug/src/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/debug/src/node.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/debug/src/common.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/ms/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/rfdc/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/configuration.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/layouts.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/date-format/lib/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/levels.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/clustering.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/LoggingEvent.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/flatted/cjs/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/adapters.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/console.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/stdout.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/stderr.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/logLevelFilter.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/categoryFilter.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/noLogFilter.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/file.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/lib/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/lib/RollingFileWriteStream.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/fs/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/universalify/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/graceful-fs/graceful-fs.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/graceful-fs/polyfills.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/graceful-fs/legacy-streams.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/graceful-fs/clone.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/copy-sync/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/copy-sync/copy-sync.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/mkdirs/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/mkdirs/mkdirs.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/mkdirs/win32.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/mkdirs/mkdirs-sync.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/util/utimes.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/util/stat.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/copy/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/copy/copy.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/path-exists/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/empty/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/remove/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/remove/rimraf.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/file.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/link.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/symlink.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/symlink-paths.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/symlink-type.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/json/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/json/jsonfile.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/jsonfile/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/json/output-json.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/json/output-json-sync.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/move-sync/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/move-sync/move-sync.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/move/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/move/move.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/output/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/lib/now.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/lib/fileNameFormatter.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/lib/fileNameParser.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/lib/moveAndMaybeCompressFile.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/lib/RollingFileStream.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/lib/DateRollingFileStream.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/dateFile.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/fileSync.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/tcp.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/categories.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/logger.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/connect-logger.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/recording.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/src/common/util/local-file-writer.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/fs/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/universalify/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/copy/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/copy/copy.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/mkdirs/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/mkdirs/make-dir.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/mkdirs/utils.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/path-exists/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/util/utimes.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/util/stat.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/copy/copy-sync.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/empty/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/remove/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/ensure/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/ensure/file.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/ensure/link.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/ensure/symlink.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/ensure/symlink-paths.js',\n  ... 1869 more items\n]", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861924668929}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e679f8df-4bd6-4ff6-9345-7d32f8d877ad", "name": "hvigorfile, resolve hvigorfile dependencies in 165 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862022823463}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f4d562e-a890-4371-b832-9f9d7303d70b", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861858323505, "endTime": 652862022986184}, "additional": {"logType": "info", "children": [], "durationId": "eaaed2cb-e421-41c1-a2ef-8f75047b763d", "parent": "4b9bd515-db3e-44ce-81d1-a6405b2087c6"}}, {"head": {"id": "9d679ba1-1a10-4f54-a37d-34f64f237acc", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862023629841, "endTime": 652862023980803}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "1932ce57-5a5e-4f00-bb9e-5a7f20d468d8", "logId": "9b254a1d-3cc6-4c88-8117-36ac35d1ab09"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a81846e4-0624-4281-8e2c-9faffb17affc", "name": "project has submodules:entry,lingxia", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862023664599}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4f07873-1584-44ce-ab81-dca1912216de", "name": "module:lingxia no need to execute packageHap", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862023889058}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b254a1d-3cc6-4c88-8117-36ac35d1ab09", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862023629841, "endTime": 652862023980803}, "additional": {"logType": "info", "children": [], "durationId": "9d679ba1-1a10-4f54-a37d-34f64f237acc", "parent": "4b9bd515-db3e-44ce-81d1-a6405b2087c6"}}, {"head": {"id": "5df4e742-821f-40ed-9056-c04fafeba5af", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862025020908}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7663e55a-d211-4cd4-9c8d-63fbcbc5d2f5", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862031708042}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6f1e038-412e-4b04-844c-36c730f089be", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862023995695, "endTime": 652862032183261}, "additional": {"logType": "info", "children": [], "durationId": "fc23e379-6ec8-454c-876f-f653a17c24e4", "parent": "4b9bd515-db3e-44ce-81d1-a6405b2087c6"}}, {"head": {"id": "bfd28709-ee7d-4b6d-86df-6dd2a250887c", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862032229382}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc32c9b2-e0b0-4a2f-b656-5c71c01d7420", "name": "Module harmony Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862034024941}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11bdfd65-b15a-4610-a5cf-0a5dffea761a", "name": "Module harmony's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862034137327}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3194766d-d33e-4034-ad1f-d7f421d8abfd", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862034661950}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f7e598a-2f01-4d68-9018-ba55e4c9bdc6", "name": "Module entry Collected Dependency: /Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862035777556}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "86dff4b2-76f2-4a89-af5f-4eaa8947423f", "name": "Module entry's total dependency: 1", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862035884126}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf021bd0-f7cd-43a2-b517-e2b264f65b68", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862039701528}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4b6b86d-a858-463d-aa42-f325ba12531a", "name": "Module ling<PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862040238885}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ac4159e-b431-4416-96b7-fa6d7f0ca1ed", "name": "Module lingxia's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862040322836}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ed66f22-2bbb-449c-9f8c-c2c5e1392964", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862032208585, "endTime": 652862042355996}, "additional": {"logType": "info", "children": [], "durationId": "aa010d6c-d599-43e5-9a3a-576e044dcbb9", "parent": "4b9bd515-db3e-44ce-81d1-a6405b2087c6"}}, {"head": {"id": "868ad44d-0334-4698-bf80-f4627ce4dc60", "name": "Configuration phase cost:433 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862042413093}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b58a4cff-bd40-4d7a-8763-43ef743c247c", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862042382639, "endTime": 652862042507875}, "additional": {"logType": "info", "children": [], "durationId": "2bbebf4a-2ba2-45d1-8b72-d8da8971ef8d", "parent": "4b9bd515-db3e-44ce-81d1-a6405b2087c6"}}, {"head": {"id": "4b9bd515-db3e-44ce-81d1-a6405b2087c6", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861593191121, "endTime": 652862042522755}, "additional": {"logType": "info", "children": ["ab34e8af-dd96-49a1-a450-cbf7efc35f98", "0d1e46d2-655e-40f9-a296-26f419e3ec4b", "05f6dd4a-e762-4ab2-8695-7e1bc8596de9", "95860eff-7246-43ab-b2a3-0946958da616", "0f4d562e-a890-4371-b832-9f9d7303d70b", "c6f1e038-412e-4b04-844c-36c730f089be", "1ed66f22-2bbb-449c-9f8c-c2c5e1392964", "b58a4cff-bd40-4d7a-8763-43ef743c247c", "9b254a1d-3cc6-4c88-8117-36ac35d1ab09"], "durationId": "1932ce57-5a5e-4f00-bb9e-5a7f20d468d8", "parent": "9e4e0acb-e2e5-413a-8a0c-a8fd4c452589"}}, {"head": {"id": "107bf2dd-9bc1-4263-a9db-2062e76cda15", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862045731989, "endTime": 652862045788538}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "4f292787-2fcc-4c4b-a49f-df6069342c1f", "logId": "3f3620f6-d0da-48d7-a8fa-45846469641f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3f3620f6-d0da-48d7-a8fa-45846469641f", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862045731989, "endTime": 652862045788538}, "additional": {"logType": "info", "children": [], "durationId": "107bf2dd-9bc1-4263-a9db-2062e76cda15", "parent": "9e4e0acb-e2e5-413a-8a0c-a8fd4c452589"}}, {"head": {"id": "2634f943-e8ed-45e7-92f5-d7b0bddb2140", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862042557199, "endTime": 652862045821153}, "additional": {"logType": "info", "children": [], "durationId": "f44b2099-f559-4cf9-9354-8dd042a98f02", "parent": "9e4e0acb-e2e5-413a-8a0c-a8fd4c452589"}}, {"head": {"id": "23963865-1f78-4a5d-87a6-e4db3ef463ab", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862045831023, "endTime": 652862045896655}, "additional": {"logType": "info", "children": [], "durationId": "53b60920-30d3-49fc-b07c-49ba0bf24465", "parent": "9e4e0acb-e2e5-413a-8a0c-a8fd4c452589"}}, {"head": {"id": "9e4e0acb-e2e5-413a-8a0c-a8fd4c452589", "name": "init", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861534594515, "endTime": 652862045920731}, "additional": {"logType": "info", "children": ["bfe19dbe-58b4-4cf4-9abf-3d1973207640", "4b9bd515-db3e-44ce-81d1-a6405b2087c6", "2634f943-e8ed-45e7-92f5-d7b0bddb2140", "23963865-1f78-4a5d-87a6-e4db3ef463ab", "e1ad6379-ca26-4bd8-a24b-3352bafb501d", "3cb7324a-43f7-4c7a-a696-04840c65d783", "3f3620f6-d0da-48d7-a8fa-45846469641f"], "durationId": "4f292787-2fcc-4c4b-a49f-df6069342c1f"}}, {"head": {"id": "d42538c9-e3f6-4703-b39b-9c5e6c3b1932", "name": "Configuration task cost before running: 520 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862046391158}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9b6a5df1-b0c2-4806-9bf6-3762f3845da1", "name": "entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862050499563, "endTime": 652862056289174}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "e16ac208-6cf3-49d1-a6c5-43e921df5a2e", "logId": "e386eb0f-5b58-48f8-a047-c1ce381246ca"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e16ac208-6cf3-49d1-a6c5-43e921df5a2e", "name": "create entry:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862047913875}, "additional": {"logType": "detail", "children": [], "durationId": "9b6a5df1-b0c2-4806-9bf6-3762f3845da1"}}, {"head": {"id": "cc7d5ee5-2e5e-43b6-bc9a-3f126833c174", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862048244960}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "985a40c2-ad8b-4bdd-a438-3cb8181a7ccf", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862048378293}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3d879f91-a5e5-48d6-b882-709972d372ad", "name": "Executing task :entry:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862050540208}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5df71aa8-7e04-4d58-829e-6c8d5f7dcc02", "name": "Incremental task entry:default@PreBuild pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862055783522}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ade3d99-9164-4b1b-babb-3fd8de04cf91", "name": "entry : default@PreBuild cost memory 0.24739837646484375", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862056112373}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e386eb0f-5b58-48f8-a047-c1ce381246ca", "name": "UP-TO-DATE :entry:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862050499563, "endTime": 652862056289174}, "additional": {"logType": "info", "children": [], "durationId": "9b6a5df1-b0c2-4806-9bf6-3762f3845da1"}}, {"head": {"id": "7156f42e-d340-4e4e-8f3c-6fb4e7252ce5", "name": "lingxia:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862060361214, "endTime": 652862063915602}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "lingxia", "category": "Verification", "taskRunReasons": [], "detailId": "0c745e11-e544-4632-b81b-68ed9c4cd611", "logId": "405f8fec-d461-4f26-a40d-ab7177bf95bc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0c745e11-e544-4632-b81b-68ed9c4cd611", "name": "create lingxia:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862058438245}, "additional": {"logType": "detail", "children": [], "durationId": "7156f42e-d340-4e4e-8f3c-6fb4e7252ce5"}}, {"head": {"id": "032110bc-6db7-44d7-aa64-6157ba9eb435", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862058642068}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b18aea41-19e7-490f-bada-9f50483b2fae", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862058756752}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "efa64b49-d08d-4681-b554-7f658af2df22", "name": "Executing task :lingxia:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862060379667}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23335ad5-e26f-46dd-86fa-4ef599acdaae", "name": "Incremental task lingxia:default@PreBuild pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862063578949}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "30d51024-68a2-4ec6-bb47-199c782ae01a", "name": "lingxia : default@PreBuild cost memory 0.15641021728515625", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862063779937}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "405f8fec-d461-4f26-a40d-ab7177bf95bc", "name": "UP-TO-DATE :lingxia:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862060361214, "endTime": 652862063915602}, "additional": {"logType": "info", "children": [], "durationId": "7156f42e-d340-4e4e-8f3c-6fb4e7252ce5"}}, {"head": {"id": "659a7223-9c7c-4ab7-9491-29f86c0063d7", "name": "entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862066896705, "endTime": 652862068356536}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "4fa4c093-1712-4e6f-82cd-c4e9c92bc15f", "logId": "eb8cbfb2-17fc-4cf9-b281-a14fc0b2c34d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4fa4c093-1712-4e6f-82cd-c4e9c92bc15f", "name": "create entry:default@CreateModuleInfo task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862066012332}, "additional": {"logType": "detail", "children": [], "durationId": "659a7223-9c7c-4ab7-9491-29f86c0063d7"}}, {"head": {"id": "0917f5a1-8429-4739-8b87-d6cc2e03d039", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862066287791}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e071506d-b279-45f2-ac00-1330f92c4c51", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862066423408}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94f36cbc-a2ee-4cef-95df-f057ce26dcad", "name": "Executing task :entry:default@CreateModuleInfo", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862066907977}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e610aade-74f0-4d56-a82b-d88f5fea80e0", "name": "Task 'entry:default@CreateModuleInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862067539843}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8f1aae69-cf02-4ce3-9328-9eba6319ca16", "name": "entry : default@CreateModuleInfo cost memory 0.0511627197265625", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862068135672}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c6b4b22-f078-4940-8d5f-65cbe6baedc5", "name": "runTaskFromQueue task cost before running: 542 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862068279924}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb8cbfb2-17fc-4cf9-b281-a14fc0b2c34d", "name": "Finished :entry:default@CreateModuleInfo", "description": "Create the ModuleInfo.ts file for the hap/hsp package.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862066896705, "endTime": 652862068356536, "totalTime": 1352398}, "additional": {"logType": "info", "children": [], "durationId": "659a7223-9c7c-4ab7-9491-29f86c0063d7"}}, {"head": {"id": "95815463-286d-41ba-a4fc-6c5e26d572ad", "name": "entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862073997652, "endTime": 652862075813488}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "abe23fd7-ca97-4203-9313-17bb3266bc48", "logId": "ff9d8482-d73d-487a-b9e5-97aea9fd16d9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "abe23fd7-ca97-4203-9313-17bb3266bc48", "name": "create entry:default@GenerateMetadata task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862071136669}, "additional": {"logType": "detail", "children": [], "durationId": "95815463-286d-41ba-a4fc-6c5e26d572ad"}}, {"head": {"id": "daf21eaa-c0ea-4b21-b41c-163f52a73b50", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862071683739}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a15c9a1-8352-4774-9454-388044b4b516", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862071865173}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f3437e59-995d-4330-a6ef-b2ddc499e7a7", "name": "Executing task :entry:default@GenerateMetadata", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862074016266}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6fdefc09-312a-4404-95a9-04babb1b1c7b", "name": "Task 'entry:default@GenerateMetadata' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862074913200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "858b5ca2-17a5-41c5-9e9e-f888a6221573", "name": "Incremental task entry:default@GenerateMetadata pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862075553595}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dcd7c8a1-ebde-4dd6-b533-d3c6441888a7", "name": "entry : default@GenerateMetadata cost memory 0.08650970458984375", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862075700907}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff9d8482-d73d-487a-b9e5-97aea9fd16d9", "name": "UP-TO-DATE :entry:default@GenerateMetadata", "description": "Generate metadata in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862073997652, "endTime": 652862075813488}, "additional": {"logType": "info", "children": [], "durationId": "95815463-286d-41ba-a4fc-6c5e26d572ad"}}, {"head": {"id": "f88749ea-1c76-4afc-9e80-137bdf51ec49", "name": "entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862079506340, "endTime": 652862080231111}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Verification", "taskRunReasons": [], "detailId": "aeddc94b-c038-4658-bf30-be6cefb9bbde", "logId": "2a1c63fe-dcac-4db2-b7f5-37db522eb097"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aeddc94b-c038-4658-bf30-be6cefb9bbde", "name": "create entry:default@PreCheckSyscap task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862077786908}, "additional": {"logType": "detail", "children": [], "durationId": "f88749ea-1c76-4afc-9e80-137bdf51ec49"}}, {"head": {"id": "0fe2fec8-809f-4fb2-a362-c7904956a5c5", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862078280798}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ea25269-24a9-4b41-9b91-6738438d2cf1", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862078546190}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0d62675-b056-4ba6-9aec-6b2d4aa3372c", "name": "Executing task :entry:default@PreCheckSyscap", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862079521847}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47d47c48-0a50-438b-8c5b-e5c25727c0b8", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862079754577}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2347ec4c-9c94-4df7-b91d-ab51823076a5", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862079845211}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3211a30f-9282-4c91-9994-4dc053a929e2", "name": "entry : default@PreCheckSyscap cost memory 0.0393524169921875", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862079977288}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4cdfa89-f103-4a99-9194-8e60ac59ef42", "name": "runTaskFromQueue task cost before running: 554 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862080130095}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a1c63fe-dcac-4db2-b7f5-37db522eb097", "name": "Finished :entry:default@PreCheckSyscap", "description": "Pre-check SysCap.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862079506340, "endTime": 652862080231111, "totalTime": 598935}, "additional": {"logType": "info", "children": [], "durationId": "f88749ea-1c76-4afc-9e80-137bdf51ec49"}}, {"head": {"id": "dccd896d-0597-43a0-a0b0-9e5605e465ae", "name": "entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862086688944, "endTime": 652862087958187}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "ee0267af-5e2e-4c2c-bfc6-019084c77ae5", "logId": "070afd87-2ffe-4a21-8cde-8db3e5b11757"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ee0267af-5e2e-4c2c-bfc6-019084c77ae5", "name": "create entry:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862081798144}, "additional": {"logType": "detail", "children": [], "durationId": "dccd896d-0597-43a0-a0b0-9e5605e465ae"}}, {"head": {"id": "6e240124-093e-46dc-b7c8-d939e9bf3dc7", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862082077152}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2659df17-e332-4414-bcee-9d54df41e6cf", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862082207340}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "506fca08-8721-4b6b-8415-756c5ab8bd69", "name": "Executing task :entry:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862086720966}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36955c36-58e7-4fbf-aa2a-1e2fac2109a9", "name": "Task 'entry:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862087101075}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c98a6c85-d409-46f8-b2c9-2da7b6277bf0", "name": "Incremental task entry:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862087691159}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e5379bb-3673-46c5-8718-67f1c934eec8", "name": "entry : default@GeneratePkgContextInfo cost memory 0.14778900146484375", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862087834641}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "070afd87-2ffe-4a21-8cde-8db3e5b11757", "name": "UP-TO-DATE :entry:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862086688944, "endTime": 652862087958187}, "additional": {"logType": "info", "children": [], "durationId": "dccd896d-0597-43a0-a0b0-9e5605e465ae"}}, {"head": {"id": "223e05dd-ba02-4f48-aeac-8e78287d7391", "name": "entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862092055218, "endTime": 652862094454930}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The output file '/Users/<USER>/github/LingXia/examples/harmony/build/cache/default/integrated_hsp/integratedHspCache.json' does not exist."], "detailId": "282a9295-fd0c-436d-a20d-1ebf33f17972", "logId": "b2cc6d31-d805-45a0-bac5-1aaa088f96df"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "282a9295-fd0c-436d-a20d-1ebf33f17972", "name": "create entry:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862090182146}, "additional": {"logType": "detail", "children": [], "durationId": "223e05dd-ba02-4f48-aeac-8e78287d7391"}}, {"head": {"id": "9e56cd7f-2e92-4f5f-ad1c-778c3296789e", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862090471156}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a3229928-ec7a-41ef-a6f4-8111508afba6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862090631781}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "53431b54-531f-49a7-94c5-dc3e8f2f062f", "name": "Executing task :entry:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862092067833}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "584303b5-d44a-497b-a059-dc0aa1174228", "name": "entry:default@ProcessIntegratedHsp is not up-to-date, since the output file '/Users/<USER>/github/LingXia/examples/harmony/build/cache/default/integrated_hsp/integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862093346117}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "761ba983-cebd-4e35-bc0d-e6d4c7ac4ad5", "name": "Incremental task entry:default@ProcessIntegratedHsp pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862093505708}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd0dcf94-3c54-4099-96b5-33d56f744c1a", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862093656957}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58e5f6ec-ede6-4c01-ac70-650bfb81e120", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862093742684}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0fb9dfee-7219-4131-9aba-9334d691fde9", "name": "entry : default@ProcessIntegratedHsp cost memory 0.10968780517578125", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862093983388}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25eb0651-df03-4cf2-8d38-14348bf5a689", "name": "runTaskFromQueue task cost before running: 568 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862094252770}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2cc6d31-d805-45a0-bac5-1aaa088f96df", "name": "Finished :entry:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862092055218, "endTime": 652862094454930, "totalTime": 2062578}, "additional": {"logType": "info", "children": [], "durationId": "223e05dd-ba02-4f48-aeac-8e78287d7391"}}, {"head": {"id": "fb0f7f15-ff75-4ab7-8382-3ee75ca89e1a", "name": "lingxia:default@CreateHarBuildProfile", "description": "Create the BuildProfile.ets file for the HAR package.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862098114747, "endTime": 652862100872190}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "lingxia", "category": "Generate", "taskRunReasons": ["The output file '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/BuildProfile.ets' has been changed."], "detailId": "035e0b99-2c2d-41b2-870c-c642b98d9ecd", "logId": "974c5d39-ba6a-4a88-b6b4-b5b3abbab5ac"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "035e0b99-2c2d-41b2-870c-c642b98d9ecd", "name": "create lingxia:default@CreateHarBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862096921731}, "additional": {"logType": "detail", "children": [], "durationId": "fb0f7f15-ff75-4ab7-8382-3ee75ca89e1a"}}, {"head": {"id": "29a55e83-43d7-45c7-84ab-899aef297c46", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862097172722}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99180d49-1790-4a1c-a433-a77490be57d1", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862097294459}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d6c39435-8ba3-47d3-8498-cad0bd6f648c", "name": "Executing task :lingxia:default@CreateHarBuildProfile", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862098137413}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d748de11-4795-43b4-b9fe-b84767c3510c", "name": "Task 'lingxia:default@CreateHarBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862098363705}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8cfaf631-ebb6-48ff-a619-1e293385eb2b", "name": "lingxia:default@CreateHarBuildProfile is not up-to-date, since the output file '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/BuildProfile.ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862098819621}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbca7b2a-6227-480e-b2ac-983afd4ac4c9", "name": "Incremental task lingxia:default@CreateHarBuildProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862098995903}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2703f5ef-e104-497f-8ba0-907f647ddf91", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862099227935}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "776b6b01-dd12-4e1c-8a68-f0eab7e0890a", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862099313169}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28645427-0846-4c07-aa6d-3ef4e31e9374", "name": "lingxia : default@CreateHarBuildProfile cost memory 0.10170745849609375", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862099929797}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd468476-aa2b-4c9f-8489-4129d1066b8c", "name": "runTaskFromQueue task cost before running: 574 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862100739504}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "974c5d39-ba6a-4a88-b6b4-b5b3abbab5ac", "name": "Finished :lingxia:default@CreateHarBuildProfile", "description": "Create the BuildProfile.ets file for the HAR package.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862098114747, "endTime": 652862100872190, "totalTime": 2584769}, "additional": {"logType": "info", "children": [], "durationId": "fb0f7f15-ff75-4ab7-8382-3ee75ca89e1a"}}, {"head": {"id": "0d95ecbb-a345-47a8-ab27-58ac1897b065", "name": "lingxia:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862103283705, "endTime": 652862103995199}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "lingxia", "category": "Native", "taskRunReasons": [], "detailId": "7e8026f4-5e19-4c35-a7aa-28edca232f13", "logId": "751a7803-f2be-47d2-9d9a-89a7a0840302"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7e8026f4-5e19-4c35-a7aa-28edca232f13", "name": "create lingxia:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862102684612}, "additional": {"logType": "detail", "children": [], "durationId": "0d95ecbb-a345-47a8-ab27-58ac1897b065"}}, {"head": {"id": "d7a6e6a2-ec81-4560-91ce-3ce38d15dcfc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862102950371}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0e33b4a-1147-4ef4-8f38-8b0d9d253e43", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862103056037}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "378f079f-caa9-4519-a05d-52f7f814c4f2", "name": "Executing task :lingxia:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862103298470}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c327a585-9d29-4c15-a0f1-05d445d34b9d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862103494425}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "441a74b2-1451-4e59-a35f-c81d2aca84df", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862103628332}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d18012b-f758-42b3-a430-142f368f4be1", "name": "lingxia : default@ConfigureCmake cost memory 0.0369720458984375", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862103773275}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38a8e78e-837c-4fd4-91a5-31e973cd787e", "name": "runTaskFromQueue task cost before running: 577 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862103914333}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "751a7803-f2be-47d2-9d9a-89a7a0840302", "name": "Finished :lingxia:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862103283705, "endTime": 652862103995199, "totalTime": 601797}, "additional": {"logType": "info", "children": [], "durationId": "0d95ecbb-a345-47a8-ab27-58ac1897b065"}}, {"head": {"id": "09db66ab-31bc-4ae7-af08-132a9f297cef", "name": "lingxia:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862108333689, "endTime": 652862199335112}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "lingxia", "category": "Config", "taskRunReasons": ["The output file '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/merge_profile/default/module.json' has been changed."], "detailId": "2635cd72-30be-4509-87ed-e942c3d1e439", "logId": "1a5e6105-c073-476d-8cf5-53ff72113009"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2635cd72-30be-4509-87ed-e942c3d1e439", "name": "create lingxia:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862107140335}, "additional": {"logType": "detail", "children": [], "durationId": "09db66ab-31bc-4ae7-af08-132a9f297cef"}}, {"head": {"id": "a2bae7bc-a287-4cdd-89ce-0cd69fb02304", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862107604930}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e601e1b0-85f3-481e-9f67-a04c85aa8375", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862107750864}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58f5f455-30ae-48f6-9e24-fab288127021", "name": "Executing task :lingxia:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862108356607}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "516a2305-1535-49c5-94af-5d84ea0cdafb", "name": "lingxia:default@MergeProfile is not up-to-date, since the output file '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/merge_profile/default/module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862109024060}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ecfd58a-c682-4f1b-9a9e-de7f68c19160", "name": "Incremental task lingxia:default@MergeProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862109162673}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8e0b6ef-3646-4ef2-9f75-bf64b772416b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862109280958}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48282106-d6fc-4694-856b-c1d91325a165", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862109359374}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef5ba057-f7d2-4b1a-8177-88f60ca0ead4", "name": "Change app api release type with 'Release'", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862109567635}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a63fbbf4-27ff-4eab-ac65-3925227d2574", "name": "Change app compile API version with '*********'", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862109787330}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23538f53-5fcb-46d6-afe0-876c83cf92b1", "name": "Change app target API version with '50005017'", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862109945555}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ee14ef02-00fc-4d4a-af7d-fd735218b7a6", "name": "Change app minimum API version with '50005017'", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862110019074}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64189107-1b28-4903-9923-728ac18c588f", "name": "Use cli appEnvironment", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862110088528}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b76b9496-c81f-457c-b536-d0ac88dc17dd", "name": "Clean the ArkTS cache due to bundleName is changed.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862111875129}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47dbcc90-2711-4142-bfd2-65bca349516d", "name": "lingxia : default@MergeProfile cost memory -2.7298431396484375", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862198946486}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14938866-6ed2-4f72-b940-98159cd76979", "name": "runTaskFromQueue task cost before running: 673 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862199204647}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a5e6105-c073-476d-8cf5-53ff72113009", "name": "Finished :lingxia:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862108333689, "endTime": 652862199335112, "totalTime": 90825762}, "additional": {"logType": "info", "children": [], "durationId": "09db66ab-31bc-4ae7-af08-132a9f297cef"}}, {"head": {"id": "2457037c-d59d-4647-9707-c0ad25d3d5b4", "name": "entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862206455450, "endTime": 652862211515837}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": ["The output file '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/syscap/default/rpcid.sc' does not exist."], "detailId": "e2515b7f-f09f-4fe4-bbc8-3435231485a9", "logId": "e32b743a-7bb0-4ac2-8c0a-260e00a69e8f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e2515b7f-f09f-4fe4-bbc8-3435231485a9", "name": "create entry:default@SyscapTransform task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862203348671}, "additional": {"logType": "detail", "children": [], "durationId": "2457037c-d59d-4647-9707-c0ad25d3d5b4"}}, {"head": {"id": "1dd4e3db-01a1-43ba-8abd-9902962f62dd", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862203947200}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc53c866-83dc-4c26-8591-85caccb0f296", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862204121979}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5ba2e85-095a-4818-9a1f-f6c518ea3ff4", "name": "Executing task :entry:default@SyscapTransform", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862206481015}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6405964e-f6cc-4a3d-9fe8-5e53edefcbc2", "name": "File: '/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/syscap.json' from 'sysCapJsonPath' is not exists, just ignore.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862206919343}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "747e77d4-938d-467c-9ee7-893085e74669", "name": "Task 'entry:default@SyscapTransform' cost while wrapping incremental declarations: 2 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862207988011}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd5ab916-384a-4eea-8ad5-32d6e1a0df95", "name": "entry:default@SyscapTransform is not up-to-date, since the output file '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/syscap/default/rpcid.sc' does not exist.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862209118168}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "051f117f-d4b7-44c8-9142-99117df41dc6", "name": "Incremental task entry:default@SyscapTransform pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862209730080}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4970e5fb-4096-4c2b-91f1-cf5164730c42", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862209922510}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3773670c-43f4-47c8-89fb-904a1ec3658a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862210027793}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e9c45a8-883c-4655-9c23-3bfd6cd6a56b", "name": "entry : default@SyscapTransform cost memory 0.128082275390625", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862210179495}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ecee00c-a3db-44cd-8f32-2107bd4a1b35", "name": "runTaskFromQueue task cost before running: 685 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862211304365}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e32b743a-7bb0-4ac2-8c0a-260e00a69e8f", "name": "Finished :entry:default@SyscapTransform", "description": "Transform SysCap in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862206455450, "endTime": 652862211515837, "totalTime": 4687223}, "additional": {"logType": "info", "children": [], "durationId": "2457037c-d59d-4647-9707-c0ad25d3d5b4"}}, {"head": {"id": "4a5fd769-f97f-4634-972a-c323aa42e2a8", "name": "entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862218552478, "endTime": 652862223442301}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "8eedeb9a-8ca7-4acd-b872-c7f7fb262353", "logId": "19191964-a161-4c27-87b4-fc032a8f3b70"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8eedeb9a-8ca7-4acd-b872-c7f7fb262353", "name": "create entry:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862214926742}, "additional": {"logType": "detail", "children": [], "durationId": "4a5fd769-f97f-4634-972a-c323aa42e2a8"}}, {"head": {"id": "8e5ffbb6-b8db-420f-a249-4a99e586280f", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862215363762}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc457b20-71a0-4c7a-8f19-84e6e4b1a345", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862215540134}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf53f751-bcc5-4fd3-85d6-7678194298c1", "name": "Executing task :entry:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862218574804}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed454962-49a6-4189-8acb-bed0fb53fe5c", "name": "Incremental task entry:default@ProcessRouterMap pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862223167398}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c8c1acce-8503-4bb3-b4c9-63370eaae5b4", "name": "entry : default@ProcessRouterMap cost memory 0.18018341064453125", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862223355158}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "19191964-a161-4c27-87b4-fc032a8f3b70", "name": "UP-TO-DATE :entry:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862218552478, "endTime": 652862223442301}, "additional": {"logType": "info", "children": [], "durationId": "4a5fd769-f97f-4634-972a-c323aa42e2a8"}}, {"head": {"id": "8558e573-9947-47df-aa6d-9ae030cf6f1f", "name": "entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862227046657, "endTime": 652862229243362}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "378d0fa5-4d15-4357-b180-b11567615367", "logId": "47f92c1b-33c7-4c31-802e-8e3632beffd5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "378d0fa5-4d15-4357-b180-b11567615367", "name": "create entry:default@CreateBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862225752962}, "additional": {"logType": "detail", "children": [], "durationId": "8558e573-9947-47df-aa6d-9ae030cf6f1f"}}, {"head": {"id": "86c02f7e-e917-4787-a74b-c4c2a289183c", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862226029329}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90776a1d-5b03-4cbd-8240-f92a66cbdd3b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862226138852}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de855a29-b24a-42be-8e6e-d46f591b0068", "name": "Executing task :entry:default@CreateBuildProfile", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862227070750}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09000395-52e0-4614-989e-7c94c8daa331", "name": "Task 'entry:default@CreateBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862228092046}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec50ee58-d7e0-4762-9dba-38ad82e38e32", "name": "Incremental task entry:default@CreateBuildProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862228916317}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b981a961-96b7-4099-aef9-eaf294d92b88", "name": "entry : default@CreateBuildProfile cost memory 0.08814239501953125", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862229135084}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47f92c1b-33c7-4c31-802e-8e3632beffd5", "name": "UP-TO-DATE :entry:default@CreateBuildProfile", "description": "Create the BuildProfile.ets file for the HAP/HSP package.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862227046657, "endTime": 652862229243362}, "additional": {"logType": "info", "children": [], "durationId": "8558e573-9947-47df-aa6d-9ae030cf6f1f"}}, {"head": {"id": "7207e91f-f753-4bff-820d-f30dccb27dc8", "name": "lingxia:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862231954455, "endTime": 652862232585928}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "lingxia", "category": "Native", "taskRunReasons": [], "detailId": "e25cb3a3-972f-4426-b944-f9f440756c9e", "logId": "ffa04313-e187-4b28-9620-ad217aa8ab04"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e25cb3a3-972f-4426-b944-f9f440756c9e", "name": "create lingxia:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862230843936}, "additional": {"logType": "detail", "children": [], "durationId": "7207e91f-f753-4bff-820d-f30dccb27dc8"}}, {"head": {"id": "02316c8f-6a4f-485a-866a-88733907a746", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862231160538}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dbf4f6ff-1c5c-49c5-a58f-679fe997a67d", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862231268193}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e56e8a31-05f6-4571-90c9-af99fe18458e", "name": "Executing task :lingxia:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862231977609}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd3bc2ad-bf32-4654-8b44-4678fb2c102b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862232173206}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f538962-d734-46c3-bb4d-7b6d71c497a2", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862232294424}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8e37fe7d-a2e4-4acc-a656-4bafeb258bc1", "name": "lingxia : default@BuildNativeWithCmake cost memory 0.036407470703125", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862232402216}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db9514cc-09cc-431c-8002-7fc7c65c6677", "name": "runTaskFromQueue task cost before running: 706 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862232523062}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ffa04313-e187-4b28-9620-ad217aa8ab04", "name": "Finished :lingxia:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862231954455, "endTime": 652862232585928, "totalTime": 544364}, "additional": {"logType": "info", "children": [], "durationId": "7207e91f-f753-4bff-820d-f30dccb27dc8"}}, {"head": {"id": "1a040843-adee-42e1-b495-f8fb284ac048", "name": "entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862235154491, "endTime": 652862241701597}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": ["The input file '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/merge_profile/default/module.json' has been changed."], "detailId": "de95a09b-853c-4b69-8643-e57ca31c9a08", "logId": "7c8c3249-9e41-423b-b926-f6dc62a0658e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "de95a09b-853c-4b69-8643-e57ca31c9a08", "name": "create entry:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862234187038}, "additional": {"logType": "detail", "children": [], "durationId": "1a040843-adee-42e1-b495-f8fb284ac048"}}, {"head": {"id": "f3c1649e-823b-4631-b41a-68660f8fc177", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862234522813}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb6cf8c6-ee9d-4cc6-ba10-169a667f298b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862234636833}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31ee333a-590d-413e-ae8e-88b94fbe3751", "name": "Executing task :entry:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862235167022}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06dc9caa-c7bd-4055-ae28-e74f3795a66d", "name": "entry:default@MergeProfile is not up-to-date, since the input file '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/merge_profile/default/module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862236314062}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c21f04cb-ebb5-4788-881b-9fa6765af36f", "name": "Incremental task entry:default@MergeProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862236437623}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1bc585e-05da-4fd3-88ec-dbc8227d7e22", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862236527209}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "09b8f4ed-6405-47b0-bdff-2e723e1b02d3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862236599860}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cff6bed5-2b49-4370-a3a6-3bccdcd4707e", "name": "Change app api release type with 'Release'", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862237051301}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16a44d4a-75d8-4dfd-92f3-648f0c29cbc7", "name": "Change app compile API version with '*********'", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862237202276}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e22020bd-9c92-49de-8eef-ca79fb74f262", "name": "Change app target API version with '50005017'", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862237273066}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "616b25af-0f19-4773-936e-5ff8ab94060f", "name": "Change app minimum API version with '50005017'", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862237325611}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ac7c1d6-64fa-4370-9e6a-0ec9fa6a4196", "name": "Use cli appEnvironment", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862237379482}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c12fe30b-6998-4b9a-afa7-565846edb55b", "name": "entry : default@MergeProfile cost memory 0.27605438232421875", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862241357373}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8eeee2e4-7cbe-4ea8-9475-0d9e950f091b", "name": "runTaskFromQueue task cost before running: 715 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862241548848}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c8c3249-9e41-423b-b926-f6dc62a0658e", "name": "Finished :entry:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862235154491, "endTime": 652862241701597, "totalTime": 6367505}, "additional": {"logType": "info", "children": [], "durationId": "1a040843-adee-42e1-b495-f8fb284ac048"}}, {"head": {"id": "424be293-0761-4e1a-a596-67bc085a500d", "name": "entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862250705186, "endTime": 652862273203111}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "344717c3-a22c-4e11-a477-1a0fd6acd021", "logId": "38c594f5-f119-467e-9a5f-830d7f83c711"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "344717c3-a22c-4e11-a477-1a0fd6acd021", "name": "create entry:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862245780247}, "additional": {"logType": "detail", "children": [], "durationId": "424be293-0761-4e1a-a596-67bc085a500d"}}, {"head": {"id": "82ed2a0c-0b07-406a-bf93-0fc3c91b22c0", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862246323844}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad45f353-adc2-45ba-bc24-bd545fa0c8c0", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862246506615}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "735419bb-5b9f-4262-94a8-c1ada464838b", "name": "Module 'lingxia' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862247622309}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62eb481e-0e74-4527-959d-fc5c282c12b6", "name": "Executing task :entry:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862250726484}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ab3a14b-5ee4-4b8e-abf7-54e92a415b82", "name": "Module 'lingxia' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862252640569}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31d099b4-5b18-458b-a43b-5c1748578bb0", "name": "Module 'lingxia' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862257632471}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f486fcf3-bbc2-45a0-817b-787668dffe4a", "name": "Module 'lingxia' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862263267650}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb2a4db5-b3f2-484f-b5a0-25c8f4a90fa7", "name": "Module 'lingxia' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862266674986}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0e33f2e-7942-4f19-9512-ec9abdf33c58", "name": "Module 'lingxia' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862269972619}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8336a5fa-a106-4942-a365-3042f8052ead", "name": "Incremental task entry:default@GenerateLoaderJson pre-execution cost: 12 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862271840957}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b99530b2-b040-4665-bc6f-579e7c14e1bb", "name": "entry : default@GenerateLoaderJson cost memory 0.55902099609375", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862272109740}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38c594f5-f119-467e-9a5f-830d7f83c711", "name": "UP-TO-DATE :entry:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862250705186, "endTime": 652862273203111}, "additional": {"logType": "info", "children": [], "durationId": "424be293-0761-4e1a-a596-67bc085a500d"}}, {"head": {"id": "f7d9ebd1-d662-4799-8b75-07f0e201d38d", "name": "Module 'lingxia' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862292229197}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ecef12a-fa9f-4535-ba3e-1a03119c4c04", "name": "Module 'lingxia' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862297101252}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "777f3210-7a8a-4652-8dc5-35a3aeb62dca", "name": "entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862302384427, "endTime": 652862304060099}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "51d4cd98-db44-48d3-8aa8-0a2697e49c1c", "logId": "f523cd47-8035-450f-9a32-1619baded9c8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "51d4cd98-db44-48d3-8aa8-0a2697e49c1c", "name": "create entry:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862300116677}, "additional": {"logType": "detail", "children": [], "durationId": "777f3210-7a8a-4652-8dc5-35a3aeb62dca"}}, {"head": {"id": "307b7b6a-dd4c-4f28-9da0-bf7b4366c8ba", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862300560923}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0984c5a-4855-469e-9ce3-cf7a51367cfb", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862301257732}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d01e0ef-8c33-4a46-b087-fef499e70dbc", "name": "Executing task :entry:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862302453060}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb1f33a2-6c1f-4db0-bbd8-ce14260acb63", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862303317866}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e06c6ee2-d61b-492d-bb98-02f7ed24817e", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862303656987}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db9b5a5f-3e19-4aa5-8acc-1ef78c71da5e", "name": "entry : default@ConfigureCmake cost memory 0.041259765625", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862303823978}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2e86c080-bc94-4ae9-ab50-3131f8bcef7a", "name": "runTaskFromQueue task cost before running: 778 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862303974962}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f523cd47-8035-450f-9a32-1619baded9c8", "name": "Finished :entry:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862302384427, "endTime": 652862304060099, "totalTime": 1991303}, "additional": {"logType": "info", "children": [], "durationId": "777f3210-7a8a-4652-8dc5-35a3aeb62dca"}}, {"head": {"id": "812a6eef-871f-4424-8942-91ba68be0c87", "name": "lingxia:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862310816561, "endTime": 652862313569382}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "lingxia", "category": "Native", "taskRunReasons": [], "detailId": "40959008-4081-4c37-b55c-d110338cea90", "logId": "e35f2c65-c5b0-454a-977d-95860a6cf977"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "40959008-4081-4c37-b55c-d110338cea90", "name": "create lingxia:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862307908894}, "additional": {"logType": "detail", "children": [], "durationId": "812a6eef-871f-4424-8942-91ba68be0c87"}}, {"head": {"id": "298371ff-4126-4450-98da-f01dd64c9c0d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862308759489}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d36d446-8fed-49a9-9881-ee5116b338a2", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862308953600}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8efdd6e4-6d4b-4df7-a467-cf69e1f599f9", "name": "Executing task :lingxia:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862310852253}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89063a9c-1de8-4046-ad0f-3b8dda9cca04", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862311212170}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62648d70-3e9a-41f1-9b69-eceb7720f3cb", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862311444502}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17e80ccf-ecf1-42d0-85a0-18365819e1c2", "name": "lingxia : default@BuildNativeWithNinja cost memory 0.05123138427734375", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862313257625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad3bb270-8fd1-4767-aa01-1872da81647a", "name": "runTaskFromQueue task cost before running: 787 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862313469587}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e35f2c65-c5b0-454a-977d-95860a6cf977", "name": "Finished :lingxia:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862310816561, "endTime": 652862313569382, "totalTime": 2624195}, "additional": {"logType": "info", "children": [], "durationId": "812a6eef-871f-4424-8942-91ba68be0c87"}}, {"head": {"id": "23e03cfe-cbaa-4f60-a365-160bb3be517c", "name": "entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862318076113, "endTime": 652862324674353}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "73078b1a-e996-45c6-a859-945a37e8e09f", "logId": "b34ca569-0ac8-4cd3-b9f6-7edb16560f56"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "73078b1a-e996-45c6-a859-945a37e8e09f", "name": "create entry:default@MakePackInfo task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862316319884}, "additional": {"logType": "detail", "children": [], "durationId": "23e03cfe-cbaa-4f60-a365-160bb3be517c"}}, {"head": {"id": "c503103b-048b-4203-9c44-30b816699131", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862316765602}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "195702a1-e1a8-4810-80ed-091af4483407", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862316921631}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47a8a1ba-d649-4689-bbf4-4055ce76c572", "name": "Executing task :entry:default@MakePackInfo", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862318093962}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d91ff06b-38ec-4c8b-8fef-f08bfe235e07", "name": "Incremental task entry:default@MakePackInfo pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862323478687}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0208f127-1dae-440b-85f2-301f72adc7b8", "name": "entry : default@MakePackInfo cost memory 0.19678497314453125", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862324496377}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b34ca569-0ac8-4cd3-b9f6-7edb16560f56", "name": "UP-TO-DATE :entry:default@MakePackInfo", "description": "Generate module pack.info in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862318076113, "endTime": 652862324674353}, "additional": {"logType": "info", "children": [], "durationId": "23e03cfe-cbaa-4f60-a365-160bb3be517c"}}, {"head": {"id": "90631a1a-d10c-4724-8d85-8b11f95a7570", "name": "entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862331475267, "endTime": 652862524849018}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Config", "taskRunReasons": ["The input file '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/merge_profile/default/module.json' has been changed."], "detailId": "2bdb457f-fa54-4c1d-86b8-bbdd1929c5e5", "logId": "5645d842-1eff-4e41-b620-85f47bc71932"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2bdb457f-fa54-4c1d-86b8-bbdd1929c5e5", "name": "create entry:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862328825384}, "additional": {"logType": "detail", "children": [], "durationId": "90631a1a-d10c-4724-8d85-8b11f95a7570"}}, {"head": {"id": "17b3a489-ffa7-4a85-a4cd-3204c918c6f4", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862329214815}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4842b14b-9923-4460-a388-5cb91af9cdaf", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862329558679}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab95fe13-0453-4be7-bf02-b0005614d24f", "name": "Executing task :entry:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862331496288}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7b65a55b-cf23-45b6-94f0-5521cef6a458", "name": "entry:default@ProcessProfile is not up-to-date, since the input file '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/merge_profile/default/module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862332434235}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ed3b713-d2fb-4cb4-bea2-1041cc612a11", "name": "Incremental task entry:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862332594289}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57d8580f-cedf-44eb-a804-bfbf3d5eece4", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862332705678}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "acd19c65-86f4-4d90-8991-9874eb3d4da3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862332770164}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ebc5c7e-8fd6-4df6-bc8c-8345a86d70cf", "name": "********", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862522974044}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa091328-0bb4-422f-b29d-0d44ae245223", "name": "entry : default@ProcessProfile cost memory 0.328399658203125", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862524613614}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d549f445-7f82-46e5-ba75-41f811f54aab", "name": "runTaskFromQueue task cost before running: 998 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862524778892}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5645d842-1eff-4e41-b620-85f47bc71932", "name": "Finished :entry:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862331475267, "endTime": 652862524849018, "totalTime": 193277422}, "additional": {"logType": "info", "children": [], "durationId": "90631a1a-d10c-4724-8d85-8b11f95a7570"}}, {"head": {"id": "cf51bc72-9d0d-4730-a8ad-de0d4284ded2", "name": "entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862528483388, "endTime": 652862529894209}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "95ace657-15b6-40f3-8429-0cd7af62c7af", "logId": "04342d46-94f0-4d4b-8ba0-998602c51dd1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "95ace657-15b6-40f3-8429-0cd7af62c7af", "name": "create entry:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862526256259}, "additional": {"logType": "detail", "children": [], "durationId": "cf51bc72-9d0d-4730-a8ad-de0d4284ded2"}}, {"head": {"id": "7b9250e5-7364-41c3-9ada-0d9c92bc1b0a", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862526559113}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5969afa4-1386-41ed-87a3-99e81d34d571", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862526667971}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48e46ec6-292d-4772-b895-ca7e41872345", "name": "Executing task :entry:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862528922769}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a9270e5-abfe-427a-8703-57fd492b896a", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862529537312}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c632c2ca-f725-4b39-b3c0-503a9fa0b9e3", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862529610185}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a5726466-f7a8-447c-bc50-6136fd99d3d7", "name": "entry : default@BuildNativeWithCmake cost memory 0.04018402099609375", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862529720283}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a1e8ae3-91df-460e-a0e1-207c1bde0e7f", "name": "runTaskFromQueue task cost before running: 1 s 3 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862529830401}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04342d46-94f0-4d4b-8ba0-998602c51dd1", "name": "Finished :entry:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862528483388, "endTime": 652862529894209, "totalTime": 1317769}, "additional": {"logType": "info", "children": [], "durationId": "cf51bc72-9d0d-4730-a8ad-de0d4284ded2"}}, {"head": {"id": "5e100a76-14e6-4d49-8472-519aab0a8330", "name": "lingxia:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862532529586, "endTime": 652862535016365}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "lingxia", "category": "Resources", "taskRunReasons": [], "detailId": "8c98eb36-88e1-4702-a36b-cc8dd26fc7f6", "logId": "12bd7877-a0bc-4b1c-a579-3646ee2de2cc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8c98eb36-88e1-4702-a36b-cc8dd26fc7f6", "name": "create lingxia:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862531356240}, "additional": {"logType": "detail", "children": [], "durationId": "5e100a76-14e6-4d49-8472-519aab0a8330"}}, {"head": {"id": "c0cf6b93-34a8-45ad-a482-8a313ebadd3e", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862531630693}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4a92fcfe-060e-4971-b69f-a842af0b743e", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862531731322}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6c0927f8-cca1-478d-a85e-27f7c19c5e6b", "name": "Executing task :lingxia:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862532543772}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "753f447b-8098-4878-b9f3-ac62df860665", "name": "Incremental task lingxia:default@ProcessLibs pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862534620531}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e6b6f8c-4252-4dbc-b84c-deab4891e553", "name": "lingxia : default@ProcessLibs cost memory 0.10193634033203125", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862534872085}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12bd7877-a0bc-4b1c-a579-3646ee2de2cc", "name": "UP-TO-DATE :lingxia:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862532529586, "endTime": 652862535016365}, "additional": {"logType": "info", "children": [], "durationId": "5e100a76-14e6-4d49-8472-519aab0a8330"}}, {"head": {"id": "783791df-f288-42e1-af74-a659a9d65c70", "name": "entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862542470182, "endTime": 652862549735767}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": [], "detailId": "149ee552-32ae-4e17-b9e9-c605d80b2813", "logId": "6a75fae9-e104-4259-b831-c62a9b850155"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "149ee552-32ae-4e17-b9e9-c605d80b2813", "name": "create entry:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862538214187}, "additional": {"logType": "detail", "children": [], "durationId": "783791df-f288-42e1-af74-a659a9d65c70"}}, {"head": {"id": "8a8514a6-8876-457a-aac4-e99bd20b8902", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862538743611}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47f396ed-636c-4449-b145-50479f97cbae", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862538913683}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "38d43279-80ca-4353-a66a-984cbe9290f9", "name": "restool module names: entry,lingxia; moduleName=entry, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862540516164}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "537977b5-42cd-4846-9b52-497c152d9f9d", "name": "Executing task :entry:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862546206039}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b412e3b-5332-4f63-aa06-52c8149a5759", "name": "Incremental task entry:default@ProcessResource pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862548151868}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "439eb491-87b2-41e4-899a-10cb20bb3d88", "name": "entry : default@ProcessResource cost memory 0.1371612548828125", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862548343799}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a75fae9-e104-4259-b831-c62a9b850155", "name": "UP-TO-DATE :entry:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862542470182, "endTime": 652862549735767}, "additional": {"logType": "info", "children": [], "durationId": "783791df-f288-42e1-af74-a659a9d65c70"}}, {"head": {"id": "437b13be-47c1-40c2-8a81-c6266d0f2df3", "name": "lingxia:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862557574462, "endTime": 652862558686236}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "lingxia", "category": "Native", "taskRunReasons": [], "detailId": "09ef444b-c14c-4c49-88c1-cf4c14369cf7", "logId": "cf6029ab-914e-460d-997f-3717e2242ace"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "09ef444b-c14c-4c49-88c1-cf4c14369cf7", "name": "create lingxia:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862554027043}, "additional": {"logType": "detail", "children": [], "durationId": "437b13be-47c1-40c2-8a81-c6266d0f2df3"}}, {"head": {"id": "1165a15e-434d-4170-a3c0-b92bcdc85c3a", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862554412422}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78d47f93-fab8-4e6d-8dc8-07e88e88de17", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862554891485}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f6970031-e3da-4c71-a17d-6af9fa3d3541", "name": "Executing task :lingxia:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862557593244}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2542be90-b777-4024-b212-4ae97d921031", "name": "Task 'lingxia:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862557835351}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f99fe82b-f75c-4e4f-846b-1a5d468c4f37", "name": "Incremental task lingxia:default@DoNativeStrip pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862558504693}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f1d4325-e5f0-4f13-abd3-bd69361bb8e4", "name": "lingxia : default@DoNativeStrip cost memory 0.06273651123046875", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862558617218}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cf6029ab-914e-460d-997f-3717e2242ace", "name": "UP-TO-DATE :lingxia:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862557574462, "endTime": 652862558686236}, "additional": {"logType": "info", "children": [], "durationId": "437b13be-47c1-40c2-8a81-c6266d0f2df3"}}, {"head": {"id": "64460d57-67a8-45d6-9eb4-cc24cde85c39", "name": "entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862565986687, "endTime": 652862939541500}, "additional": {"children": ["c888c730-1aa3-4342-8fa7-712250af8559", "5d6036e9-ee57-4442-b416-49899e66278c"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": ["The input file '/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources' has been changed."], "detailId": "ac4784ce-897a-48e7-8289-0e17df101106", "logId": "fd608810-4ca7-43da-bf02-3ca52cf4412a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ac4784ce-897a-48e7-8289-0e17df101106", "name": "create entry:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862562131357}, "additional": {"logType": "detail", "children": [], "durationId": "64460d57-67a8-45d6-9eb4-cc24cde85c39"}}, {"head": {"id": "d790eb12-dc79-4bd1-bc54-23dc99cf1846", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862562592863}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c02d5c11-c86e-4ae2-b6ef-d041f648162d", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862562714951}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7fb5f367-a5ec-4636-9053-c1c1f6770967", "name": "restool module names: entry,lingxia; moduleName=entry, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862563445511}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3009884-f644-4591-a39f-2019b9edf310", "name": "Executing task :entry:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862566030444}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "291fae1d-b909-43f3-ac34-8687b402a2da", "name": "entry:default@CompileResource is not up-to-date, since the input file '/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources' has been changed.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862571179970}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a4eff33d-d6a5-4d22-bfd7-97810187569d", "name": "Incremental task entry:default@CompileResource pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862571342980}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c888c730-1aa3-4342-8fa7-712250af8559", "name": "create intermediate resource category", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862572669359, "endTime": 652862573762858}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "64460d57-67a8-45d6-9eb4-cc24cde85c39", "logId": "c8eb7162-3436-4d78-9e73-11cac3d16c46"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c8eb7162-3436-4d78-9e73-11cac3d16c46", "name": "create intermediate resource category", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862572669359, "endTime": 652862573762858}, "additional": {"logType": "info", "children": [], "durationId": "c888c730-1aa3-4342-8fa7-712250af8559", "parent": "fd608810-4ca7-43da-bf02-3ca52cf4412a"}}, {"head": {"id": "c13e7c09-cda2-4bf8-a221-695179d2de4b", "name": "Use tool [/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/toolchains/restool]\n [\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/toolchains/restool',\n  '-l',\n  '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resConfig.json'\n]", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862574453373}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d6036e9-ee57-4442-b416-49899e66278c", "name": "execute compile resource command using restool", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862575354819, "endTime": 652862938367310}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "64460d57-67a8-45d6-9eb4-cc24cde85c39", "logId": "8d5358ac-16c5-423b-961c-d90fe5638a12"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a4ec9492-6d5b-4254-9c41-21581b64effa", "name": "current process  memoryUsage: {\n  rss: 244297728,\n  heapTotal: 146227200,\n  heapUsed: 121055656,\n  external: 3176927,\n  arrayBuffers: 1188401\n} os memoryUsage :15.122241973876953", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862577080334}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7fe53197-2d69-476a-8e1a-43ed4c8b2305", "name": "Info: thread pool is started\n", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862778492248}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "054974bb-beea-40ca-be41-7608050adc2a", "name": "Info: GenericCompiler::CompileFiles\n", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862780827459}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "41d42fb9-a3ac-442a-a21a-c5779b253585", "name": "Info: thread pool is started\n", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862781104629}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc530c19-db10-4e15-8873-8dce4b41a18f", "name": "Info: thread pool is stopped\n", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862785902526}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d290f79d-2595-4a18-aa50-25e25da49f75", "name": "Info: GenericCompiler::CompileFiles\n", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862788915567}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f62f8098-7756-4d75-884a-5b756c73e6ee", "name": "Info: thread pool is started\n", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862789729219}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39b1bef0-6f74-4d09-abba-cedaaa3f3a99", "name": "Info: thread pool is stopped\n", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862791299579}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e40a348e-a873-4bbb-892c-786fa8c6d1b8", "name": "Info: GenericCompiler::CompileFiles\n", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862791587752}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db853b36-e0e5-461b-afb6-de80c197c6d8", "name": "Info: thread pool is started\n", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862791851175}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "935816df-6010-4104-8d9f-f5e5c0e51cfd", "name": "Info: thread pool is stopped\n", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862793244858}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5e61d842-c3bc-4f21-989c-55ad9fd51944", "name": "07-31 21:44:34.453 61672 8767961 E C01400/ImageTranscoderUtils: DecodeToRGBA IN\n", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862797904702}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ceccaf88-4510-4f71-a37f-f84fc3bf8027", "name": "07-31 21:44:34.454 61672 8767961 E C01400/ImageTranscoderUtils: ImageFilter IN /Users/<USER>/github/LingXia/examples/harmony/AppScope/resources/base/media/background.png\n", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862798222443}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c4fb8570-ea97-4557-bc79-65bbc8fca292", "name": "07-31 21:44:34.533 61672 8767961 E C01400/ImageTranscoderUtils: DecodeToRGBA IN\n07-31 21:44:34.533 61672 8767961 E C01400/ImageTranscoderUtils: ImageFilter IN /Users/<USER>/github/LingXia/examples/harmony/AppScope/resources/base/media/foreground.png\n", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862877014250}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0aee78e-64a2-4767-a722-bfabaf7929e9", "name": "Info: can't scale media json file.\n", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862930434339}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95e4a539-721a-4174-8e31-fdb7a42bff0f", "name": "Warning: /Users/<USER>/github/LingXia/examples/harmony/AppScope/resources/base/media/layered_image.json is not png format\n", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862930686103}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "985e2eb1-fc32-4fb2-b899-dbfe1f7099b2", "name": "Info: thread pool is stopped\n", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862931744355}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "51596b7e-e33e-4c75-8503-366a69ff8868", "name": "Info: restool resources compile success.\n", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862931995204}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85c708f3-5c65-4a7b-b3ac-566b252a3fa6", "name": "astcenc customized so is not be opened when dlclose!\n", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862934981574}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d5358ac-16c5-423b-961c-d90fe5638a12", "name": "execute compile resource command using restool", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862575354819, "endTime": 652862938367310}, "additional": {"logType": "info", "children": [], "durationId": "5d6036e9-ee57-4442-b416-49899e66278c", "parent": "fd608810-4ca7-43da-bf02-3ca52cf4412a"}}, {"head": {"id": "f76ad0de-3beb-4560-9b40-d935a83aa704", "name": "entry : default@CompileResource cost memory 0.9079132080078125", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862939211699}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88700769-d02a-45d4-aa28-5e401fd4dcaa", "name": "runTaskFromQueue task cost before running: 1 s 413 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862939407308}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd608810-4ca7-43da-bf02-3ca52cf4412a", "name": "Finished :entry:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862565986687, "endTime": 652862939541500, "totalTime": 373360171}, "additional": {"logType": "info", "children": ["c8eb7162-3436-4d78-9e73-11cac3d16c46", "8d5358ac-16c5-423b-961c-d90fe5638a12"], "durationId": "64460d57-67a8-45d6-9eb4-cc24cde85c39"}}, {"head": {"id": "f94e7da9-2c62-4e7f-81ec-ac1203f0132c", "name": "entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862943910008, "endTime": 652862946188495}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": [], "detailId": "28b30506-97bd-4aed-a60e-5566ae3fcd95", "logId": "88b8f6ce-d537-49b6-9a99-0d96f8783a5d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "28b30506-97bd-4aed-a60e-5566ae3fcd95", "name": "create entry:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862942007746}, "additional": {"logType": "detail", "children": [], "durationId": "f94e7da9-2c62-4e7f-81ec-ac1203f0132c"}}, {"head": {"id": "c400d67b-ce65-4c05-add0-0847f9ae14aa", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862942382246}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "14118ae2-0e21-4518-875a-30fed99f8ff7", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862942541506}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "330538d1-cfc7-4637-887d-dce310d90fee", "name": "Executing task :entry:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862943929858}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1394e32c-a5f6-438c-a07b-512d810cfe7f", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862944151092}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "07bae564-1b5f-4702-9cac-9a6b26ace7cd", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862944271865}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f12b2bd-2173-4a0e-9550-db9f039685bf", "name": "entry : default@BuildNativeWithNinja cost memory 0.05472564697265625", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862945821825}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29183c10-bba6-44f7-8ab1-56aa4da18954", "name": "runTaskFromQueue task cost before running: 1 s 420 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862946048088}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88b8f6ce-d537-49b6-9a99-0d96f8783a5d", "name": "Finished :entry:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862943910008, "endTime": 652862946188495, "totalTime": 2093419}, "additional": {"logType": "info", "children": [], "durationId": "f94e7da9-2c62-4e7f-81ec-ac1203f0132c"}}, {"head": {"id": "12bd2bf0-dd3f-4a86-a899-a50d233aeea3", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862953800447, "endTime": 652864919774913}, "additional": {"children": ["bd66c6ae-a733-4c64-be64-c8ef1fb8b5eb", "85d7a9dd-9b0b-4a22-8c3d-a9df9aeae40d"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": ["The input file '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile' has been changed."], "detailId": "29e6e6e1-5cbd-4fce-a04d-a98c4546ceaf", "logId": "9fc4146b-9e13-405c-852f-28e531f4a91e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "29e6e6e1-5cbd-4fce-a04d-a98c4546ceaf", "name": "create entry:default@CompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862949404418}, "additional": {"logType": "detail", "children": [], "durationId": "12bd2bf0-dd3f-4a86-a899-a50d233aeea3"}}, {"head": {"id": "5d1c1b91-932c-4101-8923-a737f0ae11ce", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862949895412}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4016706d-6ff5-4534-a046-f20626416271", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862950114867}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "254b1de6-9b2a-44cd-a516-a47313afdf9d", "name": "Executing task :entry:default@CompileArkTS", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862953818690}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1a82bca9-3717-4bd0-9749-8af6ac799766", "name": "entry:default@CompileArkTS is not up-to-date, since the input file '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile' has been changed.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862964718783}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae093fc1-d769-44d8-a2b3-d42b0f4c35f8", "name": "Incremental task entry:default@CompileArkTS pre-execution cost: 7 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862964876372}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2607bb8f-f6aa-4d75-a6d1-9a01ef1707e8", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862977249087}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e755bdd-d00e-4018-b863-5ce424c4c5b7", "name": "Compile arkts with external api path: /Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/hms/ets", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862978330864}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a79de19b-3c29-44ec-8ce8-39ebd5eb7464", "name": "default@CompileArkTS work[17] is submitted.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862978990772}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd66c6ae-a733-4c64-be64-c8ef1fb8b5eb", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Worker4", "startTime": 652863005283423, "endTime": 652864901247024}, "additional": {"children": ["81662b18-619c-4af4-8791-79f535d27676", "1fa3ecfc-bcf4-4209-bbcd-ae10036fab22", "ca42057f-1919-4afe-958a-7eb1a4c23414", "73310755-3462-4b54-878f-141535b97059", "921a4700-5487-444b-9417-5060fd7b5419", "31331d5d-f9e6-4491-a44f-04b0e16d3754"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "12bd2bf0-dd3f-4a86-a899-a50d233aeea3", "logId": "6fc28b31-d33c-467b-a638-e746aa72e9f0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0ef8ef24-9f08-4621-a103-47152a78beb0", "name": "default@CompileArkTS work[17] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862979753167}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77ba946d-0f75-439d-bbbd-8d5e4949f728", "name": "A work dispatched to worker[7] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862979848659}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8a552ae9-5af3-4efa-858e-243ccab7a405", "name": "A work dispatched to worker[6] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862979895742}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d8c002ac-f7d8-4c3b-b414-336bb176f799", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862979931288}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc0d0b81-73d0-4d21-a583-1e6b5fb51feb", "name": "default@CompileArkTS work[17] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862980157815}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b93c646e-9e13-4976-81a5-c2ce16eeced9", "name": "default@CompileArkTS work[17] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862980224574}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34f811ba-e71d-4cd9-8566-08c127e8dfe9", "name": "CopyResources startTime: 652862980269204", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862980272564}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3142bea0-c455-4068-b8bf-833fdcce489b", "name": "default@CompileArkTS work[18] is submitted.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862980350883}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85d7a9dd-9b0b-4a22-8c3d-a9df9aeae40d", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Worker6", "startTime": 652864834566382, "endTime": 652864856240120}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "12bd2bf0-dd3f-4a86-a899-a50d233aeea3", "logId": "240385e0-9af4-4976-b313-3d81b01f7ed9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bd064741-56a6-4ea9-b0d7-2114e9c90d13", "name": "default@CompileArkTS work[18] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862981062118}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8802bab3-d20f-4aa9-8bbc-a6a98fbe2596", "name": "A work dispatched to worker[7] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862981152335}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b9e18d12-67cf-4748-8d7d-0315349cbbc7", "name": "Create  resident worker with id: 6.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862981214305}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b659daf0-2989-46cb-afef-3114df3222f6", "name": "default@CompileArkTS work[18] has been dispatched to worker[6].", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862982431006}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b3f94b9-4d2e-4107-a51b-5de2e624a7c2", "name": "default@CompileArkTS work[18] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862982583183}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "21ca9f47-b8a2-407b-9f6d-9f2bf3ac3a5a", "name": "entry : default@CompileArkTS cost memory 1.6326446533203125", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862982735995}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d966257-1e7e-43a3-9ad7-fe77f4aa7720", "name": "entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862990327142, "endTime": 652862996092673}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "ArkTS", "taskRunReasons": [], "detailId": "08168b5e-555a-498f-bcba-03a8121640e1", "logId": "d00d5084-9610-4a69-b746-6cad7174d97c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "08168b5e-555a-498f-bcba-03a8121640e1", "name": "create entry:default@BuildJS task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862984911548}, "additional": {"logType": "detail", "children": [], "durationId": "4d966257-1e7e-43a3-9ad7-fe77f4aa7720"}}, {"head": {"id": "4679fe51-7f35-449b-b665-4fb7b7ff2af7", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862985387553}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "739ea91e-3dfe-487f-a304-42400d9b2062", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862985537795}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "aa57bfa5-d33f-463a-b2c2-8ffa8515b391", "name": "Executing task :entry:default@BuildJS", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862990351807}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "adc1fc31-0796-4c9d-9cd8-4c90f039804e", "name": "entry : default@BuildJS cost memory 0.1313629150390625", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862995503858}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7a02908-fef7-46ee-b035-486e277014c5", "name": "runTaskFromQueue task cost before running: 1 s 469 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862995714929}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d00d5084-9610-4a69-b746-6cad7174d97c", "name": "Finished :entry:default@BuildJS", "description": "Compile ArkTS or JS components using Node.js for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862990327142, "endTime": 652862996092673, "totalTime": 5349432}, "additional": {"logType": "info", "children": [], "durationId": "4d966257-1e7e-43a3-9ad7-fe77f4aa7720"}}, {"head": {"id": "3d2889fd-13bf-45c3-9f47-dff20b0c12c9", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862999876607, "endTime": 652865151277363}, "additional": {"children": ["7acf2e40-fc5d-4ceb-b200-dc6680f215fa"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Resources", "taskRunReasons": ["The input file '/Users/<USER>/github/LingXia/examples/harmony/entry/libs' has been changed."], "detailId": "f8203d4a-b185-4e28-a427-c1af533e0901", "logId": "b5516b41-b026-4380-9955-3523df814c66"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f8203d4a-b185-4e28-a427-c1af533e0901", "name": "create entry:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862998342647}, "additional": {"logType": "detail", "children": [], "durationId": "3d2889fd-13bf-45c3-9f47-dff20b0c12c9"}}, {"head": {"id": "064f06b6-66eb-4cc5-89bb-d535d165a07f", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862998739798}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d99d603-6653-4c74-92de-55819ffb9c8c", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862998895047}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e482d01a-4e48-4ceb-a80c-b0cc5116b5a7", "name": "Executing task :entry:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862999896031}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff0a51ee-3a21-4552-a7d2-1e057e78d0e1", "name": "entry:default@ProcessLibs is not up-to-date, since the input file '/Users/<USER>/github/LingXia/examples/harmony/entry/libs' has been changed.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652863006099172}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92514c9e-a93e-4fce-86d9-71dcbf1255f9", "name": "Incremental task entry:default@ProcessLibs pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652863006237549}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d0fb5588-01c4-43d2-8db1-e924cb3e0da3", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652863006343743}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2acf652d-2864-429f-98ca-385b42d3490a", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652863006396272}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd1c1a5c-8762-4990-abed-c1cb35e4dd28", "name": "default@ProcessLibs work[19] is submitted.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652863034714906}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7acf2e40-fc5d-4ceb-b200-dc6680f215fa", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Worker5", "startTime": 652865127842203, "endTime": 652865150401785}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "3d2889fd-13bf-45c3-9f47-dff20b0c12c9", "logId": "ad91ad6f-ce65-4748-8121-a0c939dbc6ae"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6864215f-8465-43b8-9dcc-e83e6ac3e944", "name": "default@ProcessLibs work[19] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652863035849812}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb9d6735-fdff-4107-9f4b-b0920ec1dd96", "name": "A work dispatched to worker[7] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652863035999945}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e743a64f-f97d-4a28-8cb3-12421a9396ae", "name": "A work dispatched to worker[6] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652863036079666}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce7b6fb0-86f2-4a4f-b97f-91ae14df55d2", "name": "Create  resident worker with id: 5.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652863036172914}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1e57eccd-eede-41be-b0f3-4262c98e668d", "name": "default@ProcessLibs work[19] has been dispatched to worker[5].", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652863037543143}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5649da6c-3663-440d-88b3-d0968b0ab2f2", "name": "default@ProcessLibs work[19] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652863037921158}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd56a52f-f2fc-48db-a6dd-0347f8017955", "name": "entry : default@ProcessLibs cost memory -6.487884521484375", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652863038095163}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d25d41b1-659c-4383-bc2f-065358a6c71b", "name": "worker[6] has one work done.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652864856552106}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de396746-10eb-48c4-a701-68ae850e61f4", "name": "CopyResources is end, endTime: 652864856730230", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652864856741640}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "92305258-ad61-4e62-8354-a27025b2c629", "name": "default@CompileArkTS work[18] done.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652864856904665}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "240385e0-9af4-4976-b313-3d81b01f7ed9", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Worker6", "startTime": 652864834566382, "endTime": 652864856240120}, "additional": {"logType": "info", "children": [], "durationId": "85d7a9dd-9b0b-4a22-8c3d-a9df9aeae40d", "parent": "9fc4146b-9e13-405c-852f-28e531f4a91e"}}, {"head": {"id": "a18f186c-b7de-43f8-93db-fb2add0b7553", "name": "A work dispatched to worker[6] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652864857051436}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e716c6cb-537c-4f99-a03b-f592d801d790", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652864911366560}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81662b18-619c-4af4-8791-79f535d27676", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Worker4", "startTime": 652863019034316, "endTime": 652863075514603}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "bd66c6ae-a733-4c64-be64-c8ef1fb8b5eb", "logId": "4bd80870-f304-4253-b0f8-5dea4ee2a000"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4bd80870-f304-4253-b0f8-5dea4ee2a000", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652863019034316, "endTime": 652863075514603}, "additional": {"logType": "info", "children": [], "durationId": "81662b18-619c-4af4-8791-79f535d27676", "parent": "6fc28b31-d33c-467b-a638-e746aa72e9f0"}}, {"head": {"id": "1fa3ecfc-bcf4-4209-bbcd-ae10036fab22", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Worker4", "startTime": 652863080528437, "endTime": 652863080900023}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "bd66c6ae-a733-4c64-be64-c8ef1fb8b5eb", "logId": "a03534df-65cb-4697-8de4-5e0ebd6635a0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "a03534df-65cb-4697-8de4-5e0ebd6635a0", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652863080528437, "endTime": 652863080900023}, "additional": {"logType": "info", "children": [], "durationId": "1fa3ecfc-bcf4-4209-bbcd-ae10036fab22", "parent": "6fc28b31-d33c-467b-a638-e746aa72e9f0"}}, {"head": {"id": "ca42057f-1919-4afe-958a-7eb1a4c23414", "name": "remove uncacheable modules", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Worker4", "startTime": 652863082063019, "endTime": 652863088418090}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "bd66c6ae-a733-4c64-be64-c8ef1fb8b5eb", "logId": "4f925d24-aa39-41a4-998e-298f3c453930"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4f925d24-aa39-41a4-998e-298f3c453930", "name": "remove uncacheable modules", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652863082063019, "endTime": 652863088418090}, "additional": {"logType": "info", "children": [], "durationId": "ca42057f-1919-4afe-958a-7eb1a4c23414", "parent": "6fc28b31-d33c-467b-a638-e746aa72e9f0"}}, {"head": {"id": "73310755-3462-4b54-878f-141535b97059", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Worker4", "startTime": 652863088591764, "endTime": 652864777129950}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "bd66c6ae-a733-4c64-be64-c8ef1fb8b5eb", "logId": "98a6af06-7704-4931-b672-2afbee310506"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "98a6af06-7704-4931-b672-2afbee310506", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652863088591764, "endTime": 652864777129950}, "additional": {"logType": "info", "children": [], "durationId": "73310755-3462-4b54-878f-141535b97059", "parent": "6fc28b31-d33c-467b-a638-e746aa72e9f0"}}, {"head": {"id": "921a4700-5487-444b-9417-5060fd7b5419", "name": "write build package cache", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Worker4", "startTime": 652864777192329, "endTime": 652864788148767}, "additional": {"children": ["05cc76d7-e81d-43cc-a216-2d0798b9f750", "a233c1cd-35cb-464c-8bef-69c9455f542a", "2dc825d3-db25-4d38-8e3c-f38c6016aaac"], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "bd66c6ae-a733-4c64-be64-c8ef1fb8b5eb", "logId": "912321c8-195c-4ddf-a269-8372929aa05c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "912321c8-195c-4ddf-a269-8372929aa05c", "name": "write build package cache", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652864777192329, "endTime": 652864788148767}, "additional": {"logType": "info", "children": ["ca6b327b-e7f2-4612-b63e-28e36982d69e", "6da360a0-9a74-4586-aa48-5714970b34df", "4ae52da2-3940-481b-b98f-cd5296b361c0"], "durationId": "921a4700-5487-444b-9417-5060fd7b5419", "parent": "6fc28b31-d33c-467b-a638-e746aa72e9f0"}}, {"head": {"id": "05cc76d7-e81d-43cc-a216-2d0798b9f750", "name": "get final cache", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Worker4", "startTime": 652864777258508, "endTime": 652864777327360}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "921a4700-5487-444b-9417-5060fd7b5419", "logId": "ca6b327b-e7f2-4612-b63e-28e36982d69e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ca6b327b-e7f2-4612-b63e-28e36982d69e", "name": "get final cache", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652864777258508, "endTime": 652864777327360}, "additional": {"logType": "info", "children": [], "durationId": "05cc76d7-e81d-43cc-a216-2d0798b9f750", "parent": "912321c8-195c-4ddf-a269-8372929aa05c"}}, {"head": {"id": "a233c1cd-35cb-464c-8bef-69c9455f542a", "name": "pack cache", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Worker4", "startTime": 652864777340484, "endTime": 652864779732267}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "921a4700-5487-444b-9417-5060fd7b5419", "logId": "6da360a0-9a74-4586-aa48-5714970b34df"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6da360a0-9a74-4586-aa48-5714970b34df", "name": "pack cache", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652864777340484, "endTime": 652864779732267}, "additional": {"logType": "info", "children": [], "durationId": "a233c1cd-35cb-464c-8bef-69c9455f542a", "parent": "912321c8-195c-4ddf-a269-8372929aa05c"}}, {"head": {"id": "2dc825d3-db25-4d38-8e3c-f38c6016aaac", "name": "write cache", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Worker4", "startTime": 652864779740503, "endTime": 652864788092255}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "921a4700-5487-444b-9417-5060fd7b5419", "logId": "4ae52da2-3940-481b-b98f-cd5296b361c0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4ae52da2-3940-481b-b98f-cd5296b361c0", "name": "write cache", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652864779740503, "endTime": 652864788092255}, "additional": {"logType": "info", "children": [], "durationId": "2dc825d3-db25-4d38-8e3c-f38c6016aaac", "parent": "912321c8-195c-4ddf-a269-8372929aa05c"}}, {"head": {"id": "31331d5d-f9e6-4491-a44f-04b0e16d3754", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Worker4", "startTime": 652864788170546, "endTime": 652864896908745}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "bd66c6ae-a733-4c64-be64-c8ef1fb8b5eb", "logId": "88d7f6be-c7c3-4119-9fa6-1bd7f6b6e579"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "88d7f6be-c7c3-4119-9fa6-1bd7f6b6e579", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652864788170546, "endTime": 652864896908745}, "additional": {"logType": "info", "children": [], "durationId": "31331d5d-f9e6-4491-a44f-04b0e16d3754", "parent": "6fc28b31-d33c-467b-a638-e746aa72e9f0"}}, {"head": {"id": "f1166a05-2b50-46e6-84b8-148dcb619c71", "name": "default@CompileArkTS work[17] done.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652864919389854}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6fc28b31-d33c-467b-a638-e746aa72e9f0", "name": "entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Worker4", "startTime": 652863005283423, "endTime": 652864901247024}, "additional": {"logType": "info", "children": ["4bd80870-f304-4253-b0f8-5dea4ee2a000", "a03534df-65cb-4697-8de4-5e0ebd6635a0", "4f925d24-aa39-41a4-998e-298f3c453930", "98a6af06-7704-4931-b672-2afbee310506", "912321c8-195c-4ddf-a269-8372929aa05c", "88d7f6be-c7c3-4119-9fa6-1bd7f6b6e579"], "durationId": "bd66c6ae-a733-4c64-be64-c8ef1fb8b5eb", "parent": "9fc4146b-9e13-405c-852f-28e531f4a91e"}}, {"head": {"id": "d4c319db-5d77-4eec-816f-a9e4c33c18df", "name": "A work dispatched to worker[4] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652864919656079}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9fc4146b-9e13-405c-852f-28e531f4a91e", "name": "Finished :entry:default@CompileArkTS", "description": "Compile ArkTS or JS components for large-system devices in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862953800447, "endTime": 652864919774913, "totalTime": 1924996724}, "additional": {"logType": "info", "children": ["6fc28b31-d33c-467b-a638-e746aa72e9f0", "240385e0-9af4-4976-b313-3d81b01f7ed9"], "durationId": "12bd2bf0-dd3f-4a86-a899-a50d233aeea3"}}, {"head": {"id": "339941fd-b5bd-40cb-9ccf-f309946fb0d1", "name": "worker[5] has one work done.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652865150762991}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c739d96-2880-445e-8a30-dce1d415994c", "name": "default@ProcessLibs work[19] done.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652865151009378}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ad91ad6f-ce65-4748-8121-a0c939dbc6ae", "name": "entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Worker5", "startTime": 652865127842203, "endTime": 652865150401785}, "additional": {"logType": "info", "children": [], "durationId": "7acf2e40-fc5d-4ceb-b200-dc6680f215fa", "parent": "b5516b41-b026-4380-9955-3523df814c66"}}, {"head": {"id": "b48113ac-f6b7-4756-931e-1105568b7c7d", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652865151167612}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b5516b41-b026-4380-9955-3523df814c66", "name": "Finished :entry:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652862999876607, "endTime": 652865151277363, "totalTime": 60953419}, "additional": {"logType": "info", "children": ["ad91ad6f-ce65-4748-8121-a0c939dbc6ae"], "durationId": "3d2889fd-13bf-45c3-9f47-dff20b0c12c9"}}, {"head": {"id": "f705e652-d7c8-4945-b77e-1aa3a7af996e", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652865157817871, "endTime": 652865746473426}, "additional": {"children": ["1106c89a-d668-452e-b3ee-63f05b03b847"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": ["The input file '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/libs/default' has been changed."], "detailId": "4dd6d6af-085a-4984-b9a9-85dd7a8ce932", "logId": "6550f352-0ba0-494c-9f39-8d1bb2f171aa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4dd6d6af-085a-4984-b9a9-85dd7a8ce932", "name": "create entry:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652865154115403}, "additional": {"logType": "detail", "children": [], "durationId": "f705e652-d7c8-4945-b77e-1aa3a7af996e"}}, {"head": {"id": "d47ea539-0abc-44cf-b77a-744d8dfea1ab", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652865154578444}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fa21682-c58d-4562-8856-044f218a1ca6", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652865154745188}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a51170f0-8d48-483b-836b-afbf7b780c31", "name": "Executing task :entry:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652865157966467}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6a063b5-2200-42ff-a950-43f4aef5a27c", "name": "Task 'entry:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652865158741391}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7981aea1-c27a-4a9b-8ebf-294f6b150671", "name": "entry:default@DoNativeStrip is not up-to-date, since the input file '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/libs/default' has been changed.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652865159572537}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b8202fcd-917c-476c-a288-96bc8a5e8bbe", "name": "Incremental task entry:default@DoNativeStrip pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652865159772424}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a0a5ec07-c4c4-4e3b-b819-2914ecd44842", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652865159965152}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5a8610df-181c-4b2d-8b0d-5594dff136ca", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652865160055409}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "465e7c2f-cd29-4168-8e9b-294ad4a17988", "name": "default@DoNativeStrip work[20] is submitted.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652865160814143}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1106c89a-d668-452e-b3ee-63f05b03b847", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 50614, "tid": "Worker6", "startTime": 652865572629610, "endTime": 652865745831551}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "f705e652-d7c8-4945-b77e-1aa3a7af996e", "logId": "b3bba53f-b74e-4826-810a-9e291d06640c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6b3919b2-1fb9-40a7-9d07-b4dabafcdb7d", "name": "default@DoNativeStrip work[20] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652865162312621}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7850095-4ce3-4996-b9c4-5432917be197", "name": "A work dispatched to worker[7] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652865162485409}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45daf589-7497-4b44-a0c0-43e4900d6ef8", "name": "default@DoNativeStrip work[20] has been dispatched to worker[6].", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652865162688227}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4965524d-7114-4bb1-81ca-cc591beecd74", "name": "default@DoNativeStrip work[20] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652865162788582}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1eedb37f-d81f-4920-9bba-2762f0ece61e", "name": "entry : default@DoNativeStrip cost memory 0.22351837158203125", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652865162937618}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2268071e-74a2-4db4-b823-69f44bbd6eda", "name": "worker[6] has one work done.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652865745976001}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b4d005b-b680-41bb-9a27-96f9361bc100", "name": "default@DoNativeStrip work[20] done.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652865746212954}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3bba53f-b74e-4826-810a-9e291d06640c", "name": "entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 50614, "tid": "Worker6", "startTime": 652865572629610, "endTime": 652865745831551}, "additional": {"logType": "info", "children": [], "durationId": "1106c89a-d668-452e-b3ee-63f05b03b847", "parent": "6550f352-0ba0-494c-9f39-8d1bb2f171aa"}}, {"head": {"id": "686a593b-b67b-4535-a86a-7e06ddb2edf1", "name": "A work dispatched to worker[6] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652865746375628}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6550f352-0ba0-494c-9f39-8d1bb2f171aa", "name": "Finished :entry:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652865157817871, "endTime": 652865746473426, "totalTime": 178467938}, "additional": {"logType": "info", "children": ["b3bba53f-b74e-4826-810a-9e291d06640c"], "durationId": "f705e652-d7c8-4945-b77e-1aa3a7af996e"}}, {"head": {"id": "18ba0ce8-429d-4b28-b0bf-a79ada1cde57", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652865752057738, "endTime": 652867310330751}, "additional": {"children": ["5802703b-5e51-498b-898c-613460edf2de"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Native", "taskRunReasons": ["The input file '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/stripped_native_libs/default' has been changed."], "detailId": "1af563aa-a593-4550-971d-c918a755e88a", "logId": "9058895f-8515-41f4-b18d-4948ada1ef1b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1af563aa-a593-4550-971d-c918a755e88a", "name": "create entry:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652865748916565}, "additional": {"logType": "detail", "children": [], "durationId": "18ba0ce8-429d-4b28-b0bf-a79ada1cde57"}}, {"head": {"id": "2c3c78d7-c395-43af-8a1e-905247d7aa4d", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652865749354571}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "033c7507-b1a4-4b6c-8634-9ded692ff4c4", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652865749547242}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "060fd690-2a6c-4e41-8758-2c9db8bb1a4f", "name": "Executing task :entry:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652865752083473}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99d609d8-2fd1-430d-bcb8-85a13be1c1ee", "name": "Task 'entry:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652865752413249}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e94892c8-2ed7-46cc-86e8-15104ee95cd2", "name": "entry:default@CacheNativeLibs is not up-to-date, since the input file '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/stripped_native_libs/default' has been changed.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652865753161767}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e8d0125-9614-44db-9b8c-4782b9dad66e", "name": "Incremental task entry:default@CacheNativeLibs pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652865753352948}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "815b328f-3b9a-4e2b-897c-d37070c3d202", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652865753499234}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac61564a-0ec3-43eb-b866-d9237bc53581", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652865753595373}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f1881622-42a1-4e04-93bf-219845857085", "name": "default@CacheNativeLibs work[21] is submitted.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652865753738812}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5802703b-5e51-498b-898c-613460edf2de", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 50614, "tid": "Worker6", "startTime": 652867301971635, "endTime": 652867309369117}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "18ba0ce8-429d-4b28-b0bf-a79ada1cde57", "logId": "ce598d4e-29cf-4c34-8199-7a28823eba94"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d0be729f-53df-4473-9745-3ebdbc71eaa0", "name": "default@CacheNativeLibs work[21] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652865754854375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b385e50b-9fd9-40d2-bfd5-da8290b796c3", "name": "A work dispatched to worker[7] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652865755000423}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9c72e76d-b240-4ff2-8a35-2a021552ae15", "name": "default@CacheNativeLibs work[21] has been dispatched to worker[6].", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652865755159907}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "57164587-80ac-4d13-9184-25379d046710", "name": "default@CacheNativeLibs work[21] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652865755245835}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8b32afd-b57c-4e02-9b28-9d31b9874dec", "name": "entry : default@CacheNativeLibs cost memory 0.2072906494140625", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652865755366811}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "029190f5-ae63-43ea-a13b-64394e0ac768", "name": "worker[6] has one work done.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652867309640545}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56aae49e-db6e-4f6e-99b0-06c82819d9bd", "name": "default@CacheNativeLibs work[21] done.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652867309932067}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce598d4e-29cf-4c34-8199-7a28823eba94", "name": "entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 50614, "tid": "Worker6", "startTime": 652867301971635, "endTime": 652867309369117}, "additional": {"logType": "info", "children": [], "durationId": "5802703b-5e51-498b-898c-613460edf2de", "parent": "9058895f-8515-41f4-b18d-4948ada1ef1b"}}, {"head": {"id": "85a79212-f3c2-4684-adbb-e332cf2e9a8a", "name": "A work dispatched to worker[6] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652867310155170}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9058895f-8515-41f4-b18d-4948ada1ef1b", "name": "Finished :entry:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652865752057738, "endTime": 652867310330751, "totalTime": 10818522}, "additional": {"logType": "info", "children": ["ce598d4e-29cf-4c34-8199-7a28823eba94"], "durationId": "18ba0ce8-429d-4b28-b0bf-a79ada1cde57"}}, {"head": {"id": "010b443a-a75c-4fff-9980-577e2c20f91a", "name": "entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652867325139679, "endTime": 652867329323674}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": ["The input file '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/module.json' has been changed."], "detailId": "e88708f3-2c58-4597-a08b-e59b2f027d88", "logId": "211fdcae-57fc-4263-bc60-7226c84b061c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e88708f3-2c58-4597-a08b-e59b2f027d88", "name": "create entry:default@GeneratePkgModuleJson task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652867313822793}, "additional": {"logType": "detail", "children": [], "durationId": "010b443a-a75c-4fff-9980-577e2c20f91a"}}, {"head": {"id": "b7002938-29c0-4d4b-bca8-9d616ac2322b", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652867317216305}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "471bb68b-f98e-4efb-acde-73bd8ada0264", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652867317434933}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52b1e9d8-04e1-4841-8fa1-dc622872eb45", "name": "Executing task :entry:default@GeneratePkgModuleJson", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652867325178454}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a71a5dda-5331-44f6-ad36-de326935d09d", "name": "Task 'entry:default@GeneratePkgModuleJson' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652867326162476}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "33baa373-4e5a-4e71-ae3c-5831904b6830", "name": "entry:default@GeneratePkgModuleJson is not up-to-date, since the input file '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652867326678042}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b1b44a21-3822-4fee-84ce-47832eadd9a0", "name": "Incremental task entry:default@GeneratePkgModuleJson pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652867326897932}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8442b537-8168-4cee-b5fd-de53174c8af1", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652867327163467}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "451c948f-d0e1-4c28-ad9f-4ec2797efc1f", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652867327269306}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f424d31-f210-471e-9618-e28b0a100155", "name": "entry : default@GeneratePkgModuleJson cost memory 0.1204376220703125", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652867328729004}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "787547be-f7d2-4e20-af88-9033230bff28", "name": "runTaskFromQueue task cost before running: 5 s 803 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652867329096824}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "211fdcae-57fc-4263-bc60-7226c84b061c", "name": "Finished :entry:default@GeneratePkgModuleJson", "description": "Generate the module.json in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652867325139679, "endTime": 652867329323674, "totalTime": 3824905}, "additional": {"logType": "info", "children": [], "durationId": "010b443a-a75c-4fff-9980-577e2c20f91a"}}, {"head": {"id": "b0af5fda-067b-4ca4-b562-a67ef77ff51d", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652867347218834, "endTime": 652868351327939}, "additional": {"children": ["99bcc59f-4dc5-4687-a5f3-ded8b308b54e", "d43a982c-2def-4d07-b822-7f68125dd87c", "d56bcaae-fd8c-4dfd-bafb-c8b76343e5ca"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Package", "taskRunReasons": ["The input file '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/stripped_native_libs/default' has been changed."], "detailId": "27dad65c-1e19-4b0a-94c4-5a70f9be67f7", "logId": "d98c6adc-7b0d-4a44-b5b9-49775911ad27"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "27dad65c-1e19-4b0a-94c4-5a70f9be67f7", "name": "create entry:default@PackageHap task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652867333723383}, "additional": {"logType": "detail", "children": [], "durationId": "b0af5fda-067b-4ca4-b562-a67ef77ff51d"}}, {"head": {"id": "1a0eba04-58a1-4ea9-9708-45d4313ac3fe", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652867334307108}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "676c5e51-3dec-4a17-a789-7e7ca75768ca", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652867334500063}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f06cc2eb-2a25-459b-a404-68f9c2326fdf", "name": "Executing task :entry:default@PackageHap", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652867347265772}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ffd879e-e0fe-4bc6-b118-783841770f06", "name": "entry:default@PackageHap is not up-to-date, since the input file '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/stripped_native_libs/default' has been changed.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652867353583976}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7476ce3-0c1c-4761-a4aa-74e9a60f3621", "name": "Incremental task entry:default@PackageHap pre-execution cost: 5 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652867353828822}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b88105e-f115-4945-8aef-b4c1881b0ceb", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652867354129920}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31f88cb9-cfba-438a-9b43-0d58c09e6e13", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652867354303180}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99bcc59f-4dc5-4687-a5f3-ded8b308b54e", "name": "generate HAP packaging command", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652867355723792, "endTime": 652867358625055}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b0af5fda-067b-4ca4-b562-a67ef77ff51d", "logId": "aacba011-1f4f-42b8-898a-eeb53a7d01e0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7b324701-c31f-489a-acb3-083dd550a61e", "name": "Use tool [/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/toolchains/lib/app_packing_tool.jar]\n [\n  'java',\n  '-Dfile.encoding=utf-8',\n  '-jar',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/toolchains/lib/app_packing_tool.jar',\n  '--mode',\n  'hap',\n  '--force',\n  'true',\n  '--lib-path',\n  '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/stripped_native_libs/default',\n  '--json-path',\n  '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/package/default/module.json',\n  '--resources-path',\n  '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources',\n  '--index-path',\n  '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources.index',\n  '--pack-info-path',\n  '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/outputs/default/pack.info',\n  '--out-path',\n  '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/outputs/default/entry-default-unsigned.hap',\n  '--ets-path',\n  '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader_out/default/ets',\n  '--pkg-context-path',\n  '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default/pkgContextInfo.json'\n]", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652867358253924}, "additional": {"logType": "debug", "children": [], "durationId": "b0af5fda-067b-4ca4-b562-a67ef77ff51d"}}, {"head": {"id": "aacba011-1f4f-42b8-898a-eeb53a7d01e0", "name": "generate HAP packaging command", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652867355723792, "endTime": 652867358625055}, "additional": {"logType": "info", "children": [], "durationId": "99bcc59f-4dc5-4687-a5f3-ded8b308b54e", "parent": "d98c6adc-7b0d-4a44-b5b9-49775911ad27"}}, {"head": {"id": "d43a982c-2def-4d07-b822-7f68125dd87c", "name": "submit HAP packaging task to work pool", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652867359821183, "endTime": 652867362976369}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "b0af5fda-067b-4ca4-b562-a67ef77ff51d", "logId": "7e318c5f-9174-41bf-a1bc-6588dd5c2f0b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0165695d-c2a7-4c5a-9d9d-de5e55861790", "name": "default@PackageHap work[22] is submitted.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652867360969850}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d56bcaae-fd8c-4dfd-bafb-c8b76343e5ca", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "duration"}, "body": {"pid": 50614, "tid": "Worker6", "startTime": 652867365307430, "endTime": 652868350400311}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "", "taskRunReasons": [], "parent": "b0af5fda-067b-4ca4-b562-a67ef77ff51d", "logId": "db5cb44c-fdd7-4c37-9d76-9a936f877b6e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6e69ffca-1ddc-4a2e-ace2-bbc6a88a2255", "name": "default@PackageHap work[22] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652867361962217}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "958835e4-a526-4c33-93e7-8af814215e70", "name": "A work dispatched to worker[7] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652867362075242}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ed3e70a-4958-4a7c-a7fb-03b9c9f57483", "name": "default@PackageHap work[22] has been dispatched to worker[6].", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652867362404314}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b42b694-d964-46c3-bbc9-3f8caef4b33b", "name": "default@PackageHap work[22] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652867362676897}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7e318c5f-9174-41bf-a1bc-6588dd5c2f0b", "name": "submit HAP packaging task to work pool", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652867359821183, "endTime": 652867362976369}, "additional": {"logType": "info", "children": [], "durationId": "d43a982c-2def-4d07-b822-7f68125dd87c", "parent": "d98c6adc-7b0d-4a44-b5b9-49775911ad27"}}, {"head": {"id": "f6f7c075-d168-49bd-a02a-46daa03cd15b", "name": "entry : default@PackageHap cost memory 0.6262359619140625", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652867368100002}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "153c5a8a-dc99-449f-a779-ddffbdb0219d", "name": "worker[6] has one work done.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868350547807}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90dab4e9-e155-4657-bb2e-69d5b32ee593", "name": "default@PackageHap work[22] done.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868350793183}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db5cb44c-fdd7-4c37-9d76-9a936f877b6e", "name": "entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Worker6", "startTime": 652867365307430, "endTime": 652868350400311}, "additional": {"logType": "info", "children": [], "durationId": "d56bcaae-fd8c-4dfd-bafb-c8b76343e5ca", "parent": "d98c6adc-7b0d-4a44-b5b9-49775911ad27"}}, {"head": {"id": "fa4edbe6-acf7-440f-a670-895c246f768f", "name": "A work dispatched to worker[6] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868351111271}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d98c6adc-7b0d-4a44-b5b9-49775911ad27", "name": "Finished :entry:default@PackageHap", "description": "Build the HAP package in the stage model.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652867347218834, "endTime": 652868351327939, "totalTime": 1003191530}, "additional": {"logType": "info", "children": ["aacba011-1f4f-42b8-898a-eeb53a7d01e0", "7e318c5f-9174-41bf-a1bc-6588dd5c2f0b", "db5cb44c-fdd7-4c37-9d76-9a936f877b6e"], "durationId": "b0af5fda-067b-4ca4-b562-a67ef77ff51d"}}, {"head": {"id": "631ef435-c314-4d38-bc28-e350dc4492ae", "name": "entry:default@SignHap", "description": "Sign the HAP package.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868360843885, "endTime": 652868810754303}, "additional": {"children": ["3b3970cb-bf06-49f9-aff3-4e2bcd28de7b", "7a20eace-1542-44c1-8eb4-33e52fd334c7"], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Sign", "taskRunReasons": ["The input file '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/outputs/default/entry-default-unsigned.hap' has been changed."], "detailId": "13fa5348-0ef8-436a-bb3a-d46baaf99df9", "logId": "36cbee56-0863-4455-8c0e-1ff710eec03a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "13fa5348-0ef8-436a-bb3a-d46baaf99df9", "name": "create entry:default@SignHap task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868355496708}, "additional": {"logType": "detail", "children": [], "durationId": "631ef435-c314-4d38-bc28-e350dc4492ae"}}, {"head": {"id": "b16037ce-d930-47d3-8f26-0787a2167e32", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868355973617}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "374e6325-1eb8-44e2-82c5-01b0385a3f3b", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868356803214}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4da08c05-b344-4c31-8912-9ad0319c1331", "name": "Executing task :entry:default@SignHap", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868360872975}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f0fb808-bae9-4517-89d7-776405cb7b88", "name": "entry:default@SignHap is not up-to-date, since the input file '/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/outputs/default/entry-default-unsigned.hap' has been changed.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868362828634}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f5bce6cd-45b7-47d5-abd1-9c76b5695ee4", "name": "Incremental task entry:default@SignHap pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868363039108}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c49e6f72-7d42-439d-8254-f10ab5ba05e6", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868363207933}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec87e733-4f37-47cf-9ea2-af39d5011191", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868363291122}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b3970cb-bf06-49f9-aff3-4e2bcd28de7b", "name": "generate hos_hap signing command", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868369955856, "endTime": 652868484284366}, "additional": {"children": ["678603a2-7bbd-4092-aa9f-452941f911f5"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "631ef435-c314-4d38-bc28-e350dc4492ae", "logId": "a5bb0903-e170-4071-9c07-5657eece2d8a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "678603a2-7bbd-4092-aa9f-452941f911f5", "name": "verify signing configuration and get bundle name", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868394973169, "endTime": 652868482846204}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "3b3970cb-bf06-49f9-aff3-4e2bcd28de7b", "logId": "ae2907dd-edbd-400a-9af6-00ec7cafe4ce"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c3063af9-addc-412c-a03a-19e57ebe661b", "name": "java daemon socket received message:{\"code\":0,\"message\":\"verify profile success\"}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868482252555}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae2907dd-edbd-400a-9af6-00ec7cafe4ce", "name": "verify signing configuration and get bundle name", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868394973169, "endTime": 652868482846204}, "additional": {"logType": "info", "children": [], "durationId": "678603a2-7bbd-4092-aa9f-452941f911f5", "parent": "a5bb0903-e170-4071-9c07-5657eece2d8a"}}, {"head": {"id": "a5bb0903-e170-4071-9c07-5657eece2d8a", "name": "generate hos_hap signing command", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868369955856, "endTime": 652868484284366}, "additional": {"logType": "info", "children": ["ae2907dd-edbd-400a-9af6-00ec7cafe4ce"], "durationId": "3b3970cb-bf06-49f9-aff3-4e2bcd28de7b", "parent": "36cbee56-0863-4455-8c0e-1ff710eec03a"}}, {"head": {"id": "7a20eace-1542-44c1-8eb4-33e52fd334c7", "name": "execute hos_hap signing command", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868485131009, "endTime": 652868810226464}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "631ef435-c314-4d38-bc28-e350dc4492ae", "logId": "443a69de-97e1-47a4-a72f-0eb24463f512"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4383e210-8e41-4c08-a969-8031b87d0163", "name": "java daemon socket close code:1000 reason:close by user", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868487692476}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9971899-809e-47ef-bd6a-2e1f531b4327", "name": "java daemon socket received message:{\"code\":0,\"message\":\"sign app success\"}", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868809363996}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "443a69de-97e1-47a4-a72f-0eb24463f512", "name": "execute hos_hap signing command", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868485131009, "endTime": 652868810226464}, "additional": {"logType": "info", "children": [], "durationId": "7a20eace-1542-44c1-8eb4-33e52fd334c7", "parent": "36cbee56-0863-4455-8c0e-1ff710eec03a"}}, {"head": {"id": "d6c14f96-0afa-4660-878a-494ce3da7a3e", "name": "entry : default@SignHap cost memory 0.7784500122070312", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868810388684}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "895b60bb-a83c-4e0f-8b4e-2904911896a6", "name": "runTaskFromQueue task cost before running: 7 s 284 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868810628699}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36cbee56-0863-4455-8c0e-1ff710eec03a", "name": "Finished :entry:default@SignHap", "description": "Sign the HAP package.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868360843885, "endTime": 652868810754303, "totalTime": 449738372}, "additional": {"logType": "info", "children": ["a5bb0903-e170-4071-9c07-5657eece2d8a", "443a69de-97e1-47a4-a72f-0eb24463f512"], "durationId": "631ef435-c314-4d38-bc28-e350dc4492ae"}}, {"head": {"id": "d6ec67f7-a4cc-4a15-92c3-472affa8b3cf", "name": "entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868815409563, "endTime": 652868820932019}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "entry", "category": "Generate", "taskRunReasons": [], "detailId": "2ff37332-b657-4cf7-a543-3173eb132d4a", "logId": "a2043c65-6345-4c0c-93ab-8d6d950542f4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "2ff37332-b657-4cf7-a543-3173eb132d4a", "name": "create entry:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868813541046}, "additional": {"logType": "detail", "children": [], "durationId": "d6ec67f7-a4cc-4a15-92c3-472affa8b3cf"}}, {"head": {"id": "8f3a1a4a-5891-4b75-851f-9610b5f5f0de", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868814097547}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebef6508-cb12-4d4f-ae3d-ce6ab2e1e529", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868814261473}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7526c780-54d2-4ca6-891a-a43b00b36f36", "name": "Executing task :entry:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868815429952}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5b049ef8-540e-447f-8341-d016710a22e5", "name": "jsonObjWithoutParam {\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868820365042}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc83e2a5-8e50-49c5-b2fa-b87f4fd41022", "name": "jsonObjWithoutParam {\"name\":\"entry\",\"version\":\"1.0.0\",\"description\":\"Please describe the basic information.\",\"main\":\"\",\"author\":\"\",\"license\":\"\",\"dependencies\":{\"lingxia\":\"file:../../../lingxia-sdk/harmony/lingxia\"}} at undefined", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868820519962}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9f70a21-f47d-4992-903c-15d5dd937b40", "name": "entry : default@CollectDebugSymbol cost memory 0.20169830322265625", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868820684490}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd38d7d2-b00b-443d-959c-a3beaa37f7f1", "name": "runTaskFromQueue task cost before running: 7 s 294 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868820836254}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a2043c65-6345-4c0c-93ab-8d6d950542f4", "name": "Finished :entry:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868815409563, "endTime": 652868820932019, "totalTime": 5388346}, "additional": {"logType": "info", "children": [], "durationId": "d6ec67f7-a4cc-4a15-92c3-472affa8b3cf"}}, {"head": {"id": "70ba8e39-78e5-4f8f-bae8-4e88d38b4a56", "name": "entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868823445732, "endTime": 652868824125579}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "entry", "category": "Hook", "taskRunReasons": [], "detailId": "aa097899-6bbc-48fd-b382-e59f7246a6f8", "logId": "bfe05c25-f822-48af-a7c0-8fd0cde56bff"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "aa097899-6bbc-48fd-b382-e59f7246a6f8", "name": "create entry:assembleHap task", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868823363434}, "additional": {"logType": "detail", "children": [], "durationId": "70ba8e39-78e5-4f8f-bae8-4e88d38b4a56"}}, {"head": {"id": "72a06039-16ac-4f3c-a53b-a399367f7187", "name": "Executing task :entry:assembleHap", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868823458667}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43823518-69a5-4845-ad34-c0b0f68f7e19", "name": "entry : assembleHap cost memory 0.01160430908203125", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868823647615}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01131909-bdb2-40a0-bb1c-56df9bb0ea62", "name": "runTaskFromQueue task cost before running: 7 s 297 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868823897659}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bfe05c25-f822-48af-a7c0-8fd0cde56bff", "name": "Finished :entry:assembleHap", "description": "Assemble the task for the packaged HAP file.", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868823445732, "endTime": 652868824125579, "totalTime": 361627}, "additional": {"logType": "info", "children": [], "durationId": "70ba8e39-78e5-4f8f-bae8-4e88d38b4a56"}}, {"head": {"id": "08db4d01-001e-4ca5-b52d-47d476dd0071", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868826813424, "endTime": 652868826843294}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "ac45cd00-0846-4c28-8a6c-f16e9cc96999", "logId": "33751876-9145-41ba-a544-e28cd5214e5c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "33751876-9145-41ba-a544-e28cd5214e5c", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868826813424, "endTime": 652868826843294}, "additional": {"logType": "info", "children": [], "durationId": "08db4d01-001e-4ca5-b52d-47d476dd0071"}}, {"head": {"id": "62d4e48f-d20b-4900-842e-e2c2f1ad58ed", "name": "BUILD SUCCESSFUL in 7 s 300 ms ", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868826896057}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "8ffe848e-e336-4f16-a040-6b9f7d2f01d6", "name": "assembleHap", "description": "", "type": "mark"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652861526935203, "endTime": 652868827290667}, "additional": {"time": {"year": 2025, "month": 7, "day": 31, "hour": 21, "minute": 44}, "completeCommand": "{\"prop\":[],\"_\":[\"assembleHap\"]};assembleHap", "hvigorVersion": "5.17.2", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "4e67eb8f-a0b1-4f51-bdf0-57f96af113e8", "name": "There is no need to refresh cache, since the incremental task entry:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868827329293}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "055bd03c-fecf-4b1c-89b9-9c16364d812e", "name": "There is no need to refresh cache, since the incremental task lingxia:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868827506820}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4dca55bb-0ab3-49fa-82cf-ae9f4c9d53c0", "name": "Update task entry:default@CreateModuleInfo output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/generated/profile/default/ModuleInfo.ts cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868827669962}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b3fa5fc7-5502-49be-871c-d5187b0a6390", "name": "Incremental task entry:default@CreateModuleInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868828109508}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7ee277db-261a-4cdd-b88c-b09317a89695", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateMetadata is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868828273856}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72594926-f5f8-4e31-aa0d-99bd3840d298", "name": "There is no need to refresh cache, since the incremental task entry:default@GeneratePkgContextInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868828449845}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1764d829-ddde-412a-b9be-93b8673280df", "name": "Update task entry:default@ProcessIntegratedHsp output file:/Users/<USER>/github/LingXia/examples/harmony/build/cache/default/integrated_hsp/integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868829109995}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b10eba08-4ea9-493d-bcd7-969b7a92734a", "name": "Incremental task entry:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868829469846}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cfcf750b-f23e-4484-9530-bd95ab52536f", "name": "Update task lingxia:default@CreateHarBuildProfile input file:/Users/<USER>/github/LingXia/examples/harmony/AppScope/app.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868829638290}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9ad38755-d189-4154-8736-734e6d1e96e5", "name": "Update task lingxia:default@CreateHarBuildProfile input file:/Users/<USER>/github/LingXia/examples/harmony/build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868829884527}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "85ef8b4c-51f7-4e72-bc86-7cc503c80f2e", "name": "Update task lingxia:default@CreateHarBuildProfile output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/BuildProfile.ets cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868830085161}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "771ec1f3-1b17-40db-8f37-c96e20220011", "name": "Incremental task lingxia:default@CreateHarBuildProfile post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868830287786}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0ffc0925-a7c9-446a-bb43-2d34d5ba959c", "name": "Update task lingxia:default@MergeProfile input file:/Users/<USER>/github/LingXia/examples/harmony/AppScope/app.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868830410692}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ae26d23b-cf11-43f6-8c81-b18336ca1be9", "name": "Update task lingxia:default@MergeProfile input file:/Users/<USER>/github/LingXia/examples/harmony/build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868830579054}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec2fe7e4-86dc-4f73-b63a-fe1a6ad86e7b", "name": "Update task lingxia:default@MergeProfile input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/module.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868830739586}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f55cf67d-cf03-4b31-9fd2-5f2b1a0123ec", "name": "Update task lingxia:default@MergeProfile output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/merge_profile/default/module.json cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868830915166}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f7418a3-f680-4b8f-aed2-7c1f5576de24", "name": "Incremental task lingxia:default@MergeProfile post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868831148882}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1ee916dc-7c37-4ca1-a53a-652ce6690f55", "name": "Update task entry:default@SyscapTransform input file:/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/toolchains/syscap_tool cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868831298603}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7c833ba-4c1f-4f09-b4d4-cb8c95522e58", "name": "Update task entry:default@SyscapTransform input file:/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/device-define cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868831605284}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d03aee2f-237c-4cbe-9c48-645fbfba94cc", "name": "Update task entry:default@SyscapTransform output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/syscap/default/rpcid.sc cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868832333240}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "147e5424-f2c4-420c-86d0-4179e86ba8e7", "name": "Incremental task entry:default@SyscapTransform post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868832648153}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db07ae8c-350b-44a7-814f-79b94a3817c3", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessRouterMap is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868832772328}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c074dc1d-d2e5-4a04-b23c-64b66930f455", "name": "There is no need to refresh cache, since the incremental task entry:default@CreateBuildProfile is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868832852138}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87228184-e2dc-4601-b504-c93febd357f6", "name": "Update task entry:default@MergeProfile input file:/Users/<USER>/github/LingXia/examples/harmony/AppScope/app.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868832968444}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39a83b14-36d5-4b87-ad5f-2ac3df90d277", "name": "Update task entry:default@MergeProfile input file:/Users/<USER>/github/LingXia/examples/harmony/build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868833189012}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d8e299f-a1e4-44cf-89bb-b0f4cacddf0a", "name": "Update task entry:default@MergeProfile input file:/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/module.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868833363165}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "95be2d3b-db8e-4ae2-8b81-6c3c168a815e", "name": "Update task entry:default@MergeProfile input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/merge_profile/default/module.json cache from map.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868833506209}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "27865568-e84b-4b2f-86fa-c714f81e43df", "name": "Update task entry:default@MergeProfile output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/merge_profile/default/module.json cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868833599441}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5100f132-8298-4bb9-963f-f4277ab85c2f", "name": "Incremental task entry:default@MergeProfile post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868833774886}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1325694b-b403-41ca-aed4-3a450a4699b0", "name": "There is no need to refresh cache, since the incremental task entry:default@GenerateLoaderJson is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868833860161}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75d97215-dfa4-4167-ae84-91cca98eb199", "name": "There is no need to refresh cache, since the incremental task entry:default@MakePackInfo is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868833908499}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2aad230f-e73b-4639-a28e-3b81e4a82713", "name": "Update task entry:default@ProcessProfile input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/merge_profile/default/module.json cache from map.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868833964338}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11d9aada-8e34-47ed-8227-8b0ecf8e3780", "name": "Update task entry:default@ProcessProfile output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/process_profile/default/module.json cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868834042448}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04a7589a-7ddd-4517-b4f5-89074784056b", "name": "Incremental task entry:default@ProcessProfile post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868834355015}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "685c829d-83aa-417e-bc85-3e120910953a", "name": "There is no need to refresh cache, since the incremental task lingxia:default@ProcessLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868834476621}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a21bc648-21ba-4919-89ee-2c478853b1b1", "name": "There is no need to refresh cache, since the incremental task entry:default@ProcessResource is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868834572896}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d120cd16-1cf1-402b-ac7a-2d67a805bcc2", "name": "There is no need to refresh cache, since the incremental task lingxia:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868834653740}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1280d8ba-d36e-4534-9d21-118ec2d64533", "name": "Update task entry:default@CompileResource input file:/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources cache from map.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868835643742}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4064f2c5-dc5f-4391-8434-3503a5850d52", "name": "Update task entry:default@CompileResource input file:/Users/<USER>/github/LingXia/examples/harmony/AppScope/resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868835813485}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "270e15fa-0e58-47e2-a660-81ee502856ae", "name": "Update task entry:default@CompileResource input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/process_profile/default/module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868836864873}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61b4cdd4-3cca-4f85-b685-f056bf2e460e", "name": "Update task entry:default@CompileResource input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resConfig.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868837036631}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1f6c6a1-734e-4435-a9a8-bfd27b93142e", "name": "Update task entry:default@CompileResource output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868837163550}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3378ab8e-845b-4721-b238-be81a5ac1af3", "name": "Update task entry:default@CompileResource output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/generated/r/default/ResourceTable.h cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868842336090}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ea01ce6-8bfe-43c5-86a4-819bbbd15aa3", "name": "Update task entry:default@CompileResource output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/generated/r/default cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868842656923}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6891fe08-a3e3-4b2e-a779-39f0b5a064d8", "name": "Incremental task entry:default@CompileResource post-execution cost:9 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868843053253}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fb4f3f12-f164-4ea1-892b-62eda7d63163", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868844380079}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "266f82ea-817b-4eb5-b820-7808653756de", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/Index.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868844833147}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ccc15aba-6df5-4879-9474-e2390b4ae4ae", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868845004655}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b789956-7255-43c7-89de-6574b51f7b99", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile cache from map.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868846045719}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d030f863-b127-4efc-8e6d-d9647fb41dce", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868846159990}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f870401b-7f3b-4406-a2e6-8b9dd8d6a284", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868846299214}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a8ca7d7-d58e-498c-947a-b11ac2ced4dd", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/base/profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868846441662}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c629bb7f-289d-4b82-9fe8-ce10d05a9645", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868846780467}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4f05d6bf-ed85-4cb0-a636-7645add86827", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default/pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868847398983}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3574cb7b-1726-4f84-8e84-05cf08db26ae", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/generated/profile/default/BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868847539616}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "549af736-bcf2-4f30-94f5-c8596a4b33dc", "name": "Update task entry:default@CompileArkTS input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868847680523}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c55c2f26-a6d4-4bf6-b699-4b2ab3e991ba", "name": "Update task entry:default@CompileArkTS output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader_out/default/ets cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868847818488}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce69bd1d-e888-4a45-9653-6f92b690206a", "name": "Incremental task entry:default@CompileArkTS post-execution cost:6 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868848302540}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "10b0410b-ce49-4c91-b97f-8d4a69af6733", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868849842099}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b2ebf5b1-f76c-48ed-994e-c88cec5088b0", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/Index.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868850303807}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3fe4c4a-af11-4d16-9d13-a9854b6ac082", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/rawfile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868850454516}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "106a10b5-8534-46c1-a111-3c4efed8091c", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/ResourceTable.txt cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868852347231}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "933e1d79-df18-4552-a429-04efbb107683", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868852568006}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c14bab0-37ef-4ff6-a034-52b988defd2c", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources/base/profile cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868852742580}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64107568-1b45-4e88-a39e-6d615feff4e9", "name": "Update task entry:default@BuildJS input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default/pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868853284687}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "40b8f660-8c13-44b2-b887-96dd860046bd", "name": "Update task entry:default@BuildJS output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader_out/default/js cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868853499147}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2456b6ee-0d13-449b-bfb5-6888205d252d", "name": "Incremental task entry:default@BuildJS post-execution cost:6 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868853799380}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fc3e7996-63fd-4437-bbf9-5bc7cf155d9f", "name": "Update task entry:default@ProcessLibs input file:/Users/<USER>/github/LingXia/examples/harmony/entry/libs cache from map.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868853956059}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "46624cd8-67a0-4a3f-8dda-9538353c1ae4", "name": "Update task entry:default@ProcessLibs input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/libs/default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868854029469}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "50b7b673-a4e3-494f-ae52-2a3b946d1823", "name": "Update task entry:default@ProcessLibs input file:/Users/<USER>/github/LingXia/examples/harmony/build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868854226618}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dff4e3e8-ce69-49f9-a37d-e94cdda48a04", "name": "Update task entry:default@ProcessLibs input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868854370960}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e31d7284-0355-4d32-94bc-6aeeff69a7c5", "name": "Update task entry:default@ProcessLibs output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/libs/default cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868854498162}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1c118974-9e79-49c5-924a-ed24c61c485e", "name": "Incremental task entry:default@ProcessLibs post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868854857146}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31eb22a6-81fb-4076-b407-a3c346181ead", "name": "Update task entry:default@DoNativeStrip input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/libs/default cache from map.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868854968451}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a382696c-f0d5-4a1e-9611-0eeab78731ee", "name": "Update task entry:default@DoNativeStrip output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/stripped_native_libs/default cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868855047687}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6865fe27-2a18-4e31-aaa0-9f86cec2974d", "name": "Incremental task entry:default@DoNativeStrip post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868855811978}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e75847ad-5664-45ad-8939-57e2fa32942f", "name": "Update task entry:default@CacheNativeLibs input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/stripped_native_libs/default cache from map.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868855953778}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d05e0558-9125-494b-8130-893619aa7fed", "name": "Update task entry:default@CacheNativeLibs input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/libs/default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868856103254}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58dfdb32-06e5-415c-b518-f4dade920374", "name": "Update task entry:default@CacheNativeLibs output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/patch/default/base_native_libs.json cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868857243733}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6a42716-5a0d-4f81-9799-089efbe1d9f7", "name": "Incremental task entry:default@CacheNativeLibs post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868857678120}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "136cfc50-ddd6-4ce4-8bf2-6c0b94af79c7", "name": "Update task entry:default@GeneratePkgModuleJson input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/module.json cache from map.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868857806092}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a703695d-08d2-4208-88f3-4c4c05cd9647", "name": "Update task entry:default@GeneratePkgModuleJson output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/package/default/module.json cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868857889030}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2c9c58f-f5c1-4aab-9280-b85d1b550070", "name": "Incremental task entry:default@GeneratePkgModuleJson post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868858113924}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "acaf517b-53ee-44ff-95a4-4dd66a6a4a41", "name": "Update task entry:default@PackageHap input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/stripped_native_libs/default cache from map.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868858936306}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6045e61e-9e51-4959-94ed-16335e54140e", "name": "Update task entry:default@PackageHap input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868859107896}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4bed643c-452a-4ae9-91f4-577605b5a45f", "name": "Update task entry:default@PackageHap input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868859484902}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "382339ca-9d17-487e-80f4-4c458fb185b4", "name": "Update task entry:default@PackageHap input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/resources.index cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868864239603}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "71031008-45b7-4da7-8c8c-4adf96797e9b", "name": "Update task entry:default@PackageHap input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/outputs/default/pack.info cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868864676428}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b25068e-f192-49dc-974f-bb95a485b15b", "name": "Update task entry:default@PackageHap input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader_out/default/ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868865013671}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "72ae134d-62a0-4ec0-83a2-721c71c02314", "name": "Update task entry:default@PackageHap input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader/default/pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868865746338}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b6620c2a-9d27-45d0-8f4d-6a60f5ed0288", "name": "Update task entry:default@PackageHap input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader_out/default/ets/sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868866135251}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "55521197-4053-4585-aae5-84e25ffa4189", "name": "Update task entry:default@PackageHap input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/generated/profile/default/BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868866423683}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0a311ef-e3d9-4d01-9bc4-ba1911bad635", "name": "Update task entry:default@PackageHap output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/outputs/default/entry-default-unsigned.hap cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868867162575}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2ce787ff-7a9b-40cd-b4a8-43c352a8cd8f", "name": "Update task entry:default@PackageHap output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/source_map/default/sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868867401122}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16312363-857a-4a5e-9acf-b8553eadbcfe", "name": "Update task entry:default@PackageHap output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/outputs/default/mapping/sourceMaps.map cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868867603344}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "908e5a63-c03d-4cd7-a767-746d0dc57b23", "name": "Incremental task entry:default@PackageHap post-execution cost:10 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868867859192}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0260137a-1052-4254-ab3a-81aec427587e", "name": "Update task entry:default@SignHap input file:/Users/<USER>/.ohos/config/default_harmony_Ox-3HNfvclQdbq4KOWhaXIc2hg7i7fx0_2L33NsWpQw=.cer cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868868063987}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d2c20f4-cb8a-4088-a51c-cb661ca27ebf", "name": "Update task entry:default@SignHap input file:/Users/<USER>/.ohos/config/default_harmony_Ox-3HNfvclQdbq4KOWhaXIc2hg7i7fx0_2L33NsWpQw=.p7b cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868868219829}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "856967a8-2b43-490a-882e-71547230dff0", "name": "Update task entry:default@SignHap input file:/Users/<USER>/.ohos/config/default_harmony_Ox-3HNfvclQdbq4KOWhaXIc2hg7i7fx0_2L33NsWpQw=.p12 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868868375132}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e0a42fc8-c8c5-4d62-8e87-dfde9bd34800", "name": "Update task entry:default@SignHap input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/outputs/default/entry-default-unsigned.hap cache from map.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868868530635}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a9eef957-096c-4d79-b005-b35e0b3894f8", "name": "Update task entry:default@SignHap output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/outputs/default/entry-default-signed.hap cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868868626777}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c7f8ce0-2e6b-43b2-9803-58d282b4c47a", "name": "Incremental task entry:default@SignHap post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868868821515}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "240a25e6-497d-4264-9e14-2c04c94cfd4b", "name": "Update task entry:default@CollectDebugSymbol input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader_out/default/ets/sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868870783812}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "381f7bd8-24fe-43e7-b94c-6b644da34d5e", "name": "Update task entry:default@CollectDebugSymbol input file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/libs/default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868871119223}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "22350cb2-935c-4ad1-b8df-235c9dd26162", "name": "Update task entry:default@CollectDebugSymbol output file:/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/outputs/default/symbol cache.", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868871737013}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1952e7af-f280-4c80-8c70-f79224d3fc92", "name": "Incremental task entry:default@CollectDebugSymbol post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 50614, "tid": "Main Thread", "startTime": 652868872264238}, "additional": {"logType": "debug", "children": []}}], "workLog": []}