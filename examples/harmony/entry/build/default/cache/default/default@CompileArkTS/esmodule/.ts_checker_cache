{"runtimeOS": "HarmonyOS", "sdkInfo": "false:17:5.0.5.165:Release", "fileList": {"/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.AbilityConstant.d.ts": {"mtimeMs": 978278400000, "children": [], "parent": ["/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/ets/entryability/EntryAbility.ets", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.UIAbility.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.Ability.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/UIAbilityContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.EnvironmentCallback.d.ts", "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/ets/entryability/EntryAbility.ets"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.ConfigurationConstant.d.ts": {"mtimeMs": 978278400000, "children": [], "parent": ["/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/ets/entryability/EntryAbility.ets", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.Configuration.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/ApplicationContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.window.d.ts", "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/ets/entryability/EntryAbility.ets"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.UIAbility.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.Ability.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.AbilityConstant.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/UIAbilityContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.rpc.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.Want.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.window.d.ts"], "parent": ["/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/ets/entryability/EntryAbility.ets", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/UIAbilityContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.AbilityLifecycleCallback.d.ts", "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/ets/entryability/EntryAbility.ets"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.Want.d.ts": {"mtimeMs": 978278400000, "children": [], "parent": ["/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/ets/entryability/EntryAbility.ets", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.UIAbility.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/UIAbilityContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.bundle.bundleManager.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/ability/abilityResult.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/ApplicationContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.data.unifiedDataChannel.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/UIExtensionContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.ability.featureAbility.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/ability/startAbilityParameter.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.bundle.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.dialogRequest.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/build-tools/ets-loader/declarations/embedded_component.d.ts", "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/ets/entryability/EntryAbility.ets"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.hilog.d.ts": {"mtimeMs": 978278400000, "children": [], "parent": ["/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/ets/entryability/EntryAbility.ets", "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/LxApp.ets", "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/LxAppContainer.ets", "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/WebView.ets", "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/NavigationBar.ets", "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/TabBar.ets", "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/NativeBridge.ets", "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/LxAppNavigation.ets", "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/ets/pages/Index.ets", "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/ets/entryability/EntryAbility.ets"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.window.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.base.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/BaseContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.multimedia.image.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.UIContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.ConfigurationConstant.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.bundle.bundleManager.d.ts"], "parent": ["/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/ets/entryability/EntryAbility.ets", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.UIAbility.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/UIAbilityContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.AbilityLifecycleCallback.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.ability.featureAbility.d.ts", "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/LxApp.ets", "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/LxAppContainer.ets", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/build-tools/ets-loader/declarations/folder_stack.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/build-tools/ets-loader/declarations/navigation.d.ts", "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/ets/entryability/EntryAbility.ets"], "error": false}, "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/Index.ets": {"mtimeMs": 1753953304883.0427, "children": ["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/LxApp.ets", "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/LxAppNavigation.ets"], "parent": ["/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/ets/entryability/EntryAbility.ets", "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/ets/pages/Index.ets", "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/ets/entryability/EntryAbility.ets", "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/ets/pages/Index.ets"], "error": false}, "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/ets/entryability/EntryAbility.ets": {"mtimeMs": 1752040554877.3013, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.AbilityConstant.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.ConfigurationConstant.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.UIAbility.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.Want.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.hilog.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.window.d.ts", "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/Index.ets"], "parent": [], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.Ability.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.AbilityConstant.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.Configuration.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.UIAbility.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/UIAbilityContext.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundleManager/AbilityInfo.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/ability/abilityResult.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.base.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/ability/connectOptions.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundleManager/HapModuleInfo.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/Context.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.Want.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.StartOptions.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.OpenLinkOptions.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.Configuration.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.UIAbility.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.multimedia.image.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.dialogRequest.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.AbilityConstant.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/AbilityStartCallback.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.window.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.AtomicServiceOptions.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/UIServiceProxy.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/UIServiceExtensionConnectCallback.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.UIAbility.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.observer.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.common.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/EmbeddableUIAbilityContext.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.rpc.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.base.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.UIAbility.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/ability/connectOptions.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.multimedia.image.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.Configuration.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.ConfigurationConstant.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.Ability.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/UIAbilityContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/ExtensionContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/AbilityStageContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.EnvironmentCallback.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundleManager/AbilityInfo.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundleManager/ApplicationInfo.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundleManager/Metadata.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.bundle.bundleManager.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundleManager/Skill.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/UIAbilityContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.bundle.bundleManager.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundleManager/HapModuleInfo.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/ability/abilityResult.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.Want.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/UIAbilityContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.common.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/UIExtensionContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.ability.featureAbility.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/AbilityStartCallback.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/PhotoEditorExtensionContext.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.base.d.ts": {"mtimeMs": 978278400000, "children": [], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/UIAbilityContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.bundle.bundleManager.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.rpc.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/Context.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.resourceManager.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.multimedia.image.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/ApplicationContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.window.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.UIContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.mediaquery.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.observer.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.promptAction.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.router.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.componentSnapshot.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.dragController.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.data.unifiedDataChannel.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/UIExtensionContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/ability/dataAbilityHelper.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.ability.featureAbility.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/app/context.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.bundle.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.data.rdb.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.multimodalInput.pointer.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.appManager.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.web.webview.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.security.cert.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.security.cryptoFramework.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.print.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.file.fs.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.util.stream.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.events.emitter.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/graphics3d/SceneResources.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/build-tools/ets-loader/declarations/embedded_component.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/build-tools/ets-loader/declarations/nav_destination.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/build-tools/ets-loader/declarations/navigation.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/build-tools/ets-loader/declarations/pattern_lock.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/ability/connectOptions.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundleManager/ElementName.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.rpc.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/UIAbilityContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.common.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/UIExtensionContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.ability.featureAbility.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundleManager/HapModuleInfo.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundleManager/AbilityInfo.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundleManager/ExtensionAbilityInfo.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundleManager/Metadata.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.bundle.bundleManager.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/UIAbilityContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.bundle.bundleManager.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundleManager/BundleInfo.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/ExtensionContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/AbilityStageContext.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/Context.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundleManager/ApplicationInfo.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.base.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.resourceManager.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/BaseContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/EventHub.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/ApplicationContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.contextConstant.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/UIAbilityContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/ApplicationContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.common.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/ExtensionContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/AbilityStageContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.print.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/build-tools/ets-loader/declarations/common.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.StartOptions.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.contextConstant.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.multimedia.image.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.bundle.bundleManager.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/UIAbilityContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/UIExtensionContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.AtomicServiceOptions.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.OpenLinkOptions.d.ts": {"mtimeMs": 978278400000, "children": [], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/UIAbilityContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/UIExtensionContext.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.multimedia.image.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.base.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.graphics.colorSpaceManager.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.resourceManager.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.rpc.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/UIAbilityContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.drawableDescriptor.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.window.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.UIContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.graphics.drawing.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.componentSnapshot.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.data.unifiedDataChannel.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.StartOptions.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.bundle.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/PhotoEditorExtensionContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.multimodalInput.pointer.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.web.webview.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/build-tools/ets-loader/declarations/common.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.dialogRequest.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.Want.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/UIAbilityContext.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/AbilityStartCallback.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/ability/abilityResult.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/UIAbilityContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.common.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.AtomicServiceOptions.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.StartOptions.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/UIAbilityContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/UIExtensionContext.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/UIServiceProxy.d.ts": {"mtimeMs": 978278400000, "children": [], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/UIAbilityContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.common.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/UIExtensionContext.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/UIServiceExtensionConnectCallback.d.ts": {"mtimeMs": 978278400000, "children": [], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/UIAbilityContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.common.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/UIExtensionContext.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundleManager/ApplicationInfo.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundleManager/Metadata.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/global/resource.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.bundle.bundleManager.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundleManager/AbilityInfo.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.bundle.bundleManager.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundleManager/BundleInfo.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundleManager/ExtensionAbilityInfo.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/Context.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundleManager/Metadata.d.ts": {"mtimeMs": 978278400000, "children": [], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundleManager/AbilityInfo.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundleManager/ApplicationInfo.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.bundle.bundleManager.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundleManager/HapModuleInfo.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundleManager/ExtensionAbilityInfo.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.bundle.bundleManager.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.base.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundleManager/ApplicationInfo.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundleManager/Metadata.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundleManager/ElementName.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.Want.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundleManager/AbilityInfo.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundleManager/BundleInfo.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundleManager/HapModuleInfo.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundleManager/ExtensionAbilityInfo.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundleManager/Skill.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundleManager/AbilityInfo.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundleManager/ApplicationInfo.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundleManager/BundleInfo.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundleManager/HapModuleInfo.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundleManager/ExtensionAbilityInfo.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.window.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.StartOptions.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/ProcessInformation.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundleManager/Skill.d.ts": {"mtimeMs": 978278400000, "children": [], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundleManager/AbilityInfo.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.bundle.bundleManager.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundleManager/ExtensionAbilityInfo.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/global/resource.d.ts": {"mtimeMs": 978278400000, "children": [], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundleManager/ApplicationInfo.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.resourceManager.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/Graphics.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/build-tools/ets-loader/declarations/units.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundleManager/ElementName.d.ts": {"mtimeMs": 978278400000, "children": [], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.bundle.bundleManager.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/ability/connectOptions.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundleManager/BundleInfo.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundleManager/ApplicationInfo.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundleManager/HapModuleInfo.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.bundle.bundleManager.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.bundle.bundleManager.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundleManager/ExtensionAbilityInfo.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundleManager/ApplicationInfo.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundleManager/Metadata.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.bundle.bundleManager.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundleManager/Skill.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.bundle.bundleManager.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundleManager/HapModuleInfo.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/ExtensionContext.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.resourceManager.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/global/rawFileDescriptor.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/global/resource.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.base.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.drawableDescriptor.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/Context.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.multimedia.image.d.ts", "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/LxApp.ets"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/BaseContext.d.ts": {"mtimeMs": 978278400000, "children": [], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/Context.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.window.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.common.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/app/context.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.data.rdb.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/EventHub.d.ts": {"mtimeMs": 978278400000, "children": [], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/Context.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.common.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/ApplicationContext.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.base.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/Context.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.AbilityLifecycleCallback.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.EnvironmentCallback.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.ApplicationStateChangeCallback.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/ProcessInformation.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.ConfigurationConstant.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.Want.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/Context.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.common.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.contextConstant.d.ts": {"mtimeMs": 978278400000, "children": [], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/Context.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.StartOptions.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/global/rawFileDescriptor.d.ts": {"mtimeMs": 978278400000, "children": [], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.resourceManager.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.drawableDescriptor.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.multimedia.image.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.resourceManager.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/build-tools/ets-loader/declarations/image.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.graphics.colorSpaceManager.d.ts": {"mtimeMs": 978278400000, "children": [], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.multimedia.image.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.AbilityLifecycleCallback.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.UIAbility.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.window.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/ApplicationContext.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.EnvironmentCallback.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.AbilityConstant.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.Configuration.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/ApplicationContext.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.ApplicationStateChangeCallback.d.ts": {"mtimeMs": 978278400000, "children": [], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/ApplicationContext.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/ProcessInformation.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.appManager.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.bundle.bundleManager.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/ApplicationContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.appManager.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.UIContext.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.font.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.mediaquery.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.inspector.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.observer.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.promptAction.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.router.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.componentUtils.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.node.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.animator.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.base.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.measure.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.componentSnapshot.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.dragController.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.multimedia.image.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.common.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.multimodalInput.pointer.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.window.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/BuilderNode.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/FrameNode.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/ComponentContent.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/NodeController.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/XComponentNode.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/build-tools/ets-loader/declarations/common.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.font.d.ts": {"mtimeMs": 978278400000, "children": [], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.UIContext.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.mediaquery.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.base.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.UIContext.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.inspector.d.ts": {"mtimeMs": 978278400000, "children": [], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.UIContext.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.observer.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.base.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/UIAbilityContext.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.UIContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/build-tools/ets-loader/declarations/common.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.promptAction.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.base.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.UIContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/build-tools/ets-loader/declarations/action_sheet.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.router.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.base.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.UIContext.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.componentUtils.d.ts": {"mtimeMs": 978278400000, "children": [], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.UIContext.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.node.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/BuilderNode.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/BuilderNode.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/NodeController.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/FrameNode.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/FrameNode.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/Graphics.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/Graphics.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/RenderNode.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/XComponentNode.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/Content.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/ComponentContent.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/NodeContent.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.UIContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/build-tools/ets-loader/declarations/content_slot.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/build-tools/ets-loader/declarations/node_container.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.animator.d.ts": {"mtimeMs": 978278400000, "children": [], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.UIContext.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.measure.d.ts": {"mtimeMs": 978278400000, "children": [], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.UIContext.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.componentSnapshot.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.base.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.multimedia.image.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.UIContext.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.dragController.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.base.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.data.unifiedDataChannel.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.UIContext.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.common.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/UIAbilityContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/UIExtensionContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/AbilityStageContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/ApplicationContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/BaseContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/Context.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/ExtensionContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/FormExtensionContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/EventHub.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/ability/dataAbilityHelper.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/ability/abilityResult.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/AbilityStartCallback.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/ability/connectOptions.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/VpnExtensionContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/EmbeddableUIAbilityContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/PhotoEditorExtensionContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/UIServiceProxy.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/UIServiceExtensionConnectCallback.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.UIContext.d.ts", "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/LxAppContainer.ets"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.multimodalInput.pointer.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.base.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.multimedia.image.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.UIContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/build-tools/ets-loader/declarations/common.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/BuilderNode.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.UIContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/FrameNode.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/Graphics.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.node.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/ComponentContent.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/XComponentNode.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/NodeController.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.UIContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/FrameNode.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/Graphics.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.node.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/FrameNode.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.UIContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/RenderNode.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/Graphics.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/Graphics.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/ComponentContent.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.node.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/BuilderNode.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/NodeController.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/XComponentNode.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/NodeContent.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/build-tools/ets-loader/declarations/canvas.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/Graphics.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.graphics.drawing.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.graphics.common2D.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/global/resource.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.node.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/BuilderNode.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/FrameNode.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/RenderNode.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/NodeController.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/build-tools/ets-loader/declarations/common.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/build-tools/ets-loader/declarations/particle.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/build-tools/ets-loader/declarations/units.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/RenderNode.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/Graphics.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.node.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/FrameNode.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/XComponentNode.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.UIContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/BuilderNode.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/FrameNode.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.node.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/Content.d.ts": {"mtimeMs": 978278400000, "children": [], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.node.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/ComponentContent.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/NodeContent.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/ComponentContent.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/BuilderNode.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/Content.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.UIContext.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.node.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/FrameNode.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/build-tools/ets-loader/declarations/common.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/NodeContent.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/Content.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/FrameNode.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.node.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.graphics.drawing.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.multimedia.image.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.graphics.common2D.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/Graphics.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/build-tools/ets-loader/declarations/canvas.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/build-tools/ets-loader/declarations/image.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.graphics.text.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.graphics.common2D.d.ts": {"mtimeMs": 978278400000, "children": [], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/Graphics.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.graphics.drawing.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.graphics.text.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.data.unifiedDataChannel.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.base.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.multimedia.image.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.Want.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.dragController.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/build-tools/ets-loader/declarations/common.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/UIExtensionContext.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/ability/abilityResult.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.base.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/ability/connectOptions.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/ExtensionContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.Want.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.StartOptions.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.AtomicServiceOptions.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.OpenLinkOptions.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/UIServiceProxy.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/UIServiceExtensionConnectCallback.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.common.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/AbilityStageContext.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundleManager/HapModuleInfo.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.Configuration.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/Context.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.common.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/ExtensionContext.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundleManager/HapModuleInfo.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.Configuration.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/Context.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundleManager/ExtensionAbilityInfo.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.common.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/UIExtensionContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/FormExtensionContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/VpnExtensionContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/PhotoEditorExtensionContext.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/FormExtensionContext.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/ExtensionContext.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.common.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/ability/dataAbilityHelper.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.base.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/data/rdb/resultSet.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/ability/dataAbilityOperation.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/ability/dataAbilityResult.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.data.dataAbility.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.data.rdb.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.common.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.ability.featureAbility.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/VpnExtensionContext.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/ExtensionContext.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.common.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/EmbeddableUIAbilityContext.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/UIAbilityContext.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.common.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/PhotoEditorExtensionContext.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/ExtensionContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/ability/abilityResult.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.multimedia.image.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.common.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/data/rdb/resultSet.d.ts": {"mtimeMs": 978278400000, "children": [], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/ability/dataAbilityHelper.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.data.rdb.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/ability/dataAbilityOperation.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.ability.featureAbility.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.data.dataAbility.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.data.rdb.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/ability/dataAbilityHelper.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/ability/dataAbilityResult.d.ts": {"mtimeMs": 978278400000, "children": [], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/ability/dataAbilityHelper.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.data.dataAbility.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.data.rdb.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/ability/dataAbilityHelper.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/ability/dataAbilityOperation.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.data.rdb.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.base.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/data/rdb/resultSet.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/BaseContext.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/ability/dataAbilityHelper.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/ability/dataAbilityOperation.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.data.dataAbility.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.ability.featureAbility.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.base.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.Want.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/ability/startAbilityParameter.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/ability/abilityResult.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/app/appVersionInfo.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/app/context.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/ability/dataAbilityHelper.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/ability/connectOptions.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/app/processInfo.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.window.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/ability/dataAbilityOperation.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/ability/startAbilityParameter.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.Want.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.ability.featureAbility.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/app/appVersionInfo.d.ts": {"mtimeMs": 978278400000, "children": [], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.ability.featureAbility.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/app/context.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/app/context.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.base.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundle/applicationInfo.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/app/processInfo.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundle/elementName.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/BaseContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundle/hapModuleInfo.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/app/appVersionInfo.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundle/abilityInfo.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.bundle.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.ability.featureAbility.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/app/processInfo.d.ts": {"mtimeMs": 978278400000, "children": [], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.ability.featureAbility.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/app/context.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundle/applicationInfo.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundle/moduleInfo.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundle/customizeData.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/app/context.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundle/abilityInfo.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.bundle.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundle/bundleInfo.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundle/elementName.d.ts": {"mtimeMs": 978278400000, "children": [], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/app/context.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundle/hapModuleInfo.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundle/abilityInfo.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/app/context.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundle/bundleInfo.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundle/abilityInfo.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundle/applicationInfo.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundle/customizeData.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.bundle.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/app/context.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundle/hapModuleInfo.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.bundle.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundle/bundleInfo.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.bundle.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.base.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundle/applicationInfo.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundle/abilityInfo.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.Want.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.multimedia.image.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundle/bundleInfo.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/app/context.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundle/abilityInfo.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundle/moduleInfo.d.ts": {"mtimeMs": 978278400000, "children": [], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundle/applicationInfo.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundle/customizeData.d.ts": {"mtimeMs": 978278400000, "children": [], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundle/applicationInfo.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundle/abilityInfo.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundle/bundleInfo.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundle/abilityInfo.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundle/applicationInfo.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/bundle/hapModuleInfo.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.bundle.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.appManager.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.base.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/ApplicationStateObserver.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/ProcessInformation.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/ProcessInformation.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/ApplicationStateObserver.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/AppStateData.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/AbilityStateData.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/ProcessData.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.appManager.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/AppStateData.d.ts": {"mtimeMs": 978278400000, "children": [], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/ApplicationStateObserver.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/AbilityStateData.d.ts": {"mtimeMs": 978278400000, "children": [], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/ApplicationStateObserver.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/ProcessData.d.ts": {"mtimeMs": 978278400000, "children": [], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/ApplicationStateObserver.d.ts"], "error": false}, "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/LxApp.ets": {"mtimeMs": 1753954054502.252, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.hilog.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.web.webview.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.window.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.resourceManager.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.taskpool.d.ts", "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/LxAppContainer.ets", "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/NativeBridge.ets", "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/WebView.ets"], "parent": ["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/Index.ets", "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/LxAppContainer.ets", "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/NativeBridge.ets", "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/LxAppNavigation.ets"], "error": true, "errorCodes": [28014]}, "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/LxAppNavigation.ets": {"mtimeMs": 1753953304886.7397, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.hilog.d.ts", "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/LxApp.ets", "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/LxAppContainer.ets"], "parent": ["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/Index.ets"], "error": true, "errorCodes": [28014]}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.web.webview.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.base.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.base.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.security.cert.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.multimedia.image.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.print.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.web.netErrorList.d.ts"], "parent": ["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/LxApp.ets", "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/LxAppContainer.ets", "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/WebView.ets", "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/NativeBridge.ets", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/build-tools/ets-loader/declarations/web.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.taskpool.d.ts": {"mtimeMs": 978278400000, "children": [], "parent": ["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/LxApp.ets"], "error": false}, "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/LxAppContainer.ets": {"mtimeMs": 1753953304885.8257, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.hilog.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.web.webview.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.window.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.common.d.ts", "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/LxApp.ets", "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/WebView.ets", "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/NavigationBar.ets", "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/TabBar.ets"], "parent": ["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/LxApp.ets", "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/LxAppNavigation.ets"], "error": true, "errorCodes": [28014]}, "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/NativeBridge.ets": {"mtimeMs": 1753953304886.952, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.hilog.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.web.webview.d.ts", "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/LxApp.ets", "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/WebView.ets"], "parent": ["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/LxApp.ets"], "error": false}, "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/WebView.ets": {"mtimeMs": 1753953304889.5916, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.hilog.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.web.webview.d.ts"], "parent": ["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/LxApp.ets", "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/LxAppContainer.ets", "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/NativeBridge.ets"], "error": true, "errorCodes": [28014]}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.security.cert.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.base.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.security.cryptoFramework.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.web.webview.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.print.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.base.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/Context.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.web.webview.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.web.netErrorList.d.ts": {"mtimeMs": 978278400000, "children": [], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.web.webview.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.security.cryptoFramework.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.base.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.security.cert.d.ts"], "error": false}, "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/NavigationBar.ets": {"mtimeMs": 1753953304888.3215, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.hilog.d.ts"], "parent": ["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/LxAppContainer.ets"], "error": false}, "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/TabBar.ets": {"mtimeMs": 1753953304889.1882, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.hilog.d.ts"], "parent": ["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/LxAppContainer.ets"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.file.fs.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.base.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.util.stream.d.ts"], "parent": ["/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/ets/pages/Index.ets"], "error": false}, "/Users/<USER>/github/LingXia/lingxia-webview/examples/harmony/entry/src/main/ets/pages/Index.ets": {"mtimeMs": 1753960222071.2974, "children": ["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/Index.ets", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.hilog.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.file.fs.d.ts"], "parent": [], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.util.stream.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.base.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.events.emitter.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.file.fs.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.events.emitter.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.base.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.util.stream.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/build-tools/ets-loader/declarations/action_sheet.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.promptAction.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.promptAction.d.ts"], "parent": [], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/build-tools/ets-loader/declarations/canvas.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.graphics.drawing.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/FrameNode.d.ts"], "parent": [], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.data.uniformTypeDescriptor.d.ts": {"mtimeMs": 978278400000, "children": [], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/build-tools/ets-loader/declarations/common.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.multimodalInput.intentionCode.d.ts": {"mtimeMs": 978278400000, "children": [], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/build-tools/ets-loader/declarations/common.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/ImageModifier.d.ts": {"mtimeMs": 978278400000, "children": [], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/build-tools/ets-loader/declarations/common.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/SymbolGlyphModifier.d.ts": {"mtimeMs": 978278400000, "children": [], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/build-tools/ets-loader/declarations/common.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.shape.d.ts": {"mtimeMs": 978278400000, "children": [], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/build-tools/ets-loader/declarations/common.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.graphics.uiEffect.d.ts": {"mtimeMs": 978278400000, "children": [], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/build-tools/ets-loader/declarations/common.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.theme.d.ts": {"mtimeMs": 978278400000, "children": [], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/build-tools/ets-loader/declarations/common.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/build-tools/ets-loader/declarations/with_theme.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/build-tools/ets-loader/declarations/common.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/application/Context.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.multimodalInput.pointer.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.multimedia.image.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.data.unifiedDataChannel.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.data.unifiedDataChannel.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.data.uniformTypeDescriptor.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.data.unifiedDataChannel.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.multimodalInput.intentionCode.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/ImageModifier.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/SymbolGlyphModifier.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.shape.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.shape.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.shape.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.shape.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.observer.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.observer.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.observer.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.UIContext.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/Graphics.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.graphics.uiEffect.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.graphics.uiEffect.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/ComponentContent.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.theme.d.ts"], "parent": [], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.graphics.scene.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/graphics3d/ScenePostProcessSettings.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/graphics3d/SceneTypes.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/graphics3d/SceneResources.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/graphics3d/SceneNodes.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/graphics3d/Scene.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/build-tools/ets-loader/declarations/component3d.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/build-tools/ets-loader/declarations/component3d.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.graphics.scene.d.ts"], "parent": [], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/graphics3d/ScenePostProcessSettings.d.ts": {"mtimeMs": 978278400000, "children": [], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.graphics.scene.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/graphics3d/SceneNodes.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/graphics3d/SceneTypes.d.ts": {"mtimeMs": 978278400000, "children": [], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.graphics.scene.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/graphics3d/SceneResources.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/graphics3d/SceneNodes.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/graphics3d/SceneResources.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/graphics3d/SceneTypes.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.base.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.graphics.scene.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/graphics3d/SceneNodes.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/graphics3d/Scene.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/graphics3d/SceneNodes.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/graphics3d/SceneResources.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/graphics3d/SceneTypes.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/graphics3d/ScenePostProcessSettings.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.graphics.scene.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/graphics3d/Scene.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/graphics3d/Scene.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/graphics3d/SceneResources.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/graphics3d/SceneNodes.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.graphics.scene.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/build-tools/ets-loader/declarations/content_slot.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.node.d.ts"], "parent": [], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/build-tools/ets-loader/declarations/embedded_component.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.Want.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.Want.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.base.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.base.d.ts"], "parent": [], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/build-tools/ets-loader/declarations/folder_stack.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.window.d.ts"], "parent": [], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.matrix4.d.ts": {"mtimeMs": 978278400000, "children": [], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/build-tools/ets-loader/declarations/image.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/build-tools/ets-loader/declarations/image.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.drawableDescriptor.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.graphics.drawing.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.graphics.drawing.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.matrix4.d.ts"], "parent": [], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/build-tools/ets-loader/declarations/nav_destination.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.base.d.ts"], "parent": [], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/build-tools/ets-loader/declarations/navigation.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.window.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.base.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.base.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.base.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.base.d.ts"], "parent": [], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/build-tools/ets-loader/declarations/node_container.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.node.d.ts"], "parent": [], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/build-tools/ets-loader/declarations/particle.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/Graphics.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/Graphics.d.ts"], "parent": [], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/build-tools/ets-loader/declarations/pattern_lock.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.base.d.ts"], "parent": [], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/CommonModifier.d.ts": {"mtimeMs": 978278400000, "children": [], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/build-tools/ets-loader/declarations/tabs.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/build-tools/ets-loader/declarations/tabs.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/CommonModifier.d.ts"], "parent": [], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.graphics.text.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.graphics.drawing.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.graphics.common2D.d.ts"], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/build-tools/ets-loader/declarations/text_common.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/build-tools/ets-loader/declarations/text_common.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.graphics.text.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.graphics.text.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.graphics.text.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.graphics.text.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.graphics.text.d.ts"], "parent": [], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.intl.d.ts": {"mtimeMs": 978278400000, "children": [], "parent": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/build-tools/ets-loader/declarations/time_picker.d.ts"], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/build-tools/ets-loader/declarations/time_picker.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.intl.d.ts"], "parent": [], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/build-tools/ets-loader/declarations/with_theme.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.arkui.theme.d.ts"], "parent": [], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/build-tools/ets-loader/declarations/units.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/global/resource.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/Graphics.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/Graphics.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/arkui/Graphics.d.ts"], "parent": [], "error": false}, "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/build-tools/ets-loader/declarations/web.d.ts": {"mtimeMs": 978278400000, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.web.webview.d.ts"], "parent": [], "error": false}, "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/ets/entryability/EntryAbility.ets": {"mtimeMs": 1752040554877.3013, "children": ["/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.AbilityConstant.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.ConfigurationConstant.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.UIAbility.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.app.ability.Want.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.hilog.d.ts", "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/api/@ohos.window.d.ts", "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/Index.ets"], "parent": [], "error": false}, "/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/ets/pages/Index.ets": {"mtimeMs": 1753964728116.6133, "children": ["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/Index.ets"], "parent": [], "error": false}}}