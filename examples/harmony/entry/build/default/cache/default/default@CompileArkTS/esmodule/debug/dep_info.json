{"resolveConflictMode": true, "depName2RootPath": {"lingxia": "/Users/<USER>/github/Ling<PERSON>ia/lingxia-sdk/harmony/lingxia", "@ohos/hypium": "/Users/<USER>/github/LingXia/examples/harmony/oh_modules/.ohpm/@ohos+hypium@1.0.21/oh_modules/@ohos/hypium", "@ohos/hamock": "/Users/<USER>/github/LingXia/examples/harmony/oh_modules/.ohpm/@ohos+hamock@1.0.0/oh_modules/@ohos/hamock"}, "depName2DepInfo": {"lingxia": {"pkgRootPath": "/Users/<USER>/github/Ling<PERSON>ia/lingxia-sdk/harmony/lingxia", "pkgName": "lingxia", "pkgVersion": "1.0.0"}, "@ohos/hypium": {"pkgRootPath": "/Users/<USER>/github/LingXia/examples/harmony/oh_modules/.ohpm/@ohos+hypium@1.0.21/oh_modules/@ohos/hypium", "pkgName": "@ohos/hypium", "pkgVersion": "1.0.21"}, "@ohos/hamock": {"pkgRootPath": "/Users/<USER>/github/LingXia/examples/harmony/oh_modules/.ohpm/@ohos+hamock@1.0.0/oh_modules/@ohos/hamock", "pkgName": "@ohos/hamock", "pkgVersion": "1.0.0"}}}