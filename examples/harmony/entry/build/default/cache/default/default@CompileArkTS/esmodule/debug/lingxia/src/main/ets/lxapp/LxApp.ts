if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface LxAppManager_Params {
    isReady?: boolean;
    homeAppId?: string;
    homeAppPath?: string;
    homeAppCurrentPath?: string;
    errorMessage?: string;
    modalLxAppVisible?: boolean;
    modalLxAppId?: string;
    modalLxAppPath?: string;
    hideHomeLxApp?: boolean;
    homeContainerRef?: LxAppContainer | null;
    modalContainerRef?: LxAppContainer | null;
}
import hilog from "@ohos:hilog";
import webview from "@ohos:web.webview";
import type window from "@ohos:window";
import taskpool from "@ohos:taskpool";
import { LxAppContainer } from "@normalized:N&&&lingxia/src/main/ets/lxapp/LxAppContainer&1.0.0";
import { initNativeBridge, getNativeCallbackFunction } from "@normalized:N&&&lingxia/src/main/ets/lxapp/NativeBridge&1.0.0";
import { lxappInit, onLxappOpened, onLxappClosed, getLxAppInfo } from "@normalized:Y&&&liblingxia.so&";
import type { LxAppInfo } from "@normalized:Y&&&liblingxia.so&";
const DOMAIN = 0x0000;
const TAG = 'LingXia.LxApp';
// Task function for handling LxApp close operation asynchronously
function closeLxAppTask(appId: string): number {
    "use concurrent";
    return onLxappClosed(appId);
}
interface NavigationInstance {
    openLxApp(appId: string, path: string): void;
    closeLxApp(appId: string): void;
}
export class LxApp {
    private static instance: LxApp | null = null;
    public static managerInstance: LxAppManager | null = null;
    public static context: Context | null = null;
    public static windowStage: window.WindowStage | null = null;
    // Modal miniapp state (for non-home miniapps)
    public static modalLxAppId: string | null = null;
    public static modalLxAppPath: string | null = null;
    public static modalLxAppVisible: boolean = false;
    // Control home miniapp visibility when modal is open
    public static hideHomeLxApp: boolean = false;
    // Store navigation instance for Navigation-based architecture
    public static navigationInstance: NavigationInstance | null = null;
    private homeLxAppId: string | null = null;
    // Cache for initial routes of all LxApps
    public static initialRouteCache: Map<string, string> = new Map();
    private constructor() {
        hilog.info(DOMAIN, TAG, 'LxApp instance created');
    }
    public static getInstance(): LxApp {
        if (!LxApp.instance) {
            LxApp.instance = new LxApp();
        }
        return LxApp.instance;
    }
    public static initialize(context?: Context, windowStage?: window.WindowStage): void {
        const instance = LxApp.getInstance();
        if (instance.homeLxAppId !== null) {
            hilog.info(DOMAIN, TAG, 'LxApp already successfully initialized');
            return;
        }
        // Check if native layer has already been initialized by checking homeLxAppId
        if (instance.homeLxAppId !== null) {
            hilog.warn(DOMAIN, TAG, 'Native layer already initialized, skipping native_lxapp_init');
            return;
        }
        // Store context and windowStage for window operations
        if (context) {
            LxApp.context = context;
        }
        if (windowStage) {
            LxApp.windowStage = windowStage;
            // Set window to full screen layout
            const mainWindow = windowStage.getMainWindowSync();
            mainWindow.setWindowLayoutFullScreen(true);
        }
        // Initialize native bridge
        initNativeBridge();
        // Get application-level file system paths (not module-specific)
        const appContext = context!.getApplicationContext();
        const dataDir = appContext.filesDir;
        const cacheDir = appContext.cacheDir;
        const resourceManager = context!.resourceManager;
        hilog.info(DOMAIN, TAG, `Using application context - dataDir: ${dataDir}, cacheDir: ${cacheDir}`);
        const callbackFunction = getNativeCallbackFunction();
        hilog.info(DOMAIN, TAG, `Calling lxappInit with callback and file system paths (FIRST AND ONLY TIME)`);
        const result: string | null = lxappInit(callbackFunction, dataDir, cacheDir, resourceManager);
        if (result) {
            hilog.info(DOMAIN, TAG, `LxApp initialized successfully with home app: ${result}`);
            instance.homeLxAppId = result;
            // Initialize Web component core: initialize the Browser process and create BrowserContext.
            webview.WebviewController.initializeWebEngine();
            webview.WebviewController.setWebDebuggingAccess(true);
        }
        else {
            hilog.error(DOMAIN, TAG, 'Failed to get home LxApp details from native init.');
        }
    }
    public static openHomeLxApp(): void {
        hilog.info(DOMAIN, TAG, 'openHomeLxApp called');
        const instance = LxApp.getInstance();
        const homeAppId = instance.getHomeLxAppId();
        if (homeAppId) {
            // Use the general openLxApp method with initial route
            LxApp.openLxApp(homeAppId, ''); // Empty path means use initial route
        }
        else {
            hilog.error(DOMAIN, TAG, 'Home LxApp ID not available for openHomeLxApp');
        }
    }
    public static closeLxApp(appId: string): void {
        // Call native onLxappClosed asynchronously (fire and forget)
        taskpool.execute(new taskpool.Task(closeLxAppTask, appId));
        if (LxApp.navigationInstance) {
            hilog.info(DOMAIN, TAG, 'Using LxAppNavigation for closeLxApp');
            LxApp.navigationInstance.closeLxApp(appId);
            return;
        }
        if (LxApp.modalLxAppId === appId) {
            LxApp.modalLxAppId = null;
            LxApp.modalLxAppPath = null;
            LxApp.modalLxAppVisible = false;
            LxApp.hideHomeLxApp = false;
            if (LxApp.managerInstance) {
                LxApp.managerInstance.closeModalLxApp();
            }
            else {
                hilog.warn(DOMAIN, TAG, ' No manager instance available!');
            }
        }
        else {
            hilog.warn(DOMAIN, TAG, `Cannot close miniapp ${appId} - not currently displayed modal miniapp (current: ${LxApp.modalLxAppId})`);
        }
    }
    public static openLxApp(appId: string, path: string): void {
        hilog.info(DOMAIN, TAG, `openLxApp called: ${appId}:${path}`);
        // Ensure initial route is cached for this app (if not already cached)
        if (!LxApp.initialRouteCache.has(appId)) {
            const appInfo: LxAppInfo | null = getLxAppInfo(appId);
            if (appInfo) {
                LxApp.initialRouteCache.set(appId, appInfo.initialRoute);
            }
        }
        // If path is empty, get the initial route from native
        let actualPath = path;
        if (!path || path === '') {
            const cachedInitialRoute = LxApp.initialRouteCache.get(appId);
            if (cachedInitialRoute) {
                actualPath = cachedInitialRoute;
                hilog.info(DOMAIN, TAG, `Using cached initial route for ${appId}: ${actualPath}`);
            }
            else {
                hilog.error(DOMAIN, TAG, `Could not get initial route for ${appId}`);
                return;
            }
        }
        if (LxApp.navigationInstance) {
            hilog.info(DOMAIN, TAG, 'Using LxAppNavigation for openLxApp');
            LxApp.navigationInstance.openLxApp(appId, actualPath);
            return;
        }
        if (!LxApp.managerInstance) {
            hilog.info(DOMAIN, TAG, 'No LxApp instance ready, waiting...');
            setTimeout(() => {
                LxApp.openLxApp(appId, actualPath);
            }, 50);
            return;
        }
        const instance = LxApp.getInstance();
        const isHomeLxApp = (appId === instance.getHomeLxAppId());
        if (isHomeLxApp && LxApp.managerInstance.isReady) {
            hilog.info(DOMAIN, TAG, 'Home LxApp already loaded');
            return;
        }
        hilog.info(DOMAIN, TAG, 'Using LxAppManager for openLxApp (legacy)');
        LxApp.managerInstance.doOpenLxApp(appId, actualPath, isHomeLxApp);
    }
    /**
     * Switch to a specific page within a LxApp
     * @param appId - LxApp ID
     * @param path - Page path to switch to
     */
    public static switchPage(appId: string, path: string): boolean {
        hilog.info(DOMAIN, TAG, `switchPage API called: ${appId}:${path}`);
        // The manager finds the right container and handles the switch
        if (LxApp.managerInstance) {
            return LxApp.managerInstance.switchToPage(appId, path);
        }
        hilog.warn(DOMAIN, TAG, 'No LxApp manager ready for switchPage');
        return false;
    }
    /**
     * Get Home LxApp ID
     */
    public getHomeLxAppId(): string | null {
        return this.homeLxAppId;
    }
    /**
     * Set manager instance (called by LxAppManager)
     */
    public static setManagerInstance(manager: LxAppManager): void {
        LxApp.managerInstance = manager;
    }
    /**
     * Set navigation instance (called by LxAppNavigation)
     */
    public static setNavigationInstance(navigation: NavigationInstance): void {
        LxApp.navigationInstance = navigation;
    }
    /**
     * Setup system status bar transparency (call after window is created)
     * Should be called in onWindowStageCreate
     */
    public static setupSystemStatusBar(windowStage: window.WindowStage): void {
        //const instance = LxApp.getInstance();
        //instance.setSystemStatusBarTransparent(windowStage);
    }
    /**
     * Switch to specific page in current LxApp
     * @param appId LxApp ID
     * @param path Page path to switch to
     * @returns Promise<boolean> Success status
     */
    public static async switchToPage(appId: string, path: string): Promise<boolean> {
        const instance = LxApp.getInstance();
        return await instance.switchToPageInternal(appId, path);
    }
    /**
     * Switch to specific page in current LxApp (internal implementation)
     * @param appId LxApp ID
     * @param path Page path to switch to
     * @returns Promise<boolean> Success status
     */
    public async switchToPageInternal(appId: string, path: string): Promise<boolean> {
        hilog.info(DOMAIN, TAG, `switchToPageInternal called: appId=${appId}, path=${path}`);
        try {
            // Find the container and switch to the page
            if (LxApp.managerInstance) {
                const success: boolean = LxApp.managerInstance.switchToPage(appId, path);
                if (success) {
                    hilog.info(DOMAIN, TAG, `Successfully switched to page: ${path}`);
                    return true;
                }
                else {
                    hilog.warn(DOMAIN, TAG, `Failed to switch to page: ${path} - page not found in TabBar`);
                    return false;
                }
            }
            else {
                hilog.error(DOMAIN, TAG, `No LxApp manager instance available for page switch`);
                return false;
            }
        }
        catch (error) {
            hilog.error(DOMAIN, TAG, `Error switching to page ${path}: ${error}`);
            return false;
        }
    }
}
class LxAppManager extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__isReady = new ObservedPropertySimplePU(false, this, "isReady");
        this.__homeAppId = new ObservedPropertySimplePU('', this, "homeAppId");
        this.__homeAppPath = new ObservedPropertySimplePU('', this, "homeAppPath");
        this.__homeAppCurrentPath = new ObservedPropertySimplePU('', this, "homeAppCurrentPath");
        this.__errorMessage = new ObservedPropertySimplePU('', this, "errorMessage");
        this.__modalLxAppVisible = new ObservedPropertySimplePU(false, this, "modalLxAppVisible");
        this.__modalLxAppId = new ObservedPropertySimplePU('', this, "modalLxAppId");
        this.__modalLxAppPath = new ObservedPropertySimplePU('', this, "modalLxAppPath");
        this.__hideHomeLxApp = new ObservedPropertySimplePU(false
        // References to containers for direct method calls
        , this, "hideHomeLxApp");
        this.homeContainerRef = null;
        this.modalContainerRef = null;
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: LxAppManager_Params) {
        if (params.isReady !== undefined) {
            this.isReady = params.isReady;
        }
        if (params.homeAppId !== undefined) {
            this.homeAppId = params.homeAppId;
        }
        if (params.homeAppPath !== undefined) {
            this.homeAppPath = params.homeAppPath;
        }
        if (params.homeAppCurrentPath !== undefined) {
            this.homeAppCurrentPath = params.homeAppCurrentPath;
        }
        if (params.errorMessage !== undefined) {
            this.errorMessage = params.errorMessage;
        }
        if (params.modalLxAppVisible !== undefined) {
            this.modalLxAppVisible = params.modalLxAppVisible;
        }
        if (params.modalLxAppId !== undefined) {
            this.modalLxAppId = params.modalLxAppId;
        }
        if (params.modalLxAppPath !== undefined) {
            this.modalLxAppPath = params.modalLxAppPath;
        }
        if (params.hideHomeLxApp !== undefined) {
            this.hideHomeLxApp = params.hideHomeLxApp;
        }
        if (params.homeContainerRef !== undefined) {
            this.homeContainerRef = params.homeContainerRef;
        }
        if (params.modalContainerRef !== undefined) {
            this.modalContainerRef = params.modalContainerRef;
        }
    }
    updateStateVars(params: LxAppManager_Params) {
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__isReady.purgeDependencyOnElmtId(rmElmtId);
        this.__homeAppId.purgeDependencyOnElmtId(rmElmtId);
        this.__homeAppPath.purgeDependencyOnElmtId(rmElmtId);
        this.__homeAppCurrentPath.purgeDependencyOnElmtId(rmElmtId);
        this.__errorMessage.purgeDependencyOnElmtId(rmElmtId);
        this.__modalLxAppVisible.purgeDependencyOnElmtId(rmElmtId);
        this.__modalLxAppId.purgeDependencyOnElmtId(rmElmtId);
        this.__modalLxAppPath.purgeDependencyOnElmtId(rmElmtId);
        this.__hideHomeLxApp.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__isReady.aboutToBeDeleted();
        this.__homeAppId.aboutToBeDeleted();
        this.__homeAppPath.aboutToBeDeleted();
        this.__homeAppCurrentPath.aboutToBeDeleted();
        this.__errorMessage.aboutToBeDeleted();
        this.__modalLxAppVisible.aboutToBeDeleted();
        this.__modalLxAppId.aboutToBeDeleted();
        this.__modalLxAppPath.aboutToBeDeleted();
        this.__hideHomeLxApp.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __isReady: ObservedPropertySimplePU<boolean>;
    get isReady() {
        return this.__isReady.get();
    }
    set isReady(newValue: boolean) {
        this.__isReady.set(newValue);
    }
    private __homeAppId: ObservedPropertySimplePU<string>;
    get homeAppId() {
        return this.__homeAppId.get();
    }
    set homeAppId(newValue: string) {
        this.__homeAppId.set(newValue);
    }
    private __homeAppPath: ObservedPropertySimplePU<string>;
    get homeAppPath() {
        return this.__homeAppPath.get();
    }
    set homeAppPath(newValue: string) {
        this.__homeAppPath.set(newValue);
    }
    private __homeAppCurrentPath: ObservedPropertySimplePU<string>;
    get homeAppCurrentPath() {
        return this.__homeAppCurrentPath.get();
    }
    set homeAppCurrentPath(newValue: string) {
        this.__homeAppCurrentPath.set(newValue);
    }
    private __errorMessage: ObservedPropertySimplePU<string>;
    get errorMessage() {
        return this.__errorMessage.get();
    }
    set errorMessage(newValue: string) {
        this.__errorMessage.set(newValue);
    }
    private __modalLxAppVisible: ObservedPropertySimplePU<boolean>;
    get modalLxAppVisible() {
        return this.__modalLxAppVisible.get();
    }
    set modalLxAppVisible(newValue: boolean) {
        this.__modalLxAppVisible.set(newValue);
    }
    private __modalLxAppId: ObservedPropertySimplePU<string>;
    get modalLxAppId() {
        return this.__modalLxAppId.get();
    }
    set modalLxAppId(newValue: string) {
        this.__modalLxAppId.set(newValue);
    }
    private __modalLxAppPath: ObservedPropertySimplePU<string>;
    get modalLxAppPath() {
        return this.__modalLxAppPath.get();
    }
    set modalLxAppPath(newValue: string) {
        this.__modalLxAppPath.set(newValue);
    }
    private __hideHomeLxApp: ObservedPropertySimplePU<boolean>;
    get hideHomeLxApp() {
        return this.__hideHomeLxApp.get();
    }
    set hideHomeLxApp(newValue: boolean) {
        this.__hideHomeLxApp.set(newValue);
    }
    // References to containers for direct method calls
    private homeContainerRef: LxAppContainer | null;
    private modalContainerRef: LxAppContainer | null;
    aboutToAppear() {
        hilog.info(DOMAIN, TAG, 'LxAppManager starting - intelligent initialization');
        // Set static instance in LxApp
        LxApp.setManagerInstance(this);
        // Manager is ready for openHomeLxApp() calls
        hilog.info(DOMAIN, TAG, 'LxAppManager ready, waiting for openHomeLxApp() call');
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Stack.create();
            Stack.width('100%');
            Stack.height('100%');
            Stack.expandSafeArea(this.shouldExpandSafeArea() ? [SafeAreaType.SYSTEM] : []);
        }, Stack);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.isReady && this.homeAppId && this.homeAppPath && !this.hideHomeLxApp) {
                this.ifElseBranchUpdateFunction(0, () => {
                    {
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            if (isInitialRender) {
                                let componentCall = new 
                                // Display Home LxApp (hidden when modal miniapp is open)
                                // Use current path if available, otherwise use initial path
                                LxAppContainer(this, {
                                    appId: this.homeAppId,
                                    initialPath: this.homeAppCurrentPath || this.homeAppPath,
                                    externalPath: this.homeAppCurrentPath || this.homeAppPath
                                }, undefined, elmtId, () => { }, { page: "../../lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/LxApp.ets", line: 329, col: 9 });
                                ViewPU.create(componentCall);
                                let paramsLambda = () => {
                                    return {
                                        appId: this.homeAppId,
                                        initialPath: this.homeAppCurrentPath || this.homeAppPath,
                                        externalPath: this.homeAppCurrentPath || this.homeAppPath
                                    };
                                };
                                componentCall.paramsGenerator_ = paramsLambda;
                            }
                            else {
                                this.updateStateVarsOfChildByElmtId(elmtId, {
                                    appId: this.homeAppId,
                                    initialPath: this.homeAppCurrentPath || this.homeAppPath,
                                    externalPath: this.homeAppCurrentPath || this.homeAppPath
                                });
                            }
                        }, { name: "LxAppContainer" });
                    }
                });
            }
            else if (this.errorMessage) {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // Minimalist error display
                        Column.create();
                        // Minimalist error display
                        Column.justifyContent(FlexAlign.Center);
                        // Minimalist error display
                        Column.alignItems(HorizontalAlign.Center);
                        // Minimalist error display
                        Column.width('100%');
                        // Minimalist error display
                        Column.height('100%');
                        // Minimalist error display
                        Column.padding(20);
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Text.create('⚠️');
                        Text.fontSize(32);
                        Text.margin({ bottom: 8 });
                    }, Text);
                    Text.pop();
                    // Minimalist error display
                    Column.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(2, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        // Clean loading state
                        Column.create();
                        // Clean loading state
                        Column.justifyContent(FlexAlign.Center);
                        // Clean loading state
                        Column.alignItems(HorizontalAlign.Center);
                        // Clean loading state
                        Column.width('100%');
                        // Clean loading state
                        Column.height('100%');
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        LoadingProgress.create();
                        LoadingProgress.width(24);
                        LoadingProgress.height(24);
                        LoadingProgress.color('#1677FF');
                    }, LoadingProgress);
                    // Clean loading state
                    Column.pop();
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            // Display modal miniapp on top if visible
            if (this.modalLxAppVisible && this.modalLxAppId && this.modalLxAppPath) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        __Common__.create();
                        __Common__.zIndex(999);
                    }, __Common__);
                    {
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            if (isInitialRender) {
                                let componentCall = new LxAppContainer(this, {
                                    appId: this.modalLxAppId,
                                    initialPath: this.modalLxAppPath,
                                    externalPath: this.modalLxAppPath
                                }, undefined, elmtId, () => { }, { page: "../../lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/LxApp.ets", line: 362, col: 7 });
                                ViewPU.create(componentCall);
                                let paramsLambda = () => {
                                    return {
                                        appId: this.modalLxAppId,
                                        initialPath: this.modalLxAppPath,
                                        externalPath: this.modalLxAppPath
                                    };
                                };
                                componentCall.paramsGenerator_ = paramsLambda;
                            }
                            else {
                                this.updateStateVarsOfChildByElmtId(elmtId, {
                                    appId: this.modalLxAppId,
                                    initialPath: this.modalLxAppPath,
                                    externalPath: this.modalLxAppPath
                                });
                            }
                        }, { name: "LxAppContainer" });
                    }
                    __Common__.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
        Stack.pop();
    }
    /**
     * Open LxApp (unified method for both home and other miniapps)
     */
    public doOpenLxApp(appId: string, path: string, isHomeLxApp: boolean = false): void {
        hilog.info(DOMAIN, TAG, `Opening LxApp: ${appId}:${path}, isHome: ${isHomeLxApp}`);
        try {
            // Get and cache LxApp info
            const appInfo: LxAppInfo | null = getLxAppInfo(appId);
            if (appInfo) {
                hilog.info(DOMAIN, TAG, `LxApp info: ${appInfo.appName}, initial route: ${appInfo.initialRoute}`);
                // Cache initial route for later comparison
                this.cacheInitialRoute(appId, appInfo.initialRoute);
            }
            const result: number = onLxappOpened(appId, path);
            hilog.info(DOMAIN, TAG, `Native onLxappOpened result: ${result}`);
            if (isHomeLxApp) {
                this.homeAppId = appId;
                this.homeAppPath = path;
                this.homeAppCurrentPath = path; // Initialize current page
                this.isReady = true;
                hilog.info(DOMAIN, TAG, 'Home LxApp loaded successfully');
            }
            else {
                // Modal miniapp: create new LxAppContainer and display it
                hilog.info(DOMAIN, TAG, `Opening modal miniapp: ${appId}:${path}`);
                // Store modal miniapp info in both static and instance state
                LxApp.modalLxAppId = appId;
                LxApp.modalLxAppPath = path;
                LxApp.modalLxAppVisible = true;
                // Hide home miniapp when modal is open
                LxApp.hideHomeLxApp = true;
                this.hideHomeLxApp = true;
                hilog.info(DOMAIN, TAG, `HIDING home miniapp - set to: ${this.hideHomeLxApp}`);
                // Update UI state immediately with priority
                this.modalLxAppId = appId;
                this.modalLxAppPath = path;
                this.modalLxAppVisible = true;
            }
        }
        catch (error) {
            hilog.error(DOMAIN, TAG, `Failed to open LxApp ${appId}: ${error}`);
            if (isHomeLxApp) {
                this.errorMessage = `Failed to load Home LxApp: ${error}`;
            }
        }
    }
    /**
     * Determine if safe area should be expanded for status bar transparency
     */
    private shouldExpandSafeArea(): boolean {
        // Always expand safe area for status bar transparency
        // This applies to both home miniapp and modal miniapp
        return true;
    }
    /**
     * Update home miniapp current path (called when user switches tabs)
     */
    public updateHomeLxAppCurrentPath(path: string): void {
        if (this.homeAppId) {
            this.homeAppCurrentPath = path;
        }
    }
    /**
     * Close modal miniapp
     */
    public closeModalLxApp(): void {
        hilog.info(DOMAIN, TAG, `Closing modal miniapp: visible=${this.modalLxAppVisible}, id=${this.modalLxAppId}`);
        // Update UI state immediately (onLxappClosed already called in static closeLxApp)
        this.modalLxAppVisible = false;
        this.modalLxAppId = '';
        this.modalLxAppPath = '';
        // Show home miniapp when modal is closed
        LxApp.hideHomeLxApp = false;
        this.hideHomeLxApp = false;
    }
    /**
     * Switch to specific page in any LxApp (External API for Rust)
     * @param appId LxApp ID
     * @param path Page path to switch to
     * @returns true if successful, false otherwise
     */
    public switchToPage(appId: string, path: string): boolean {
        hilog.info(DOMAIN, TAG, `switchToPage API called: ${appId}:${path}`);
        // Handle home miniapp
        if (appId === this.homeAppId) {
            hilog.info(DOMAIN, TAG, `Switching home miniapp page to: ${path}`);
            this.homeAppCurrentPath = path;
            return true;
        }
        // Handle modal miniapp
        if (appId === this.modalLxAppId) {
            hilog.info(DOMAIN, TAG, `Switching modal miniapp page to: ${path}`);
            this.modalLxAppPath = path;
            return true;
        }
        hilog.warn(DOMAIN, TAG, `LxApp ${appId} not currently active`);
        return false;
    }
    /**
     * Cache initial route for a LxApp
     */
    private cacheInitialRoute(appId: string, initialRoute: string): void {
        LxApp.initialRouteCache.set(appId, initialRoute);
        hilog.info(DOMAIN, TAG, `Cached initial route for ${appId}: ${initialRoute}`);
    }
    /**
     * Check if a path is the initial route for a LxApp
     */
    public static isInitialRoute(appId: string, path: string): boolean {
        const initialRoute = LxApp.initialRouteCache.get(appId);
        return initialRoute === path;
    }
    /**
     * Get cached initial route for a LxApp
     */
    public static getInitialRoute(appId: string): string | null {
        return LxApp.initialRouteCache.get(appId) || null;
    }
    rerender() {
        this.updateDirtyElements();
    }
}
