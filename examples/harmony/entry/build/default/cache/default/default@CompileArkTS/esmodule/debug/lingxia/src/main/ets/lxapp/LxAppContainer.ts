if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface LxAppContainer_Params {
    appId?: string;
    initialPath?: string;
    navigationMode?: boolean;
    closeCallback?: string;
    path?: string;
    pageConfig?: NavigationBarConfig | null;
    tabBarConfig?: TabBarConfig | null;
    selectedTabIndex?: number;
    webViewComponents?: Map<string, webview.WebviewController>;
    currentWebTag?: string;
    webViewKeys?: string[];
    pageShowTriggered?: Set<string>;
    externalPath?: string;
    animationOffsetY?: number;
}
import hilog from "@ohos:hilog";
import type webview from "@ohos:web.webview";
import { LxApp } from "@normalized:N&&&lingxia/src/main/ets/lxapp/LxApp&1.0.0";
import { findWebview, handleScrollChanged, toWebTag, extractWebTag, setWebViewUiCallback } from "@normalized:N&&&lingxia/src/main/ets/lxapp/WebView&1.0.0";
import type { WebViewInfo } from "@normalized:N&&&lingxia/src/main/ets/lxapp/WebView&1.0.0";
import { NavigationBar, type NavigationBarConfig } from "@normalized:N&&&lingxia/src/main/ets/lxapp/NavigationBar&1.0.0";
import { TabBar, TabBarPosition, type TabBarConfig, type TabBarItem } from "@normalized:N&&&lingxia/src/main/ets/lxapp/TabBar&1.0.0";
import { getTabBarConfig, getNavigationBarConfig, onPageShow } from "@normalized:Y&&&liblingxia.so&";
const DOMAIN = 0x0000;
const TAG = 'LingXia.Container';
export class LxAppContainer extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__appId = new SynchedPropertySimpleOneWayPU(params.appId, this, "appId");
        this.__initialPath = new SynchedPropertySimpleOneWayPU(params.initialPath, this, "initialPath");
        this.__navigationMode = new SynchedPropertySimpleOneWayPU(params.navigationMode, this, "navigationMode");
        this.__closeCallback = new SynchedPropertySimpleOneWayPU(params.closeCallback, this, "closeCallback");
        this.__path = new ObservedPropertySimplePU('', this, "path");
        this.__pageConfig = new ObservedPropertyObjectPU(null, this, "pageConfig");
        this.__tabBarConfig = new ObservedPropertyObjectPU(null, this, "tabBarConfig");
        this.__selectedTabIndex = new ObservedPropertySimplePU(0, this, "selectedTabIndex");
        this.__webViewComponents = new ObservedPropertyObjectPU(new Map() // Store Web components for each WebView
        , this, "webViewComponents");
        this.__currentWebTag = new ObservedPropertySimplePU('' // Current active WebView tag
        , this, "currentWebTag");
        this.__webViewKeys = new ObservedPropertyObjectPU([] // Array of webView keys for ForEach
        , this, "webViewKeys");
        this.pageShowTriggered = new Set() // Track which pages have triggered onPageShow
        ;
        this.__externalPath = new SynchedPropertySimpleOneWayPU(params.externalPath, this, "externalPath");
        this.__animationOffsetY = new ObservedPropertySimplePU(0 // 0 = final position, positive = off-screen bottom
        , this, "animationOffsetY");
        this.setInitiallyProvidedValue(params);
        this.declareWatch("externalPath", this.onExternalPathChange);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: LxAppContainer_Params) {
        if (params.navigationMode === undefined) {
            this.__navigationMode.set(false);
        }
        if (params.closeCallback === undefined) {
            this.__closeCallback.set('');
        }
        if (params.path !== undefined) {
            this.path = params.path;
        }
        if (params.pageConfig !== undefined) {
            this.pageConfig = params.pageConfig;
        }
        if (params.tabBarConfig !== undefined) {
            this.tabBarConfig = params.tabBarConfig;
        }
        if (params.selectedTabIndex !== undefined) {
            this.selectedTabIndex = params.selectedTabIndex;
        }
        if (params.webViewComponents !== undefined) {
            this.webViewComponents = params.webViewComponents;
        }
        if (params.currentWebTag !== undefined) {
            this.currentWebTag = params.currentWebTag;
        }
        if (params.webViewKeys !== undefined) {
            this.webViewKeys = params.webViewKeys;
        }
        if (params.pageShowTriggered !== undefined) {
            this.pageShowTriggered = params.pageShowTriggered;
        }
        if (params.externalPath === undefined) {
            this.__externalPath.set(''
            // Manual animation control for new miniapps
            );
        }
        if (params.animationOffsetY !== undefined) {
            this.animationOffsetY = params.animationOffsetY;
        }
    }
    updateStateVars(params: LxAppContainer_Params) {
        this.__appId.reset(params.appId);
        this.__initialPath.reset(params.initialPath);
        this.__navigationMode.reset(params.navigationMode);
        this.__closeCallback.reset(params.closeCallback);
        this.__externalPath.reset(params.externalPath);
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__appId.purgeDependencyOnElmtId(rmElmtId);
        this.__initialPath.purgeDependencyOnElmtId(rmElmtId);
        this.__navigationMode.purgeDependencyOnElmtId(rmElmtId);
        this.__closeCallback.purgeDependencyOnElmtId(rmElmtId);
        this.__path.purgeDependencyOnElmtId(rmElmtId);
        this.__pageConfig.purgeDependencyOnElmtId(rmElmtId);
        this.__tabBarConfig.purgeDependencyOnElmtId(rmElmtId);
        this.__selectedTabIndex.purgeDependencyOnElmtId(rmElmtId);
        this.__webViewComponents.purgeDependencyOnElmtId(rmElmtId);
        this.__currentWebTag.purgeDependencyOnElmtId(rmElmtId);
        this.__webViewKeys.purgeDependencyOnElmtId(rmElmtId);
        this.__externalPath.purgeDependencyOnElmtId(rmElmtId);
        this.__animationOffsetY.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__appId.aboutToBeDeleted();
        this.__initialPath.aboutToBeDeleted();
        this.__navigationMode.aboutToBeDeleted();
        this.__closeCallback.aboutToBeDeleted();
        this.__path.aboutToBeDeleted();
        this.__pageConfig.aboutToBeDeleted();
        this.__tabBarConfig.aboutToBeDeleted();
        this.__selectedTabIndex.aboutToBeDeleted();
        this.__webViewComponents.aboutToBeDeleted();
        this.__currentWebTag.aboutToBeDeleted();
        this.__webViewKeys.aboutToBeDeleted();
        this.__externalPath.aboutToBeDeleted();
        this.__animationOffsetY.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __appId: SynchedPropertySimpleOneWayPU<string>;
    get appId() {
        return this.__appId.get();
    }
    set appId(newValue: string) {
        this.__appId.set(newValue);
    }
    private __initialPath: SynchedPropertySimpleOneWayPU<string>;
    get initialPath() {
        return this.__initialPath.get();
    }
    set initialPath(newValue: string) {
        this.__initialPath.set(newValue);
    }
    private __navigationMode: SynchedPropertySimpleOneWayPU<boolean>;
    get navigationMode() {
        return this.__navigationMode.get();
    }
    set navigationMode(newValue: boolean) {
        this.__navigationMode.set(newValue);
    }
    private __closeCallback: SynchedPropertySimpleOneWayPU<string>;
    get closeCallback() {
        return this.__closeCallback.get();
    }
    set closeCallback(newValue: string) {
        this.__closeCallback.set(newValue);
    }
    private __path: ObservedPropertySimplePU<string>;
    get path() {
        return this.__path.get();
    }
    set path(newValue: string) {
        this.__path.set(newValue);
    }
    private __pageConfig: ObservedPropertyObjectPU<NavigationBarConfig | null>;
    get pageConfig() {
        return this.__pageConfig.get();
    }
    set pageConfig(newValue: NavigationBarConfig | null) {
        this.__pageConfig.set(newValue);
    }
    private __tabBarConfig: ObservedPropertyObjectPU<TabBarConfig | null>;
    get tabBarConfig() {
        return this.__tabBarConfig.get();
    }
    set tabBarConfig(newValue: TabBarConfig | null) {
        this.__tabBarConfig.set(newValue);
    }
    private __selectedTabIndex: ObservedPropertySimplePU<number>;
    get selectedTabIndex() {
        return this.__selectedTabIndex.get();
    }
    set selectedTabIndex(newValue: number) {
        this.__selectedTabIndex.set(newValue);
    }
    private __webViewComponents: ObservedPropertyObjectPU<Map<string, webview.WebviewController>>; // Store Web components for each WebView
    get webViewComponents() {
        return this.__webViewComponents.get();
    }
    set webViewComponents(newValue: Map<string, webview.WebviewController>) {
        this.__webViewComponents.set(newValue);
    }
    private __currentWebTag: ObservedPropertySimplePU<string>; // Current active WebView tag
    get currentWebTag() {
        return this.__currentWebTag.get();
    }
    set currentWebTag(newValue: string) {
        this.__currentWebTag.set(newValue);
    }
    private __webViewKeys: ObservedPropertyObjectPU<string[]>; // Array of webView keys for ForEach
    get webViewKeys() {
        return this.__webViewKeys.get();
    }
    set webViewKeys(newValue: string[]) {
        this.__webViewKeys.set(newValue);
    }
    private pageShowTriggered: Set<string>; // Track which pages have triggered onPageShow
    /**
     * Trigger onPageShow for a WebView if it hasn't been triggered yet
     */
    private triggerOnPageShowIfNeeded(webTag: string) {
        if (!this.pageShowTriggered.has(webTag)) {
            const extracted = extractWebTag(webTag);
            if (extracted) {
                const appId = extracted.appId;
                const path = extracted.path;
                const actualPath = path.replace(/-/g, '/');
                // Check if this is the first page (LxApp runtime might not be ready)
                const isFirstPage = this.pageShowTriggered.size === 0;
                this.pageShowTriggered.add(webTag);
                if (isFirstPage) {
                    // Delay first page to ensure LxApp runtime is ready
                    setTimeout(() => {
                        hilog.info(DOMAIN, TAG, `Triggering onPageShow for first page: ${appId}:${actualPath}`);
                        onPageShow(appId, actualPath);
                    }, 100);
                }
                else {
                    // No delay for subsequent pages
                    hilog.info(DOMAIN, TAG, `Triggering onPageShow for: ${appId}:${actualPath}`);
                    onPageShow(appId, actualPath);
                }
            }
        }
        else {
            hilog.info(DOMAIN, TAG, `onPageShow already triggered for: ${webTag}, skipping`);
        }
    }
    // Watch for external path changes from LxApp.switchPage API
    private __externalPath: SynchedPropertySimpleOneWayPU<string>;
    get externalPath() {
        return this.__externalPath.get();
    }
    set externalPath(newValue: string) {
        this.__externalPath.set(newValue);
    }
    // Manual animation control for new miniapps
    private __animationOffsetY: ObservedPropertySimplePU<number>; // 0 = final position, positive = off-screen bottom
    get animationOffsetY() {
        return this.__animationOffsetY.get();
    }
    set animationOffsetY(newValue: number) {
        this.__animationOffsetY.set(newValue);
    }
    aboutToAppear() {
        this.path = this.initialPath;
        hilog.info(DOMAIN, TAG, `Container creating for ${this.appId}:${this.path}, isHome=${this.isHomeLxApp()}`);
        // Set up WebView UI callback to handle component creation
        setWebViewUiCallback((action: string, info: WebViewInfo) => {
            this.handleWebViewUiEvent(action, info);
        });
        this.pageConfig = getNavigationBarConfig(this.appId, this.path);
        this.tabBarConfig = getTabBarConfig(this.appId);
        this.setInitialSelectedTab();
        // Only find WebView for the initial page when actually needed
        const initialWebTag = toWebTag(this.appId, this.path);
        const initialController = findWebview(this.appId, this.path);
        if (initialController) {
            this.storeWebViewComponent(initialWebTag, initialController);
            // Ensure currentWebTag is set for initial page
            this.currentWebTag = initialWebTag;
            hilog.info(DOMAIN, TAG, `Set initial currentWebTag to: ${initialWebTag}`);
            // Don't mark as triggered yet - let onAppear handle the first trigger
            hilog.info(DOMAIN, TAG, `Initial page setup completed, will trigger onPageShow when WebView appears: ${initialWebTag}`);
        }
    }
    /**
     * Handle external path changes from LxApp.switchPage API
     */
    onExternalPathChange() {
        if (this.externalPath && this.externalPath !== this.path) {
            hilog.info(DOMAIN, TAG, `External path change detected: ${this.externalPath}, switching from ${this.path}`);
            this.switchToPageInternal(this.externalPath);
        }
    }
    /**
     * Handle WebView UI events from WebViewManager
     */
    handleWebViewUiEvent(action: string, info: WebViewInfo) {
        hilog.info(DOMAIN, TAG, `WebView UI event: ${action} for ${info.webtag}`);
        switch (action) {
            case 'create':
                this.webViewComponents.set(info.webtag, info.controller);
                this.webViewKeys = Array.from(this.webViewComponents.keys());
                hilog.info(DOMAIN, TAG, `Stored WebView component: ${info.webtag}, total components: ${this.webViewComponents.size}`);
                break;
            case 'destroy':
                this.webViewComponents.delete(info.webtag);
                this.webViewKeys = Array.from(this.webViewComponents.keys());
                // If the destroyed WebView was currently displayed, switch to another one
                if (this.currentWebTag === info.webtag) {
                    if (this.webViewKeys.length > 0) {
                        // Switch to the first available WebView
                        this.currentWebTag = this.webViewKeys[0];
                        hilog.info(DOMAIN, TAG, `Destroyed current WebView ${info.webtag}, switched to: ${this.currentWebTag}`);
                    }
                    else {
                        // No WebViews left
                        this.currentWebTag = '';
                        hilog.info(DOMAIN, TAG, `Destroyed last WebView: ${info.webtag}`);
                    }
                }
                break;
        }
    }
    storeWebViewComponent(webTag: string, controller: webview.WebviewController) {
        hilog.info(DOMAIN, TAG, `Storing Web component for webTag: ${webTag}`);
        this.webViewComponents.set(webTag, controller);
        this.webViewKeys = Array.from(this.webViewComponents.keys());
        // If this is the current tab, set it as current (onPageShow will be triggered when WebView appears)
        if (webTag === toWebTag(this.appId, this.path)) {
            this.currentWebTag = webTag;
            hilog.info(DOMAIN, TAG, `Set as current WebView: ${webTag}, onPageShow will be triggered when WebView appears`);
        }
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.isTabBarVertical()) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Row.create();
                        Row.width('100%');
                        Row.height('100%');
                        Row.backgroundColor(Color.Transparent);
                        Row.expandSafeArea(this.shouldExpandSafeAreaForContainer() ? [SafeAreaType.SYSTEM] : [], this.shouldExpandSafeAreaForContainer() ? [SafeAreaEdge.TOP, SafeAreaEdge.BOTTOM] : []);
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        If.create();
                        if (this.tabBarConfig && this.tabBarConfig.position === TabBarPosition.LEFT) {
                            this.ifElseBranchUpdateFunction(0, () => {
                                this.buildTabBar.bind(this)();
                            });
                        }
                        else {
                            this.ifElseBranchUpdateFunction(1, () => {
                            });
                        }
                    }, If);
                    If.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.layoutWeight(1);
                        Column.height('100%');
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        If.create();
                        if (this.shouldShowCapsuleButton()) {
                            this.ifElseBranchUpdateFunction(0, () => {
                                this.buildCapsuleButton.bind(this)();
                            });
                        }
                        else {
                            this.ifElseBranchUpdateFunction(1, () => {
                            });
                        }
                    }, If);
                    If.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        If.create();
                        if (this.pageConfig && this.pageConfig.navigationStyle === 0 && !this.isInitialRoute()) {
                            this.ifElseBranchUpdateFunction(0, () => {
                                {
                                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                                        if (isInitialRender) {
                                            let componentCall = new NavigationBar(this, {
                                                config: this.pageConfig,
                                                appId: this.appId,
                                                currentPath: this.path,
                                                onBackPressed: (appId: string): boolean => {
                                                    this.handleNavigationBack();
                                                    return true;
                                                }
                                            }, undefined, elmtId, () => { }, { page: "../../lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/LxAppContainer.ets", line: 164, col: 13 });
                                            ViewPU.create(componentCall);
                                            let paramsLambda = () => {
                                                return {
                                                    config: this.pageConfig,
                                                    appId: this.appId,
                                                    currentPath: this.path,
                                                    onBackPressed: (appId: string): boolean => {
                                                        this.handleNavigationBack();
                                                        return true;
                                                    }
                                                };
                                            };
                                            componentCall.paramsGenerator_ = paramsLambda;
                                        }
                                        else {
                                            this.updateStateVarsOfChildByElmtId(elmtId, {
                                                config: this.pageConfig,
                                                appId: this.appId,
                                                currentPath: this.path
                                            });
                                        }
                                    }, { name: "NavigationBar" });
                                }
                            });
                        }
                        else {
                            this.ifElseBranchUpdateFunction(1, () => {
                            });
                        }
                    }, If);
                    If.pop();
                    this.buildWebViewArea.bind(this)();
                    Column.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        If.create();
                        if (this.tabBarConfig && this.tabBarConfig.position === TabBarPosition.RIGHT) {
                            this.ifElseBranchUpdateFunction(0, () => {
                                this.buildTabBar.bind(this)();
                            });
                        }
                        else {
                            this.ifElseBranchUpdateFunction(1, () => {
                            });
                        }
                    }, If);
                    If.pop();
                    Row.pop();
                });
            }
            else if (this.isTabBarTransparent()) {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Stack.create({ alignContent: this.getTabBarAlignment() });
                        Stack.width('100%');
                        Stack.height('100%');
                        Stack.backgroundColor(Color.Transparent);
                        Stack.expandSafeArea(this.shouldExpandSafeAreaForContainer() ? [SafeAreaType.SYSTEM] : [], this.shouldExpandSafeAreaForContainer() ? [SafeAreaEdge.TOP, SafeAreaEdge.BOTTOM] : []);
                    }, Stack);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.width('100%');
                        Column.height('100%');
                    }, Column);
                    this.buildNavigationBar.bind(this)();
                    this.buildWebViewArea.bind(this)();
                    Column.pop();
                    this.buildTabBar.bind(this)();
                    this.buildStandaloneCapsuleButton.bind(this)();
                    Stack.pop();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(2, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Stack.create();
                        Stack.width('100%');
                        Stack.height('100%');
                        Stack.backgroundColor(Color.Transparent);
                        Stack.expandSafeArea(this.shouldExpandSafeAreaForContainer() ? [SafeAreaType.SYSTEM] : [], this.shouldExpandSafeAreaForContainer() ? [SafeAreaEdge.TOP, SafeAreaEdge.BOTTOM] : []);
                    }, Stack);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.width('100%');
                        Column.height('100%');
                    }, Column);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        If.create();
                        if (this.tabBarConfig && this.tabBarConfig.position === TabBarPosition.TOP) {
                            this.ifElseBranchUpdateFunction(0, () => {
                                this.buildTabBar.bind(this)();
                            });
                        }
                        else {
                            this.ifElseBranchUpdateFunction(1, () => {
                            });
                        }
                    }, If);
                    If.pop();
                    this.buildNavigationBar.bind(this)();
                    this.buildWebViewArea.bind(this)();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        If.create();
                        if (this.tabBarConfig && (this.tabBarConfig.position === TabBarPosition.BOTTOM || !this.tabBarConfig.position)) {
                            this.ifElseBranchUpdateFunction(0, () => {
                                this.buildTabBar.bind(this)();
                            });
                        }
                        else {
                            this.ifElseBranchUpdateFunction(1, () => {
                            });
                        }
                    }, If);
                    If.pop();
                    Column.pop();
                    this.buildStandaloneCapsuleButton.bind(this)();
                    Stack.pop();
                });
            }
        }, If);
        If.pop();
    }
    private isHomeLxApp(): boolean {
        const miniApp = LxApp.getInstance();
        const homeAppId = miniApp.getHomeLxAppId();
        return this.appId === homeAppId;
    }
    private shouldExpandSafeAreaForContainer(): boolean {
        const shouldExpand = true;
        hilog.info(DOMAIN, TAG, `Always expand safe area for full screen coverage: ${this.appId}`);
        return shouldExpand;
    }
    /**
     * Check if TabBar is transparent
     * When transparent, TabBar overlays WebView; when opaque, they are separate
     */
    private isTabBarTransparent(): boolean {
        return this.tabBarConfig?.backgroundColor === "transparent";
    }
    /**
     * Check if TabBar is vertical (left/right position)
     */
    private isTabBarVertical(): boolean {
        return this.tabBarConfig?.position === TabBarPosition.LEFT || this.tabBarConfig?.position === TabBarPosition.RIGHT;
    }
    /**
     * Get TabBar alignment for Stack layout
     */
    private getTabBarAlignment(): Alignment {
        const position = this.tabBarConfig?.position || 'bottom';
        switch (position) {
            case 'top':
                return Alignment.Top;
            case 'left':
                return Alignment.Start;
            case 'right':
                return Alignment.End;
            case 'bottom':
            default:
                return Alignment.Bottom;
        }
    }
    /**
     * Build TabBar component
     */
    buildTabBar(parent = null): void {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.tabBarConfig && this.tabBarConfig.list.length > 0) {
                this.ifElseBranchUpdateFunction(0, () => {
                    {
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            if (isInitialRender) {
                                let componentCall = new TabBar(this, {
                                    config: this.tabBarConfig,
                                    appId: this.appId,
                                    selectedIndex: this.selectedTabIndex,
                                    onTabSelected: (appId: string, index: number, item: TabBarItem): void => this.handleTabSelected(index)
                                }, undefined, elmtId, () => { }, { page: "../../lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/LxAppContainer.ets", line: 281, col: 7 });
                                ViewPU.create(componentCall);
                                let paramsLambda = () => {
                                    return {
                                        config: this.tabBarConfig,
                                        appId: this.appId,
                                        selectedIndex: this.selectedTabIndex,
                                        onTabSelected: (appId: string, index: number, item: TabBarItem): void => this.handleTabSelected(index)
                                    };
                                };
                                componentCall.paramsGenerator_ = paramsLambda;
                            }
                            else {
                                this.updateStateVarsOfChildByElmtId(elmtId, {
                                    config: this.tabBarConfig,
                                    appId: this.appId,
                                    selectedIndex: this.selectedTabIndex
                                });
                            }
                        }, { name: "TabBar" });
                    }
                });
            }
            else /**
             * Build NavigationBar component (shared by both branches)
             */ {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
    }
    /**
     * Build NavigationBar component (shared by both branches)
     */
    buildNavigationBar(parent = null): void {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.pageConfig && this.pageConfig.navigationStyle === 0 && !this.isInitialRoute()) {
                this.ifElseBranchUpdateFunction(0, () => {
                    {
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            if (isInitialRender) {
                                let componentCall = new NavigationBar(this, {
                                    config: this.pageConfig,
                                    appId: this.appId,
                                    currentPath: this.path,
                                    showCapsuleButton: false,
                                    onBackPressed: (appId: string): boolean => {
                                        this.handleNavigationBack();
                                        return true;
                                    },
                                    onCapsuleMore: () => this.handleCapsuleMore(),
                                    onCapsuleClose: () => this.handleCapsuleClose()
                                }, undefined, elmtId, () => { }, { page: "../../lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/LxAppContainer.ets", line: 296, col: 7 });
                                ViewPU.create(componentCall);
                                let paramsLambda = () => {
                                    return {
                                        config: this.pageConfig,
                                        appId: this.appId,
                                        currentPath: this.path,
                                        showCapsuleButton: false,
                                        onBackPressed: (appId: string): boolean => {
                                            this.handleNavigationBack();
                                            return true;
                                        },
                                        onCapsuleMore: () => this.handleCapsuleMore(),
                                        onCapsuleClose: () => this.handleCapsuleClose()
                                    };
                                };
                                componentCall.paramsGenerator_ = paramsLambda;
                            }
                            else {
                                this.updateStateVarsOfChildByElmtId(elmtId, {
                                    config: this.pageConfig,
                                    appId: this.appId,
                                    currentPath: this.path,
                                    showCapsuleButton: false
                                });
                            }
                        }, { name: "NavigationBar" });
                    }
                });
            }
            else {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
    }
    buildStandaloneCapsuleButton(parent = null): void {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.shouldShowCapsuleButton()) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Stack.create();
                        Stack.position({ x: '100%', y: 44 });
                        Stack.translate({ x: -102, y: 6 });
                        Stack.width(102);
                        Stack.height(32);
                        Stack.zIndex(9999);
                    }, Stack);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Row.create();
                        Row.backgroundColor('rgba(255,255,255,0.9)');
                        Row.borderRadius(16);
                        Row.border({ width: 0.5, color: '#DDDDDD' });
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Button.createWithChild();
                        Button.width(44);
                        Button.height(32);
                        Button.backgroundColor(Color.Transparent);
                        Button.onClick(() => {
                            hilog.info(DOMAIN, TAG, 'Capsule more button pressed');
                            this.handleCapsuleMore();
                        });
                    }, Button);
                    this.buildThreeDots.bind(this)();
                    Button.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.width(0.5);
                        Column.height(16);
                        Column.backgroundColor('#DDDDDD');
                    }, Column);
                    Column.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Button.createWithChild();
                        Button.width(44);
                        Button.height(32);
                        Button.backgroundColor(Color.Transparent);
                        Button.onClick(() => {
                            hilog.info(DOMAIN, TAG, `Capsule close button pressed for appId: ${this.appId}`);
                            this.handleCapsuleClose();
                        });
                    }, Button);
                    this.buildCloseIcon.bind(this)();
                    Button.pop();
                    Row.pop();
                    Stack.pop();
                });
            }
            else /**
             * Check if should show capsule button
             * homeminiapp doesn't show capsule button, other miniapps do
             */ {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
    }
    /**
     * Check if should show capsule button
     * homeminiapp doesn't show capsule button, other miniapps do
     */
    private shouldShowCapsuleButton(): boolean {
        const isHome = this.isHomeLxApp();
        const shouldShow = !isHome;
        hilog.info(DOMAIN, TAG, `shouldShowCapsuleButton: appId=${this.appId}, isHome=${isHome}, shouldShow=${shouldShow}`);
        return shouldShow;
    }
    /**
     * Build capsule button
     */
    buildCapsuleButton(parent = null): void {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.width('100%');
            Row.height(56);
            Row.backgroundColor(Color.Transparent);
            Row.zIndex(9999);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Blank.create();
        }, Blank);
        Blank.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.backgroundColor('rgba(255,255,255,0.9)');
            Row.borderRadius(16);
            Row.border({ width: 0.5, color: '#DDDDDD' });
            Row.margin({ top: 8, right: 12 });
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithChild();
            Button.width(44);
            Button.height(32);
            Button.backgroundColor(Color.Transparent);
            Button.onClick(() => this.handleCapsuleMore());
        }, Button);
        this.buildThreeDots.bind(this)();
        Button.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.width(0.5);
            Column.height(16);
            Column.backgroundColor('#DDDDDD');
        }, Column);
        Column.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Button.createWithChild();
            Button.width(44);
            Button.height(32);
            Button.backgroundColor(Color.Transparent);
            Button.enabled(true);
            Button.onClick(() => {
                this.handleCapsuleClose();
            });
            Button.onTouch((event) => {
                hilog.info(DOMAIN, TAG, `Close button touched: ${event.type}`);
            });
        }, Button);
        this.buildCloseIcon.bind(this)();
        Button.pop();
        Row.pop();
        Row.pop();
    }
    /**
     * Build three dots icon
     */
    buildThreeDots(parent = null): void {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.justifyContent(FlexAlign.Center);
            Row.alignItems(VerticalAlign.Center);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // Three custom drawn dots - center dot larger, side dots smaller (matching Android)
            Circle.create();
            // Three custom drawn dots - center dot larger, side dots smaller (matching Android)
            Circle.width(3);
            // Three custom drawn dots - center dot larger, side dots smaller (matching Android)
            Circle.height(3);
            // Three custom drawn dots - center dot larger, side dots smaller (matching Android)
            Circle.fill('#000000');
        }, Circle);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Circle.create();
            Circle.width(5);
            Circle.height(5);
            Circle.fill('#000000');
            Circle.margin({ left: 4 });
        }, Circle);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Circle.create();
            Circle.width(3);
            Circle.height(3);
            Circle.fill('#000000');
            Circle.margin({ left: 4 });
        }, Circle);
        Row.pop();
    }
    /**
     * Build close icon (outer circle LINE with inner dot, transparent middle)
     */
    buildCloseIcon(parent = null): void {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Stack.create();
        }, Stack);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // Outer circle - use stroke instead of border to ensure circular shape
            Circle.create();
            // Outer circle - use stroke instead of border to ensure circular shape
            Circle.width(16);
            // Outer circle - use stroke instead of border to ensure circular shape
            Circle.height(16);
            // Outer circle - use stroke instead of border to ensure circular shape
            Circle.fillOpacity(0);
            // Outer circle - use stroke instead of border to ensure circular shape
            Circle.stroke('#000000');
            // Outer circle - use stroke instead of border to ensure circular shape
            Circle.strokeWidth(2.5);
        }, Circle);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // Inner dot (thick dot in center) - radius should be outer radius / 2.5
            Circle.create();
            // Inner dot (thick dot in center) - radius should be outer radius / 2.5
            Circle.width(6.5);
            // Inner dot (thick dot in center) - radius should be outer radius / 2.5
            Circle.height(6.5);
            // Inner dot (thick dot in center) - radius should be outer radius / 2.5
            Circle.fill('#000000');
        }, Circle);
        Stack.pop();
    }
    /**
     * Build WebView area
     */
    buildWebViewArea(parent = null): void {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Stack.create();
            Stack.width('100%');
            Stack.height('100%');
            Stack.layoutWeight(this.isTabBarTransparent() ? 0 : 1);
            Stack.backgroundColor(this.navigationMode ? Color.White : Color.Transparent);
            Stack.clip(this.navigationMode);
        }, Stack);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            // Render all WebView components so they can be associated with controllers
            ForEach.create();
            const forEachItemGenFunction = _item => {
                const webTag = _item;
                this.buildWebViewComponent.bind(this)(webTag, this.webViewComponents.get(webTag)!);
            };
            this.forEachUpdateFunction(elmtId, this.webViewKeys, forEachItemGenFunction);
        }, ForEach);
        // Render all WebView components so they can be associated with controllers
        ForEach.pop();
        Stack.pop();
    }
    private setInitialSelectedTab() {
        if (this.tabBarConfig && this.tabBarConfig.list.length > 0) {
            // Find corresponding tab based on current path
            const currentTabIndex = this.tabBarConfig.list.findIndex((item: TabBarItem) => this.path.includes(item.pagePath));
            this.selectedTabIndex = currentTabIndex >= 0 ? currentTabIndex : 0;
            hilog.info(DOMAIN, TAG, `setInitialSelectedTab: appId=${this.appId}, path=${this.path}, selectedTabIndex=${this.selectedTabIndex}`);
            this.tabBarConfig.list.forEach((item, index) => {
                hilog.info(DOMAIN, TAG, `Tab ${index}: path=${item.pagePath}, selected=${index === this.selectedTabIndex} (appId: ${this.appId})`);
            });
        }
    }
    private handleTabSelected(index: number) {
        hilog.info(DOMAIN, TAG, `Tab selected: ${index}, current: ${this.selectedTabIndex}`);
        if (this.tabBarConfig && index < this.tabBarConfig.list.length) {
            this.switchToTabByIndex(index);
        }
    }
    /**
     * Switch to tab by index
     */
    private switchToTabByIndex(index: number): void {
        if (!this.tabBarConfig || index < 0 || index >= this.tabBarConfig.list.length) {
            hilog.warn(DOMAIN, TAG, `Invalid tab index: ${index}`);
            return;
        }
        const newPath = this.tabBarConfig.list[index].pagePath;
        // Skip if already on this tab
        if (this.selectedTabIndex === index) {
            hilog.info(DOMAIN, TAG, `Already on tab ${index}, skipping switch`);
            return;
        }
        hilog.info(DOMAIN, TAG, `Switching from tab ${this.selectedTabIndex} to tab ${index}, path: ${newPath}`);
        // Update selected index to trigger UI visibility change
        this.selectedTabIndex = index;
        // Update current path to the new tab path
        this.path = newPath;
        // Find WebView for the new tab (only when switching)
        hilog.info(DOMAIN, TAG, `Finding WebView for tab switch: ${this.appId}:${newPath}`);
        const newWebTag = toWebTag(this.appId, newPath);
        const newWebViewController = findWebview(this.appId, newPath);
        if (newWebViewController) {
            hilog.info(DOMAIN, TAG, `WebView for tab switch ${newPath}: found`);
            // Store the Web component for this WebView
            this.webViewComponents.set(newWebTag, newWebViewController);
            this.webViewKeys = Array.from(this.webViewComponents.keys()); // Update ForEach keys
            this.currentWebTag = newWebTag;
            // Clear the pageShow trigger state for this WebView so it can trigger again
            this.pageShowTriggered.delete(newWebTag);
            hilog.info(DOMAIN, TAG, `Updated currentWebTag to: ${newWebTag}`);
            // Trigger onPageShow immediately for tab switches
            this.triggerOnPageShowIfNeeded(newWebTag);
        }
        else {
            hilog.error(DOMAIN, TAG, `WebView for tab switch ${newPath}: not found`);
        }
        // Update page config
        this.pageConfig = getNavigationBarConfig(this.appId, newPath);
        // Update manager state if this is home miniapp
        if (this.isHomeLxApp()) {
            const manager = LxApp.managerInstance;
            if (manager) {
                manager.updateHomeLxAppCurrentPath(newPath);
            }
        }
        hilog.info(DOMAIN, TAG, `Tab switched to ${index}, onPageShow will be triggered automatically for path: ${newPath}`);
    }
    /**
     * Switch to page internally (handles both tab and non-tab pages)
     * Called by external API or tab clicks
     */
    private switchToPageInternal(path: string): void {
        hilog.info(DOMAIN, TAG, `switchToPageInternal called: ${this.appId}:${path}`);
        // Use cached TabBar config to find tab index
        if (this.tabBarConfig) {
            const tabIndex = this.tabBarConfig.list.findIndex(item => item.pagePath === path || item.pagePath.includes(path));
            if (tabIndex >= 0) {
                hilog.info(DOMAIN, TAG, `Found tab ${tabIndex} for path ${path}, switching tab`);
                this.switchToTabByIndex(tabIndex);
                return;
            }
        }
        // For non-tabbar pages, directly switch
        hilog.info(DOMAIN, TAG, `Direct page switch for path ${path}`);
        this.path = path;
        const webTag = toWebTag(this.appId, path);
        const controller = findWebview(this.appId, path);
        if (controller) {
            this.storeWebViewComponent(webTag, controller);
        }
        hilog.info(DOMAIN, TAG, `WebView for page switch ${path}: ${controller ? 'found' : 'not found'}`);
        this.pageConfig = getNavigationBarConfig(this.appId, path);
    }
    private handleNavigationBack() {
        hilog.info(DOMAIN, TAG, 'Navigation back pressed');
        // TODO: Implement navigation back logic
    }
    private handleCapsuleMore(): void {
        hilog.info(DOMAIN, TAG, 'Capsule more button pressed');
        // TODO: Implement more menu
    }
    private handleCapsuleClose(): void {
        // Close second miniapp if this is not home miniapp
        if (!this.isHomeLxApp()) {
            LxApp.closeLxApp(this.appId);
        }
    }
    /**
     * Build individual WebView component for a specific webTag
     */
    buildWebViewComponent(webTag: string, controller: webview.WebviewController, parent = null): void {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Web.create({
                src: '',
                controller: controller
            });
            Web.visibility(webTag === this.currentWebTag ? Visibility.Visible : Visibility.Hidden);
            Web.onAppear(() => {
                hilog.info(DOMAIN, TAG, `WebView component appeared for webTag: ${webTag}, visible: ${webTag === this.currentWebTag}`);
                // Trigger onPageShow if this is the current WebView
                if (webTag === this.currentWebTag) {
                    this.triggerOnPageShowIfNeeded(webTag);
                }
            });
            Web.onScroll((event) => {
                // Handle scroll event when scroll listener is enabled
                handleScrollChanged(webTag, event.xOffset, event.yOffset);
            });
            Web.width('100%');
            Web.height('100%');
            Web.backgroundColor(Color.Transparent);
        }, Web);
    }
    /**
     * Check if current page is the initial route
     */
    private isInitialRoute(): boolean {
        const cachedInitialRoute = LxApp.initialRouteCache.get(this.appId);
        return cachedInitialRoute === this.path;
    }
    rerender() {
        this.updateDirtyElements();
    }
}
