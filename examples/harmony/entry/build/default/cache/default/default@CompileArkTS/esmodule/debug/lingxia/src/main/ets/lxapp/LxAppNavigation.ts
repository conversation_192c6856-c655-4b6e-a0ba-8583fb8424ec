if (!("finalizeConstruction" in ViewPU.prototype)) {
    Reflect.set(ViewPU.prototype, "finalizeConstruction", () => { });
}
interface LxAppNavigation_Params {
    isReady?: boolean;
    homeAppId?: string;
    homeAppPath?: string;
    errorMessage?: string;
    autoOpenHome?: boolean;
    miniAppStack?: LxAppStackItem[];
    hiddenApps?: Set<string>;
    navPageInfos?: NavPathStack;
}
import hilog from "@ohos:hilog";
import { LxApp } from "@normalized:N&&&lingxia/src/main/ets/lxapp/LxApp&1.0.0";
import { LxAppContainer } from "@normalized:N&&&lingxia/src/main/ets/lxapp/LxAppContainer&1.0.0";
import { onLxappOpened, onLxappClosed } from "@normalized:Y&&&liblingxia.so&";
const DOMAIN = 0xA000;
const TAG = 'LingXia.Navigation';
/**
 * LxApp stack item for manual layer management
 */
interface LxAppStackItem {
    appId: string;
    path: string;
    offsetY: number; // For manual animation
    zIndex: number; // Layer index
}
export class LxAppNavigation extends ViewPU {
    constructor(parent, params, __localStorage, elmtId = -1, paramsLambda = undefined, extraInfo) {
        super(parent, __localStorage, elmtId, extraInfo);
        if (typeof paramsLambda === "function") {
            this.paramsGenerator_ = paramsLambda;
        }
        this.__isReady = new ObservedPropertySimplePU(false, this, "isReady");
        this.__homeAppId = new ObservedPropertySimplePU('', this, "homeAppId");
        this.__homeAppPath = new ObservedPropertySimplePU('', this, "homeAppPath");
        this.__errorMessage = new ObservedPropertySimplePU('', this, "errorMessage");
        this.__autoOpenHome = new SynchedPropertySimpleOneWayPU(params.autoOpenHome, this, "autoOpenHome");
        this.__miniAppStack = new ObservedPropertyObjectPU([], this, "miniAppStack");
        this.__hiddenApps = new ObservedPropertyObjectPU(new Set<string>(), this, "hiddenApps");
        this.__navPageInfos = new ObservedPropertyObjectPU(new NavPathStack(), this, "navPageInfos");
        this.addProvidedVar("navPageInfos", this.__navPageInfos, false);
        this.setInitiallyProvidedValue(params);
        this.finalizeConstruction();
    }
    setInitiallyProvidedValue(params: LxAppNavigation_Params) {
        if (params.isReady !== undefined) {
            this.isReady = params.isReady;
        }
        if (params.homeAppId !== undefined) {
            this.homeAppId = params.homeAppId;
        }
        if (params.homeAppPath !== undefined) {
            this.homeAppPath = params.homeAppPath;
        }
        if (params.errorMessage !== undefined) {
            this.errorMessage = params.errorMessage;
        }
        if (params.autoOpenHome === undefined) {
            this.__autoOpenHome.set(true);
        }
        if (params.miniAppStack !== undefined) {
            this.miniAppStack = params.miniAppStack;
        }
        if (params.hiddenApps !== undefined) {
            this.hiddenApps = params.hiddenApps;
        }
        if (params.navPageInfos !== undefined) {
            this.navPageInfos = params.navPageInfos;
        }
    }
    updateStateVars(params: LxAppNavigation_Params) {
        this.__autoOpenHome.reset(params.autoOpenHome);
    }
    purgeVariableDependenciesOnElmtId(rmElmtId) {
        this.__isReady.purgeDependencyOnElmtId(rmElmtId);
        this.__homeAppId.purgeDependencyOnElmtId(rmElmtId);
        this.__homeAppPath.purgeDependencyOnElmtId(rmElmtId);
        this.__errorMessage.purgeDependencyOnElmtId(rmElmtId);
        this.__autoOpenHome.purgeDependencyOnElmtId(rmElmtId);
        this.__miniAppStack.purgeDependencyOnElmtId(rmElmtId);
        this.__hiddenApps.purgeDependencyOnElmtId(rmElmtId);
        this.__navPageInfos.purgeDependencyOnElmtId(rmElmtId);
    }
    aboutToBeDeleted() {
        this.__isReady.aboutToBeDeleted();
        this.__homeAppId.aboutToBeDeleted();
        this.__homeAppPath.aboutToBeDeleted();
        this.__errorMessage.aboutToBeDeleted();
        this.__autoOpenHome.aboutToBeDeleted();
        this.__miniAppStack.aboutToBeDeleted();
        this.__hiddenApps.aboutToBeDeleted();
        this.__navPageInfos.aboutToBeDeleted();
        SubscriberManager.Get().delete(this.id__());
        this.aboutToBeDeletedInternal();
    }
    private __isReady: ObservedPropertySimplePU<boolean>;
    get isReady() {
        return this.__isReady.get();
    }
    set isReady(newValue: boolean) {
        this.__isReady.set(newValue);
    }
    private __homeAppId: ObservedPropertySimplePU<string>;
    get homeAppId() {
        return this.__homeAppId.get();
    }
    set homeAppId(newValue: string) {
        this.__homeAppId.set(newValue);
    }
    private __homeAppPath: ObservedPropertySimplePU<string>;
    get homeAppPath() {
        return this.__homeAppPath.get();
    }
    set homeAppPath(newValue: string) {
        this.__homeAppPath.set(newValue);
    }
    private __errorMessage: ObservedPropertySimplePU<string>;
    get errorMessage() {
        return this.__errorMessage.get();
    }
    set errorMessage(newValue: string) {
        this.__errorMessage.set(newValue);
    }
    private __autoOpenHome: SynchedPropertySimpleOneWayPU<boolean>;
    get autoOpenHome() {
        return this.__autoOpenHome.get();
    }
    set autoOpenHome(newValue: boolean) {
        this.__autoOpenHome.set(newValue);
    }
    private __miniAppStack: ObservedPropertyObjectPU<LxAppStackItem[]>;
    get miniAppStack() {
        return this.__miniAppStack.get();
    }
    set miniAppStack(newValue: LxAppStackItem[]) {
        this.__miniAppStack.set(newValue);
    }
    private __hiddenApps: ObservedPropertyObjectPU<Set<string>>;
    get hiddenApps() {
        return this.__hiddenApps.get();
    }
    set hiddenApps(newValue: Set<string>) {
        this.__hiddenApps.set(newValue);
    }
    private __navPageInfos: ObservedPropertyObjectPU<NavPathStack>;
    get navPageInfos() {
        return this.__navPageInfos.get();
    }
    set navPageInfos(newValue: NavPathStack) {
        this.__navPageInfos.set(newValue);
    }
    aboutToAppear() {
        hilog.info(DOMAIN, TAG, 'LxAppNavigation starting with WebView preservation');
        LxApp.setNavigationInstance(this);
        this.initializeLxApp();
    }
    private async initializeLxApp(): Promise<void> {
        try {
            const instance = LxApp.getInstance();
            const homeAppId = instance.getHomeLxAppId();
            const homeAppPath: string | null = homeAppId ? LxApp.initialRouteCache.get(homeAppId) || null : null;
            if (homeAppId && homeAppPath) {
                this.homeAppId = homeAppId;
                this.homeAppPath = homeAppPath;
                if (this.autoOpenHome) {
                    hilog.info(DOMAIN, TAG, `Auto-opening home LxApp: ${this.homeAppId}:${this.homeAppPath}`);
                    LxApp.openHomeLxApp();
                }
                else {
                    hilog.info(DOMAIN, TAG, `LxApp initialized, waiting for manual openHomeLxApp call: ${this.homeAppId}:${this.homeAppPath}`);
                }
                this.isReady = true;
                hilog.info(DOMAIN, TAG, `LxApp Navigation ready: ${this.homeAppId}:${this.homeAppPath}`);
            }
            else {
                this.errorMessage = 'LxApp not initialized. Please call LxApp.initialize() in EntryAbility first.';
                hilog.error(DOMAIN, TAG, 'LxApp not initialized');
            }
        }
        catch (error) {
            this.errorMessage = `Initialization error: ${error}`;
            hilog.error(DOMAIN, TAG, `Initialization error: ${error}`);
        }
    }
    public openLxApp(appId: string, path: string): void {
        hilog.info(DOMAIN, TAG, `Opening miniapp: ${appId}:${path}`);
        try {
            const result: number = onLxappOpened(appId, path);
            hilog.info(DOMAIN, TAG, `onLxappOpened result: ${result}`);
            const existingIndex = this.miniAppStack.findIndex(item => item.appId === appId);
            if (existingIndex >= 0) {
                hilog.info(DOMAIN, TAG, `LxApp ${appId} already exists, showing and bringing to front`);
                this.showLxApp(appId, existingIndex);
            }
            else {
                this.createNewLxApp(appId, path);
            }
        }
        catch (error) {
            hilog.error(DOMAIN, TAG, `Failed to open LxApp ${appId}: ${error}`);
        }
    }
    public closeLxApp(appId: string): void {
        hilog.info(DOMAIN, TAG, `Hiding miniapp (preserving WebViews): ${appId}`);
        try {
            const stackIndex = this.miniAppStack.findIndex(item => item.appId === appId);
            if (stackIndex >= 0) {
                this.hideLxAppWithAnimation(appId, stackIndex);
            }
            else {
                hilog.warn(DOMAIN, TAG, `LxApp not found in stack: ${appId}`);
            }
        }
        catch (error) {
            hilog.error(DOMAIN, TAG, `Failed to close LxApp ${appId}: ${error}`);
        }
    }
    private showLxApp(appId: string, stackIndex: number): void {
        const newHiddenApps = new Set<string>();
        this.hiddenApps.forEach(id => {
            if (id !== appId) {
                newHiddenApps.add(id);
            }
        });
        this.hiddenApps = newHiddenApps;
        this.miniAppStack[stackIndex].offsetY = 0;
        this.miniAppStack[stackIndex].zIndex = 1000 + this.miniAppStack.length;
    }
    private createNewLxApp(appId: string, path: string): void {
        const newStackItem: LxAppStackItem = {
            appId: appId,
            path: path,
            offsetY: 0,
            zIndex: 1000 + this.miniAppStack.length
        };
        this.miniAppStack.push(newStackItem);
        hilog.info(DOMAIN, TAG, `LxApp created: ${appId}:${path}, stack size: ${this.miniAppStack.length}`);
    }
    private hideLxAppWithAnimation(appId: string, stackIndex: number): void {
        Context.animateTo({
            duration: 250,
            curve: Curve.EaseIn,
            playMode: PlayMode.Normal
        }, () => {
            if (stackIndex < this.miniAppStack.length) {
                this.miniAppStack[stackIndex].offsetY = 800;
            }
        });
        setTimeout(() => {
            if (stackIndex < this.miniAppStack.length) {
                this.hiddenApps = new Set(this.hiddenApps).add(appId);
                hilog.info(DOMAIN, TAG, `LxApp ${appId} hidden, WebViews preserved for reuse`);
            }
            onLxappClosed(appId);
        }, 300);
    }
    private isLxAppVisible(appId: string): boolean {
        return !this.hiddenApps.has(appId);
    }
    initialRender() {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Stack.create();
            Stack.width('100%');
            Stack.height('100%');
        }, Stack);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (this.isReady && this.homeAppId && this.homeAppPath) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        __Common__.create();
                        __Common__.zIndex(0);
                    }, __Common__);
                    {
                        this.observeComponentCreation2((elmtId, isInitialRender) => {
                            if (isInitialRender) {
                                let componentCall = new LxAppContainer(this, {
                                    appId: this.homeAppId,
                                    initialPath: this.homeAppPath,
                                    navigationMode: false,
                                    closeCallback: undefined
                                }, undefined, elmtId, () => { }, { page: "../../lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/LxAppNavigation.ets", line: 159, col: 9 });
                                ViewPU.create(componentCall);
                                let paramsLambda = () => {
                                    return {
                                        appId: this.homeAppId,
                                        initialPath: this.homeAppPath,
                                        navigationMode: false,
                                        closeCallback: undefined
                                    };
                                };
                                componentCall.paramsGenerator_ = paramsLambda;
                            }
                            else {
                                this.updateStateVarsOfChildByElmtId(elmtId, {
                                    appId: this.homeAppId,
                                    initialPath: this.homeAppPath,
                                    navigationMode: false,
                                    closeCallback: undefined
                                });
                            }
                        }, { name: "LxAppContainer" });
                    }
                    __Common__.pop();
                });
            }
            else if (this.errorMessage) {
                this.ifElseBranchUpdateFunction(1, () => {
                    this.buildErrorPage.bind(this)();
                });
            }
            else {
                this.ifElseBranchUpdateFunction(2, () => {
                    this.buildLoadingPage.bind(this)();
                });
            }
        }, If);
        If.pop();
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            ForEach.create();
            const forEachItemGenFunction = (_item, index: number) => {
                const stackItem = _item;
                this.observeComponentCreation2((elmtId, isInitialRender) => {
                    __Common__.create();
                    __Common__.width('100%');
                    __Common__.height('100%');
                    __Common__.zIndex(this.isLxAppVisible(stackItem.appId) ? stackItem.zIndex : -1);
                    __Common__.backgroundColor(Color.White);
                    __Common__.expandSafeArea([SafeAreaType.SYSTEM], [SafeAreaEdge.TOP, SafeAreaEdge.BOTTOM]);
                    __Common__.translate({ y: stackItem.offsetY });
                    __Common__.visibility(this.isLxAppVisible(stackItem.appId) ? Visibility.Visible : Visibility.Hidden);
                    __Common__.enabled(this.isLxAppVisible(stackItem.appId));
                }, __Common__);
                {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        if (isInitialRender) {
                            let componentCall = new LxAppContainer(this, {
                                appId: stackItem.appId,
                                initialPath: stackItem.path,
                                navigationMode: true,
                                closeCallback: 'navigation'
                            }, undefined, elmtId, () => { }, { page: "../../lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/LxAppNavigation.ets", line: 173, col: 9 });
                            ViewPU.create(componentCall);
                            let paramsLambda = () => {
                                return {
                                    appId: stackItem.appId,
                                    initialPath: stackItem.path,
                                    navigationMode: true,
                                    closeCallback: 'navigation'
                                };
                            };
                            componentCall.paramsGenerator_ = paramsLambda;
                        }
                        else {
                            this.updateStateVarsOfChildByElmtId(elmtId, {
                                appId: stackItem.appId,
                                initialPath: stackItem.path,
                                navigationMode: true,
                                closeCallback: 'navigation'
                            });
                        }
                    }, { name: "LxAppContainer" });
                }
                __Common__.pop();
            };
            this.forEachUpdateFunction(elmtId, this.miniAppStack, forEachItemGenFunction, undefined, true, false);
        }, ForEach);
        ForEach.pop();
        Stack.pop();
    }
    buildFixedCapsuleButton(appId: string, parent = null): void {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            If.create();
            if (appId !== LxApp.getInstance().getHomeLxAppId()) {
                this.ifElseBranchUpdateFunction(0, () => {
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Stack.create();
                        Stack.width('100%');
                        Stack.height('100%');
                        Stack.backgroundColor(Color.Transparent);
                        Stack.zIndex(9999);
                        Stack.alignContent(Alignment.TopEnd);
                    }, Stack);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Row.create();
                        Row.backgroundColor('rgba(255,255,255,0.9)');
                        Row.borderRadius(20);
                        Row.border({ width: 0.5, color: '#DDDDDD' });
                        Row.margin({ top: 52, right: 12 });
                    }, Row);
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Button.createWithChild();
                        Button.width(44);
                        Button.height(36);
                        Button.backgroundColor(Color.Transparent);
                        Button.onClick(() => {
                            hilog.info(DOMAIN, TAG, 'Capsule more button pressed');
                        });
                    }, Button);
                    this.buildThreeDots.bind(this)();
                    Button.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Column.create();
                        Column.width(0.5);
                        Column.height(18);
                        Column.backgroundColor('#DDDDDD');
                    }, Column);
                    Column.pop();
                    this.observeComponentCreation2((elmtId, isInitialRender) => {
                        Button.createWithChild();
                        Button.width(44);
                        Button.height(36);
                        Button.backgroundColor(Color.Transparent);
                        Button.onClick(() => {
                            hilog.info(DOMAIN, TAG, `Capsule close button pressed for appId: ${appId}`);
                            this.closeLxApp(appId);
                        });
                    }, Button);
                    this.buildCloseIcon.bind(this)();
                    Button.pop();
                    Row.pop();
                    Stack.pop();
                });
            }
            else /**
             * Build three dots icon
             */ {
                this.ifElseBranchUpdateFunction(1, () => {
                });
            }
        }, If);
        If.pop();
    }
    /**
     * Build three dots icon
     */
    buildThreeDots(parent = null): void {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Row.create();
            Row.justifyContent(FlexAlign.Center);
            Row.alignItems(VerticalAlign.Center);
        }, Row);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Circle.create();
            Circle.width(3);
            Circle.height(3);
            Circle.fill('#000000');
        }, Circle);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Circle.create();
            Circle.width(5);
            Circle.height(5);
            Circle.fill('#000000');
            Circle.margin({ left: 4 });
        }, Circle);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Circle.create();
            Circle.width(3);
            Circle.height(3);
            Circle.fill('#000000');
            Circle.margin({ left: 4 });
        }, Circle);
        Row.pop();
    }
    /**
     * Build close icon
     */
    buildCloseIcon(parent = null): void {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Stack.create();
        }, Stack);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Circle.create();
            Circle.width(16);
            Circle.height(16);
            Circle.fillOpacity(0);
            Circle.stroke('#000000');
            Circle.strokeWidth(2.5);
        }, Circle);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Circle.create();
            Circle.width(6.5);
            Circle.height(6.5);
            Circle.fill('#000000');
        }, Circle);
        Stack.pop();
    }
    buildErrorPage(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.justifyContent(FlexAlign.Center);
            Column.alignItems(HorizontalAlign.Center);
            Column.width('100%');
            Column.height('100%');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Text.create('⚠️');
            Text.fontSize(32);
        }, Text);
        Text.pop();
        Column.pop();
    }
    /**
     * Build clean loading page
     */
    buildLoadingPage(parent = null) {
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            Column.create();
            Column.justifyContent(FlexAlign.Center);
            Column.alignItems(HorizontalAlign.Center);
            Column.width('100%');
            Column.height('100%');
        }, Column);
        this.observeComponentCreation2((elmtId, isInitialRender) => {
            LoadingProgress.create();
            LoadingProgress.width(24);
            LoadingProgress.height(24);
            LoadingProgress.color('#1677FF');
        }, LoadingProgress);
        Column.pop();
    }
    rerender() {
        this.updateDirtyElements();
    }
}
