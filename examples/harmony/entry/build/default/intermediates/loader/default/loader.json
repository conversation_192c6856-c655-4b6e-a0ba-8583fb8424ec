{"modulePathMap": {"entry": "/Users/<USER>/github/Ling<PERSON>ia/examples/harmony/entry", "lingxia": "/Users/<USER>/github/Ling<PERSON>ia/lingxia-sdk/harmony/lingxia"}, "compileMode": "esmodule", "projectRootPath": "/Users/<USER>/github/Ling<PERSON>ia/examples/harmony", "nodeModulesPath": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader_out/default/node_modules", "byteCodeHarInfo": {}, "declarationEntry": [], "moduleName": "entry", "hspNameOhmMap": {}, "harNameOhmMap": {}, "packageManagerType": "ohpm", "compileEntry": [], "otherCompileFiles": [], "dynamicImportLibInfo": {}, "routerMap": [], "hspResourcesMap": {}, "updateVersionInfo": {}, "anBuildOutPut": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/loader_out/default/an/arm64-v8a", "anBuildMode": "type"}