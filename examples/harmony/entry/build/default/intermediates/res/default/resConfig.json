{"configPath": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/process_profile/default/module.json", "packageName": "app.lingxia.lxapp.example", "output": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default", "moduleNames": "entry,lingxia", "ResourceTable": ["/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/generated/r/default/ResourceTable.h"], "applicationResource": "/Users/<USER>/github/LingXia/examples/harmony/AppScope/resources", "moduleResources": ["/Users/<USER>/github/LingXia/examples/harmony/entry/src/main/resources"], "dependencies": [], "iconCheck": true, "compression": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/opt-compression.json", "ids": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/ids_map", "definedIds": "/Users/<USER>/github/LingXia/examples/harmony/entry/build/default/intermediates/res/default/ids_map/id_defined.json", "definedSysIds": "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/hms/toolchains/id_defined.json"}