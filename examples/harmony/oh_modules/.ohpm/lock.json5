{"lockVersion": "1.0", "settings": {"resolveConflict": true, "resolveConflictStrict": false, "installAll": true}, "overrides": {}, "overrideDependencyMap": {}, "modules": {".": {"name": "", "dependencies": {}, "devDependencies": {"@ohos/hypium": {"specifier": "1.0.21", "version": "1.0.21"}, "@ohos/hamock": {"specifier": "1.0.0", "version": "1.0.0"}}, "dynamicDependencies": {}, "maskedByOverrideDependencyMap": false}, "../../lingxia-sdk/harmony/lingxia": {"name": "lingxia", "dependencies": {}, "devDependencies": {}, "dynamicDependencies": {}, "maskedByOverrideDependencyMap": false}, "entry": {"name": "entry", "dependencies": {"lingxia": {"specifier": "file:../../lingxia-sdk/harmony/lingxia", "version": "file:../../lingxia-sdk/harmony/lingxia"}}, "devDependencies": {}, "dynamicDependencies": {}, "maskedByOverrideDependencyMap": false}}, "packages": {"@ohos/hypium@1.0.21": {"integrity": "sha512-iyKGMXxE+9PpCkqEwu0VykN/7hNpb+QOeIuHwkmZnxOpI+dFZt6yhPB7k89EgV1MiSK/ieV/hMjr5Z2mWwRfMQ==", "storePath": "oh_modules/.ohpm/@ohos+hypium@1.0.21", "dependencies": {}, "dynamicDependencies": {}, "dev": true, "dynamic": false, "maskedByOverrideDependencyMap": false}, "@ohos/hamock@1.0.0": {"integrity": "sha512-K6lDPYc6VkKe6ZBNQa9aoG+ZZMiwqfcR/7yAVFSUGIuOAhPvCJAo9+t1fZnpe0dBRBPxj2bxPPbKh69VuyAtDg==", "storePath": "oh_modules/.ohpm/@ohos+hamock@1.0.0", "dependencies": {}, "dynamicDependencies": {}, "dev": true, "dynamic": false, "maskedByOverrideDependencyMap": false}, "lingxia@file:../../lingxia-sdk/harmony/lingxia": {"storePath": "../../lingxia-sdk/harmony/lingxia", "dependencies": {}, "dynamicDependencies": {}, "dev": false, "dynamic": false, "maskedByOverrideDependencyMap": false}}}