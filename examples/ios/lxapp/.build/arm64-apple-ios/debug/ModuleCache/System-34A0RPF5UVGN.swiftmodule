---
path:            '/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos/prebuilt-modules/18.5/System.swiftmodule/arm64-apple-ios.swiftmodule'
dependencies:
  - mtime:           1746660049000000000
    path:            '/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos/prebuilt-modules/18.5/System.swiftmodule/arm64-apple-ios.swiftmodule'
    size:            412088
  - mtime:           1745047023000000000
    path:            'usr/lib/swift/Swift.swiftmodule/arm64-apple-ios.swiftinterface'
    size:            1929394
    sdk_relative:    true
  - mtime:           1745048865000000000
    path:            'usr/include/_time.apinotes'
    size:            1132
    sdk_relative:    true
  - mtime:           1745048979000000000
    path:            'usr/lib/swift/_errno.swiftmodule/arm64-apple-ios.swiftinterface'
    size:            3843
    sdk_relative:    true
  - mtime:           1745048998000000000
    path:            'usr/lib/swift/_signal.swiftmodule/arm64-apple-ios.swiftinterface'
    size:            1054
    sdk_relative:    true
  - mtime:           1745048997000000000
    path:            'usr/lib/swift/sys_time.swiftmodule/arm64-apple-ios.swiftinterface'
    size:            1055
    sdk_relative:    true
  - mtime:           1745048986000000000
    path:            'usr/lib/swift/_time.swiftmodule/arm64-apple-ios.swiftinterface'
    size:            1018
    sdk_relative:    true
  - mtime:           1745048994000000000
    path:            'usr/lib/swift/_stdio.swiftmodule/arm64-apple-ios.swiftinterface'
    size:            1466
    sdk_relative:    true
  - mtime:           1745049004000000000
    path:            'usr/lib/swift/unistd.swiftmodule/arm64-apple-ios.swiftinterface'
    size:            806
    sdk_relative:    true
  - mtime:           1745048978000000000
    path:            'usr/lib/swift/_math.swiftmodule/arm64-apple-ios.swiftinterface'
    size:            15237
    sdk_relative:    true
  - mtime:           1745047098000000000
    path:            'usr/lib/swift/_Builtin_float.swiftmodule/arm64-apple-ios.swiftinterface'
    size:            4214
    sdk_relative:    true
  - mtime:           1745049014000000000
    path:            'usr/lib/swift/Darwin.swiftmodule/arm64-apple-ios.swiftinterface'
    size:            18208
    sdk_relative:    true
  - mtime:           1745049469000000000
    path:            'usr/lib/swift/_Concurrency.swiftmodule/arm64-apple-ios.swiftinterface'
    size:            230583
    sdk_relative:    true
  - mtime:           1745049589000000000
    path:            'usr/lib/swift/_StringProcessing.swiftmodule/arm64-apple-ios.swiftinterface'
    size:            22860
    sdk_relative:    true
  - mtime:           1745050193000000000
    path:            'usr/lib/swift/System.swiftmodule/arm64-apple-ios.swiftinterface'
    size:            95457
    sdk_relative:    true
version:         1
...
