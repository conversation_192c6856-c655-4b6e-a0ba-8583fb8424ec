---
path:            '/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos/prebuilt-modules/18.5/UniformTypeIdentifiers.swiftmodule/arm64-apple-ios.swiftmodule'
dependencies:
  - mtime:           1746660089000000000
    path:            '/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/lib/swift/iphoneos/prebuilt-modules/18.5/UniformTypeIdentifiers.swiftmodule/arm64-apple-ios.swiftmodule'
    size:            72756
  - mtime:           1745047023000000000
    path:            'usr/lib/swift/Swift.swiftmodule/arm64-apple-ios.swiftinterface'
    size:            1929394
    sdk_relative:    true
  - mtime:           1745048865000000000
    path:            'usr/include/_time.apinotes'
    size:            1132
    sdk_relative:    true
  - mtime:           1745048979000000000
    path:            'usr/lib/swift/_errno.swiftmodule/arm64-apple-ios.swiftinterface'
    size:            3843
    sdk_relative:    true
  - mtime:           1745048998000000000
    path:            'usr/lib/swift/_signal.swiftmodule/arm64-apple-ios.swiftinterface'
    size:            1054
    sdk_relative:    true
  - mtime:           1745048997000000000
    path:            'usr/lib/swift/sys_time.swiftmodule/arm64-apple-ios.swiftinterface'
    size:            1055
    sdk_relative:    true
  - mtime:           1745048986000000000
    path:            'usr/lib/swift/_time.swiftmodule/arm64-apple-ios.swiftinterface'
    size:            1018
    sdk_relative:    true
  - mtime:           1745048994000000000
    path:            'usr/lib/swift/_stdio.swiftmodule/arm64-apple-ios.swiftinterface'
    size:            1466
    sdk_relative:    true
  - mtime:           1745049004000000000
    path:            'usr/lib/swift/unistd.swiftmodule/arm64-apple-ios.swiftinterface'
    size:            806
    sdk_relative:    true
  - mtime:           1745048978000000000
    path:            'usr/lib/swift/_math.swiftmodule/arm64-apple-ios.swiftinterface'
    size:            15237
    sdk_relative:    true
  - mtime:           1745047098000000000
    path:            'usr/lib/swift/_Builtin_float.swiftmodule/arm64-apple-ios.swiftinterface'
    size:            4214
    sdk_relative:    true
  - mtime:           1745049014000000000
    path:            'usr/lib/swift/Darwin.swiftmodule/arm64-apple-ios.swiftinterface'
    size:            18208
    sdk_relative:    true
  - mtime:           1745049469000000000
    path:            'usr/lib/swift/_Concurrency.swiftmodule/arm64-apple-ios.swiftinterface'
    size:            230583
    sdk_relative:    true
  - mtime:           1745049589000000000
    path:            'usr/lib/swift/_StringProcessing.swiftmodule/arm64-apple-ios.swiftinterface'
    size:            22860
    sdk_relative:    true
  - mtime:           1745050180000000000
    path:            'System/Library/Frameworks/Combine.framework/Modules/Combine.swiftmodule/arm64-apple-ios.swiftinterface'
    size:            167787
    sdk_relative:    true
  - mtime:           1745043435000000000
    path:            'usr/include/ObjectiveC.apinotes'
    size:            11147
    sdk_relative:    true
  - mtime:           1745043136000000000
    path:            'usr/include/Dispatch.apinotes'
    size:            19
    sdk_relative:    true
  - mtime:           1745050163000000000
    path:            'usr/lib/swift/ObjectiveC.swiftmodule/arm64-apple-ios.swiftinterface'
    size:            6550
    sdk_relative:    true
  - mtime:           1745050426000000000
    path:            'usr/lib/swift/Dispatch.swiftmodule/arm64-apple-ios.swiftinterface'
    size:            57123
    sdk_relative:    true
  - mtime:           1745050598000000000
    path:            'usr/lib/swift/CoreFoundation.swiftmodule/arm64-apple-ios.swiftinterface'
    size:            22812
    sdk_relative:    true
  - mtime:           1745209109000000000
    path:            'System/Library/Frameworks/Security.framework/Headers/Security.apinotes'
    size:            162
    sdk_relative:    true
  - mtime:           1745378346000000000
    path:            'usr/include/XPC.apinotes'
    size:            123
    sdk_relative:    true
  - mtime:           1745045809000000000
    path:            'System/Library/Frameworks/Foundation.framework/Headers/Foundation.apinotes'
    size:            81098
    sdk_relative:    true
  - mtime:           1745050566000000000
    path:            'usr/lib/swift/XPC.swiftmodule/arm64-apple-ios.swiftinterface'
    size:            33607
    sdk_relative:    true
  - mtime:           1745049506000000000
    path:            'usr/lib/swift/Observation.swiftmodule/arm64-apple-ios.swiftinterface'
    size:            3441
    sdk_relative:    true
  - mtime:           1745050193000000000
    path:            'usr/lib/swift/System.swiftmodule/arm64-apple-ios.swiftinterface'
    size:            95457
    sdk_relative:    true
  - mtime:           1745051025000000000
    path:            'System/Library/Frameworks/Foundation.framework/Modules/Foundation.swiftmodule/arm64-apple-ios.swiftinterface'
    size:            991688
    sdk_relative:    true
  - mtime:           1745052458000000000
    path:            'System/Library/Frameworks/UniformTypeIdentifiers.framework/Headers/UniformTypeIdentifiers.apinotes'
    size:            1666
    sdk_relative:    true
  - mtime:           1745051326000000000
    path:            'usr/lib/swift/UniformTypeIdentifiers.swiftmodule/arm64-apple-ios.swiftinterface'
    size:            20563
    sdk_relative:    true
version:         1
...
