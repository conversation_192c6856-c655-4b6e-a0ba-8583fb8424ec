{"": {"swift-dependencies": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp.build/master.swiftdeps"}, "/Users/<USER>/github/LingXia/examples/ios/lxapp/Sources/lxapp/lxapp/App.swift": {"dependencies": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp.build/App.d", "object": "/Users/<USER>/github/Ling<PERSON>ia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp.build/App.swift.o", "swiftmodule": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp.build/App~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp.build/App.swiftdeps"}, "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp.build/DerivedSources/resource_bundle_accessor.swift": {"dependencies": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp.build/resource_bundle_accessor.d", "object": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp.build/resource_bundle_accessor.swift.o", "swiftmodule": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp.build/resource_bundle_accessor~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp.build/resource_bundle_accessor.swiftdeps"}}