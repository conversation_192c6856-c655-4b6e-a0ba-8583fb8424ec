client:
  name: basic
  file-system: device-agnostic
tools: {}
targets:
  "PackageStructure": ["<PackageStructure>"]
  "lingxia-arm64-apple-ios-debug.module": ["<lingxia-arm64-apple-ios-debug.module>"]
  "lxapp-App-arm64-apple-ios-debug.exe": ["<lxapp-App-arm64-apple-ios-debug.exe>"]
  "lxapp-App-arm64-apple-ios-debug.module": ["<lxapp-App-arm64-apple-ios-debug.module>"]
  "lxapp-arm64-apple-ios-debug.module": ["<lxapp-arm64-apple-ios-debug.module>"]
  "main": ["<lxapp-App-arm64-apple-ios-debug.exe>","<lxapp-App-arm64-apple-ios-debug.module>"]
  "test": ["<lxapp-App-arm64-apple-ios-debug.exe>","<lxapp-App-arm64-apple-ios-debug.module>"]
default: "main"
nodes:
  "/Users/<USER>/github/LingXia/examples/ios/lxapp/xtool/.xtool-tmp/Sources/":
    is-directory-structure: true
    content-exclusion-patterns: [".git",".build"]
commands:
  "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/sources":
    tool: write-auxiliary-file
    inputs: ["<sources-file-list>","/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/LxApp.swift","/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/LxAppBase.swift","/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/LxAppViewController.swift","/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/NavigationBar.swift","/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/Notification.swift","/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/PageNavigation.swift","/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/PlatformTypes.swift","/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/TabBar.swift","/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/WebView.swift","/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/generated/LingXiaFFI/LingXiaFFI.swift","/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/generated/SwiftBridgeCore.swift","/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSCapsuleButton.swift","/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSLxApp.swift","/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSLxAppViewController.swift","/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSNavigationBar.swift","/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSPageNavigation.swift","/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSTabBar.swift","/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSViewController.swift","/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSCapsuleButton.swift","/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSLxApp.swift","/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSLxAppViewController.swift","/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSLxAppWindowController.swift","/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSNavigationBar.swift","/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSPageNavigation.swift","/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSTabBar.swift","/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSTabBarSupport.swift","/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSViewController.swift","/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSWindow.swift"]
    outputs: ["/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/sources"]
    description: "Write auxiliary file /Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/sources"

  "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp-App.product/Objects.LinkFileList":
    tool: write-auxiliary-file
    inputs: ["<link-file-list>","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/LingXiaFFI.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/LxApp.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/LxAppBase.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/LxAppViewController.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/NavigationBar.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/Notification.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/PageNavigation.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/PlatformTypes.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/SwiftBridgeCore.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/TabBar.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/WebView.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/iOSCapsuleButton.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/iOSLxApp.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/iOSLxAppViewController.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/iOSNavigationBar.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/iOSPageNavigation.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/iOSTabBar.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/iOSViewController.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSCapsuleButton.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSLxApp.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSLxAppViewController.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSLxAppWindowController.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSNavigationBar.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSPageNavigation.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSTabBar.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSTabBarSupport.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSViewController.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSWindow.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp.build/App.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp.build/resource_bundle_accessor.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp_App.build/stub.c.o"]
    outputs: ["/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp-App.product/Objects.LinkFileList"]
    description: "Write auxiliary file /Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp-App.product/Objects.LinkFileList"

  "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp.build/sources":
    tool: write-auxiliary-file
    inputs: ["<sources-file-list>","/Users/<USER>/github/LingXia/examples/ios/lxapp/Sources/lxapp/lxapp/App.swift","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp.build/DerivedSources/resource_bundle_accessor.swift"]
    outputs: ["/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp.build/sources"]
    description: "Write auxiliary file /Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp.build/sources"

  "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp_App.build/stub.c.o":
    tool: clang
    inputs: ["/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/Modules/lxapp.swiftmodule","/Users/<USER>/github/LingXia/examples/ios/lxapp/xtool/.xtool-tmp/Sources/stub.c"]
    outputs: ["/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp_App.build/stub.c.o"]
    description: "Compiling lxapp-App stub.c"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/clang","-fobjc-arc","-target","arm64-apple-ios17.0","-O0","-DSWIFT_PACKAGE=1","-DDEBUG=1","-fblocks","-index-store-path","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/index/store","-fmodules","-fmodule-name=lxapp_App","-fmodules-cache-path=/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/ModuleCache","-I","/Users/<USER>/github/LingXia/examples/ios/lxapp/xtool/.xtool-tmp/Sources/include","-fmodule-map-file=/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp.build/module.modulemap","-fmodule-map-file=/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/module.modulemap","-fmodule-map-file=/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/generated/module.modulemap","-isysroot","/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Library/Frameworks","-F","/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Library/PrivateFrameworks","-fPIC","-g","-MD","-MT","dependencies","-MF","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp_App.build/stub.c.d","-c","/Users/<USER>/github/LingXia/examples/ios/lxapp/xtool/.xtool-tmp/Sources/stub.c","-o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp_App.build/stub.c.o"]
    deps: "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp_App.build/stub.c.d"

  "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp_lxapp.bundle/Resources":
    tool: copy-tool
    inputs: ["/Users/<USER>/github/LingXia/examples/ios/lxapp/Sources/lxapp/Resources/"]
    outputs: ["/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp_lxapp.bundle/Resources/"]
    description: "Copying /Users/<USER>/github/LingXia/examples/ios/lxapp/Sources/lxapp/Resources"

  "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/swift-version--58304C5D6DBC2206.txt":
    tool: write-auxiliary-file
    inputs: ["<swift-get-version>","/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"]
    outputs: ["/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/swift-version--58304C5D6DBC2206.txt"]
    always-out-of-date: "true"
    description: "Write auxiliary file /Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/swift-version--58304C5D6DBC2206.txt"

  "<lingxia-arm64-apple-ios-debug.module>":
    tool: phony
    inputs: ["/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/LxApp.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/LxAppBase.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/LxAppViewController.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/NavigationBar.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/Notification.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/PageNavigation.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/PlatformTypes.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/TabBar.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/WebView.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/LingXiaFFI.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/SwiftBridgeCore.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/iOSCapsuleButton.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/iOSLxApp.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/iOSLxAppViewController.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/iOSNavigationBar.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/iOSPageNavigation.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/iOSTabBar.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/iOSViewController.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSCapsuleButton.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSLxApp.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSLxAppViewController.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSLxAppWindowController.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSNavigationBar.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSPageNavigation.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSTabBar.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSTabBarSupport.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSViewController.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSWindow.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/Modules/lingxia.swiftmodule"]
    outputs: ["<lingxia-arm64-apple-ios-debug.module>"]

  "<lxapp-App-arm64-apple-ios-debug.exe>":
    tool: phony
    inputs: ["/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp-App"]
    outputs: ["<lxapp-App-arm64-apple-ios-debug.exe>"]

  "<lxapp-App-arm64-apple-ios-debug.module>":
    tool: phony
    inputs: ["/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp_App.build/stub.c.o"]
    outputs: ["<lxapp-App-arm64-apple-ios-debug.module>"]

  "<lxapp-arm64-apple-ios-debug.module>":
    tool: phony
    inputs: ["/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp.build/App.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp.build/resource_bundle_accessor.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/Modules/lxapp.swiftmodule"]
    outputs: ["<lxapp-arm64-apple-ios-debug.module>"]

  "C.lingxia-arm64-apple-ios-debug.module":
    tool: shell
    inputs: ["/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/LxApp.swift","/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/LxAppBase.swift","/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/LxAppViewController.swift","/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/NavigationBar.swift","/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/Notification.swift","/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/PageNavigation.swift","/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/PlatformTypes.swift","/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/TabBar.swift","/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/WebView.swift","/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/generated/LingXiaFFI/LingXiaFFI.swift","/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/generated/SwiftBridgeCore.swift","/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSCapsuleButton.swift","/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSLxApp.swift","/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSLxAppViewController.swift","/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSNavigationBar.swift","/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSPageNavigation.swift","/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSTabBar.swift","/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSViewController.swift","/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSCapsuleButton.swift","/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSLxApp.swift","/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSLxAppViewController.swift","/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSLxAppWindowController.swift","/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSNavigationBar.swift","/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSPageNavigation.swift","/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSTabBar.swift","/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSTabBarSupport.swift","/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSViewController.swift","/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSWindow.swift","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/swift-version--58304C5D6DBC2206.txt","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/sources"]
    outputs: ["/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/LxApp.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/LxAppBase.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/LxAppViewController.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/NavigationBar.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/Notification.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/PageNavigation.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/PlatformTypes.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/TabBar.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/WebView.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/LingXiaFFI.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/SwiftBridgeCore.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/iOSCapsuleButton.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/iOSLxApp.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/iOSLxAppViewController.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/iOSNavigationBar.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/iOSPageNavigation.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/iOSTabBar.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/iOSViewController.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSCapsuleButton.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSLxApp.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSLxAppViewController.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSLxAppWindowController.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSNavigationBar.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSPageNavigation.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSTabBar.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSTabBarSupport.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSViewController.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSWindow.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/Modules/lingxia.swiftmodule"]
    description: "Compiling Swift Module 'lingxia' (28 sources)"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-module-name","lingxia","-emit-dependencies","-emit-module","-emit-module-path","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/Modules/lingxia.swiftmodule","-output-file-map","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/output-file-map.json","-parse-as-library","-incremental","-c","@/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/sources","-I","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/Modules","-target","arm64-apple-ios17.0","-enable-batch-mode","-index-store-path","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/index/store","-Onone","-enable-testing","-j8","-DSWIFT_PACKAGE","-DDEBUG","-Xcc","-fmodule-map-file=/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/generated/module.modulemap","-module-cache-path","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/ModuleCache","-parseable-output","-parse-as-library","-emit-objc-header","-emit-objc-header-path","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/lingxia-Swift.h","-color-diagnostics","-swift-version","6","-Xcc","-I/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/generated","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Library/Frameworks","-F","/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Library/PrivateFrameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/lib","-g","-Xcc","-isysroot","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Library/Frameworks","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Library/PrivateFrameworks","-Xcc","-fPIC","-Xcc","-g","-package-name","apple"]

  "C.lxapp-App-arm64-apple-ios-debug.exe":
    tool: shell
    inputs: ["/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/LingXiaFFI.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/LxApp.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/LxAppBase.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/LxAppViewController.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/NavigationBar.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/Notification.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/PageNavigation.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/PlatformTypes.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/SwiftBridgeCore.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/TabBar.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/WebView.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/iOSCapsuleButton.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/iOSLxApp.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/iOSLxAppViewController.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/iOSNavigationBar.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/iOSPageNavigation.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/iOSTabBar.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/iOSViewController.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSCapsuleButton.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSLxApp.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSLxAppViewController.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSLxAppWindowController.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSNavigationBar.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSPageNavigation.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSTabBar.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSTabBarSupport.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSViewController.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSWindow.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp.build/App.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp.build/resource_bundle_accessor.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp_App.build/stub.c.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp-App.product/Objects.LinkFileList"]
    outputs: ["/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp-App"]
    description: "Linking /Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp-App"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-L","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug","-o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp-App","-module-name","lxapp_App","-emit-executable","-Xlinker","-rpath","-Xlinker","@loader_path","@/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp-App.product/Objects.LinkFileList","-runtime-compatibility-version","none","-target","arm64-apple-ios17.0","-framework","JavaScriptCore","-framework","WebKit","/Users/<USER>/github/LingXia/examples/ios/target/aarch64-apple-ios/release/liblingxia.a","-Xlinker","-add_ast_path","-Xlinker","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/Modules/lingxia.swiftmodule","-Xlinker","-add_ast_path","-Xlinker","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/Modules/lxapp.swiftmodule","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Library/Frameworks","-F","/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Library/PrivateFrameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/lib","-g","-Xlinker","-rpath","-Xlinker","@executable_path/Frameworks"]

  "C.lxapp-arm64-apple-ios-debug.module":
    tool: shell
    inputs: ["/Users/<USER>/github/LingXia/examples/ios/lxapp/Sources/lxapp/lxapp/App.swift","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp.build/DerivedSources/resource_bundle_accessor.swift","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/swift-version--58304C5D6DBC2206.txt","<lxapp-arm64-apple-ios-debug.module-resources>","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/Modules/lingxia.swiftmodule","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp.build/sources"]
    outputs: ["/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp.build/App.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp.build/resource_bundle_accessor.swift.o","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/Modules/lxapp.swiftmodule"]
    description: "Compiling Swift Module 'lxapp' (2 sources)"
    args: ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc","-module-name","lxapp","-emit-dependencies","-emit-module","-emit-module-path","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/Modules/lxapp.swiftmodule","-output-file-map","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp.build/output-file-map.json","-parse-as-library","-incremental","-c","@/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp.build/sources","-I","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/Modules","-target","arm64-apple-ios17.0","-enable-batch-mode","-index-store-path","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/index/store","-Onone","-enable-testing","-j8","-DSWIFT_PACKAGE","-DDEBUG","-Xcc","-fmodule-map-file=/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/generated/module.modulemap","-module-cache-path","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/ModuleCache","-parseable-output","-parse-as-library","-emit-objc-header","-emit-objc-header-path","/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp.build/lxapp-Swift.h","-color-diagnostics","-swift-version","6","-sdk","/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk","-F","/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Library/Frameworks","-F","/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Library/PrivateFrameworks","-I","/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/lib","-L","/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/lib","-g","-Xcc","-isysroot","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Library/Frameworks","-Xcc","-F","-Xcc","/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Library/PrivateFrameworks","-Xcc","-fPIC","-Xcc","-g","-package-name","lxapp"]

  "PackageStructure":
    tool: package-structure-tool
    inputs: ["/Users/<USER>/github/LingXia/examples/ios/lxapp/xtool/.xtool-tmp/Sources/","/Users/<USER>/github/LingXia/examples/ios/lxapp/xtool/.xtool-tmp/Package.swift","/Users/<USER>/github/LingXia/examples/ios/lxapp/xtool/.xtool-tmp/Package.resolved"]
    outputs: ["<PackageStructure>"]
    description: "Planning build"
    allow-missing-inputs: true

  "lxapp-arm64-apple-ios-debug.module-resources":
    tool: phony
    inputs: ["/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp_lxapp.bundle/Resources/"]
    outputs: ["<lxapp-arm64-apple-ios-debug.module-resources>"]

