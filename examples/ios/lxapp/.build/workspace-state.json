{"object": {"artifacts": [], "dependencies": [{"basedOn": null, "packageRef": {"identity": "apple", "kind": "fileSystem", "location": "/Users/<USER>/github/LingXia/lingxia-sdk/apple", "name": "lingxia"}, "state": {"name": "fileSystem", "path": "/Users/<USER>/github/LingXia/lingxia-sdk/apple"}, "subpath": "apple"}, {"basedOn": null, "packageRef": {"identity": "lxapp", "kind": "fileSystem", "location": "/Users/<USER>/github/Ling<PERSON>ia/examples/ios/lxapp", "name": "lxapp"}, "state": {"name": "fileSystem", "path": "/Users/<USER>/github/Ling<PERSON>ia/examples/ios/lxapp"}, "subpath": "lxapp"}], "prebuilts": []}, "version": 7}