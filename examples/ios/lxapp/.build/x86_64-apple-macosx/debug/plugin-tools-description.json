{"builtTestProducts": [], "copyCommands": {"/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp_lxapp.bundle/Resources": {"inputs": [{"kind": "directory", "name": "/Users/<USER>/github/LingXia/examples/ios/lxapp/Sources/lxapp/Resources"}], "outputs": [{"kind": "directory", "name": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp_lxapp.bundle/Resources"}]}}, "explicitTargetDependencyImportCheckingMode": {"none": {}}, "generatedSourceTargetSet": [], "pluginDescriptions": [], "swiftCommands": {"C.lingxia-arm64-apple-ios-debug.module": {"executable": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "fileList": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/sources", "importPath": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/Modules", "inputs": [{"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/LxApp.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/LxAppBase.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/LxAppViewController.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/NavigationBar.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/Notification.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/PageNavigation.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/PlatformTypes.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/TabBar.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/WebView.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/generated/LingXiaFFI/LingXiaFFI.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/generated/SwiftBridgeCore.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSCapsuleButton.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSLxApp.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSLxAppViewController.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSNavigationBar.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSPageNavigation.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSTabBar.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSViewController.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSCapsuleButton.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSLxApp.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSLxAppViewController.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSLxAppWindowController.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSNavigationBar.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSPageNavigation.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSTabBar.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSTabBarSupport.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSViewController.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSWindow.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/swift-version--58304C5D6DBC2206.txt"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/sources"}], "isLibrary": true, "moduleName": "lingxia", "moduleOutputPath": "/Users/<USER>/github/Ling<PERSON>ia/examples/ios/lxapp/.build/arm64-apple-ios/debug/Modules/lingxia.swiftmodule", "objects": ["/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/LxApp.swift.o", "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/LxAppBase.swift.o", "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/LxAppViewController.swift.o", "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/NavigationBar.swift.o", "/Users/<USER>/github/Ling<PERSON>ia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/Notification.swift.o", "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/PageNavigation.swift.o", "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/PlatformTypes.swift.o", "/Users/<USER>/github/Ling<PERSON>ia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/TabBar.swift.o", "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/WebView.swift.o", "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/LingXiaFFI.swift.o", "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/SwiftBridgeCore.swift.o", "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/iOSCapsuleButton.swift.o", "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/iOSLxApp.swift.o", "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/iOSLxAppViewController.swift.o", "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/iOSNavigationBar.swift.o", "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/iOSPageNavigation.swift.o", "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/iOSTabBar.swift.o", "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/iOSViewController.swift.o", "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSCapsuleButton.swift.o", "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSLxApp.swift.o", "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSLxAppViewController.swift.o", "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSLxAppWindowController.swift.o", "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSNavigationBar.swift.o", "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSPageNavigation.swift.o", "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSTabBar.swift.o", "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSTabBarSupport.swift.o", "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSViewController.swift.o", "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSWindow.swift.o"], "otherArguments": ["-target", "arm64-apple-ios17.0", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-j8", "-DSWIFT_PACKAGE", "-DDEBUG", "-Xcc", "-fmodule-map-file=/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/generated/module.modulemap", "-module-cache-path", "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/lingxia-Swift.h", "-color-diagnostics", "-swift-version", "6", "-Xcc", "-I/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/generated", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "apple"], "outputFileMapPath": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/output-file-map.json", "outputs": [{"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/LxApp.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/LxAppBase.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/LxAppViewController.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/NavigationBar.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/Ling<PERSON>ia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/Notification.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/PageNavigation.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/PlatformTypes.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/Ling<PERSON>ia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/TabBar.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/WebView.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/LingXiaFFI.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/SwiftBridgeCore.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/iOSCapsuleButton.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/iOSLxApp.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/iOSLxAppViewController.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/iOSNavigationBar.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/iOSPageNavigation.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/iOSTabBar.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/iOSViewController.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSCapsuleButton.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSLxApp.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSLxAppViewController.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSLxAppWindowController.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSNavigationBar.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSPageNavigation.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSTabBar.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSTabBarSupport.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSViewController.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSWindow.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/Ling<PERSON>ia/examples/ios/lxapp/.build/arm64-apple-ios/debug/Modules/lingxia.swiftmodule"}], "prepareForIndexing": false, "sources": ["/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/LxApp.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/LxAppBase.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/LxAppViewController.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/NavigationBar.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/Notification.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/PageNavigation.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/PlatformTypes.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/TabBar.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/WebView.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/generated/LingXiaFFI/LingXiaFFI.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/generated/SwiftBridgeCore.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSCapsuleButton.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSLxApp.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSLxAppViewController.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSNavigationBar.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSPageNavigation.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSTabBar.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSViewController.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSCapsuleButton.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSLxApp.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSLxAppViewController.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSLxAppWindowController.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSNavigationBar.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSPageNavigation.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSTabBar.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSTabBarSupport.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSViewController.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSWindow.swift"], "tempsPath": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build", "wholeModuleOptimization": false}, "C.lxapp-arm64-apple-ios-debug.module": {"executable": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "fileList": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp.build/sources", "importPath": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/Modules", "inputs": [{"kind": "file", "name": "/Users/<USER>/github/Ling<PERSON>ia/examples/ios/lxapp/Sources/lxapp/lxapp/App.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp.build/DerivedSources/resource_bundle_accessor.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/swift-version--58304C5D6DBC2206.txt"}, {"kind": "virtual", "name": "<lxapp-arm64-apple-ios-debug.module-resources>"}, {"kind": "file", "name": "/Users/<USER>/github/Ling<PERSON>ia/examples/ios/lxapp/.build/arm64-apple-ios/debug/Modules/lingxia.swiftmodule"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp.build/sources"}], "isLibrary": true, "moduleName": "lxapp", "moduleOutputPath": "/Users/<USER>/github/Ling<PERSON>ia/examples/ios/lxapp/.build/arm64-apple-ios/debug/Modules/lxapp.swiftmodule", "objects": ["/Users/<USER>/github/Ling<PERSON>ia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp.build/App.swift.o", "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp.build/resource_bundle_accessor.swift.o"], "otherArguments": ["-target", "arm64-apple-ios17.0", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-j8", "-DSWIFT_PACKAGE", "-DDEBUG", "-Xcc", "-fmodule-map-file=/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/generated/module.modulemap", "-module-cache-path", "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp.build/lxapp-Swift.h", "-color-diagnostics", "-swift-version", "6", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "lxapp"], "outputFileMapPath": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp.build/output-file-map.json", "outputs": [{"kind": "file", "name": "/Users/<USER>/github/Ling<PERSON>ia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp.build/App.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp.build/resource_bundle_accessor.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/Ling<PERSON>ia/examples/ios/lxapp/.build/arm64-apple-ios/debug/Modules/lxapp.swiftmodule"}], "prepareForIndexing": false, "sources": ["/Users/<USER>/github/Ling<PERSON>ia/examples/ios/lxapp/Sources/lxapp/lxapp/App.swift", "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp.build/DerivedSources/resource_bundle_accessor.swift"], "tempsPath": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp.build", "wholeModuleOptimization": false}}, "swiftFrontendCommands": {}, "swiftTargetScanArgs": {"lingxia": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "-module-name", "lingxia", "-package-name", "apple", "-incremental", "-c", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/LxApp.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/LxAppBase.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/LxAppViewController.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/NavigationBar.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/Notification.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/PageNavigation.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/PlatformTypes.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/TabBar.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/WebView.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/generated/LingXiaFFI/LingXiaFFI.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/generated/SwiftBridgeCore.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSCapsuleButton.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSLxApp.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSLxAppViewController.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSNavigationBar.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSPageNavigation.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSTabBar.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSViewController.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSCapsuleButton.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSLxApp.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSLxAppViewController.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSLxAppWindowController.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSNavigationBar.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSPageNavigation.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSTabBar.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSTabBarSupport.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSViewController.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSWindow.swift", "-I", "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/Modules", "-target", "arm64-apple-ios17.0", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-j8", "-DSWIFT_PACKAGE", "-DDEBUG", "-Xcc", "-fmodule-map-file=/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/generated/module.modulemap", "-module-cache-path", "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/lingxia-Swift.h", "-color-diagnostics", "-swift-version", "6", "-Xcc", "-I/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/generated", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "apple", "-driver-use-frontend-path", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"], "lxapp": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "-module-name", "lxapp", "-package-name", "lxapp", "-incremental", "-c", "/Users/<USER>/github/Ling<PERSON>ia/examples/ios/lxapp/Sources/lxapp/lxapp/App.swift", "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp.build/DerivedSources/resource_bundle_accessor.swift", "-I", "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/Modules", "-target", "arm64-apple-ios17.0", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-j8", "-DSWIFT_PACKAGE", "-DDEBUG", "-Xcc", "-fmodule-map-file=/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/generated/module.modulemap", "-module-cache-path", "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp.build/lxapp-Swift.h", "-color-diagnostics", "-swift-version", "6", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.5.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/iPhoneOS.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "lxapp", "-driver-use-frontend-path", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"]}, "targetDependencyMap": {"lingxia": ["CLingXiaFFI"], "lxapp": ["lingxia", "CLingXiaFFI"], "lxapp_App": ["lxapp", "lingxia", "CLingXiaFFI"]}, "testDiscoveryCommands": {}, "testEntryPointCommands": {}, "traitConfiguration": {"enableAllTraits": false}, "writeCommands": {"/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/sources": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<sources-file-list>"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/LxApp.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/LxAppBase.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/LxAppViewController.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/NavigationBar.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/Notification.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/PageNavigation.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/PlatformTypes.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/TabBar.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/WebView.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/generated/LingXiaFFI/LingXiaFFI.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/generated/SwiftBridgeCore.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSCapsuleButton.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSLxApp.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSLxAppViewController.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSNavigationBar.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSPageNavigation.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSTabBar.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSViewController.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSCapsuleButton.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSLxApp.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSLxAppViewController.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSLxAppWindowController.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSNavigationBar.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSPageNavigation.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSTabBar.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSTabBarSupport.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSViewController.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSWindow.swift"}], "outputFilePath": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/sources"}, "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp-App.product/Objects.LinkFileList": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<link-file-list>"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/LingXiaFFI.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/LxApp.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/LxAppBase.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/LxAppViewController.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/NavigationBar.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/Ling<PERSON>ia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/Notification.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/PageNavigation.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/PlatformTypes.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/SwiftBridgeCore.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/Ling<PERSON>ia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/TabBar.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/WebView.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/iOSCapsuleButton.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/iOSLxApp.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/iOSLxAppViewController.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/iOSNavigationBar.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/iOSPageNavigation.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/iOSTabBar.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/iOSViewController.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSCapsuleButton.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSLxApp.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSLxAppViewController.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSLxAppWindowController.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSNavigationBar.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSPageNavigation.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSTabBar.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSTabBarSupport.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSViewController.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lingxia.build/macOSWindow.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/Ling<PERSON>ia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp.build/App.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp.build/resource_bundle_accessor.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp_App.build/stub.c.o"}], "outputFilePath": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp-App.product/Objects.LinkFileList"}, "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp.build/sources": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<sources-file-list>"}, {"kind": "file", "name": "/Users/<USER>/github/Ling<PERSON>ia/examples/ios/lxapp/Sources/lxapp/lxapp/App.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp.build/DerivedSources/resource_bundle_accessor.swift"}], "outputFilePath": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/lxapp.build/sources"}, "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/swift-version--58304C5D6DBC2206.txt": {"alwaysOutOfDate": true, "inputs": [{"kind": "virtual", "name": "<swift-get-version>"}, {"kind": "file", "name": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"}], "outputFilePath": "/Users/<USER>/github/LingXia/examples/ios/lxapp/.build/arm64-apple-ios/debug/swift-version--58304C5D6DBC2206.txt"}}}