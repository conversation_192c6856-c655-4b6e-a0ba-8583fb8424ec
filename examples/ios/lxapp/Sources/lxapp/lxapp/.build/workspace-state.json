{"object": {"artifacts": [], "dependencies": [{"basedOn": null, "packageRef": {"identity": "miniapp", "kind": "fileSystem", "location": "/Users/<USER>/github/LingXia/lingxia-webview/examples/ios/miniapp/Sources/miniapp", "name": "miniapp"}, "state": {"name": "fileSystem", "path": "/Users/<USER>/github/LingXia/lingxia-webview/examples/ios/miniapp/Sources/miniapp"}, "subpath": "miniapp"}], "prebuilts": []}, "version": 7}