.container {
    background-color: #fff;
    padding: 2rem 2rem 3rem 2rem;
    border-radius: 8px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    width: 80%;
    max-width: 400px;
    z-index: 1;
    position: relative;
}

h1 {
    color: #3b82f6;
    margin-top: 0;
    margin-bottom: 1.5rem;
    text-align: center;
    font-size: 2rem;
    font-weight: 600;
    letter-spacing: -0.5px;
}

.input-group {
    display: flex;
    margin-bottom: 1.5rem;
}

input {
    flex: 1;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px 0 0 4px;
    font-size: 1rem;
}

input:focus {
    outline: none;
    border-color: #3b82f6;
}

button {
    background-color: #3b82f6;
    color: white;
    border: none;
    padding: 0.75rem 1.5rem;
    border-radius: 0 4px 4px 0;
    cursor: pointer;
    font-size: 1rem;
    transition: background-color 0.2s;
}

button:hover {
    background-color: #2563eb;
}

button:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

.result {
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 4px;
    border-left: 4px solid #3b82f6;
    min-height: 1.5rem;
}

.hidden {
    display: none;
}

.ip-info {
    position: absolute;
    bottom: 10px;
    left: 20px;
    right: 20px;
    padding: 0.4rem 0.8rem;
    color: #888;
    font-size: 0.8rem;
    font-family: monospace;
    text-align: center;
    z-index: 2;
}

.footer {
    position: absolute;
    bottom: 10px;
    left: 10px;
    padding: 0.5rem 1rem;
    text-align: left;
    color: #333;
    font-size: 0.9rem;
    background-color: rgba(255, 255, 255, 0.8);
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    max-width: 80%;
    z-index: 2;
}
