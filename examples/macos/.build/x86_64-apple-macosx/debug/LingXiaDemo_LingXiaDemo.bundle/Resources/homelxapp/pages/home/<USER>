<!DOCTYPE html>
<html>
<head>
    <title>Home</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no">
    <link rel="stylesheet" href="./index.css">
</head>
<body>
    <div class="container">
        <h1>Welcome to LingXia</h1>

        <div class="input-group">
            <input type="text" id="name-input" placeholder="Enter a name" />
            <button id="greet-button">Greet</button>
        </div>

        <div id="result" class="result hidden"></div>

        <div class="ip-info" id="ip-info"></div>
    </div>

    <script>
        const nameInput = document.getElementById('name-input');
        const greetButton = document.getElementById('greet-button');
        const resultElement = document.getElementById('result');
        const ipInfo = document.getElementById('ip-info');

        // Send greeting to native layer
        function sendGreeting() {
            const name = nameInput.value.trim();
            if (!name) {
                nameInput.focus();
                return;
            }

            greetButton.disabled = true;
            greet({name});
        }

        // Display result message
        function showResult(message) {
            resultElement.textContent = message;
            resultElement.classList.remove('hidden');
        }

        // Set background image using img element (compatible with lx:// scheme)
        function setBackgroundImage(imageUrl) {
            // Remove existing background image if any
            const existingBg = document.getElementById('background-image');
            if (existingBg) {
                existingBg.remove();
            }

            // Create img element as background
            const bgImg = document.createElement('img');
            bgImg.id = 'background-image';
            bgImg.src = imageUrl;
            bgImg.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                object-fit: cover;
                z-index: -1;
                pointer-events: none;
            `;

            // Insert as first child to ensure it's behind other content
            document.body.insertBefore(bgImg, document.body.firstChild);
        }

        // Process initial data
        function handleInitialData(data) {
            if (data.greeting) showResult(data.greeting);
            if (data.imageUrl) setBackgroundImage(data.imageUrl);
            if (data.ipAddr) ipInfo.textContent = `Public IP: ${data.ipAddr}`;
        }

        // Subscribe to data updates
        LingXiaBridge.subscribe((data, callbackId, isInitialData) => {
            if (isInitialData) {
                handleInitialData(data);
            } else {
                // Handle data updates
                if (data.greeting) {
                    showResult(data.greeting);
                    greetButton.disabled = false;
                }

                // Handle IP address update from setData
                if (data.ipAddr) {
                    console.log("Received IP via setData:", data.ipAddr);
                    ipInfo.textContent = `Public IP: ${data.ipAddr}`;
                }
            }
        });

        // Event listeners
        greetButton.addEventListener('click', sendGreeting);
        nameInput.addEventListener('keypress', e => {
            if (e.key === 'Enter') sendGreeting();
        });
    </script>
<script>
window.__PAGE_FUNCTIONS = ["greet"];

// Generate bridge functions
window.__PAGE_FUNCTIONS.forEach(function(funcName) {
  window[funcName] = function(...args) {
    return window.LingXiaBridge.call(funcName, args.length === 1 ? args[0] : args);
  };
});
</script>
</body>
</html>

