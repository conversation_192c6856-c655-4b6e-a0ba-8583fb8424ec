{"": {"swift-dependencies": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/master.swiftdeps"}, "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/LxApp.swift": {"dependencies": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/LxApp.d", "object": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/LxApp.swift.o", "swiftmodule": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/LxApp~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/LxApp.swiftdeps"}, "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/LxAppBase.swift": {"dependencies": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/LxAppBase.d", "object": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/LxAppBase.swift.o", "swiftmodule": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/LxAppBase~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/LxAppBase.swiftdeps"}, "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/LxAppViewController.swift": {"dependencies": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/LxAppViewController.d", "object": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/LxAppViewController.swift.o", "swiftmodule": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/LxAppViewController~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/LxAppViewController.swiftdeps"}, "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/NavigationBar.swift": {"dependencies": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/NavigationBar.d", "object": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/NavigationBar.swift.o", "swiftmodule": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/NavigationBar~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/NavigationBar.swiftdeps"}, "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/Notification.swift": {"dependencies": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/Notification.d", "object": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/Notification.swift.o", "swiftmodule": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/Notification~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/Notification.swiftdeps"}, "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/PageNavigation.swift": {"dependencies": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/PageNavigation.d", "object": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/PageNavigation.swift.o", "swiftmodule": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/PageNavigation~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/PageNavigation.swiftdeps"}, "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/PlatformTypes.swift": {"dependencies": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/PlatformTypes.d", "object": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/PlatformTypes.swift.o", "swiftmodule": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/PlatformTypes~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/PlatformTypes.swiftdeps"}, "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/TabBar.swift": {"dependencies": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/TabBar.d", "object": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/TabBar.swift.o", "swiftmodule": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/TabBar~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/TabBar.swiftdeps"}, "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/WebView.swift": {"dependencies": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/WebView.d", "object": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/WebView.swift.o", "swiftmodule": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/WebView~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/WebView.swiftdeps"}, "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/generated/LingXiaFFI/LingXiaFFI.swift": {"dependencies": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/LingXiaFFI.d", "object": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/LingXiaFFI.swift.o", "swiftmodule": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/LingXiaFFI~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/LingXiaFFI.swiftdeps"}, "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/generated/SwiftBridgeCore.swift": {"dependencies": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/SwiftBridgeCore.d", "object": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/SwiftBridgeCore.swift.o", "swiftmodule": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/SwiftBridgeCore~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/SwiftBridgeCore.swiftdeps"}, "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSCapsuleButton.swift": {"dependencies": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/iOSCapsuleButton.d", "object": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/iOSCapsuleButton.swift.o", "swiftmodule": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/iOSCapsuleButton~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/iOSCapsuleButton.swiftdeps"}, "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSLxApp.swift": {"dependencies": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/iOSLxApp.d", "object": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/iOSLxApp.swift.o", "swiftmodule": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/iOSLxApp~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/iOSLxApp.swiftdeps"}, "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSLxAppViewController.swift": {"dependencies": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/iOSLxAppViewController.d", "object": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/iOSLxAppViewController.swift.o", "swiftmodule": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/iOSLxAppViewController~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/iOSLxAppViewController.swiftdeps"}, "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSNavigationBar.swift": {"dependencies": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/iOSNavigationBar.d", "object": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/iOSNavigationBar.swift.o", "swiftmodule": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/iOSNavigationBar~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/iOSNavigationBar.swiftdeps"}, "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSPageNavigation.swift": {"dependencies": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/iOSPageNavigation.d", "object": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/iOSPageNavigation.swift.o", "swiftmodule": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/iOSPageNavigation~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/iOSPageNavigation.swiftdeps"}, "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSTabBar.swift": {"dependencies": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/iOSTabBar.d", "object": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/iOSTabBar.swift.o", "swiftmodule": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/iOSTabBar~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/iOSTabBar.swiftdeps"}, "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSViewController.swift": {"dependencies": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/iOSViewController.d", "object": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/iOSViewController.swift.o", "swiftmodule": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/iOSViewController~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/iOSViewController.swiftdeps"}, "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSCapsuleButton.swift": {"dependencies": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSCapsuleButton.d", "object": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSCapsuleButton.swift.o", "swiftmodule": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSCapsuleButton~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSCapsuleButton.swiftdeps"}, "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSLxApp.swift": {"dependencies": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSLxApp.d", "object": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSLxApp.swift.o", "swiftmodule": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSLxApp~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSLxApp.swiftdeps"}, "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSLxAppViewController.swift": {"dependencies": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSLxAppViewController.d", "object": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSLxAppViewController.swift.o", "swiftmodule": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSLxAppViewController~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSLxAppViewController.swiftdeps"}, "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSLxAppWindowController.swift": {"dependencies": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSLxAppWindowController.d", "object": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSLxAppWindowController.swift.o", "swiftmodule": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSLxAppWindowController~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSLxAppWindowController.swiftdeps"}, "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSNavigationBar.swift": {"dependencies": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSNavigationBar.d", "object": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSNavigationBar.swift.o", "swiftmodule": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSNavigationBar~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSNavigationBar.swiftdeps"}, "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSPageNavigation.swift": {"dependencies": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSPageNavigation.d", "object": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSPageNavigation.swift.o", "swiftmodule": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSPageNavigation~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSPageNavigation.swiftdeps"}, "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSTabBar.swift": {"dependencies": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSTabBar.d", "object": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSTabBar.swift.o", "swiftmodule": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSTabBar~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSTabBar.swiftdeps"}, "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSTabBarSupport.swift": {"dependencies": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSTabBarSupport.d", "object": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSTabBarSupport.swift.o", "swiftmodule": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSTabBarSupport~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSTabBarSupport.swiftdeps"}, "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSViewController.swift": {"dependencies": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSViewController.d", "object": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSViewController.swift.o", "swiftmodule": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSViewController~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSViewController.swiftdeps"}, "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSWindow.swift": {"dependencies": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSWindow.d", "object": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSWindow.swift.o", "swiftmodule": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSWindow~partial.swiftmodule", "swift-dependencies": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSWindow.swiftdeps"}}