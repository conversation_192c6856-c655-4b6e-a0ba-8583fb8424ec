{"builtTestProducts": [], "copyCommands": {"/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/LingXiaDemo_LingXiaDemo.bundle/Resources": {"inputs": [{"kind": "directory", "name": "/Users/<USER>/github/LingXia/examples/macos/Sources/Resources"}], "outputs": [{"kind": "directory", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/LingXiaDemo_LingXiaDemo.bundle/Resources"}]}}, "explicitTargetDependencyImportCheckingMode": {"none": {}}, "generatedSourceTargetSet": [], "pluginDescriptions": [], "swiftCommands": {"C.LingXiaDemo-x86_64-apple-macosx15.0-debug.module": {"executable": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "fileList": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/LingXiaDemo.build/sources", "importPath": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/Modules", "inputs": [{"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/Sources/main.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/LingXiaDemo.build/DerivedSources/resource_bundle_accessor.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"}, {"kind": "virtual", "name": "<LingXiaDemo-x86_64-apple-macosx15.0-debug.module-resources>"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/Modules/lingxia.swiftmodule"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/LingXiaDemo.build/sources"}], "isLibrary": false, "moduleName": "LingXiaDemo", "moduleOutputPath": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/Modules/LingXiaDemo.swiftmodule", "objects": ["/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/LingXiaDemo.build/main.swift.o", "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/LingXiaDemo.build/resource_bundle_accessor.swift.o"], "otherArguments": ["-target", "x86_64-apple-macosx11.0", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-j8", "-DSWIFT_PACKAGE", "-DDEBUG", "-Xcc", "-fmodule-map-file=/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/generated/module.modulemap", "-module-cache-path", "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/ModuleCache", "-parseable-output", "-Xfrontend", "-entry-point-function-name", "-Xfrontend", "LingXiaDemo_main", "-color-diagnostics", "-swift-version", "6", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "macos"], "outputFileMapPath": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/LingXiaDemo.build/output-file-map.json", "outputs": [{"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/LingXiaDemo.build/main.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/LingXiaDemo.build/resource_bundle_accessor.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/Modules/LingXiaDemo.swiftmodule"}], "prepareForIndexing": false, "sources": ["/Users/<USER>/github/LingXia/examples/macos/Sources/main.swift", "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/LingXiaDemo.build/DerivedSources/resource_bundle_accessor.swift"], "tempsPath": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/LingXiaDemo.build", "wholeModuleOptimization": false}, "C.lingxia-x86_64-apple-macosx15.0-debug.module": {"executable": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "fileList": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/sources", "importPath": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/Modules", "inputs": [{"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/LxApp.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/LxAppBase.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/LxAppViewController.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/NavigationBar.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/Notification.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/PageNavigation.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/PlatformTypes.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/TabBar.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/WebView.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/generated/LingXiaFFI/LingXiaFFI.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/generated/SwiftBridgeCore.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSCapsuleButton.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSLxApp.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSLxAppViewController.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSNavigationBar.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSPageNavigation.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSTabBar.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSViewController.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSCapsuleButton.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSLxApp.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSLxAppViewController.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSLxAppWindowController.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSNavigationBar.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSPageNavigation.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSTabBar.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSTabBarSupport.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSViewController.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSWindow.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/sources"}], "isLibrary": true, "moduleName": "lingxia", "moduleOutputPath": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/Modules/lingxia.swiftmodule", "objects": ["/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/LxApp.swift.o", "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/LxAppBase.swift.o", "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/LxAppViewController.swift.o", "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/NavigationBar.swift.o", "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/Notification.swift.o", "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/PageNavigation.swift.o", "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/PlatformTypes.swift.o", "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/TabBar.swift.o", "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/WebView.swift.o", "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/LingXiaFFI.swift.o", "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/SwiftBridgeCore.swift.o", "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/iOSCapsuleButton.swift.o", "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/iOSLxApp.swift.o", "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/iOSLxAppViewController.swift.o", "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/iOSNavigationBar.swift.o", "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/iOSPageNavigation.swift.o", "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/iOSTabBar.swift.o", "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/iOSViewController.swift.o", "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSCapsuleButton.swift.o", "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSLxApp.swift.o", "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSLxAppViewController.swift.o", "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSLxAppWindowController.swift.o", "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSNavigationBar.swift.o", "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSPageNavigation.swift.o", "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSTabBar.swift.o", "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSTabBarSupport.swift.o", "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSViewController.swift.o", "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSWindow.swift.o"], "otherArguments": ["-target", "x86_64-apple-macosx11.0", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-j8", "-DSWIFT_PACKAGE", "-DDEBUG", "-Xcc", "-fmodule-map-file=/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/generated/module.modulemap", "-module-cache-path", "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/lingxia-Swift.h", "-color-diagnostics", "-swift-version", "6", "-Xcc", "-I/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/generated", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "apple"], "outputFileMapPath": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/output-file-map.json", "outputs": [{"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/LxApp.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/LxAppBase.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/LxAppViewController.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/NavigationBar.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/Notification.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/PageNavigation.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/PlatformTypes.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/TabBar.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/WebView.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/LingXiaFFI.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/SwiftBridgeCore.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/iOSCapsuleButton.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/iOSLxApp.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/iOSLxAppViewController.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/iOSNavigationBar.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/iOSPageNavigation.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/iOSTabBar.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/iOSViewController.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSCapsuleButton.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSLxApp.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSLxAppViewController.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSLxAppWindowController.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSNavigationBar.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSPageNavigation.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSTabBar.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSTabBarSupport.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSViewController.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSWindow.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/Modules/lingxia.swiftmodule"}], "prepareForIndexing": false, "sources": ["/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/LxApp.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/LxAppBase.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/LxAppViewController.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/NavigationBar.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/Notification.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/PageNavigation.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/PlatformTypes.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/TabBar.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/WebView.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/generated/LingXiaFFI/LingXiaFFI.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/generated/SwiftBridgeCore.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSCapsuleButton.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSLxApp.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSLxAppViewController.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSNavigationBar.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSPageNavigation.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSTabBar.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSViewController.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSCapsuleButton.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSLxApp.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSLxAppViewController.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSLxAppWindowController.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSNavigationBar.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSPageNavigation.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSTabBar.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSTabBarSupport.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSViewController.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSWindow.swift"], "tempsPath": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build", "wholeModuleOptimization": false}}, "swiftFrontendCommands": {}, "swiftTargetScanArgs": {"LingXiaDemo": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "-module-name", "LingXiaDemo", "-package-name", "macos", "-incremental", "-c", "/Users/<USER>/github/LingXia/examples/macos/Sources/main.swift", "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/LingXiaDemo.build/DerivedSources/resource_bundle_accessor.swift", "-I", "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/Modules", "-target", "x86_64-apple-macosx11.0", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-j8", "-DSWIFT_PACKAGE", "-DDEBUG", "-Xcc", "-fmodule-map-file=/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/generated/module.modulemap", "-module-cache-path", "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/ModuleCache", "-parseable-output", "-Xfrontend", "-entry-point-function-name", "-Xfrontend", "LingXiaDemo_main", "-color-diagnostics", "-swift-version", "6", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "macos", "-driver-use-frontend-path", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"], "lingxia": ["/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc", "-module-name", "lingxia", "-package-name", "apple", "-incremental", "-c", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/LxApp.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/LxAppBase.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/LxAppViewController.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/NavigationBar.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/Notification.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/PageNavigation.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/PlatformTypes.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/TabBar.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/WebView.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/generated/LingXiaFFI/LingXiaFFI.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/generated/SwiftBridgeCore.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSCapsuleButton.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSLxApp.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSLxAppViewController.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSNavigationBar.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSPageNavigation.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSTabBar.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSViewController.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSCapsuleButton.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSLxApp.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSLxAppViewController.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSLxAppWindowController.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSNavigationBar.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSPageNavigation.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSTabBar.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSTabBarSupport.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSViewController.swift", "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSWindow.swift", "-I", "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/Modules", "-target", "x86_64-apple-macosx11.0", "-enable-batch-mode", "-index-store-path", "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/index/store", "-<PERSON><PERSON>", "-enable-testing", "-j8", "-DSWIFT_PACKAGE", "-DDEBUG", "-Xcc", "-fmodule-map-file=/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/generated/module.modulemap", "-module-cache-path", "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/ModuleCache", "-parseable-output", "-parse-as-library", "-emit-objc-header", "-emit-objc-header-path", "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/lingxia-Swift.h", "-color-diagnostics", "-swift-version", "6", "-Xcc", "-I/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/generated", "-sdk", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-F", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-I", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-L", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/usr/lib", "-g", "-Xcc", "-is<PERSON><PERSON>", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/SDKs/MacOSX15.5.sdk", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/Frameworks", "-Xcc", "-F", "-Xcc", "/Applications/Xcode.app/Contents/Developer/Platforms/MacOSX.platform/Developer/Library/PrivateFrameworks", "-Xcc", "-fPIC", "-Xcc", "-g", "-package-name", "apple", "-driver-use-frontend-path", "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"]}, "targetDependencyMap": {"LingXiaDemo": ["lingxia", "CLingXiaFFI"], "lingxia": ["CLingXiaFFI"]}, "testDiscoveryCommands": {}, "testEntryPointCommands": {}, "traitConfiguration": {"enableAllTraits": false}, "writeCommands": {"/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/LingXiaDemo-entitlement.plist": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<entitlement-plist>"}, {"kind": "virtual", "name": "<com.apple.security.get-task-allow>"}], "outputFilePath": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/LingXiaDemo-entitlement.plist"}, "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/LingXiaDemo.build/sources": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<sources-file-list>"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/Sources/main.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/LingXiaDemo.build/DerivedSources/resource_bundle_accessor.swift"}], "outputFilePath": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/LingXiaDemo.build/sources"}, "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/LingXiaDemo.product/Objects.LinkFileList": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<link-file-list>"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/LingXiaDemo.build/main.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/LingXiaDemo.build/resource_bundle_accessor.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/LingXiaFFI.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/LxApp.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/LxAppBase.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/LxAppViewController.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/NavigationBar.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/Notification.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/PageNavigation.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/PlatformTypes.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/SwiftBridgeCore.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/TabBar.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/WebView.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/iOSCapsuleButton.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/iOSLxApp.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/iOSLxAppViewController.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/iOSNavigationBar.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/iOSPageNavigation.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/iOSTabBar.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/iOSViewController.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSCapsuleButton.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSLxApp.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSLxAppViewController.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSLxAppWindowController.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSNavigationBar.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSPageNavigation.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSTabBar.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSTabBarSupport.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSViewController.swift.o"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/macOSWindow.swift.o"}], "outputFilePath": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/LingXiaDemo.product/Objects.LinkFileList"}, "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/sources": {"alwaysOutOfDate": false, "inputs": [{"kind": "virtual", "name": "<sources-file-list>"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/LxApp.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/LxAppBase.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/LxAppViewController.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/NavigationBar.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/Notification.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/PageNavigation.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/PlatformTypes.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/TabBar.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/WebView.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/generated/LingXiaFFI/LingXiaFFI.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/generated/SwiftBridgeCore.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSCapsuleButton.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSLxApp.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSLxAppViewController.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSNavigationBar.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSPageNavigation.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSTabBar.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/iOS/iOSViewController.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSCapsuleButton.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSLxApp.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSLxAppViewController.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSLxAppWindowController.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSNavigationBar.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSPageNavigation.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSTabBar.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSTabBarSupport.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSViewController.swift"}, {"kind": "file", "name": "/Users/<USER>/github/LingXia/lingxia-sdk/apple/Sources/macOS/macOSWindow.swift"}], "outputFilePath": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/lingxia.build/sources"}, "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt": {"alwaysOutOfDate": true, "inputs": [{"kind": "virtual", "name": "<swift-get-version>"}, {"kind": "file", "name": "/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/bin/swiftc"}], "outputFilePath": "/Users/<USER>/github/LingXia/examples/macos/.build/x86_64-apple-macosx/debug/swift-version--58304C5D6DBC2206.txt"}}}