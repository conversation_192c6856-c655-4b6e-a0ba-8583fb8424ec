{":harmony:lingxia:default@PreBuild": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"apiType\",\"_value\":\"stageMode\",\"_valueType\":\"string\",\"_hash\":\"699c7565645ea3ea8c88551a4926a3d5\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"byteCodeHar\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"1918a3ccb645ccacd0aedd84fc6cf8a4\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"codeType\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"d6dcf21a9f078b661283507536e57ce8\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compatibleApiVersion\",\"_value\":17,\"_valueType\":\"number\",\"_hash\":\"7ebeb63c8f8703f7a7166363b4ae2928\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compileApiVersion\",\"_value\":17,\"_valueType\":\"number\",\"_hash\":\"7ed709b82b73f46ab4cfd211b8cb709b\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"configuration\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"d12f0038691f8f34d654391bbcee2f8e\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"configurationFile<PERSON><PERSON>\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"2b55287f40f7e8896b21bab4028e156b\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"customTypes\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"970a2695bffac1c5a4fa283dc36050e3\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"deviceType\",\"_value\":[\"default\",\"tablet\"],\"_valueType\":\"object\",\"_hash\":\"3f44547af15457ff8e1ec09282ca9922\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"integrated_hsp\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"2538281751f182d9123d2ab28efaf9be\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isSupportOhpmProj\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"f3a249d7e3f751316e931b8a08b074b4\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"moduleJsonOpt\",\"_value\":\"{\\\"jsonFilePath\\\":\\\"/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/module.json5\\\",\\\"profile\\\":{\\\"module\\\":{\\\"name\\\":\\\"lingxia\\\",\\\"type\\\":\\\"har\\\",\\\"deviceTypes\\\":[\\\"default\\\",\\\"tablet\\\"],\\\"requestPermissions\\\":[{\\\"name\\\":\\\"ohos.permission.INTERNET\\\"}]}},\\\"deviceTypes\\\":[\\\"default\\\",\\\"tablet\\\"],\\\"deviceConfig\\\":\\\"deviceTypes\\\",\\\"configurationProfile\\\":\\\"module.json5\\\"}\",\"_valueType\":\"string\",\"_hash\":\"f5a26e11bb22897675f723598d98a3b6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"profileModuleName\",\"_value\":\"lingxia\",\"_valueType\":\"string\",\"_hash\":\"1f20e709cdcc3e410c5dfb43b0608109\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"removePermissions\",\"_valueType\":\"undefined\",\"_hash\":\"0703cfc523a152c6195a8fd1935503e5\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"sdkToolchainsComponentVersion\",\"_value\":\"5.0.5.165\",\"_valueType\":\"string\",\"_hash\":\"9159f11811aaec1d86cd2ff98ff51b00\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"sourceRoots\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"a369c115d2c4122f3819759804ec9d35\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"targetStatusCode\",\"_value\":2,\"_valueType\":\"number\",\"_hash\":\"5a9255c0f4ee50904a9349ebafca8369\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"transformLib\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"7d270d0ce7ae5c6e2e32760cb396ea5a\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"useNormalizedOHMUrl\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"05eabcf12e6fe230e579e0fd37679db6\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "lingxia", "_taskName": "default@PreBuild", "_key": ":harmony:lingxia:default@PreBuild", "_executionId": ":harmony:lingxia:default@PreBuild:1753959082899", "_inputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/AppScope/app.json5", {"isDirectory": false, "fileSnapShotHashValue": "02c5f7337f114720a3047a05628e8b89"}], ["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/module.json5", {"isDirectory": false, "fileSnapShotHashValue": "d1e04b294833c3b0aabf4ee492042755"}], ["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/build-profile.json5", {"fileSnapShotHashValue": "c6477928a820b164e358dfcf9a324091"}], ["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build-profile.json5", {"fileSnapShotHashValue": "117dd35b0a260dbc88aa7dcfde0d2609"}], ["/Users/<USER>/github/Ling<PERSON>ia/lingxia-sdk/harmony/hvigor/hvigor-config.json5", {"isDirectory": true, "fileSnapShotHashValue": "a5078735f32381839b23546ca84d6ab7"}], ["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/oh-package.json5", {"fileSnapShotHashValue": "40de8b8b9c6b9e68c3cfc79af5606691"}], ["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/oh-package.json5", {"fileSnapShotHashValue": "911264701269ca8cdbdd3d9405356687"}]]}, "_outputFiles": {"dataType": "Map", "value": []}}, ":harmony:lingxia:default@CreateHarBuildProfile": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"buildModeName\",\"_value\":\"debug\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"buildMode\",\"_value\":\"Debug\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"buildProfileFields\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"har<PERSON><PERSON><PERSON>\",\"_value\":\"1.0.0\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"harTargetName\",\"_value\":\"default\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "lingxia", "_taskName": "default@CreateHarBuildProfile", "_key": ":harmony:lingxia:default@CreateHarBuildProfile", "_executionId": ":harmony:lingxia:default@CreateHarBuildProfile:1753969459313", "_inputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/AppScope/app.json5", {"fileSnapShotHashValue": "02c5f7337f114720a3047a05628e8b89"}], ["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/build-profile.json5", {"fileSnapShotHashValue": "c6477928a820b164e358dfcf9a324091"}]]}, "_outputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/BuildProfile.ets", {"fileSnapShotHashValue": "c6cc6e0254276d9e509a787710d45c87"}]]}}, ":harmony:lingxia:default@CreateHarModuleInfo": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"isByteCodeHar\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isOhosUiTransformOptimization\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "lingxia", "_taskName": "default@CreateHarModuleInfo", "_key": ":harmony:lingxia:default@CreateHarModuleInfo", "_executionId": ":harmony:lingxia:default@CreateHarModuleInfo:1753969459323", "_inputFiles": {"dataType": "Map", "value": []}, "_outputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/generated/profile/default/ModuleInfo.ts", {"isDirectory": false, "fileSnapShotHashValue": "8e2db218098927526824485ced705fae"}]]}}, ":harmony:lingxia:default@MergeProfile": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"targetSdkVersion\",\"_value\":17,\"_valueType\":\"number\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compatibleSdkVersion\",\"_value\":17,\"_valueType\":\"number\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"releaseType\",\"_value\":\"Release\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"buildRoot\",\"_value\":\"build\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isDebug\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"asanEnable\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"tsan<PERSON>nable\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"hwasan<PERSON><PERSON><PERSON>\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"ubsanEnable\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"module<PERSON>sonOpt\",\"_value\":\"{\\\"module\\\":{\\\"name\\\":\\\"lingxia\\\",\\\"type\\\":\\\"har\\\",\\\"deviceTypes\\\":[\\\"default\\\",\\\"tablet\\\"],\\\"requestPermissions\\\":[{\\\"name\\\":\\\"ohos.permission.INTERNET\\\"}]}}\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isHarModule\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"multiProjects\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"buildProfileAbilities\",\"_valueType\":\"undefined\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"projectConfigAppOpt\",\"_valueType\":\"undefined\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"appJsonOpt\",\"_value\":\"{\\\"app\\\":{\\\"bundleName\\\":\\\"com.lingxia.miniapp\\\",\\\"vendor\\\":\\\"LingXia Team\\\",\\\"versionCode\\\":1000000,\\\"versionName\\\":\\\"1.0.0\\\",\\\"icon\\\":\\\"$media:layered_image\\\",\\\"label\\\":\\\"$string:app_name\\\"}}\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"integratedHsp\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"removePermissions\",\"_valueType\":\"undefined\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isBundledDependencies\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"packageName\",\"_value\":\"lingxia\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "lingxia", "_taskName": "default@MergeProfile", "_key": ":harmony:lingxia:default@MergeProfile", "_executionId": ":harmony:lingxia:default@MergeProfile:1753969459336", "_inputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/AppScope/app.json5", {"fileSnapShotHashValue": "02c5f7337f114720a3047a05628e8b89"}], ["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/build-profile.json5", {"fileSnapShotHashValue": "c6477928a820b164e358dfcf9a324091"}], ["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/module.json5", {"fileSnapShotHashValue": "d1e04b294833c3b0aabf4ee492042755"}]]}, "_outputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/merge_profile/default/module.json", {"fileSnapShotHashValue": "bd1c92221f53a326125f72fefec1f86d"}]]}}, ":harmony:lingxia:default@GeneratePkgContextInfo": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"pkgContextInfoMap-lingxia\",\"_value\":\"5a2a61f3b933e38f82721abab99ff541\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"useNormalizedOHMUrl\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "lingxia", "_taskName": "default@GeneratePkgContextInfo", "_key": ":harmony:lingxia:default@GeneratePkgContextInfo", "_executionId": ":harmony:lingxia:default@GeneratePkgContextInfo:1753969459359", "_inputFiles": {"dataType": "Map", "value": []}, "_outputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/loader/default/pkgContextInfo.json", {"isDirectory": false, "fileSnapShotHashValue": "09e5c82d6d3377b1ca03aa04504dbf75"}]]}}, ":harmony:lingxia:default@ProcessIntegratedHsp": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "lingxia", "_taskName": "default@ProcessIntegratedHsp", "_key": ":harmony:lingxia:default@ProcessIntegratedHsp", "_executionId": ":harmony:lingxia:default@ProcessIntegratedHsp:1753969459367", "_inputFiles": {"dataType": "Map", "value": []}, "_outputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/build/cache/default/integrated_hsp/integratedHspCache.json", {"isDirectory": false, "fileSnapShotHashValue": ""}]]}}, ":harmony:lingxia:default@ProcessProfile": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"arkEnable\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compileMode\",\"_value\":\"esmodule\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"deviceTypes\",\"_value\":[\"default\",\"tablet\"],\"_valueType\":\"object\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"dependency\",\"_value\":\"[]\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compatibleSdkVersionStage\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "lingxia", "_taskName": "default@ProcessProfile", "_key": ":harmony:lingxia:default@ProcessProfile", "_executionId": ":harmony:lingxia:default@ProcessProfile:1753969459380", "_inputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/merge_profile/default/module.json", {"fileSnapShotHashValue": "bd1c92221f53a326125f72fefec1f86d"}]]}, "_outputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/process_profile/default/module.json", {"fileSnapShotHashValue": "dad2783f1a3720ec270df72cd59bc9f5"}]]}}, ":harmony:lingxia:default@ProcessRouterMap": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"useNormalizedOHMUrl\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"obfuscated\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"byteCodeHar\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "lingxia", "_taskName": "default@ProcessRouterMap", "_key": ":harmony:lingxia:default@ProcessRouterMap", "_executionId": ":harmony:lingxia:default@ProcessRouterMap:1753969459589", "_inputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/oh-package.json5", {"fileSnapShotHashValue": "40de8b8b9c6b9e68c3cfc79af5606691"}], ["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/oh-package.json5", {"fileSnapShotHashValue": "911264701269ca8cdbdd3d9405356687"}], ["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/module.json5", {"fileSnapShotHashValue": "d1e04b294833c3b0aabf4ee492042755"}], ["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/loader/default/pkgContextInfo.json", {"fileSnapShotHashValue": "09e5c82d6d3377b1ca03aa04504dbf75"}]]}, "_outputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/router_map/default/temp-router-map.json", {"fileSnapShotHashValue": ""}], ["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/router_map/default/loader-router-map.json", {"fileSnapShotHashValue": ""}]]}}, ":harmony:lingxia:default@ProcessResource": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"resConfigJsonContent\",\"_value\":\"{\\\"configPath\\\":\\\"/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/process_profile/default/module.json\\\",\\\"packageName\\\":\\\"com.lingxia.miniapp\\\",\\\"output\\\":\\\"/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/res/default\\\",\\\"moduleNames\\\":\\\"lingxia\\\",\\\"ResourceTable\\\":[\\\"/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/generated/r/default/ResourceTable.h\\\"],\\\"moduleResources\\\":[],\\\"dependencies\\\":[],\\\"iconCheck\\\":false,\\\"ids\\\":\\\"/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/res/default/ids_map\\\",\\\"definedIds\\\":\\\"/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/res/default/ids_map/id_defined.json\\\",\\\"definedSysIds\\\":\\\"/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/hms/toolchains/id_defined.json\\\"}\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/res/default/opt-compression.json\",\"_value\":\"{\\\"context\\\":{\\\"extensionPath\\\":\\\"/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/hms/toolchains/lib/libimage_transcoder_shared.dylib\\\"},\\\"compression\\\":{\\\"media\\\":{\\\"enable\\\":false},\\\"filters\\\":[]}}\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"resource_str\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "lingxia", "_taskName": "default@ProcessResource", "_key": ":harmony:lingxia:default@ProcessResource", "_executionId": ":harmony:lingxia:default@ProcessResource:1753969459611", "_inputFiles": {"dataType": "Map", "value": []}, "_outputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/res/default/resConfig.json", {"isDirectory": false, "fileSnapShotHashValue": "d434ca8cb84d77dccfce1e6b4977f886"}], ["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/res/default/opt-compression.json", {"isDirectory": false, "fileSnapShotHashValue": "301219b24008a615caeae807ad0b191a"}]]}}, ":harmony:lingxia:default@GenerateLoaderJson": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"projectRootPath\",\"_value\":\"/Users/<USER>/github/LingXia/lingxia-sdk/harmony\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"modulePathMap\",\"_value\":\"{\\\"lingxia\\\":\\\"/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia\\\"}\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"module_dependencies\",\"_value\":\"{}\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"overrides\",\"_valueType\":\"undefined\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"project_dependencies\",\"_value\":\"{}\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"useNormalizedOHMUrl\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isOhosTest\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isFullCompilationEnabled\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compileMode\",\"_value\":\"esmodule\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"nodeModulesPath\",\"_value\":\"/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/loader_out/default/node_modules\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"targetConfig\",\"_value\":\"{\\\"name\\\":\\\"default\\\"}\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"hspNameOhmMap\",\"_value\":\"{}\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"harNameOhmMap\",\"_value\":\"{}\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"anBuildMode\",\"_value\":\"type\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"apPath\",\"_value\":\"/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/modules.ap\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"fallbackAnBuild\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isHarWithCoverage\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compileApiVersion\",\"_value\":17,\"_valueType\":\"number\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compatibleApiVersion\",\"_value\":17,\"_valueType\":\"number\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isByteCodeHar\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"shouldTreatHarAsHap\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isBundledDependencies\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"needSubmitArkTsWidget\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "lingxia", "_taskName": "default@GenerateLoaderJson", "_key": ":harmony:lingxia:default@GenerateLoaderJson", "_executionId": ":harmony:lingxia:default@GenerateLoaderJson:1753969459628", "_inputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/loader/default/pkgContextInfo.json", {"fileSnapShotHashValue": "09e5c82d6d3377b1ca03aa04504dbf75"}], ["/Users/<USER>/github/Ling<PERSON>ia/lingxia-sdk/harmony/lingxia/src/main/ets", {"isDirectory": true, "fileSnapShotHashValue": "29ba1360fadd24ac7794638ff7f006cf"}], ["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/generated/profile/default/ModuleInfo.ts", {"isDirectory": false, "fileSnapShotHashValue": "8e2db218098927526824485ced705fae"}]]}, "_outputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/loader/default/loader.json", {"isDirectory": false, "fileSnapShotHashValue": "e7895a1b3fe52f9cccb78573e1994232"}]]}}, ":harmony:lingxia:default@ProcessLibs": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"excludeFromHar\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"c33cbd85982f2e758a878e3aa30a9f01\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isBundledDependencies\",\"_value\":false,\"_valueType\":\"boolean\",\"_hash\":\"a69aa552317c7a6e087cb84a5722e050\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isByteCodeHar\",\"_value\":true,\"_valueType\":\"boolean\",\"_hash\":\"530d026140bb1ca73510f7bb41e3aee3\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "lingxia", "_taskName": "default@ProcessLibs", "_key": ":harmony:lingxia:default@ProcessLibs", "_executionId": ":harmony:lingxia:default@ProcessLibs:1753959083651", "_inputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/build-profile.json5", {"fileSnapShotHashValue": "c6477928a820b164e358dfcf9a324091"}], ["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build-profile.json5", {"fileSnapShotHashValue": "117dd35b0a260dbc88aa7dcfde0d2609"}]]}, "_outputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/libs/default", {"isDirectory": true, "fileSnapShotHashValue": ""}]]}}, ":harmony:lingxia:default@CompileResource": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"TARGET_CONFIG\",\"_value\":\"{\\\"name\\\":\\\"default\\\"}\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/toolchains/restool,-l,/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/res/default/resConfig.json\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/toolchains/restool\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "lingxia", "_taskName": "default@CompileResource", "_key": ":harmony:lingxia:default@CompileResource", "_executionId": ":harmony:lingxia:default@CompileResource:1753969459656", "_inputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/process_profile/default/module.json", {"isDirectory": false, "fileSnapShotHashValue": "dad2783f1a3720ec270df72cd59bc9f5"}], ["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/res/default/resConfig.json", {"isDirectory": false, "fileSnapShotHashValue": "d434ca8cb84d77dccfce1e6b4977f886"}]]}, "_outputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/res/default", {"isDirectory": true, "fileSnapShotHashValue": "fdeef5e5f14768b04d0ec34b52e06c6b"}], ["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/generated/r/default/ResourceTable.h", {"isDirectory": false, "fileSnapShotHashValue": ""}], ["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/generated/r/default", {"isDirectory": true, "fileSnapShotHashValue": ""}]]}}, ":harmony:lingxia:default@HarCompileArkTS": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"debuggable\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isArk\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"needCoverageInsert\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"ark.tsImportSendable\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"customTypes\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"caseSensitive<PERSON><PERSON><PERSON>\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"useNormalizedOHMUrl\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"transformLib\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compatibleSdkVersionStage\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"autoLazyImport\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"allowEmptyBundleName\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"reExportCheckMode\",\"_value\":\"noCheck\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"arkTsWdiget\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"OBFUSCATION_ENABLE\",\"_value\":\"undefined\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"copyCodeResourceEnable\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"copyCodeResourceExcludes\",\"_value\":[],\"_valueType\":\"object\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"noExternalImportByPath\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"ohos.uiTransform.Optimization\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isByteCodeHar\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isBundledDependencies\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "lingxia", "_taskName": "default@HarCompileArkTS", "_key": ":harmony:lingxia:default@HarCompileArkTS", "_executionId": ":harmony:lingxia:default@HarCompileArkTS:1753969459675", "_inputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/loader/default", {"isDirectory": true, "fileSnapShotHashValue": "d8515e8c962c4035e824e7f2423d3e0a"}], ["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/res/default/ark_module.json", {"fileSnapShotHashValue": "e3688c9bb3bd07c80174d2a5e0d4d344"}], ["/Users/<USER>/github/Ling<PERSON>ia/lingxia-sdk/harmony/lingxia/src/main/ets", {"isDirectory": true, "fileSnapShotHashValue": "29ba1360fadd24ac7794638ff7f006cf"}], ["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/Index.ets", {"fileSnapShotHashValue": "7e9bff683cbe1b6262cf0cd24b2df5e0"}], ["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/loader/default/pkgContextInfo.json", {"fileSnapShotHashValue": "09e5c82d6d3377b1ca03aa04504dbf75"}], ["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/BuildProfile.ets", {"fileSnapShotHashValue": "c6cc6e0254276d9e509a787710d45c87"}]]}, "_outputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/loader_out/default/ets", {"isDirectory": true, "fileSnapShotHashValue": "8c9a3261c191688ad2fc772d10185d9c"}]]}}, ":harmony:lingxia:default@DoNativeStrip": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"0b5d05294fdf4e78e8482ef53eb64ac0\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"cdcdc2ec8062ca9ec1de46dbf2bbc053\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"e7f94a9422a8bc317a7d90b58efcb5f6\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"debugSymbol\",\"_value\":\"\",\"_valueType\":\"string\",\"_hash\":\"055eeab6818b3f1a4978705cfba272fa\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "lingxia", "_taskName": "default@DoNativeStrip", "_key": ":harmony:lingxia:default@DoNativeStrip", "_executionId": ":harmony:lingxia:default@DoNativeStrip:1753959085111", "_inputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/libs/default", {"isDirectory": true, "fileSnapShotHashValue": ""}]]}, "_outputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/stripped_native_libs/default", {"isDirectory": true, "fileSnapShotHashValue": ""}]]}}, ":harmony:lingxia:default@CacheNativeLibs": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"debugSymbol\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "lingxia", "_taskName": "default@CacheNativeLibs", "_key": ":harmony:lingxia:default@CacheNativeLibs", "_executionId": ":harmony:lingxia:default@CacheNativeLibs:1753969459725", "_inputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/stripped_native_libs/default", {"isDirectory": true, "fileSnapShotHashValue": ""}], ["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/libs/default", {"isDirectory": true, "fileSnapShotHashValue": ""}]]}, "_outputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/patch/default/base_native_libs.json", {"isDirectory": false, "fileSnapShotHashValue": "6078ac76c7b3d6bcbe9e8a430476ed57"}]]}}, ":harmony:lingxia:default@ProcessHarArtifacts": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"hasNativeOption\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"needCppTypes\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"har<PERSON><PERSON><PERSON><PERSON><PERSON>\",\"_value\":\"/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/cache/default/default@PackageHar/src/main/module.json5\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isOhpmProject\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"artifactType\",\"_value\":\"original\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isByteCodeHar\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"projectConfigAppOpt\",\"_valueType\":\"undefined\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"bundleType\",\"_value\":\"app\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"harLocalDependencyCheck\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isBundledDependencies\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"nonEntryImportees\",\"_value\":[],\"_valueType\":\"object\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isDebug\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isNativeStripped\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"cwd:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/cache/default/default@PackageHar:\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "lingxia", "_taskName": "default@ProcessHarArtifacts", "_key": ":harmony:lingxia:default@ProcessHarArtifacts", "_executionId": ":harmony:lingxia:default@ProcessHarArtifacts:1753969468291", "_inputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/process_profile/default", {"isDirectory": true, "fileSnapShotHashValue": "35e096e359b4cb4eb6f34f899c5d2fee"}], ["/Users/<USER>/github/Ling<PERSON>ia/lingxia-sdk/harmony/lingxia", {"isDirectory": true, "test": {"dataType": "RegExp", "value": "^(?!\\/Users\\/<USER>\\/github\\/LingXia\\/lingxia-sdk\\/harmony\\/lingxia\\/libs|\\/Users\\/<USER>\\/github\\/LingXia\\/lingxia-sdk\\/harmony\\/lingxia\\/build|\\/Users\\/<USER>\\/github\\/LingXia\\/lingxia-sdk\\/harmony\\/lingxia\\/node_modules|\\/Users\\/<USER>\\/github\\/LingXia\\/lingxia-sdk\\/harmony\\/lingxia\\/oh_modules|\\/Users\\/<USER>\\/github\\/LingXia\\/lingxia-sdk\\/harmony\\/lingxia\\/.cxx|\\/Users\\/<USER>\\/github\\/LingXia\\/lingxia-sdk\\/harmony\\/lingxia\\/.previewer|\\/Users\\/<USER>\\/github\\/LingXia\\/lingxia-sdk\\/harmony\\/lingxia\\/.hvigor|\\/Users\\/<USER>\\/github\\/LingXia\\/lingxia-sdk\\/harmony\\/lingxia\\/.gitignore|\\/Users\\/<USER>\\/github\\/LingXia\\/lingxia-sdk\\/harmony\\/lingxia\\/.ohpmignore).*"}, "fileSnapShotHashValue": "3cbdf5ccba11ad5887ab854c497f9fa7"}], ["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/generated/pm/default", {"isDirectory": true, "fileSnapShotHashValue": "b76b4d807a10307b806fc8dc1dd16316"}], ["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/stripped_native_libs/default", {"isDirectory": true, "fileSnapShotHashValue": ""}], ["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/loader/default/pkgContextInfo.json", {"fileSnapShotHashValue": "09e5c82d6d3377b1ca03aa04504dbf75"}]]}, "_outputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/cache/default/default@PackageHar", {"isDirectory": true, "fileSnapShotHashValue": "5d6e560506342037eb69692c9cef4294"}]]}}, ":harmony:lingxia:default@PackageHar": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"<PERSON><PERSON><PERSON>IN_TASK_COMMAND\",\"_value\":\"/Users/<USER>/Library/OpenHarmony/command-line-tools/tool/node/bin/npm,pack\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": true, "_projectName": "harmony", "_moduleName": "lingxia", "_taskName": "default@PackageHar", "_key": ":harmony:lingxia:default@PackageHar", "_executionId": ":harmony:lingxia:default@PackageHar:1753969468326", "_inputFiles": {"dataType": "Map", "value": []}, "_outputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/cache/default/default@PackageHar", {"isDirectory": true, "fileSnapShotHashValue": "5d6e560506342037eb69692c9cef4294"}], ["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/outputs/default", {"isDirectory": true, "fileSnapShotHashValue": "0fb88f38f13ed0a3ced89087ad872c80"}]]}}, ":harmony:lingxia:default@PackageSignHar": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"sdkToolchainsComponentVersion\",\"_value\":\"5.0.5.165\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"existSigningConfig\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isDaemon\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compileApiVersion\",\"_value\":17,\"_valueType\":\"number\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"compatibleApiVersion\",\"_value\":17,\"_valueType\":\"number\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isSigned\",\"_value\":false,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"isHarmonyOS\",\"_value\":true,\"_valueType\":\"boolean\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": false, "_projectName": "harmony", "_moduleName": "lingxia", "_taskName": "default@PackageSignHar", "_key": ":harmony:lingxia:default@PackageSignHar", "_executionId": ":harmony:lingxia:default@PackageSignHar:1753969468368", "_inputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/cache/default/default@PackageHar", {"isDirectory": true, "fileSnapShotHashValue": "5d6e560506342037eb69692c9cef4294"}], ["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/cache/default/default@PackageSignHar", {"isDirectory": true, "fileSnapShotHashValue": ""}]]}, "_outputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/outputs/default/lingxia-signed.har", {"isDirectory": false, "fileSnapShotHashValue": ""}]]}}, ":harmony:lingxia:default@CollectDebugSymbol": {"_inputs": [{"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_COMMAND\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"B<PERSON><PERSON>IN_TASK_TOOLCHAIN\",\"_value\":\"\",\"_valueType\":\"string\"}"}, {"dataType": "ValueEntry", "value": "{\"_name\":\"BUILTIN_TASK_ENV\",\"_value\":\"\",\"_valueType\":\"string\"}"}], "_successful": false, "_projectName": "harmony", "_moduleName": "lingxia", "_taskName": "default@CollectDebugSymbol", "_key": ":harmony:lingxia:default@CollectDebugSymbol", "_executionId": ":harmony:lingxia:default@CollectDebugSymbol:1753969468376", "_inputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/loader_out/default/ets/sourceMaps.map", {"fileSnapShotHashValue": "df65a8d717c24f43959e79cd7a064f03"}], ["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/libs/default", {"isDirectory": true, "fileSnapShotHashValue": ""}]]}, "_outputFiles": {"dataType": "Map", "value": [["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/outputs/default/symbol", {"isDirectory": true, "fileSnapShotHashValue": ""}]]}}}