[2025-07-31T18:51:15.949] [DEBUG] debug-file - env: nodejsVersion=v18.20.1
[2025-07-31T18:51:16.012] [DEBUG] debug-file - env: daemon=true
[2025-07-31T18:51:15.957] [DEBUG] debug-file - env: hvigor-config.json5 content = {
  modelVersion: '5.0.5',
  dependencies: {},
  execution: {},
  logging: {},
  debugging: {},
  nodeOptions: {}
}
[2025-07-31T18:51:16.658] [DEBUG] debug-file - java daemon tryConnect failed Error: connect ECONNREFUSED 127.0.0.1:45050
[2025-07-31T18:51:16.682] [DEBUG] debug-file - java daemon started at port 45050 pid 62241
[2025-07-31T18:51:16.732] [DEBUG] debug-file - session manager: set active socket. socketId=I5FUkm7up7KWvxtEAAAB
[2025-07-31T18:51:17.498] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2025-07-31T18:51:17.518] [DEBUG] debug-file - Hvigor init with startParameters:{
  hvigorfileTypeCheck: false,
  parallelExecution: true,
  incrementalExecution: true,
  printStackTrace: false,
  daemon: true,
  analyze: 0,
  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }
}
[2025-07-31T18:51:17.520] [DEBUG] debug-file - Since current hvigor version 5.17.2 differs from last hvigor version 
      undefined, delete file-cache.json and task-cache.json.
[2025-07-31T18:51:17.521] [DEBUG] debug-file - Cache service initialization finished in 2 ms 
[2025-07-31T18:51:17.538] [DEBUG] debug-file - hvigorfile, resolving /Users/<USER>/github/LingXia/lingxia-sdk/harmony/hvigorfile.ts
[2025-07-31T18:51:22.037] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-07-31T18:51:22.038] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-07-31T18:51:22.450] [DEBUG] debug-file - Start initialize project's product build option map with build mode debug.
[2025-07-31T18:51:22.450] [DEBUG] debug-file - Picking option from product 'default' with build mode 'debug'.
[2025-07-31T18:51:22.452] [DEBUG] debug-file - Product 'default' build option: {}
[2025-07-31T18:51:22.452] [DEBUG] debug-file - End initialize project's product build option map with build mode 'debug'.
[2025-07-31T18:51:22.458] [DEBUG] debug-file - Product 'default' using build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  }
} in this build.
[2025-07-31T18:51:22.459] [DEBUG] debug-file - not found resModel json file in : /Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/ohosTest/module.json5
[2025-07-31T18:51:22.476] [DEBUG] debug-file - No signingConfig found, initRemoteHspCache failed.
[2025-07-31T18:51:22.484] [DEBUG] debug-file - Local scan or download HarmonyOS sdk components toolchains,ets,js,native,previewer
[2025-07-31T18:51:22.492] [DEBUG] debug-file - Local scan or download hmscore sdk components toolchains,ets,native
[2025-07-31T18:51:22.498] [DEBUG] debug-file - Start recording SDK configuration permission data.
[2025-07-31T18:51:22.595] [DEBUG] debug-file - Sdk init in 116 ms 
[2025-07-31T18:51:22.631] [DEBUG] debug-file - Project task initialization takes 33 ms 
[2025-07-31T18:51:22.631] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-07-31T18:51:22.631] [DEBUG] debug-file - hvigorfile, no custom plugins were found in /Users/<USER>/github/LingXia/lingxia-sdk/harmony/hvigorfile.ts
[2025-07-31T18:51:22.631] [DEBUG] debug-file - hvigorfile, resolve finished /Users/<USER>/github/LingXia/lingxia-sdk/harmony/hvigorfile.ts
[2025-07-31T18:51:22.641] [DEBUG] debug-file - hvigorfile, resolving /Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/hvigorfile.ts
[2025-07-31T18:51:22.651] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-07-31T18:51:22.652] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-07-31T18:51:22.731] [DEBUG] debug-file - Start initialize module-target build option map, moduleName=lingxia, buildMode=debug
[2025-07-31T18:51:22.731] [DEBUG] debug-file - Target 'default' config: {}
[2025-07-31T18:51:22.732] [DEBUG] debug-file - Target 'ohosTest' config: {}
[2025-07-31T18:51:22.732] [DEBUG] debug-file - Module 'lingxia' target 'default' build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  }
}
[2025-07-31T18:51:22.732] [DEBUG] debug-file - Module 'lingxia' target 'ohosTest' build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  }
}
[2025-07-31T18:51:22.732] [DEBUG] debug-file - End initialize module-target build option map, moduleName=lingxia
[2025-07-31T18:51:22.733] [DEBUG] debug-file - Module 'lingxia' target 'default' using build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  }
} in this build.
[2025-07-31T18:51:22.745] [DEBUG] debug-file - Module lingxia task initialization takes 10 ms 
[2025-07-31T18:51:22.745] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-07-31T18:51:22.745] [DEBUG] debug-file - hvigorfile, no custom plugins were found in /Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/hvigorfile.ts
[2025-07-31T18:51:22.745] [DEBUG] debug-file - hvigorfile, resolve finished /Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/hvigorfile.ts
[2025-07-31T18:51:22.855] [DEBUG] debug-file - hvigorfile, resolve hvigorfile dependencies in 109 ms 
[2025-07-31T18:51:22.858] [DEBUG] debug-file - project has submodules:lingxia
[2025-07-31T18:51:22.858] [DEBUG] debug-file - module:lingxia no need to execute packageHap
[2025-07-31T18:51:22.859] [DEBUG] debug-file - start to load updatedOhPackageInfo to the disk
[2025-07-31T18:51:22.864] [DEBUG] debug-file - load to the disk finished
[2025-07-31T18:51:22.865] [DEBUG] debug-file - Start to initialize dependency information.
[2025-07-31T18:51:22.868] [DEBUG] debug-file - Module harmony Collected Dependency: 
[2025-07-31T18:51:22.868] [DEBUG] debug-file - Module harmony's total dependency: 0
[2025-07-31T18:51:22.869] [DEBUG] debug-file - Start to initialize dependency information.
[2025-07-31T18:51:22.871] [DEBUG] debug-file - Module lingxia Collected Dependency: 
[2025-07-31T18:51:22.871] [DEBUG] debug-file - Module lingxia's total dependency: 0
[2025-07-31T18:51:22.880] [DEBUG] debug-file - Configuration phase cost:5 s 351 ms 
[2025-07-31T18:51:22.883] [DEBUG] debug-file - Configuration task cost before running: 5 s 381 ms 
[2025-07-31T18:51:22.885] [DEBUG] debug-file - Executing task :lingxia:clean
[2025-07-31T18:51:22.885] [DEBUG] debug-file - clean: Worker pool is inactive.
[2025-07-31T18:51:22.889] [DEBUG] debug-file - lingxia : clean cost memory 0.16385650634765625
[2025-07-31T18:51:22.890] [DEBUG] debug-file - runTaskFromQueue task cost before running: 5 s 389 ms 
[2025-07-31T18:51:22.891] [INFO] debug-file - Finished :lingxia:clean... after 6 ms 
[2025-07-31T18:51:22.893] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T18:51:22.894] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T18:51:22.896] [DEBUG] debug-file - Executing task :lingxia:default@PreBuild
[2025-07-31T18:51:22.900] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T18:51:22.900] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T18:51:23.052] [DEBUG] debug-file - current product is not Atomic service.
[2025-07-31T18:51:23.052] [DEBUG] debug-file - Use tool [darwin: JAVA_HOME, CLASSPATH]
 [
  {
    JAVA_HOME: '/Applications/Android Studio.app/Contents/jbr/Contents/Home'
  },
  { CLASSPATH: undefined }
]
[2025-07-31T18:51:23.053] [DEBUG] debug-file - Use tool [darwin: NODE_HOME]
 [ { NODE_HOME: undefined } ]
[2025-07-31T18:51:23.264] [DEBUG] debug-file - lingxia : default@PreBuild cost memory 12.936676025390625
[2025-07-31T18:51:23.264] [DEBUG] debug-file - runTaskFromQueue task cost before running: 5 s 763 ms 
[2025-07-31T18:51:23.265] [INFO] debug-file - Finished :lingxia:default@PreBuild... after 368 ms 
[2025-07-31T18:51:23.268] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T18:51:23.268] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T18:51:23.270] [DEBUG] debug-file - Executing task :lingxia:default@ProcessOHPackageJson
[2025-07-31T18:51:23.270] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T18:51:23.271] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T18:51:23.275] [DEBUG] debug-file - lingxia : default@ProcessOHPackageJson cost memory 0.044891357421875
[2025-07-31T18:51:23.276] [DEBUG] debug-file - runTaskFromQueue task cost before running: 5 s 774 ms 
[2025-07-31T18:51:23.276] [INFO] debug-file - Finished :lingxia:default@ProcessOHPackageJson... after 6 ms 
[2025-07-31T18:51:23.278] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T18:51:23.279] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T18:51:23.280] [DEBUG] debug-file - Executing task :lingxia:default@CreateHarBuildProfile
[2025-07-31T18:51:23.281] [DEBUG] debug-file - Task 'lingxia:default@CreateHarBuildProfile' cost while wrapping incremental declarations: 1 ms 
[2025-07-31T18:51:23.282] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T18:51:23.282] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T18:51:23.283] [DEBUG] debug-file - lingxia : default@CreateHarBuildProfile cost memory 0.06960296630859375
[2025-07-31T18:51:23.283] [DEBUG] debug-file - runTaskFromQueue task cost before running: 5 s 782 ms 
[2025-07-31T18:51:23.283] [INFO] debug-file - Finished :lingxia:default@CreateHarBuildProfile... after 3 ms 
[2025-07-31T18:51:23.286] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T18:51:23.287] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T18:51:23.288] [DEBUG] debug-file - Executing task :lingxia:default@CreateHarModuleInfo
[2025-07-31T18:51:23.291] [DEBUG] debug-file - Task 'lingxia:default@CreateHarModuleInfo' cost while wrapping incremental declarations: 1 ms 
[2025-07-31T18:51:23.292] [DEBUG] debug-file - lingxia : default@CreateHarModuleInfo cost memory 0.03678131103515625
[2025-07-31T18:51:23.292] [DEBUG] debug-file - runTaskFromQueue task cost before running: 5 s 791 ms 
[2025-07-31T18:51:23.293] [INFO] debug-file - Finished :lingxia:default@CreateHarModuleInfo... after 5 ms 
[2025-07-31T18:51:23.296] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T18:51:23.296] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T18:51:23.296] [DEBUG] debug-file - Executing task :lingxia:default@ConfigureCmake
[2025-07-31T18:51:23.296] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T18:51:23.296] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T18:51:23.297] [DEBUG] debug-file - lingxia : default@ConfigureCmake cost memory 0.03833770751953125
[2025-07-31T18:51:23.297] [DEBUG] debug-file - runTaskFromQueue task cost before running: 5 s 795 ms 
[2025-07-31T18:51:23.297] [INFO] debug-file - Finished :lingxia:default@ConfigureCmake... after 1 ms 
[2025-07-31T18:51:23.310] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T18:51:23.310] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T18:51:23.311] [DEBUG] debug-file - Executing task :lingxia:default@MergeProfile
[2025-07-31T18:51:23.312] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T18:51:23.312] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T18:51:23.312] [DEBUG] debug-file - Change app api release type with 'Release'
[2025-07-31T18:51:23.313] [DEBUG] debug-file - Change app compile API version with '*********'
[2025-07-31T18:51:23.313] [DEBUG] debug-file - Change app target API version with '50005017'
[2025-07-31T18:51:23.313] [DEBUG] debug-file - Change app minimum API version with '50005017'
[2025-07-31T18:51:23.314] [DEBUG] debug-file - Use cli appEnvironment
[2025-07-31T18:51:23.317] [DEBUG] debug-file - lingxia : default@MergeProfile cost memory 0.23681640625
[2025-07-31T18:51:23.317] [DEBUG] debug-file - runTaskFromQueue task cost before running: 5 s 816 ms 
[2025-07-31T18:51:23.317] [INFO] debug-file - Finished :lingxia:default@MergeProfile... after 7 ms 
[2025-07-31T18:51:23.320] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T18:51:23.320] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T18:51:23.328] [DEBUG] debug-file - Executing task :lingxia:default@GeneratePkgContextInfo
[2025-07-31T18:51:23.329] [DEBUG] debug-file - Task 'lingxia:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms 
[2025-07-31T18:51:23.329] [DEBUG] debug-file - lingxia : default@GeneratePkgContextInfo cost memory 0.03598785400390625
[2025-07-31T18:51:23.329] [DEBUG] debug-file - runTaskFromQueue task cost before running: 5 s 828 ms 
[2025-07-31T18:51:23.330] [INFO] debug-file - Finished :lingxia:default@GeneratePkgContextInfo... after 2 ms 
[2025-07-31T18:51:23.331] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T18:51:23.332] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T18:51:23.333] [DEBUG] debug-file - Executing task :lingxia:default@ProcessIntegratedHsp
[2025-07-31T18:51:23.334] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T18:51:23.334] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T18:51:23.334] [DEBUG] debug-file - lingxia : default@ProcessIntegratedHsp cost memory 0.07077789306640625
[2025-07-31T18:51:23.334] [DEBUG] debug-file - runTaskFromQueue task cost before running: 5 s 833 ms 
[2025-07-31T18:51:23.335] [INFO] debug-file - Finished :lingxia:default@ProcessIntegratedHsp... after 2 ms 
[2025-07-31T18:51:23.337] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T18:51:23.337] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T18:51:23.338] [DEBUG] debug-file - Executing task :lingxia:default@BuildNativeWithCmake
[2025-07-31T18:51:23.338] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T18:51:23.338] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T18:51:23.338] [DEBUG] debug-file - lingxia : default@BuildNativeWithCmake cost memory 0.03865814208984375
[2025-07-31T18:51:23.338] [DEBUG] debug-file - runTaskFromQueue task cost before running: 5 s 837 ms 
[2025-07-31T18:51:23.338] [INFO] debug-file - Finished :lingxia:default@BuildNativeWithCmake... after 1 ms 
[2025-07-31T18:51:23.342] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T18:51:23.342] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T18:51:23.343] [DEBUG] debug-file - Executing task :lingxia:default@ProcessProfile
[2025-07-31T18:51:23.344] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T18:51:23.344] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T18:51:23.347] [DEBUG] debug-file - [
  '/Users/<USER>/Library/OpenHarmony/command-line-tools/tool/node/bin/node',
  '/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/build-tools/ets-loader/bin/ark/ts2abc.js',
  '--target-api-version',
  '17'
]
[2025-07-31T18:51:23.560] [DEBUG] debug-file - ********
[2025-07-31T18:51:23.562] [DEBUG] debug-file - lingxia : default@ProcessProfile cost memory 0.33984375
[2025-07-31T18:51:23.563] [DEBUG] debug-file - runTaskFromQueue task cost before running: 6 s 61 ms 
[2025-07-31T18:51:23.563] [INFO] debug-file - Finished :lingxia:default@ProcessProfile... after 220 ms 
[2025-07-31T18:51:23.565] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T18:51:23.565] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T18:51:23.567] [DEBUG] debug-file - Executing task :lingxia:default@ProcessRouterMap
[2025-07-31T18:51:23.570] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T18:51:23.570] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T18:51:23.571] [DEBUG] debug-file - lingxia : default@ProcessRouterMap cost memory 0.16269683837890625
[2025-07-31T18:51:23.575] [DEBUG] debug-file - runTaskFromQueue task cost before running: 6 s 73 ms 
[2025-07-31T18:51:23.577] [INFO] debug-file - Finished :lingxia:default@ProcessRouterMap... after 8 ms 
[2025-07-31T18:51:23.580] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T18:51:23.580] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T18:51:23.581] [DEBUG] debug-file - Executing task :lingxia:default@BuildNativeWithNinja
[2025-07-31T18:51:23.581] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T18:51:23.581] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T18:51:23.583] [DEBUG] debug-file - lingxia : default@BuildNativeWithNinja cost memory 0.058349609375
[2025-07-31T18:51:23.583] [DEBUG] debug-file - runTaskFromQueue task cost before running: 6 s 82 ms 
[2025-07-31T18:51:23.584] [INFO] debug-file - Finished :lingxia:default@BuildNativeWithNinja... after 3 ms 
[2025-07-31T18:51:23.587] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T18:51:23.588] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T18:51:23.593] [DEBUG] debug-file - restool module names: lingxia; moduleName=lingxia, taskName=default@ProcessResource
[2025-07-31T18:51:23.597] [DEBUG] debug-file - Executing task :lingxia:default@ProcessResource
[2025-07-31T18:51:23.599] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T18:51:23.599] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T18:51:23.600] [DEBUG] debug-file - lingxia : default@ProcessResource cost memory 0.167633056640625
[2025-07-31T18:51:23.601] [DEBUG] debug-file - runTaskFromQueue task cost before running: 6 s 99 ms 
[2025-07-31T18:51:23.603] [INFO] debug-file - Finished :lingxia:default@ProcessResource... after 6 ms 
[2025-07-31T18:51:23.611] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T18:51:23.611] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T18:51:23.616] [DEBUG] debug-file - Executing task :lingxia:default@GenerateLoaderJson
[2025-07-31T18:51:23.644] [DEBUG] debug-file - lingxia : default@GenerateLoaderJson cost memory 0.50445556640625
[2025-07-31T18:51:23.644] [DEBUG] debug-file - runTaskFromQueue task cost before running: 6 s 143 ms 
[2025-07-31T18:51:23.647] [INFO] debug-file - Finished :lingxia:default@GenerateLoaderJson... after 29 ms 
[2025-07-31T18:51:23.648] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T18:51:23.648] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T18:51:23.649] [DEBUG] debug-file - Executing task :lingxia:default@ProcessLibs
[2025-07-31T18:51:23.651] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T18:51:23.651] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T18:51:23.663] [DEBUG] debug-file - Since there is no instance or instance is terminated, create a new worker pool.
[2025-07-31T18:51:23.664] [DEBUG] debug-file - Worker pool is initialized with config:  {
  minPoolNum: 2,
  maxPoolNum: undefined,
  maxCoreSize: undefined,
  cacheCapacity: undefined,
  cacheTtl: undefined
}
[2025-07-31T18:51:23.665] [DEBUG] debug-file - Create  resident worker with id: 0.
[2025-07-31T18:51:23.675] [DEBUG] debug-file - Create  resident worker with id: 1.
[2025-07-31T18:51:23.681] [DEBUG] debug-file - default@ProcessLibs work[0] is submitted.
[2025-07-31T18:51:23.685] [DEBUG] debug-file - default@ProcessLibs work[0] is pushed to ready queue.
[2025-07-31T18:51:23.685] [DEBUG] debug-file - A work dispatched to worker[7] failed because unable to get work from ready queue.
[2025-07-31T18:51:23.686] [DEBUG] debug-file - Create  resident worker with id: 6.
[2025-07-31T18:51:23.692] [DEBUG] debug-file - default@ProcessLibs work[0] has been dispatched to worker[6].
[2025-07-31T18:51:23.694] [DEBUG] debug-file - default@ProcessLibs work[0] is dispatched.
[2025-07-31T18:51:23.695] [DEBUG] debug-file - lingxia : default@ProcessLibs cost memory 0.9142532348632812
[2025-07-31T18:51:23.699] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T18:51:23.700] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T18:51:23.702] [DEBUG] debug-file - restool module names: lingxia; moduleName=lingxia, taskName=default@CompileResource
[2025-07-31T18:51:23.710] [DEBUG] debug-file - Executing task :lingxia:default@CompileResource
[2025-07-31T18:51:23.714] [DEBUG] debug-file - lingxia : default@CompileResource cost memory 0.07415008544921875
[2025-07-31T18:51:23.714] [DEBUG] debug-file - runTaskFromQueue task cost before running: 6 s 213 ms 
[2025-07-31T18:51:23.715] [INFO] debug-file - Finished :lingxia:default@CompileResource... after 4 ms 
[2025-07-31T18:51:23.718] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T18:51:23.718] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T18:51:23.720] [DEBUG] debug-file - Executing task :lingxia:default@ProcessObfuscationFiles
[2025-07-31T18:51:23.720] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T18:51:23.720] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T18:51:23.721] [DEBUG] debug-file - lingxia : default@ProcessObfuscationFiles cost memory 0.03890228271484375
[2025-07-31T18:51:23.721] [DEBUG] debug-file - runTaskFromQueue task cost before running: 6 s 220 ms 
[2025-07-31T18:51:23.726] [INFO] debug-file - Finished :lingxia:default@ProcessObfuscationFiles... after 1 ms 
[2025-07-31T18:51:23.732] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T18:51:23.732] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T18:51:23.743] [DEBUG] debug-file - Executing task :lingxia:default@HarCompileArkTS
[2025-07-31T18:51:23.787] [DEBUG] debug-file - build config:
[2025-07-31T18:51:23.788] [DEBUG] debug-file - {
  moduleType: 'har',
  perf: 0,
  targetName: '.default',
  packageManagerType: 'ohpm',
  localPropertiesPath: '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/local.properties',
  isPreview: false,
  isOhosTest: false,
  isLocalTest: false,
  buildMode: 'Debug',
  watchMode: 'false',
  aceProfilePath: '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/res/default/resources/base/profile',
  etsLoaderPath: '/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/build-tools/ets-loader',
  modulePath: '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia',
  testFrameworkPar: {
    testMode: undefined,
    coveragePathFilter: undefined,
    coverageMode: undefined
  },
  needCoverageInsert: false,
  debugLine: false,
  projectTopDir: '/Users/<USER>/github/LingXia/lingxia-sdk/harmony',
  compileSdkVersion: 17,
  compatibleSdkVersion: 17,
  compatibleSdkVersionStage: undefined,
  bundleName: 'c***p',
  etsLoaderVersion: '*********',
  etsLoaderReleaseType: 'Release',
  aotCompileMode: 'type',
  apPath: '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/modules.ap',
  entryModuleName: 'lingxia',
  entryModuleVersion: '1.0.0',
  entryPackageName: 'lingxia',
  allModuleNameHash: '1be683a2a047c7a875d9c49d481b3c24',
  externalApiPaths: [
    '/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/hms/ets'
  ],
  compilerTypes: undefined,
  isCrossplatform: false,
  hvigorPluginFile: undefined,
  compilePluginPath: undefined,
  buildGeneratedProfilePath: '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/generated/profile/default',
  bundleType: 'app',
  arkTSVersion: undefined,
  apiVersion: 17,
  needCompleteSourcesMap: false,
  isFaMode: false,
  strictMode: {
    caseSensitiveCheck: true,
    useNormalizedOHMUrl: true,
    noExternalImportByPath: true
  },
  buildDir: '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build',
  deviceTypes: [ 'default', 'tablet' ],
  useNormalizedOHMUrl: true,
  pkgContextInfo: {
    lingxia: {
      packageName: 'lingxia',
      bundleName: '*****',
      moduleName: '',
      version: '1.0.0',
      entryPath: 'Index.ets',
      isSO: false,
      dependencyAlias: ''
    }
  },
  ohPackagePathMap: {},
  dependencyAliasMap: {},
  permission: { requestPermissions: [ [Object] ], definePermissions: undefined },
  integratedHsp: false,
  projectArkOption: undefined,
  sourceMapDir: undefined,
  branchElimination: false,
  transformLib: undefined,
  caseSensitiveCheck: true,
  tsImportSendable: false,
  resolveConflictMode: true,
  depName2RootPath: {},
  depName2DepInfo: {},
  rootPathSet: [ '/Users/<USER>/github/LingXia/lingxia-sdk/harmony' ],
  useNativeResolver: true,
  shouldEmitJs: true,
  autoLazyImport: undefined,
  allowEmptyBundleName: false,
  singleFileEmit: false,
  arkCompileCachePath: '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/cache/default/default@HarCompileArkTS/esmodule',
  reExportCheckMode: 'noCheck',
  aceModuleJsonPath: '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/res/default/ark_module.json',
  appResource: '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/res/default/ResourceTable.txt',
  rawFileResource: '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/res/default/resources/rawfile',
  resourceTableHash: undefined,
  runtimeOS: 'HarmonyOS',
  sdkInfo: 'false:17:*********:Release',
  aceModuleRoot: '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets',
  compileMode: 'esmodule',
  aceSuperVisualPath: '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/supervisual',
  aceBuildJson: '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/loader/default/loader.json',
  cachePath: '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/cache/default/default@HarCompileArkTS/esmodule/debug',
  aceModuleBuild: '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/loader_out/default/ets',
  supportChunks: true,
  declaredFilesPath: '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/loader_out/default/etsFortgz/lingxia',
  pkgNameToPkgBriefInfo: {
    lingxia: {
      pkgRoot: '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia',
      originalSourceRoots: undefined,
      sourceRoots: [Array],
      pkgName: 'lingxia'
    }
  },
  projectModel: {
    '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main': {
      moduleName: 'lingxia',
      modulePkgPath: '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia',
      belongProjectPath: '/Users/<USER>/github/LingXia/lingxia-sdk/harmony'
    },
    '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/src/ohosTest/ets': {
      moduleName: 'lingxia',
      modulePkgPath: '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia',
      belongProjectPath: '/Users/<USER>/github/LingXia/lingxia-sdk/harmony'
    },
    '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/ohosTest': {
      moduleName: 'lingxia',
      modulePkgPath: '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia',
      belongProjectPath: '/Users/<USER>/github/LingXia/lingxia-sdk/harmony'
    },
    '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia': {
      moduleName: 'lingxia',
      modulePkgPath: '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia',
      belongProjectPath: '/Users/<USER>/github/LingXia/lingxia-sdk/harmony'
    },
    '/Users/<USER>/github/LingXia/lingxia-sdk/harmony': {
      moduleName: 'harmony',
      modulePkgPath: '/Users/<USER>/github/LingXia/lingxia-sdk/harmony',
      belongProjectPath: '/Users/<USER>/github/LingXia/lingxia-sdk/harmony'
    }
  },
  pkgJsonFileHash: 'f6886ae0f7db79265d288ebee9eadaa8',
  allModulePaths: [ '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia' ],
  routerMap: {},
  obfuscationOptions: undefined,
  compileBlockPkg: [ '@ohos/hypium', '@ohos/hamock' ],
  mockParams: {
    decorator: '@MockSetup',
    packageName: '@ohos/hamock',
    etsSourceRootPath: 'src/main/ets',
    mockConfigPath: undefined,
    mockConfigKey2ModuleInfo: {}
  },
  copyCodeResourceEnable: true,
  copyCodeResourceExcludes: [],
  uiTransformOptimization: true,
  otherPaths: {
    'lingxia/*': [ '../*', '../../../build/default/generated/profile/default/*' ]
  },
  collectImportersConfig: undefined,
  byteCodeHar: true,
  obfuscate: undefined,
  skipBuildEnd: false
}
[2025-07-31T18:51:23.790] [DEBUG] debug-file - Compile arkts with external api path: /Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/hms/ets
[2025-07-31T18:51:23.793] [DEBUG] debug-file - default@HarCompileArkTS work[1] is submitted.
[2025-07-31T18:51:23.794] [DEBUG] debug-file - default@HarCompileArkTS work[1] is pushed to ready queue.
[2025-07-31T18:51:23.794] [DEBUG] debug-file - A work dispatched to worker[7] failed because unable to get work from ready queue.
[2025-07-31T18:51:23.794] [DEBUG] debug-file - A work dispatched to worker[6] failed because of worker busy.
[2025-07-31T18:51:23.795] [DEBUG] debug-file - A work dispatched to worker[5] failed because unable to get work from ready queue.
[2025-07-31T18:51:23.795] [DEBUG] debug-file - Create  resident worker with id: 4.
[2025-07-31T18:51:23.796] [DEBUG] debug-file - default@HarCompileArkTS work[1] has been dispatched to worker[4].
[2025-07-31T18:51:23.796] [DEBUG] debug-file - default@HarCompileArkTS work[1] is dispatched.
[2025-07-31T18:51:23.796] [DEBUG] debug-file - CopyResources startTime: 642472339793306
[2025-07-31T18:51:23.797] [DEBUG] debug-file - default@HarCompileArkTS work[2] is submitted.
[2025-07-31T18:51:23.798] [DEBUG] debug-file - default@HarCompileArkTS work[2] is pushed to ready queue.
[2025-07-31T18:51:23.798] [DEBUG] debug-file - A work dispatched to worker[7] failed because unable to get work from ready queue.
[2025-07-31T18:51:23.798] [DEBUG] debug-file - A work dispatched to worker[6] failed because of worker busy.
[2025-07-31T18:51:23.798] [DEBUG] debug-file - Create  resident worker with id: 5.
[2025-07-31T18:51:23.799] [DEBUG] debug-file - default@HarCompileArkTS work[2] has been dispatched to worker[5].
[2025-07-31T18:51:23.799] [DEBUG] debug-file - default@HarCompileArkTS work[2] is dispatched.
[2025-07-31T18:51:23.799] [DEBUG] debug-file - lingxia : default@HarCompileArkTS cost memory 1.4397735595703125
[2025-07-31T18:51:24.964] [DEBUG] debug-file - worker[5] has one work done.
[2025-07-31T18:51:24.965] [DEBUG] debug-file - CopyResources is end, endTime: 642473507958706
[2025-07-31T18:51:24.965] [DEBUG] debug-file - default@HarCompileArkTS work[2] done.
[2025-07-31T18:51:24.965] [DEBUG] debug-file - A work dispatched to worker[5] failed because unable to get work from ready queue.
[2025-07-31T18:51:25.102] [DEBUG] debug-file - worker[6] has one work done.
[2025-07-31T18:51:25.102] [DEBUG] debug-file - default@ProcessLibs work[0] done.
[2025-07-31T18:51:25.102] [DEBUG] debug-file - A work dispatched to worker[6] failed because unable to get work from ready queue.
[2025-07-31T18:51:25.103] [INFO] debug-file - Finished :lingxia:default@ProcessLibs... after 47 ms 
[2025-07-31T18:51:25.108] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T18:51:25.108] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T18:51:25.111] [DEBUG] debug-file - Executing task :lingxia:default@DoNativeStrip
[2025-07-31T18:51:25.111] [DEBUG] debug-file - Task 'lingxia:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms 
[2025-07-31T18:51:25.111] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T18:51:25.111] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T18:51:25.113] [DEBUG] debug-file - default@DoNativeStrip work[3] is submitted.
[2025-07-31T18:51:25.114] [DEBUG] debug-file - default@DoNativeStrip work[3] is pushed to ready queue.
[2025-07-31T18:51:25.114] [DEBUG] debug-file - A work dispatched to worker[7] failed because unable to get work from ready queue.
[2025-07-31T18:51:25.114] [DEBUG] debug-file - default@DoNativeStrip work[3] has been dispatched to worker[6].
[2025-07-31T18:51:25.114] [DEBUG] debug-file - default@DoNativeStrip work[3] is dispatched.
[2025-07-31T18:51:25.114] [DEBUG] debug-file - lingxia : default@DoNativeStrip cost memory 0.20156097412109375
[2025-07-31T18:51:25.133] [DEBUG] debug-file - worker[6] has one work done.
[2025-07-31T18:51:25.134] [DEBUG] debug-file - default@DoNativeStrip work[3] done.
[2025-07-31T18:51:25.134] [DEBUG] debug-file - A work dispatched to worker[6] failed because unable to get work from ready queue.
[2025-07-31T18:51:25.134] [INFO] debug-file - Finished :lingxia:default@DoNativeStrip... after 14 ms 
[2025-07-31T18:51:25.136] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T18:51:25.136] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T18:51:25.139] [DEBUG] debug-file - Executing task :lingxia:default@CacheNativeLibs
[2025-07-31T18:51:25.141] [DEBUG] debug-file - Task 'lingxia:default@CacheNativeLibs' cost while wrapping incremental declarations: 2 ms 
[2025-07-31T18:51:25.141] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T18:51:25.141] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T18:51:25.142] [DEBUG] debug-file - default@CacheNativeLibs work[4] is submitted.
[2025-07-31T18:51:25.143] [DEBUG] debug-file - default@CacheNativeLibs work[4] is pushed to ready queue.
[2025-07-31T18:51:25.143] [DEBUG] debug-file - A work dispatched to worker[7] failed because unable to get work from ready queue.
[2025-07-31T18:51:25.143] [DEBUG] debug-file - default@CacheNativeLibs work[4] has been dispatched to worker[6].
[2025-07-31T18:51:25.143] [DEBUG] debug-file - default@CacheNativeLibs work[4] is dispatched.
[2025-07-31T18:51:25.143] [DEBUG] debug-file - lingxia : default@CacheNativeLibs cost memory 0.18719482421875
[2025-07-31T18:51:26.635] [DEBUG] debug-file - worker[6] has one work done.
[2025-07-31T18:51:26.635] [DEBUG] debug-file - default@CacheNativeLibs work[4] done.
[2025-07-31T18:51:26.636] [DEBUG] debug-file - A work dispatched to worker[6] failed because unable to get work from ready queue.
[2025-07-31T18:51:26.636] [INFO] debug-file - Finished :lingxia:default@CacheNativeLibs... after 8 ms 
[2025-07-31T18:51:36.423] [INFO] debug-file - [33mWARN: [33mArkTS:WARN File: /Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/WebView.ets:3:45
 Currently module for 'liblingxia.so' is not verified. If you're importing napi, its verification will be enabled in later SDK version. Please make sure the corresponding .d.ts file is provided and the napis are correctly declared.
[39m
[33mWARN: [33mArkTS:WARN File: /Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/LxAppContainer.ets:9:69
 Currently module for 'liblingxia.so' is not verified. If you're importing napi, its verification will be enabled in later SDK version. Please make sure the corresponding .d.ts file is provided and the napis are correctly declared.
[39m
[33mWARN: [33mArkTS:WARN File: /Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/LxApp.ets:11:94
 Currently module for 'liblingxia.so' is not verified. If you're importing napi, its verification will be enabled in later SDK version. Please make sure the corresponding .d.ts file is provided and the napis are correctly declared.
[39m
[33mWARN: [33mArkTS:WARN File: /Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/LxAppNavigation.ets:4:46
 Currently module for 'liblingxia.so' is not verified. If you're importing napi, its verification will be enabled in later SDK version. Please make sure the corresponding .d.ts file is provided and the napis are correctly declared.
[39m

[2025-07-31T18:51:36.426] [DEBUG] debug-file - Ark compile task finished.finished time is 642484968163500
[2025-07-31T18:51:36.427] [DEBUG] debug-file - worker[4] has one work done.
[2025-07-31T18:51:36.435] [DEBUG] debug-file - default@HarCompileArkTS work[1] done.
[2025-07-31T18:51:36.436] [DEBUG] debug-file - A work dispatched to worker[4] failed because unable to get work from ready queue.
[2025-07-31T18:51:36.438] [INFO] debug-file - Finished :lingxia:default@HarCompileArkTS... after 9 s 864 ms 
[2025-07-31T18:51:36.444] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T18:51:36.444] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T18:51:36.450] [DEBUG] debug-file - Executing task :lingxia:default@ProcessHarArtifacts
[2025-07-31T18:51:36.453] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T18:51:36.453] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T18:51:36.490] [DEBUG] debug-file - lingxia : default@ProcessHarArtifacts cost memory 0.19983673095703125
[2025-07-31T18:51:36.490] [DEBUG] debug-file - runTaskFromQueue task cost before running: 18 s 989 ms 
[2025-07-31T18:51:36.492] [INFO] debug-file - Finished :lingxia:default@ProcessHarArtifacts... after 41 ms 
[2025-07-31T18:51:36.495] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T18:51:36.495] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T18:51:36.497] [DEBUG] debug-file - Executing task :lingxia:default@PackageHar
[2025-07-31T18:51:36.498] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T18:51:36.498] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T18:51:36.546] [DEBUG] debug-file - lingxia : default@PackageHar cost memory 0.00714874267578125
[2025-07-31T18:51:36.546] [DEBUG] debug-file - runTaskFromQueue task cost before running: 19 s 45 ms 
[2025-07-31T18:51:36.547] [INFO] debug-file - Finished :lingxia:default@PackageHar... after 49 ms 
[2025-07-31T18:51:36.549] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T18:51:36.549] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T18:51:36.552] [DEBUG] debug-file - Executing task :lingxia:default@PackageSignHar
[2025-07-31T18:51:36.553] [WARN] debug-file - Will skip sign 'har'. No signingConfigs profile is configured in current project.
             If needed, configure the signingConfigs in /Users/<USER>/github/LingXia/lingxia-sdk/harmony/build-profile.json5.
[2025-07-31T18:51:36.553] [DEBUG] debug-file - lingxia : default@PackageSignHar cost memory 0.048431396484375
[2025-07-31T18:51:36.553] [DEBUG] debug-file - runTaskFromQueue task cost before running: 19 s 52 ms 
[2025-07-31T18:51:36.554] [INFO] debug-file - Finished :lingxia:default@PackageSignHar... after 2 ms 
[2025-07-31T18:51:36.556] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T18:51:36.556] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T18:51:36.558] [DEBUG] debug-file - Executing task :lingxia:default@CollectDebugSymbol
[2025-07-31T18:51:36.563] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T18:51:36.563] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T18:51:36.563] [DEBUG] debug-file - lingxia : default@CollectDebugSymbol cost memory 0.2082366943359375
[2025-07-31T18:51:36.563] [DEBUG] debug-file - runTaskFromQueue task cost before running: 19 s 62 ms 
[2025-07-31T18:51:36.563] [INFO] debug-file - Finished :lingxia:default@CollectDebugSymbol... after 6 ms 
[2025-07-31T18:51:36.564] [DEBUG] debug-file - Executing task :lingxia:assembleHar
[2025-07-31T18:51:36.565] [DEBUG] debug-file - lingxia : assembleHar cost memory 0.011871337890625
[2025-07-31T18:51:36.565] [DEBUG] debug-file - runTaskFromQueue task cost before running: 19 s 63 ms 
[2025-07-31T18:51:36.565] [INFO] debug-file - Finished :lingxia:assembleHar... after 1 ms 
[2025-07-31T18:51:36.569] [DEBUG] debug-file - BUILD SUCCESSFUL in 19 s 67 ms 
[2025-07-31T18:51:36.571] [DEBUG] debug-file - Update task lingxia:default@PreBuild input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/AppScope/app.json5 cache by regenerate.
[2025-07-31T18:51:36.572] [DEBUG] debug-file - Update task lingxia:default@PreBuild input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/module.json5 cache by regenerate.
[2025-07-31T18:51:36.572] [DEBUG] debug-file - Update task lingxia:default@PreBuild input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/build-profile.json5 cache by regenerate.
[2025-07-31T18:51:36.573] [DEBUG] debug-file - Update task lingxia:default@PreBuild input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build-profile.json5 cache by regenerate.
[2025-07-31T18:51:36.573] [DEBUG] debug-file - Update task lingxia:default@PreBuild input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/hvigor/hvigor-config.json5 cache by regenerate.
[2025-07-31T18:51:36.573] [DEBUG] debug-file - Update task lingxia:default@PreBuild input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/oh-package.json5 cache by regenerate.
[2025-07-31T18:51:36.575] [DEBUG] debug-file - Update task lingxia:default@PreBuild input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/oh-package.json5 cache by regenerate.
[2025-07-31T18:51:36.576] [DEBUG] debug-file - Incremental task lingxia:default@PreBuild post-execution cost:6 ms .
[2025-07-31T18:51:36.576] [DEBUG] debug-file - Update task lingxia:default@CreateHarBuildProfile input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/AppScope/app.json5 cache by regenerate.
[2025-07-31T18:51:36.576] [DEBUG] debug-file - Update task lingxia:default@CreateHarBuildProfile input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/build-profile.json5 cache by regenerate.
[2025-07-31T18:51:36.577] [DEBUG] debug-file - Update task lingxia:default@CreateHarBuildProfile output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/BuildProfile.ets cache.
[2025-07-31T18:51:36.577] [DEBUG] debug-file - Incremental task lingxia:default@CreateHarBuildProfile post-execution cost:1 ms .
[2025-07-31T18:51:36.577] [DEBUG] debug-file - Update task lingxia:default@CreateHarModuleInfo output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/generated/profile/default/ModuleInfo.ts cache.
[2025-07-31T18:51:36.578] [DEBUG] debug-file - Incremental task lingxia:default@CreateHarModuleInfo post-execution cost:1 ms .
[2025-07-31T18:51:36.578] [DEBUG] debug-file - Update task lingxia:default@MergeProfile input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/AppScope/app.json5 cache by regenerate.
[2025-07-31T18:51:36.578] [DEBUG] debug-file - Update task lingxia:default@MergeProfile input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/build-profile.json5 cache by regenerate.
[2025-07-31T18:51:36.578] [DEBUG] debug-file - Update task lingxia:default@MergeProfile input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/module.json5 cache by regenerate.
[2025-07-31T18:51:36.580] [DEBUG] debug-file - Update task lingxia:default@MergeProfile output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/merge_profile/default/module.json cache.
[2025-07-31T18:51:36.581] [DEBUG] debug-file - Incremental task lingxia:default@MergeProfile post-execution cost:3 ms .
[2025-07-31T18:51:36.581] [DEBUG] debug-file - Update task lingxia:default@GeneratePkgContextInfo output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/loader/default/pkgContextInfo.json cache.
[2025-07-31T18:51:36.581] [DEBUG] debug-file - Incremental task lingxia:default@GeneratePkgContextInfo post-execution cost:1 ms .
[2025-07-31T18:51:36.582] [DEBUG] debug-file - Update task lingxia:default@ProcessIntegratedHsp output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/build/cache/default/integrated_hsp/integratedHspCache.json cache.
[2025-07-31T18:51:36.582] [DEBUG] debug-file - Incremental task lingxia:default@ProcessIntegratedHsp post-execution cost:1 ms .
[2025-07-31T18:51:36.582] [DEBUG] debug-file - Update task lingxia:default@ProcessProfile input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/merge_profile/default/module.json cache by regenerate.
[2025-07-31T18:51:36.583] [DEBUG] debug-file - Update task lingxia:default@ProcessProfile output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/process_profile/default/module.json cache.
[2025-07-31T18:51:36.583] [DEBUG] debug-file - Incremental task lingxia:default@ProcessProfile post-execution cost:1 ms .
[2025-07-31T18:51:36.584] [DEBUG] debug-file - Update task lingxia:default@ProcessRouterMap input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/oh-package.json5 cache by regenerate.
[2025-07-31T18:51:36.584] [DEBUG] debug-file - Update task lingxia:default@ProcessRouterMap input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/oh-package.json5 cache by regenerate.
[2025-07-31T18:51:36.584] [DEBUG] debug-file - Update task lingxia:default@ProcessRouterMap input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/module.json5 cache by regenerate.
[2025-07-31T18:51:36.584] [DEBUG] debug-file - Update task lingxia:default@ProcessRouterMap input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/loader/default/pkgContextInfo.json cache by regenerate.
[2025-07-31T18:51:36.585] [DEBUG] debug-file - Update task lingxia:default@ProcessRouterMap output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/router_map/default/temp-router-map.json cache.
[2025-07-31T18:51:36.585] [DEBUG] debug-file - Update task lingxia:default@ProcessRouterMap output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/router_map/default/loader-router-map.json cache.
[2025-07-31T18:51:36.585] [DEBUG] debug-file - Incremental task lingxia:default@ProcessRouterMap post-execution cost:3 ms .
[2025-07-31T18:51:36.585] [DEBUG] debug-file - Update task lingxia:default@ProcessResource output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/res/default/resConfig.json cache.
[2025-07-31T18:51:36.585] [DEBUG] debug-file - Update task lingxia:default@ProcessResource output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/res/default/opt-compression.json cache.
[2025-07-31T18:51:36.585] [DEBUG] debug-file - Incremental task lingxia:default@ProcessResource post-execution cost:1 ms .
[2025-07-31T18:51:36.587] [DEBUG] debug-file - Update task lingxia:default@GenerateLoaderJson input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/loader/default/pkgContextInfo.json cache by regenerate.
[2025-07-31T18:51:36.587] [DEBUG] debug-file - Update task lingxia:default@GenerateLoaderJson input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets cache by regenerate.
[2025-07-31T18:51:36.588] [DEBUG] debug-file - Update task lingxia:default@GenerateLoaderJson input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/generated/profile/default/ModuleInfo.ts cache by regenerate.
[2025-07-31T18:51:36.588] [DEBUG] debug-file - Update task lingxia:default@GenerateLoaderJson output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/loader/default/loader.json cache.
[2025-07-31T18:51:36.588] [DEBUG] debug-file - Incremental task lingxia:default@GenerateLoaderJson post-execution cost:3 ms .
[2025-07-31T18:51:36.588] [DEBUG] debug-file - Update task lingxia:default@ProcessLibs input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/build-profile.json5 cache by regenerate.
[2025-07-31T18:51:36.588] [DEBUG] debug-file - Update task lingxia:default@ProcessLibs input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build-profile.json5 cache by regenerate.
[2025-07-31T18:51:36.589] [DEBUG] debug-file - Update task lingxia:default@ProcessLibs output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/libs/default cache.
[2025-07-31T18:51:36.589] [DEBUG] debug-file - Incremental task lingxia:default@ProcessLibs post-execution cost:1 ms .
[2025-07-31T18:51:36.590] [DEBUG] debug-file - Update task lingxia:default@CompileResource input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/process_profile/default/module.json cache by regenerate.
[2025-07-31T18:51:36.590] [DEBUG] debug-file - Update task lingxia:default@CompileResource input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/res/default/resConfig.json cache by regenerate.
[2025-07-31T18:51:36.590] [DEBUG] debug-file - Update task lingxia:default@CompileResource output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/res/default cache.
[2025-07-31T18:51:36.591] [DEBUG] debug-file - Update task lingxia:default@CompileResource output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/generated/r/default/ResourceTable.h cache.
[2025-07-31T18:51:36.592] [DEBUG] debug-file - Update task lingxia:default@CompileResource output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/generated/r/default cache.
[2025-07-31T18:51:36.592] [DEBUG] debug-file - Incremental task lingxia:default@CompileResource post-execution cost:4 ms .
[2025-07-31T18:51:36.594] [DEBUG] debug-file - Update task lingxia:default@HarCompileArkTS input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/loader/default cache by regenerate.
[2025-07-31T18:51:36.594] [DEBUG] debug-file - Update task lingxia:default@HarCompileArkTS input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/res/default/ark_module.json cache by regenerate.
[2025-07-31T18:51:36.595] [DEBUG] debug-file - Update task lingxia:default@HarCompileArkTS input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets cache by regenerate.
[2025-07-31T18:51:36.596] [DEBUG] debug-file - Update task lingxia:default@HarCompileArkTS input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/Index.ets cache by regenerate.
[2025-07-31T18:51:36.596] [DEBUG] debug-file - Update task lingxia:default@HarCompileArkTS input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/loader/default/pkgContextInfo.json cache by regenerate.
[2025-07-31T18:51:36.596] [DEBUG] debug-file - Update task lingxia:default@HarCompileArkTS input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/BuildProfile.ets cache by regenerate.
[2025-07-31T18:51:36.596] [DEBUG] debug-file - Update task lingxia:default@HarCompileArkTS output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/loader_out/default/ets cache.
[2025-07-31T18:51:36.597] [DEBUG] debug-file - Incremental task lingxia:default@HarCompileArkTS post-execution cost:5 ms .
[2025-07-31T18:51:36.597] [DEBUG] debug-file - Update task lingxia:default@DoNativeStrip input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/libs/default cache by regenerate.
[2025-07-31T18:51:36.597] [DEBUG] debug-file - Update task lingxia:default@DoNativeStrip output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/stripped_native_libs/default cache.
[2025-07-31T18:51:36.598] [DEBUG] debug-file - Incremental task lingxia:default@DoNativeStrip post-execution cost:1 ms .
[2025-07-31T18:51:36.598] [DEBUG] debug-file - Update task lingxia:default@CacheNativeLibs input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/stripped_native_libs/default cache by regenerate.
[2025-07-31T18:51:36.598] [DEBUG] debug-file - Update task lingxia:default@CacheNativeLibs input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/libs/default cache by regenerate.
[2025-07-31T18:51:36.598] [DEBUG] debug-file - Update task lingxia:default@CacheNativeLibs output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/patch/default/base_native_libs.json cache.
[2025-07-31T18:51:36.599] [DEBUG] debug-file - Incremental task lingxia:default@CacheNativeLibs post-execution cost:1 ms .
[2025-07-31T18:51:36.599] [DEBUG] debug-file - Update task lingxia:default@ProcessHarArtifacts input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/process_profile/default cache by regenerate.
[2025-07-31T18:51:36.600] [DEBUG] debug-file - Update task lingxia:default@ProcessHarArtifacts input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia cache by regenerate.
[2025-07-31T18:51:36.601] [DEBUG] debug-file - Update task lingxia:default@ProcessHarArtifacts input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/generated/pm/default cache by regenerate.
[2025-07-31T18:51:36.601] [DEBUG] debug-file - Update task lingxia:default@ProcessHarArtifacts input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/stripped_native_libs/default cache by regenerate.
[2025-07-31T18:51:36.601] [DEBUG] debug-file - Update task lingxia:default@ProcessHarArtifacts input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/loader/default/pkgContextInfo.json cache by regenerate.
[2025-07-31T18:51:36.601] [DEBUG] debug-file - Update task lingxia:default@ProcessHarArtifacts output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/cache/default/default@PackageHar cache.
[2025-07-31T18:51:36.602] [DEBUG] debug-file - Incremental task lingxia:default@ProcessHarArtifacts post-execution cost:4 ms .
[2025-07-31T18:51:36.602] [DEBUG] debug-file - Update task lingxia:default@PackageHar output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/cache/default/default@PackageHar cache.
[2025-07-31T18:51:36.604] [DEBUG] debug-file - Update task lingxia:default@PackageHar output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/outputs/default cache.
[2025-07-31T18:51:36.604] [DEBUG] debug-file - Incremental task lingxia:default@PackageHar post-execution cost:2 ms .
[2025-07-31T18:51:36.604] [DEBUG] debug-file - Update task lingxia:default@PackageSignHar input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/cache/default/default@PackageHar cache by regenerate.
[2025-07-31T18:51:36.605] [DEBUG] debug-file - Update task lingxia:default@PackageSignHar input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/cache/default/default@PackageSignHar cache by regenerate.
[2025-07-31T18:51:36.606] [DEBUG] debug-file - Update task lingxia:default@PackageSignHar output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/outputs/default/lingxia-signed.har cache.
[2025-07-31T18:51:36.606] [DEBUG] debug-file - Incremental task lingxia:default@PackageSignHar post-execution cost:2 ms .
[2025-07-31T18:51:36.609] [DEBUG] debug-file - Update task lingxia:default@CollectDebugSymbol input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/loader_out/default/ets/sourceMaps.map cache by regenerate.
[2025-07-31T18:51:36.609] [DEBUG] debug-file - Update task lingxia:default@CollectDebugSymbol input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/libs/default cache by regenerate.
[2025-07-31T18:51:36.609] [DEBUG] debug-file - Update task lingxia:default@CollectDebugSymbol output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/outputs/default/symbol cache.
[2025-07-31T18:51:36.610] [DEBUG] debug-file - Incremental task lingxia:default@CollectDebugSymbol post-execution cost:4 ms .
[2025-07-31T18:51:36.629] [DEBUG] debug-file - Cleanup worker 0.
[2025-07-31T18:51:36.629] [DEBUG] debug-file - Worker 0 has been cleaned up.
[2025-07-31T18:51:36.629] [DEBUG] debug-file - Current idle worker size: 4.
[2025-07-31T18:51:36.629] [DEBUG] debug-file - Current resident worker size: 5.
[2025-07-31T18:51:36.629] [DEBUG] debug-file - Cleanup worker 1.
[2025-07-31T18:51:36.629] [DEBUG] debug-file - Worker 1 has been cleaned up.
[2025-07-31T18:51:36.629] [DEBUG] debug-file - Current idle worker size: 3.
[2025-07-31T18:51:36.630] [DEBUG] debug-file - Current resident worker size: 5.
[2025-07-31T18:51:36.630] [DEBUG] debug-file - Cleanup worker 5.
[2025-07-31T18:51:36.630] [DEBUG] debug-file - Worker 5 has been cleaned up.
[2025-07-31T18:51:36.630] [DEBUG] debug-file - Current idle worker size: 2.
[2025-07-31T18:51:36.630] [DEBUG] debug-file - Current resident worker size: 5.
[2025-07-31T18:51:36.630] [DEBUG] debug-file - Cleanup worker 6.
[2025-07-31T18:51:36.630] [DEBUG] debug-file - Worker 6 has been cleaned up.
[2025-07-31T18:51:36.630] [DEBUG] debug-file - Current idle worker size: 1.
[2025-07-31T18:51:36.630] [DEBUG] debug-file - Current resident worker size: 5.
[2025-07-31T18:51:36.635] [DEBUG] debug-file - session manager: send message to worker process.
[2025-07-31T18:51:36.636] [DEBUG] debug-file - session manager: send message to worker process.
[2025-07-31T18:51:36.633] [DEBUG] debug-file - hvigor build process will be closed.
[2025-07-31T18:51:36.636] [DEBUG] debug-file - worker[0] exits with exit code 1.
[2025-07-31T18:51:36.637] [DEBUG] debug-file - worker[1] exits with exit code 1.
[2025-07-31T18:51:36.638] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2025-07-31T18:51:36.639] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2025-07-31T18:51:36.646] [DEBUG] debug-file - worker[5] exits with exit code 1.
[2025-07-31T18:51:36.654] [DEBUG] debug-file - worker[6] exits with exit code 1.
[2025-07-31T18:58:34.193] [DEBUG] debug-file - java daemon exit code:null signal:SIGKILL
[2025-07-31T19:04:09.720] [DEBUG] debug-file - env: nodejsVersion=v18.20.1
[2025-07-31T19:04:09.757] [DEBUG] debug-file - env: daemon=true
[2025-07-31T19:04:09.723] [DEBUG] debug-file - env: hvigor-config.json5 content = {
  modelVersion: '5.0.5',
  dependencies: {},
  execution: {},
  logging: {},
  debugging: {},
  nodeOptions: {}
}
[2025-07-31T19:04:10.417] [DEBUG] debug-file - session manager: set active socket. socketId=1WUiPo-sQyf2oVlvAAAD
[2025-07-31T19:04:10.481] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2025-07-31T19:04:10.537] [DEBUG] debug-file - Hvigor init with startParameters:{
  hvigorfileTypeCheck: false,
  parallelExecution: true,
  incrementalExecution: true,
  printStackTrace: false,
  daemon: true,
  analyze: 0,
  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }
}
[2025-07-31T19:04:10.554] [DEBUG] debug-file - Cache service initialization finished in 16 ms 
[2025-07-31T19:04:10.580] [DEBUG] debug-file - hvigorfile, resolving /Users/<USER>/github/LingXia/lingxia-sdk/harmony/hvigorfile.ts
[2025-07-31T19:04:10.608] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-07-31T19:04:10.608] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-07-31T19:04:10.625] [DEBUG] debug-file - Start initialize project's product build option map with build mode debug.
[2025-07-31T19:04:10.626] [DEBUG] debug-file - Picking option from product 'default' with build mode 'debug'.
[2025-07-31T19:04:10.626] [DEBUG] debug-file - Product 'default' build option: {}
[2025-07-31T19:04:10.627] [DEBUG] debug-file - End initialize project's product build option map with build mode 'debug'.
[2025-07-31T19:04:10.630] [DEBUG] debug-file - Product 'default' using build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  }
} in this build.
[2025-07-31T19:04:10.631] [DEBUG] debug-file - not found resModel json file in : /Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/ohosTest/module.json5
[2025-07-31T19:04:10.638] [DEBUG] debug-file - No signingConfig found, initRemoteHspCache failed.
[2025-07-31T19:04:10.657] [DEBUG] debug-file - Start recording SDK configuration permission data.
[2025-07-31T19:04:10.704] [DEBUG] debug-file - Sdk init in 65 ms 
[2025-07-31T19:04:10.719] [DEBUG] debug-file - Project task initialization takes 14 ms 
[2025-07-31T19:04:10.720] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-07-31T19:04:10.720] [DEBUG] debug-file - hvigorfile, no custom plugins were found in /Users/<USER>/github/LingXia/lingxia-sdk/harmony/hvigorfile.ts
[2025-07-31T19:04:10.720] [DEBUG] debug-file - hvigorfile, resolve finished /Users/<USER>/github/LingXia/lingxia-sdk/harmony/hvigorfile.ts
[2025-07-31T19:04:10.724] [DEBUG] debug-file - hvigorfile, resolving /Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/hvigorfile.ts
[2025-07-31T19:04:10.730] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-07-31T19:04:10.730] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-07-31T19:04:10.735] [DEBUG] debug-file - Start initialize module-target build option map, moduleName=lingxia, buildMode=debug
[2025-07-31T19:04:10.736] [DEBUG] debug-file - Target 'default' config: {}
[2025-07-31T19:04:10.737] [DEBUG] debug-file - Target 'ohosTest' config: {}
[2025-07-31T19:04:10.737] [DEBUG] debug-file - Module 'lingxia' target 'default' build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  }
}
[2025-07-31T19:04:10.737] [DEBUG] debug-file - Module 'lingxia' target 'ohosTest' build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  }
}
[2025-07-31T19:04:10.737] [DEBUG] debug-file - End initialize module-target build option map, moduleName=lingxia
[2025-07-31T19:04:10.737] [DEBUG] debug-file - Module 'lingxia' target 'default' using build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  }
} in this build.
[2025-07-31T19:04:10.742] [DEBUG] debug-file - Module lingxia task initialization takes 4 ms 
[2025-07-31T19:04:10.743] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-07-31T19:04:10.743] [DEBUG] debug-file - hvigorfile, no custom plugins were found in /Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/hvigorfile.ts
[2025-07-31T19:04:10.743] [DEBUG] debug-file - hvigorfile, resolve finished /Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/hvigorfile.ts
[2025-07-31T19:04:10.871] [DEBUG] debug-file - hvigorfile, resolve hvigorfile dependencies in 127 ms 
[2025-07-31T19:04:10.871] [DEBUG] debug-file - project has submodules:lingxia
[2025-07-31T19:04:10.872] [DEBUG] debug-file - module:lingxia no need to execute packageHap
[2025-07-31T19:04:10.873] [DEBUG] debug-file - start to load updatedOhPackageInfo to the disk
[2025-07-31T19:04:10.877] [DEBUG] debug-file - load to the disk finished
[2025-07-31T19:04:10.877] [DEBUG] debug-file - Start to initialize dependency information.
[2025-07-31T19:04:10.879] [DEBUG] debug-file - Module harmony Collected Dependency: 
[2025-07-31T19:04:10.879] [DEBUG] debug-file - Module harmony's total dependency: 0
[2025-07-31T19:04:10.880] [DEBUG] debug-file - Start to initialize dependency information.
[2025-07-31T19:04:10.881] [DEBUG] debug-file - Module lingxia Collected Dependency: 
[2025-07-31T19:04:10.881] [DEBUG] debug-file - Module lingxia's total dependency: 0
[2025-07-31T19:04:10.883] [DEBUG] debug-file - Configuration phase cost:317 ms 
[2025-07-31T19:04:10.885] [DEBUG] debug-file - Configuration task cost before running: 377 ms 
[2025-07-31T19:04:10.887] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:04:10.887] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:04:10.889] [DEBUG] debug-file - Executing task :lingxia:default@PreBuild
[2025-07-31T19:04:10.895] [DEBUG] debug-file - Incremental task lingxia:default@PreBuild pre-execution cost: 6 ms .
[2025-07-31T19:04:10.896] [DEBUG] debug-file - lingxia : default@PreBuild cost memory 0.16617584228515625
[2025-07-31T19:04:10.897] [INFO] debug-file - UP-TO-DATE :lingxia:default@PreBuild...  
[2025-07-31T19:04:10.898] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:04:10.898] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:04:10.899] [DEBUG] debug-file - Executing task :lingxia:default@ProcessOHPackageJson
[2025-07-31T19:04:10.899] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:04:10.899] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:04:10.900] [DEBUG] debug-file - lingxia : default@ProcessOHPackageJson cost memory 0.04418182373046875
[2025-07-31T19:04:10.900] [DEBUG] debug-file - runTaskFromQueue task cost before running: 391 ms 
[2025-07-31T19:04:10.900] [INFO] debug-file - Finished :lingxia:default@ProcessOHPackageJson... after 2 ms 
[2025-07-31T19:04:10.902] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:04:10.902] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:04:10.903] [DEBUG] debug-file - Executing task :lingxia:default@CreateHarBuildProfile
[2025-07-31T19:04:10.903] [DEBUG] debug-file - Task 'lingxia:default@CreateHarBuildProfile' cost while wrapping incremental declarations: 1 ms 
[2025-07-31T19:04:10.904] [DEBUG] debug-file - Incremental task lingxia:default@CreateHarBuildProfile pre-execution cost: 1 ms .
[2025-07-31T19:04:10.904] [DEBUG] debug-file - lingxia : default@CreateHarBuildProfile cost memory 0.07794189453125
[2025-07-31T19:04:10.904] [INFO] debug-file - UP-TO-DATE :lingxia:default@CreateHarBuildProfile...  
[2025-07-31T19:04:10.906] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:04:10.906] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:04:10.907] [DEBUG] debug-file - Executing task :lingxia:default@CreateHarModuleInfo
[2025-07-31T19:04:10.907] [DEBUG] debug-file - Task 'lingxia:default@CreateHarModuleInfo' cost while wrapping incremental declarations: 1 ms 
[2025-07-31T19:04:10.908] [DEBUG] debug-file - Incremental task lingxia:default@CreateHarModuleInfo pre-execution cost: 1 ms .
[2025-07-31T19:04:10.908] [DEBUG] debug-file - lingxia : default@CreateHarModuleInfo cost memory 0.05847930908203125
[2025-07-31T19:04:10.908] [INFO] debug-file - UP-TO-DATE :lingxia:default@CreateHarModuleInfo...  
[2025-07-31T19:04:10.909] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:04:10.909] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:04:10.910] [DEBUG] debug-file - Executing task :lingxia:default@ConfigureCmake
[2025-07-31T19:04:10.910] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:04:10.910] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:04:10.910] [DEBUG] debug-file - lingxia : default@ConfigureCmake cost memory 0.03821563720703125
[2025-07-31T19:04:10.910] [DEBUG] debug-file - runTaskFromQueue task cost before running: 401 ms 
[2025-07-31T19:04:10.910] [INFO] debug-file - Finished :lingxia:default@ConfigureCmake... after 1 ms 
[2025-07-31T19:04:10.913] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:04:10.913] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:04:10.913] [DEBUG] debug-file - Executing task :lingxia:default@MergeProfile
[2025-07-31T19:04:10.914] [DEBUG] debug-file - Incremental task lingxia:default@MergeProfile pre-execution cost: 1 ms .
[2025-07-31T19:04:10.915] [DEBUG] debug-file - lingxia : default@MergeProfile cost memory 0.088287353515625
[2025-07-31T19:04:10.915] [INFO] debug-file - UP-TO-DATE :lingxia:default@MergeProfile...  
[2025-07-31T19:04:10.916] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:04:10.916] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:04:10.920] [DEBUG] debug-file - Executing task :lingxia:default@GeneratePkgContextInfo
[2025-07-31T19:04:10.920] [DEBUG] debug-file - Task 'lingxia:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms 
[2025-07-31T19:04:10.921] [DEBUG] debug-file - Incremental task lingxia:default@GeneratePkgContextInfo pre-execution cost: 1 ms .
[2025-07-31T19:04:10.921] [DEBUG] debug-file - lingxia : default@GeneratePkgContextInfo cost memory 0.058135986328125
[2025-07-31T19:04:10.921] [INFO] debug-file - UP-TO-DATE :lingxia:default@GeneratePkgContextInfo...  
[2025-07-31T19:04:10.922] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:04:10.923] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:04:10.924] [DEBUG] debug-file - Executing task :lingxia:default@ProcessIntegratedHsp
[2025-07-31T19:04:10.925] [DEBUG] debug-file - lingxia:default@ProcessIntegratedHsp is not up-to-date, since the output file '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/build/cache/default/integrated_hsp/integratedHspCache.json' does not exist.
[2025-07-31T19:04:10.925] [DEBUG] debug-file - Incremental task lingxia:default@ProcessIntegratedHsp pre-execution cost: 1 ms .
[2025-07-31T19:04:10.925] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:04:10.925] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:04:10.925] [DEBUG] debug-file - lingxia : default@ProcessIntegratedHsp cost memory 0.1097412109375
[2025-07-31T19:04:10.925] [DEBUG] debug-file - runTaskFromQueue task cost before running: 416 ms 
[2025-07-31T19:04:10.926] [INFO] debug-file - Finished :lingxia:default@ProcessIntegratedHsp... after 2 ms 
[2025-07-31T19:04:10.927] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:04:10.928] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:04:10.929] [DEBUG] debug-file - Executing task :lingxia:default@BuildNativeWithCmake
[2025-07-31T19:04:10.929] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:04:10.929] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:04:10.930] [DEBUG] debug-file - lingxia : default@BuildNativeWithCmake cost memory 0.038238525390625
[2025-07-31T19:04:10.930] [DEBUG] debug-file - runTaskFromQueue task cost before running: 421 ms 
[2025-07-31T19:04:10.931] [INFO] debug-file - Finished :lingxia:default@BuildNativeWithCmake... after 1 ms 
[2025-07-31T19:04:10.933] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:04:10.933] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:04:10.934] [DEBUG] debug-file - Executing task :lingxia:default@ProcessProfile
[2025-07-31T19:04:10.935] [DEBUG] debug-file - Incremental task lingxia:default@ProcessProfile pre-execution cost: 1 ms .
[2025-07-31T19:04:10.935] [DEBUG] debug-file - lingxia : default@ProcessProfile cost memory 0.07275390625
[2025-07-31T19:04:10.936] [INFO] debug-file - UP-TO-DATE :lingxia:default@ProcessProfile...  
[2025-07-31T19:04:10.937] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:04:10.937] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:04:10.938] [DEBUG] debug-file - Executing task :lingxia:default@ProcessRouterMap
[2025-07-31T19:04:10.940] [DEBUG] debug-file - lingxia:default@ProcessRouterMap is not up-to-date, since the output file '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/router_map/default/temp-router-map.json' does not exist.
[2025-07-31T19:04:10.940] [DEBUG] debug-file - Incremental task lingxia:default@ProcessRouterMap pre-execution cost: 2 ms .
[2025-07-31T19:04:10.941] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:04:10.941] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:04:10.941] [DEBUG] debug-file - lingxia : default@ProcessRouterMap cost memory 0.2003326416015625
[2025-07-31T19:04:10.941] [DEBUG] debug-file - runTaskFromQueue task cost before running: 432 ms 
[2025-07-31T19:04:10.942] [INFO] debug-file - Finished :lingxia:default@ProcessRouterMap... after 3 ms 
[2025-07-31T19:04:10.943] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:04:10.943] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:04:10.944] [DEBUG] debug-file - Executing task :lingxia:default@BuildNativeWithNinja
[2025-07-31T19:04:10.944] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:04:10.944] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:04:10.945] [DEBUG] debug-file - lingxia : default@BuildNativeWithNinja cost memory 0.053436279296875
[2025-07-31T19:04:10.945] [DEBUG] debug-file - runTaskFromQueue task cost before running: 437 ms 
[2025-07-31T19:04:10.946] [INFO] debug-file - Finished :lingxia:default@BuildNativeWithNinja... after 2 ms 
[2025-07-31T19:04:10.948] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:04:10.948] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:04:10.948] [DEBUG] debug-file - restool module names: lingxia; moduleName=lingxia, taskName=default@ProcessResource
[2025-07-31T19:04:10.950] [DEBUG] debug-file - Executing task :lingxia:default@ProcessResource
[2025-07-31T19:04:10.952] [DEBUG] debug-file - Incremental task lingxia:default@ProcessResource pre-execution cost: 1 ms .
[2025-07-31T19:04:10.952] [DEBUG] debug-file - lingxia : default@ProcessResource cost memory 0.12842559814453125
[2025-07-31T19:04:10.957] [INFO] debug-file - UP-TO-DATE :lingxia:default@ProcessResource...  
[2025-07-31T19:04:10.961] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:04:10.961] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:04:10.968] [DEBUG] debug-file - Executing task :lingxia:default@GenerateLoaderJson
[2025-07-31T19:04:10.984] [DEBUG] debug-file - Incremental task lingxia:default@GenerateLoaderJson pre-execution cost: 11 ms .
[2025-07-31T19:04:10.985] [DEBUG] debug-file - lingxia : default@GenerateLoaderJson cost memory 0.5279388427734375
[2025-07-31T19:04:10.989] [INFO] debug-file - UP-TO-DATE :lingxia:default@GenerateLoaderJson...  
[2025-07-31T19:04:10.991] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:04:10.992] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:04:10.992] [DEBUG] debug-file - Executing task :lingxia:default@ProcessLibs
[2025-07-31T19:04:10.996] [DEBUG] debug-file - Incremental task lingxia:default@ProcessLibs pre-execution cost: 2 ms .
[2025-07-31T19:04:10.996] [DEBUG] debug-file - lingxia : default@ProcessLibs cost memory 0.1062164306640625
[2025-07-31T19:04:10.997] [INFO] debug-file - UP-TO-DATE :lingxia:default@ProcessLibs...  
[2025-07-31T19:04:11.001] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:04:11.001] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:04:11.002] [DEBUG] debug-file - restool module names: lingxia; moduleName=lingxia, taskName=default@CompileResource
[2025-07-31T19:04:11.006] [DEBUG] debug-file - Executing task :lingxia:default@CompileResource
[2025-07-31T19:04:11.008] [DEBUG] debug-file - lingxia:default@CompileResource is not up-to-date, since the output file '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/generated/r/default/ResourceTable.h' does not exist.
[2025-07-31T19:04:11.008] [DEBUG] debug-file - Incremental task lingxia:default@CompileResource pre-execution cost: 2 ms .
[2025-07-31T19:04:11.008] [DEBUG] debug-file - lingxia : default@CompileResource cost memory 0.13448333740234375
[2025-07-31T19:04:11.009] [DEBUG] debug-file - runTaskFromQueue task cost before running: 500 ms 
[2025-07-31T19:04:11.009] [INFO] debug-file - Finished :lingxia:default@CompileResource... after 4 ms 
[2025-07-31T19:04:11.012] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:04:11.012] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:04:11.015] [DEBUG] debug-file - Executing task :lingxia:default@DoNativeStrip
[2025-07-31T19:04:11.016] [DEBUG] debug-file - Task 'lingxia:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms 
[2025-07-31T19:04:11.017] [DEBUG] debug-file - Incremental task lingxia:default@DoNativeStrip pre-execution cost: 1 ms .
[2025-07-31T19:04:11.017] [DEBUG] debug-file - lingxia : default@DoNativeStrip cost memory 0.06491851806640625
[2025-07-31T19:04:11.018] [INFO] debug-file - UP-TO-DATE :lingxia:default@DoNativeStrip...  
[2025-07-31T19:04:11.020] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:04:11.020] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:04:11.021] [DEBUG] debug-file - Executing task :lingxia:default@ProcessObfuscationFiles
[2025-07-31T19:04:11.035] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:04:11.035] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:04:11.035] [DEBUG] debug-file - lingxia : default@ProcessObfuscationFiles cost memory -10.753044128417969
[2025-07-31T19:04:11.035] [DEBUG] debug-file - runTaskFromQueue task cost before running: 526 ms 
[2025-07-31T19:04:11.035] [INFO] debug-file - Finished :lingxia:default@ProcessObfuscationFiles... after 15 ms 
[2025-07-31T19:04:11.037] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:04:11.037] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:04:11.039] [DEBUG] debug-file - Executing task :lingxia:default@HarCompileArkTS
[2025-07-31T19:04:11.044] [DEBUG] debug-file - Incremental task lingxia:default@HarCompileArkTS pre-execution cost: 4 ms .
[2025-07-31T19:04:11.044] [DEBUG] debug-file - lingxia : default@HarCompileArkTS cost memory 0.43563079833984375
[2025-07-31T19:04:11.047] [INFO] debug-file - UP-TO-DATE :lingxia:default@HarCompileArkTS...  
[2025-07-31T19:04:11.048] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:04:11.048] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:04:11.050] [DEBUG] debug-file - Executing task :lingxia:default@CacheNativeLibs
[2025-07-31T19:04:11.050] [DEBUG] debug-file - Task 'lingxia:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms 
[2025-07-31T19:04:11.051] [DEBUG] debug-file - Incremental task lingxia:default@CacheNativeLibs pre-execution cost: 1 ms .
[2025-07-31T19:04:11.051] [DEBUG] debug-file - lingxia : default@CacheNativeLibs cost memory 0.07698822021484375
[2025-07-31T19:04:11.051] [INFO] debug-file - UP-TO-DATE :lingxia:default@CacheNativeLibs...  
[2025-07-31T19:04:11.053] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:04:11.053] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:04:11.055] [DEBUG] debug-file - Executing task :lingxia:default@ProcessHarArtifacts
[2025-07-31T19:04:11.059] [DEBUG] debug-file - lingxia:default@ProcessHarArtifacts is not up-to-date, since the input file '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/generated/pm/default' has been changed.
[2025-07-31T19:04:11.059] [DEBUG] debug-file - Incremental task lingxia:default@ProcessHarArtifacts pre-execution cost: 3 ms .
[2025-07-31T19:04:11.059] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:04:11.059] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:04:11.120] [DEBUG] debug-file - lingxia : default@ProcessHarArtifacts cost memory 2.2370376586914062
[2025-07-31T19:04:11.121] [DEBUG] debug-file - runTaskFromQueue task cost before running: 612 ms 
[2025-07-31T19:04:11.122] [INFO] debug-file - Finished :lingxia:default@ProcessHarArtifacts... after 66 ms 
[2025-07-31T19:04:11.123] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:04:11.123] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:04:11.125] [DEBUG] debug-file - Executing task :lingxia:default@PackageHar
[2025-07-31T19:04:11.127] [DEBUG] debug-file - lingxia:default@PackageHar is not up-to-date, since the output file '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/cache/default/default@PackageHar' has been changed.
[2025-07-31T19:04:11.127] [DEBUG] debug-file - Incremental task lingxia:default@PackageHar pre-execution cost: 2 ms .
[2025-07-31T19:04:11.128] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:04:11.128] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:04:11.163] [DEBUG] debug-file - lingxia : default@PackageHar cost memory 0.7271575927734375
[2025-07-31T19:04:11.164] [DEBUG] debug-file - runTaskFromQueue task cost before running: 655 ms 
[2025-07-31T19:04:11.164] [INFO] debug-file - Finished :lingxia:default@PackageHar... after 39 ms 
[2025-07-31T19:04:11.168] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:04:11.168] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:04:11.172] [DEBUG] debug-file - Executing task :lingxia:default@PackageSignHar
[2025-07-31T19:04:11.174] [WARN] debug-file - Will skip sign 'har'. No signingConfigs profile is configured in current project.
             If needed, configure the signingConfigs in /Users/<USER>/github/LingXia/lingxia-sdk/harmony/build-profile.json5.
[2025-07-31T19:04:11.174] [DEBUG] debug-file - lingxia : default@PackageSignHar cost memory 0.052337646484375
[2025-07-31T19:04:11.174] [DEBUG] debug-file - runTaskFromQueue task cost before running: 665 ms 
[2025-07-31T19:04:11.174] [INFO] debug-file - Finished :lingxia:default@PackageSignHar... after 3 ms 
[2025-07-31T19:04:11.176] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:04:11.177] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:04:11.177] [DEBUG] debug-file - Executing task :lingxia:default@CollectDebugSymbol
[2025-07-31T19:04:11.181] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:04:11.182] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:04:11.182] [DEBUG] debug-file - lingxia : default@CollectDebugSymbol cost memory 0.19950103759765625
[2025-07-31T19:04:11.182] [DEBUG] debug-file - runTaskFromQueue task cost before running: 673 ms 
[2025-07-31T19:04:11.182] [INFO] debug-file - Finished :lingxia:default@CollectDebugSymbol... after 5 ms 
[2025-07-31T19:04:11.184] [DEBUG] debug-file - Executing task :lingxia:assembleHar
[2025-07-31T19:04:11.184] [DEBUG] debug-file - lingxia : assembleHar cost memory 0.01177215576171875
[2025-07-31T19:04:11.184] [DEBUG] debug-file - runTaskFromQueue task cost before running: 675 ms 
[2025-07-31T19:04:11.184] [INFO] debug-file - Finished :lingxia:assembleHar... after 1 ms 
[2025-07-31T19:04:11.186] [DEBUG] debug-file - BUILD SUCCESSFUL in 677 ms 
[2025-07-31T19:04:11.187] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task lingxia:default@PreBuild is up-to-date.
[2025-07-31T19:04:11.187] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task lingxia:default@CreateHarBuildProfile is up-to-date.
[2025-07-31T19:04:11.187] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task lingxia:default@CreateHarModuleInfo is up-to-date.
[2025-07-31T19:04:11.187] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task lingxia:default@MergeProfile is up-to-date.
[2025-07-31T19:04:11.187] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task lingxia:default@GeneratePkgContextInfo is up-to-date.
[2025-07-31T19:04:11.188] [DEBUG] debug-file - Update task lingxia:default@ProcessIntegratedHsp output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/build/cache/default/integrated_hsp/integratedHspCache.json cache.
[2025-07-31T19:04:11.188] [DEBUG] debug-file - Incremental task lingxia:default@ProcessIntegratedHsp post-execution cost:1 ms .
[2025-07-31T19:04:11.188] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task lingxia:default@ProcessProfile is up-to-date.
[2025-07-31T19:04:11.190] [DEBUG] debug-file - Update task lingxia:default@ProcessRouterMap input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/oh-package.json5 cache by regenerate.
[2025-07-31T19:04:11.190] [DEBUG] debug-file - Update task lingxia:default@ProcessRouterMap input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/oh-package.json5 cache by regenerate.
[2025-07-31T19:04:11.190] [DEBUG] debug-file - Update task lingxia:default@ProcessRouterMap input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/module.json5 cache by regenerate.
[2025-07-31T19:04:11.190] [DEBUG] debug-file - Update task lingxia:default@ProcessRouterMap input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/loader/default/pkgContextInfo.json cache by regenerate.
[2025-07-31T19:04:11.190] [DEBUG] debug-file - Update task lingxia:default@ProcessRouterMap output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/router_map/default/temp-router-map.json cache.
[2025-07-31T19:04:11.191] [DEBUG] debug-file - Update task lingxia:default@ProcessRouterMap output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/router_map/default/loader-router-map.json cache.
[2025-07-31T19:04:11.191] [DEBUG] debug-file - Incremental task lingxia:default@ProcessRouterMap post-execution cost:3 ms .
[2025-07-31T19:04:11.191] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task lingxia:default@ProcessResource is up-to-date.
[2025-07-31T19:04:11.191] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task lingxia:default@GenerateLoaderJson is up-to-date.
[2025-07-31T19:04:11.191] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task lingxia:default@ProcessLibs is up-to-date.
[2025-07-31T19:04:11.192] [DEBUG] debug-file - Update task lingxia:default@CompileResource input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/process_profile/default/module.json cache by regenerate.
[2025-07-31T19:04:11.192] [DEBUG] debug-file - Update task lingxia:default@CompileResource input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/res/default/resConfig.json cache by regenerate.
[2025-07-31T19:04:11.192] [DEBUG] debug-file - Update task lingxia:default@CompileResource output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/res/default cache.
[2025-07-31T19:04:11.193] [DEBUG] debug-file - Update task lingxia:default@CompileResource output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/generated/r/default/ResourceTable.h cache.
[2025-07-31T19:04:11.193] [DEBUG] debug-file - Update task lingxia:default@CompileResource output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/generated/r/default cache.
[2025-07-31T19:04:11.193] [DEBUG] debug-file - Incremental task lingxia:default@CompileResource post-execution cost:3 ms .
[2025-07-31T19:04:11.193] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task lingxia:default@DoNativeStrip is up-to-date.
[2025-07-31T19:04:11.194] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task lingxia:default@HarCompileArkTS is up-to-date.
[2025-07-31T19:04:11.194] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task lingxia:default@CacheNativeLibs is up-to-date.
[2025-07-31T19:04:11.195] [DEBUG] debug-file - Update task lingxia:default@ProcessHarArtifacts input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/process_profile/default cache by regenerate.
[2025-07-31T19:04:11.195] [DEBUG] debug-file - Update task lingxia:default@ProcessHarArtifacts input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia cache by regenerate.
[2025-07-31T19:04:11.203] [DEBUG] debug-file - Update task lingxia:default@ProcessHarArtifacts input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/generated/pm/default cache from map.
[2025-07-31T19:04:11.203] [DEBUG] debug-file - Update task lingxia:default@ProcessHarArtifacts input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/stripped_native_libs/default cache by regenerate.
[2025-07-31T19:04:11.203] [DEBUG] debug-file - Update task lingxia:default@ProcessHarArtifacts input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/loader/default/pkgContextInfo.json cache by regenerate.
[2025-07-31T19:04:11.204] [DEBUG] debug-file - Update task lingxia:default@ProcessHarArtifacts output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/cache/default/default@PackageHar cache.
[2025-07-31T19:04:11.206] [DEBUG] debug-file - Incremental task lingxia:default@ProcessHarArtifacts post-execution cost:12 ms .
[2025-07-31T19:04:11.206] [DEBUG] debug-file - Update task lingxia:default@PackageHar output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/cache/default/default@PackageHar cache.
[2025-07-31T19:04:11.208] [DEBUG] debug-file - Update task lingxia:default@PackageHar output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/outputs/default cache.
[2025-07-31T19:04:11.208] [DEBUG] debug-file - Incremental task lingxia:default@PackageHar post-execution cost:3 ms .
[2025-07-31T19:04:11.209] [DEBUG] debug-file - Update task lingxia:default@PackageSignHar input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/cache/default/default@PackageHar cache by regenerate.
[2025-07-31T19:04:11.211] [DEBUG] debug-file - Update task lingxia:default@PackageSignHar input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/cache/default/default@PackageSignHar cache by regenerate.
[2025-07-31T19:04:11.211] [DEBUG] debug-file - Update task lingxia:default@PackageSignHar output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/outputs/default/lingxia-signed.har cache.
[2025-07-31T19:04:11.211] [DEBUG] debug-file - Incremental task lingxia:default@PackageSignHar post-execution cost:3 ms .
[2025-07-31T19:04:11.214] [DEBUG] debug-file - Update task lingxia:default@CollectDebugSymbol input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/loader_out/default/ets/sourceMaps.map cache by regenerate.
[2025-07-31T19:04:11.215] [DEBUG] debug-file - Update task lingxia:default@CollectDebugSymbol input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/libs/default cache by regenerate.
[2025-07-31T19:04:11.215] [DEBUG] debug-file - Update task lingxia:default@CollectDebugSymbol output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/outputs/default/symbol cache.
[2025-07-31T19:04:11.215] [DEBUG] debug-file - Incremental task lingxia:default@CollectDebugSymbol post-execution cost:4 ms .
[2025-07-31T19:04:11.256] [DEBUG] debug-file - hvigor build process will be closed.
[2025-07-31T19:04:11.306] [DEBUG] debug-file - session manager: send message to worker process.
[2025-07-31T19:04:11.307] [DEBUG] debug-file - session manager: send message to worker process.
[2025-07-31T19:04:11.307] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2025-07-31T19:04:11.307] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2025-07-31T19:05:14.473] [DEBUG] debug-file - env: nodejsVersion=v18.20.1
[2025-07-31T19:05:14.504] [DEBUG] debug-file - env: daemon=true
[2025-07-31T19:05:14.476] [DEBUG] debug-file - env: hvigor-config.json5 content = {
  modelVersion: '5.0.5',
  dependencies: {},
  execution: {},
  logging: {},
  debugging: {},
  nodeOptions: {}
}
[2025-07-31T19:05:15.071] [DEBUG] debug-file - session manager: set active socket. socketId=-TWsDBfeVInigFv8AAAF
[2025-07-31T19:05:15.075] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2025-07-31T19:05:15.084] [DEBUG] debug-file - Hvigor init with startParameters:{
  hvigorfileTypeCheck: false,
  parallelExecution: true,
  incrementalExecution: true,
  printStackTrace: false,
  daemon: true,
  analyze: 0,
  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }
}
[2025-07-31T19:05:15.087] [DEBUG] debug-file - Cache service initialization finished in 3 ms 
[2025-07-31T19:05:15.094] [DEBUG] debug-file - hvigorfile, resolving /Users/<USER>/github/LingXia/lingxia-sdk/harmony/hvigorfile.ts
[2025-07-31T19:05:15.105] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-07-31T19:05:15.105] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-07-31T19:05:15.110] [DEBUG] debug-file - Start initialize project's product build option map with build mode debug.
[2025-07-31T19:05:15.110] [DEBUG] debug-file - Picking option from product 'default' with build mode 'debug'.
[2025-07-31T19:05:15.110] [DEBUG] debug-file - Product 'default' build option: {}
[2025-07-31T19:05:15.110] [DEBUG] debug-file - End initialize project's product build option map with build mode 'debug'.
[2025-07-31T19:05:15.112] [DEBUG] debug-file - Product 'default' using build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  }
} in this build.
[2025-07-31T19:05:15.112] [DEBUG] debug-file - not found resModel json file in : /Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/ohosTest/module.json5
[2025-07-31T19:05:15.115] [DEBUG] debug-file - No signingConfig found, initRemoteHspCache failed.
[2025-07-31T19:05:15.122] [DEBUG] debug-file - Start recording SDK configuration permission data.
[2025-07-31T19:05:15.155] [DEBUG] debug-file - Sdk init in 39 ms 
[2025-07-31T19:05:15.178] [DEBUG] debug-file - Project task initialization takes 22 ms 
[2025-07-31T19:05:15.178] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-07-31T19:05:15.178] [DEBUG] debug-file - hvigorfile, no custom plugins were found in /Users/<USER>/github/LingXia/lingxia-sdk/harmony/hvigorfile.ts
[2025-07-31T19:05:15.178] [DEBUG] debug-file - hvigorfile, resolve finished /Users/<USER>/github/LingXia/lingxia-sdk/harmony/hvigorfile.ts
[2025-07-31T19:05:15.183] [DEBUG] debug-file - hvigorfile, resolving /Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/hvigorfile.ts
[2025-07-31T19:05:15.190] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-07-31T19:05:15.190] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-07-31T19:05:15.196] [DEBUG] debug-file - Start initialize module-target build option map, moduleName=lingxia, buildMode=debug
[2025-07-31T19:05:15.196] [DEBUG] debug-file - Target 'default' config: {}
[2025-07-31T19:05:15.196] [DEBUG] debug-file - Target 'ohosTest' config: {}
[2025-07-31T19:05:15.196] [DEBUG] debug-file - Module 'lingxia' target 'default' build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  }
}
[2025-07-31T19:05:15.196] [DEBUG] debug-file - Module 'lingxia' target 'ohosTest' build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  }
}
[2025-07-31T19:05:15.196] [DEBUG] debug-file - End initialize module-target build option map, moduleName=lingxia
[2025-07-31T19:05:15.196] [DEBUG] debug-file - Module 'lingxia' target 'default' using build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  }
} in this build.
[2025-07-31T19:05:15.199] [DEBUG] debug-file - Module lingxia task initialization takes 1 ms 
[2025-07-31T19:05:15.199] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-07-31T19:05:15.199] [DEBUG] debug-file - hvigorfile, no custom plugins were found in /Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/hvigorfile.ts
[2025-07-31T19:05:15.199] [DEBUG] debug-file - hvigorfile, resolve finished /Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/hvigorfile.ts
[2025-07-31T19:05:15.297] [DEBUG] debug-file - hvigorfile, resolve hvigorfile dependencies in 94 ms 
[2025-07-31T19:05:15.298] [DEBUG] debug-file - project has submodules:lingxia
[2025-07-31T19:05:15.298] [DEBUG] debug-file - module:lingxia no need to execute packageHap
[2025-07-31T19:05:15.298] [DEBUG] debug-file - start to load updatedOhPackageInfo to the disk
[2025-07-31T19:05:15.302] [DEBUG] debug-file - load to the disk finished
[2025-07-31T19:05:15.302] [DEBUG] debug-file - Start to initialize dependency information.
[2025-07-31T19:05:15.304] [DEBUG] debug-file - Module harmony Collected Dependency: 
[2025-07-31T19:05:15.304] [DEBUG] debug-file - Module harmony's total dependency: 0
[2025-07-31T19:05:15.304] [DEBUG] debug-file - Start to initialize dependency information.
[2025-07-31T19:05:15.305] [DEBUG] debug-file - Module lingxia Collected Dependency: 
[2025-07-31T19:05:15.305] [DEBUG] debug-file - Module lingxia's total dependency: 0
[2025-07-31T19:05:15.307] [DEBUG] debug-file - Configuration phase cost:217 ms 
[2025-07-31T19:05:15.309] [DEBUG] debug-file - Configuration task cost before running: 233 ms 
[2025-07-31T19:05:15.311] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:15.311] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:15.312] [DEBUG] debug-file - Executing task :lingxia:default@PreBuild
[2025-07-31T19:05:15.314] [DEBUG] debug-file - Incremental task lingxia:default@PreBuild pre-execution cost: 2 ms .
[2025-07-31T19:05:15.314] [DEBUG] debug-file - lingxia : default@PreBuild cost memory 0.16162109375
[2025-07-31T19:05:15.315] [INFO] debug-file - UP-TO-DATE :lingxia:default@PreBuild...  
[2025-07-31T19:05:15.316] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:15.316] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:15.318] [DEBUG] debug-file - Executing task :lingxia:default@ProcessOHPackageJson
[2025-07-31T19:05:15.318] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:15.318] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:15.319] [DEBUG] debug-file - lingxia : default@ProcessOHPackageJson cost memory 0.04405975341796875
[2025-07-31T19:05:15.319] [DEBUG] debug-file - runTaskFromQueue task cost before running: 243 ms 
[2025-07-31T19:05:15.319] [INFO] debug-file - Finished :lingxia:default@ProcessOHPackageJson... after 1 ms 
[2025-07-31T19:05:15.320] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:15.320] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:15.321] [DEBUG] debug-file - Executing task :lingxia:default@CreateHarBuildProfile
[2025-07-31T19:05:15.321] [DEBUG] debug-file - Task 'lingxia:default@CreateHarBuildProfile' cost while wrapping incremental declarations: 1 ms 
[2025-07-31T19:05:15.322] [DEBUG] debug-file - Incremental task lingxia:default@CreateHarBuildProfile pre-execution cost: 1 ms .
[2025-07-31T19:05:15.322] [DEBUG] debug-file - lingxia : default@CreateHarBuildProfile cost memory 0.07590484619140625
[2025-07-31T19:05:15.322] [INFO] debug-file - UP-TO-DATE :lingxia:default@CreateHarBuildProfile...  
[2025-07-31T19:05:15.325] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:15.325] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:15.325] [DEBUG] debug-file - Executing task :lingxia:default@CreateHarModuleInfo
[2025-07-31T19:05:15.325] [DEBUG] debug-file - Task 'lingxia:default@CreateHarModuleInfo' cost while wrapping incremental declarations: 1 ms 
[2025-07-31T19:05:15.326] [DEBUG] debug-file - Incremental task lingxia:default@CreateHarModuleInfo pre-execution cost: 1 ms .
[2025-07-31T19:05:15.326] [DEBUG] debug-file - lingxia : default@CreateHarModuleInfo cost memory 0.0577392578125
[2025-07-31T19:05:15.326] [INFO] debug-file - UP-TO-DATE :lingxia:default@CreateHarModuleInfo...  
[2025-07-31T19:05:15.327] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:15.327] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:15.327] [DEBUG] debug-file - Executing task :lingxia:default@ConfigureCmake
[2025-07-31T19:05:15.328] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:15.328] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:15.328] [DEBUG] debug-file - lingxia : default@ConfigureCmake cost memory 0.03815460205078125
[2025-07-31T19:05:15.328] [DEBUG] debug-file - runTaskFromQueue task cost before running: 252 ms 
[2025-07-31T19:05:15.328] [INFO] debug-file - Finished :lingxia:default@ConfigureCmake... after 1 ms 
[2025-07-31T19:05:15.330] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:15.330] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:15.330] [DEBUG] debug-file - Executing task :lingxia:default@MergeProfile
[2025-07-31T19:05:15.331] [DEBUG] debug-file - Incremental task lingxia:default@MergeProfile pre-execution cost: 1 ms .
[2025-07-31T19:05:15.332] [DEBUG] debug-file - lingxia : default@MergeProfile cost memory 0.0853118896484375
[2025-07-31T19:05:15.332] [INFO] debug-file - UP-TO-DATE :lingxia:default@MergeProfile...  
[2025-07-31T19:05:15.334] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:15.334] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:15.337] [DEBUG] debug-file - Executing task :lingxia:default@GeneratePkgContextInfo
[2025-07-31T19:05:15.338] [DEBUG] debug-file - Task 'lingxia:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms 
[2025-07-31T19:05:15.338] [DEBUG] debug-file - Incremental task lingxia:default@GeneratePkgContextInfo pre-execution cost: 1 ms .
[2025-07-31T19:05:15.338] [DEBUG] debug-file - lingxia : default@GeneratePkgContextInfo cost memory 0.0574493408203125
[2025-07-31T19:05:15.338] [INFO] debug-file - UP-TO-DATE :lingxia:default@GeneratePkgContextInfo...  
[2025-07-31T19:05:15.339] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:15.340] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:15.340] [DEBUG] debug-file - Executing task :lingxia:default@ProcessIntegratedHsp
[2025-07-31T19:05:15.342] [DEBUG] debug-file - lingxia:default@ProcessIntegratedHsp is not up-to-date, since the output file '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/build/cache/default/integrated_hsp/integratedHspCache.json' does not exist.
[2025-07-31T19:05:15.342] [DEBUG] debug-file - Incremental task lingxia:default@ProcessIntegratedHsp pre-execution cost: 1 ms .
[2025-07-31T19:05:15.342] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:15.342] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:15.342] [DEBUG] debug-file - lingxia : default@ProcessIntegratedHsp cost memory 0.10840606689453125
[2025-07-31T19:05:15.342] [DEBUG] debug-file - runTaskFromQueue task cost before running: 266 ms 
[2025-07-31T19:05:15.342] [INFO] debug-file - Finished :lingxia:default@ProcessIntegratedHsp... after 2 ms 
[2025-07-31T19:05:15.343] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:15.343] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:15.344] [DEBUG] debug-file - Executing task :lingxia:default@BuildNativeWithCmake
[2025-07-31T19:05:15.344] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:15.344] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:15.344] [DEBUG] debug-file - lingxia : default@BuildNativeWithCmake cost memory 0.03818511962890625
[2025-07-31T19:05:15.344] [DEBUG] debug-file - runTaskFromQueue task cost before running: 268 ms 
[2025-07-31T19:05:15.344] [INFO] debug-file - Finished :lingxia:default@BuildNativeWithCmake... after 1 ms 
[2025-07-31T19:05:15.345] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:15.345] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:15.346] [DEBUG] debug-file - Executing task :lingxia:default@ProcessProfile
[2025-07-31T19:05:15.347] [DEBUG] debug-file - Incremental task lingxia:default@ProcessProfile pre-execution cost: 1 ms .
[2025-07-31T19:05:15.347] [DEBUG] debug-file - lingxia : default@ProcessProfile cost memory 0.07149505615234375
[2025-07-31T19:05:15.347] [INFO] debug-file - UP-TO-DATE :lingxia:default@ProcessProfile...  
[2025-07-31T19:05:15.349] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:15.349] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:15.351] [DEBUG] debug-file - Executing task :lingxia:default@ProcessRouterMap
[2025-07-31T19:05:15.353] [DEBUG] debug-file - lingxia:default@ProcessRouterMap is not up-to-date, since the output file '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/router_map/default/temp-router-map.json' does not exist.
[2025-07-31T19:05:15.353] [DEBUG] debug-file - Incremental task lingxia:default@ProcessRouterMap pre-execution cost: 2 ms .
[2025-07-31T19:05:15.353] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:15.353] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:15.353] [DEBUG] debug-file - lingxia : default@ProcessRouterMap cost memory 0.19553375244140625
[2025-07-31T19:05:15.353] [DEBUG] debug-file - runTaskFromQueue task cost before running: 277 ms 
[2025-07-31T19:05:15.355] [INFO] debug-file - Finished :lingxia:default@ProcessRouterMap... after 3 ms 
[2025-07-31T19:05:15.357] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:15.357] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:15.357] [DEBUG] debug-file - Executing task :lingxia:default@BuildNativeWithNinja
[2025-07-31T19:05:15.358] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:15.358] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:15.358] [DEBUG] debug-file - lingxia : default@BuildNativeWithNinja cost memory 0.05321502685546875
[2025-07-31T19:05:15.358] [DEBUG] debug-file - runTaskFromQueue task cost before running: 282 ms 
[2025-07-31T19:05:15.359] [INFO] debug-file - Finished :lingxia:default@BuildNativeWithNinja... after 1 ms 
[2025-07-31T19:05:15.360] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:15.360] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:15.360] [DEBUG] debug-file - restool module names: lingxia; moduleName=lingxia, taskName=default@ProcessResource
[2025-07-31T19:05:15.361] [DEBUG] debug-file - Executing task :lingxia:default@ProcessResource
[2025-07-31T19:05:15.362] [DEBUG] debug-file - Incremental task lingxia:default@ProcessResource pre-execution cost: 1 ms .
[2025-07-31T19:05:15.362] [DEBUG] debug-file - lingxia : default@ProcessResource cost memory 0.12537384033203125
[2025-07-31T19:05:15.364] [INFO] debug-file - UP-TO-DATE :lingxia:default@ProcessResource...  
[2025-07-31T19:05:15.366] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:15.367] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:15.370] [DEBUG] debug-file - Executing task :lingxia:default@GenerateLoaderJson
[2025-07-31T19:05:15.378] [DEBUG] debug-file - Incremental task lingxia:default@GenerateLoaderJson pre-execution cost: 7 ms .
[2025-07-31T19:05:15.379] [DEBUG] debug-file - lingxia : default@GenerateLoaderJson cost memory -5.288841247558594
[2025-07-31T19:05:15.380] [INFO] debug-file - UP-TO-DATE :lingxia:default@GenerateLoaderJson...  
[2025-07-31T19:05:15.382] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:15.382] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:15.382] [DEBUG] debug-file - Executing task :lingxia:default@ProcessLibs
[2025-07-31T19:05:15.385] [DEBUG] debug-file - Incremental task lingxia:default@ProcessLibs pre-execution cost: 1 ms .
[2025-07-31T19:05:15.385] [DEBUG] debug-file - lingxia : default@ProcessLibs cost memory 0.10610198974609375
[2025-07-31T19:05:15.385] [INFO] debug-file - UP-TO-DATE :lingxia:default@ProcessLibs...  
[2025-07-31T19:05:15.387] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:15.387] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:15.388] [DEBUG] debug-file - restool module names: lingxia; moduleName=lingxia, taskName=default@CompileResource
[2025-07-31T19:05:15.389] [DEBUG] debug-file - Executing task :lingxia:default@CompileResource
[2025-07-31T19:05:15.391] [DEBUG] debug-file - lingxia:default@CompileResource is not up-to-date, since the output file '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/generated/r/default/ResourceTable.h' does not exist.
[2025-07-31T19:05:15.391] [DEBUG] debug-file - Incremental task lingxia:default@CompileResource pre-execution cost: 2 ms .
[2025-07-31T19:05:15.391] [DEBUG] debug-file - lingxia : default@CompileResource cost memory 0.12905120849609375
[2025-07-31T19:05:15.391] [DEBUG] debug-file - runTaskFromQueue task cost before running: 315 ms 
[2025-07-31T19:05:15.391] [INFO] debug-file - Finished :lingxia:default@CompileResource... after 2 ms 
[2025-07-31T19:05:15.392] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:15.392] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:15.394] [DEBUG] debug-file - Executing task :lingxia:default@DoNativeStrip
[2025-07-31T19:05:15.394] [DEBUG] debug-file - Task 'lingxia:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms 
[2025-07-31T19:05:15.394] [DEBUG] debug-file - Incremental task lingxia:default@DoNativeStrip pre-execution cost: 1 ms .
[2025-07-31T19:05:15.394] [DEBUG] debug-file - lingxia : default@DoNativeStrip cost memory 0.06316375732421875
[2025-07-31T19:05:15.394] [INFO] debug-file - UP-TO-DATE :lingxia:default@DoNativeStrip...  
[2025-07-31T19:05:15.396] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:15.396] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:15.397] [DEBUG] debug-file - Executing task :lingxia:default@ProcessObfuscationFiles
[2025-07-31T19:05:15.397] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:15.397] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:15.397] [DEBUG] debug-file - lingxia : default@ProcessObfuscationFiles cost memory 0.03820037841796875
[2025-07-31T19:05:15.397] [DEBUG] debug-file - runTaskFromQueue task cost before running: 321 ms 
[2025-07-31T19:05:15.397] [INFO] debug-file - Finished :lingxia:default@ProcessObfuscationFiles... after 1 ms 
[2025-07-31T19:05:15.399] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:15.399] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:15.403] [DEBUG] debug-file - Executing task :lingxia:default@HarCompileArkTS
[2025-07-31T19:05:15.411] [DEBUG] debug-file - Incremental task lingxia:default@HarCompileArkTS pre-execution cost: 5 ms .
[2025-07-31T19:05:15.411] [DEBUG] debug-file - lingxia : default@HarCompileArkTS cost memory 0.40203857421875
[2025-07-31T19:05:15.413] [INFO] debug-file - UP-TO-DATE :lingxia:default@HarCompileArkTS...  
[2025-07-31T19:05:15.414] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:15.414] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:15.416] [DEBUG] debug-file - Executing task :lingxia:default@CacheNativeLibs
[2025-07-31T19:05:15.416] [DEBUG] debug-file - Task 'lingxia:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms 
[2025-07-31T19:05:15.418] [DEBUG] debug-file - Incremental task lingxia:default@CacheNativeLibs pre-execution cost: 2 ms .
[2025-07-31T19:05:15.419] [DEBUG] debug-file - lingxia : default@CacheNativeLibs cost memory 0.070648193359375
[2025-07-31T19:05:15.419] [INFO] debug-file - UP-TO-DATE :lingxia:default@CacheNativeLibs...  
[2025-07-31T19:05:15.421] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:15.421] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:15.426] [DEBUG] debug-file - Executing task :lingxia:default@ProcessHarArtifacts
[2025-07-31T19:05:15.430] [DEBUG] debug-file - lingxia:default@ProcessHarArtifacts is not up-to-date, since the input file '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/generated/pm/default' has been changed.
[2025-07-31T19:05:15.430] [DEBUG] debug-file - Incremental task lingxia:default@ProcessHarArtifacts pre-execution cost: 3 ms .
[2025-07-31T19:05:15.430] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:15.430] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:15.489] [DEBUG] debug-file - lingxia : default@ProcessHarArtifacts cost memory 2.2680816650390625
[2025-07-31T19:05:15.489] [DEBUG] debug-file - runTaskFromQueue task cost before running: 413 ms 
[2025-07-31T19:05:15.491] [INFO] debug-file - Finished :lingxia:default@ProcessHarArtifacts... after 64 ms 
[2025-07-31T19:05:15.493] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:15.493] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:15.494] [DEBUG] debug-file - Executing task :lingxia:default@PackageHar
[2025-07-31T19:05:15.496] [DEBUG] debug-file - lingxia:default@PackageHar is not up-to-date, since the output file '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/cache/default/default@PackageHar' has been changed.
[2025-07-31T19:05:15.497] [DEBUG] debug-file - Incremental task lingxia:default@PackageHar pre-execution cost: 2 ms .
[2025-07-31T19:05:15.497] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:15.497] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:15.535] [DEBUG] debug-file - lingxia : default@PackageHar cost memory -26.80823516845703
[2025-07-31T19:05:15.535] [DEBUG] debug-file - runTaskFromQueue task cost before running: 459 ms 
[2025-07-31T19:05:15.536] [INFO] debug-file - Finished :lingxia:default@PackageHar... after 41 ms 
[2025-07-31T19:05:15.538] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:15.538] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:15.540] [DEBUG] debug-file - Executing task :lingxia:default@PackageSignHar
[2025-07-31T19:05:15.540] [WARN] debug-file - Will skip sign 'har'. No signingConfigs profile is configured in current project.
             If needed, configure the signingConfigs in /Users/<USER>/github/LingXia/lingxia-sdk/harmony/build-profile.json5.
[2025-07-31T19:05:15.541] [DEBUG] debug-file - lingxia : default@PackageSignHar cost memory 0.0486907958984375
[2025-07-31T19:05:15.541] [DEBUG] debug-file - runTaskFromQueue task cost before running: 465 ms 
[2025-07-31T19:05:15.541] [INFO] debug-file - Finished :lingxia:default@PackageSignHar... after 1 ms 
[2025-07-31T19:05:15.542] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:15.542] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:15.542] [DEBUG] debug-file - Executing task :lingxia:default@CollectDebugSymbol
[2025-07-31T19:05:15.545] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:15.545] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:15.545] [DEBUG] debug-file - lingxia : default@CollectDebugSymbol cost memory 0.20025634765625
[2025-07-31T19:05:15.545] [DEBUG] debug-file - runTaskFromQueue task cost before running: 469 ms 
[2025-07-31T19:05:15.545] [INFO] debug-file - Finished :lingxia:default@CollectDebugSymbol... after 3 ms 
[2025-07-31T19:05:15.546] [DEBUG] debug-file - Executing task :lingxia:assembleHar
[2025-07-31T19:05:15.546] [DEBUG] debug-file - lingxia : assembleHar cost memory 0.01175689697265625
[2025-07-31T19:05:15.546] [DEBUG] debug-file - runTaskFromQueue task cost before running: 470 ms 
[2025-07-31T19:05:15.546] [INFO] debug-file - Finished :lingxia:assembleHar... after 1 ms 
[2025-07-31T19:05:15.548] [DEBUG] debug-file - BUILD SUCCESSFUL in 471 ms 
[2025-07-31T19:05:15.548] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task lingxia:default@PreBuild is up-to-date.
[2025-07-31T19:05:15.548] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task lingxia:default@CreateHarBuildProfile is up-to-date.
[2025-07-31T19:05:15.548] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task lingxia:default@CreateHarModuleInfo is up-to-date.
[2025-07-31T19:05:15.548] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task lingxia:default@MergeProfile is up-to-date.
[2025-07-31T19:05:15.548] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task lingxia:default@GeneratePkgContextInfo is up-to-date.
[2025-07-31T19:05:15.548] [DEBUG] debug-file - Update task lingxia:default@ProcessIntegratedHsp output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/build/cache/default/integrated_hsp/integratedHspCache.json cache.
[2025-07-31T19:05:15.548] [DEBUG] debug-file - Incremental task lingxia:default@ProcessIntegratedHsp post-execution cost:1 ms .
[2025-07-31T19:05:15.548] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task lingxia:default@ProcessProfile is up-to-date.
[2025-07-31T19:05:15.549] [DEBUG] debug-file - Update task lingxia:default@ProcessRouterMap input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/oh-package.json5 cache by regenerate.
[2025-07-31T19:05:15.550] [DEBUG] debug-file - Update task lingxia:default@ProcessRouterMap input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/oh-package.json5 cache by regenerate.
[2025-07-31T19:05:15.550] [DEBUG] debug-file - Update task lingxia:default@ProcessRouterMap input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/module.json5 cache by regenerate.
[2025-07-31T19:05:15.550] [DEBUG] debug-file - Update task lingxia:default@ProcessRouterMap input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/loader/default/pkgContextInfo.json cache by regenerate.
[2025-07-31T19:05:15.550] [DEBUG] debug-file - Update task lingxia:default@ProcessRouterMap output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/router_map/default/temp-router-map.json cache.
[2025-07-31T19:05:15.550] [DEBUG] debug-file - Update task lingxia:default@ProcessRouterMap output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/router_map/default/loader-router-map.json cache.
[2025-07-31T19:05:15.551] [DEBUG] debug-file - Incremental task lingxia:default@ProcessRouterMap post-execution cost:2 ms .
[2025-07-31T19:05:15.551] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task lingxia:default@ProcessResource is up-to-date.
[2025-07-31T19:05:15.551] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task lingxia:default@GenerateLoaderJson is up-to-date.
[2025-07-31T19:05:15.551] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task lingxia:default@ProcessLibs is up-to-date.
[2025-07-31T19:05:15.552] [DEBUG] debug-file - Update task lingxia:default@CompileResource input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/process_profile/default/module.json cache by regenerate.
[2025-07-31T19:05:15.552] [DEBUG] debug-file - Update task lingxia:default@CompileResource input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/res/default/resConfig.json cache by regenerate.
[2025-07-31T19:05:15.552] [DEBUG] debug-file - Update task lingxia:default@CompileResource output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/res/default cache.
[2025-07-31T19:05:15.552] [DEBUG] debug-file - Update task lingxia:default@CompileResource output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/generated/r/default/ResourceTable.h cache.
[2025-07-31T19:05:15.552] [DEBUG] debug-file - Update task lingxia:default@CompileResource output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/generated/r/default cache.
[2025-07-31T19:05:15.553] [DEBUG] debug-file - Incremental task lingxia:default@CompileResource post-execution cost:2 ms .
[2025-07-31T19:05:15.553] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task lingxia:default@DoNativeStrip is up-to-date.
[2025-07-31T19:05:15.553] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task lingxia:default@HarCompileArkTS is up-to-date.
[2025-07-31T19:05:15.553] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task lingxia:default@CacheNativeLibs is up-to-date.
[2025-07-31T19:05:15.554] [DEBUG] debug-file - Update task lingxia:default@ProcessHarArtifacts input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/process_profile/default cache by regenerate.
[2025-07-31T19:05:15.554] [DEBUG] debug-file - Update task lingxia:default@ProcessHarArtifacts input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia cache by regenerate.
[2025-07-31T19:05:15.557] [DEBUG] debug-file - Update task lingxia:default@ProcessHarArtifacts input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/generated/pm/default cache from map.
[2025-07-31T19:05:15.557] [DEBUG] debug-file - Update task lingxia:default@ProcessHarArtifacts input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/stripped_native_libs/default cache by regenerate.
[2025-07-31T19:05:15.557] [DEBUG] debug-file - Update task lingxia:default@ProcessHarArtifacts input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/loader/default/pkgContextInfo.json cache by regenerate.
[2025-07-31T19:05:15.557] [DEBUG] debug-file - Update task lingxia:default@ProcessHarArtifacts output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/cache/default/default@PackageHar cache.
[2025-07-31T19:05:15.559] [DEBUG] debug-file - Incremental task lingxia:default@ProcessHarArtifacts post-execution cost:6 ms .
[2025-07-31T19:05:15.559] [DEBUG] debug-file - Update task lingxia:default@PackageHar output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/cache/default/default@PackageHar cache.
[2025-07-31T19:05:15.561] [DEBUG] debug-file - Update task lingxia:default@PackageHar output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/outputs/default cache.
[2025-07-31T19:05:15.561] [DEBUG] debug-file - Incremental task lingxia:default@PackageHar post-execution cost:3 ms .
[2025-07-31T19:05:15.561] [DEBUG] debug-file - Update task lingxia:default@PackageSignHar input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/cache/default/default@PackageHar cache by regenerate.
[2025-07-31T19:05:15.563] [DEBUG] debug-file - Update task lingxia:default@PackageSignHar input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/cache/default/default@PackageSignHar cache by regenerate.
[2025-07-31T19:05:15.563] [DEBUG] debug-file - Update task lingxia:default@PackageSignHar output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/outputs/default/lingxia-signed.har cache.
[2025-07-31T19:05:15.563] [DEBUG] debug-file - Incremental task lingxia:default@PackageSignHar post-execution cost:2 ms .
[2025-07-31T19:05:15.566] [DEBUG] debug-file - Update task lingxia:default@CollectDebugSymbol input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/loader_out/default/ets/sourceMaps.map cache by regenerate.
[2025-07-31T19:05:15.566] [DEBUG] debug-file - Update task lingxia:default@CollectDebugSymbol input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/libs/default cache by regenerate.
[2025-07-31T19:05:15.566] [DEBUG] debug-file - Update task lingxia:default@CollectDebugSymbol output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/outputs/default/symbol cache.
[2025-07-31T19:05:15.567] [DEBUG] debug-file - Incremental task lingxia:default@CollectDebugSymbol post-execution cost:4 ms .
[2025-07-31T19:05:15.574] [DEBUG] debug-file - hvigor build process will be closed.
[2025-07-31T19:05:15.639] [DEBUG] debug-file - session manager: send message to worker process.
[2025-07-31T19:05:15.640] [DEBUG] debug-file - session manager: send message to worker process.
[2025-07-31T19:05:15.640] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2025-07-31T19:05:15.640] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2025-07-31T19:05:47.076] [DEBUG] debug-file - env: nodejsVersion=v18.20.1
[2025-07-31T19:05:47.116] [DEBUG] debug-file - env: daemon=true
[2025-07-31T19:05:47.079] [DEBUG] debug-file - env: hvigor-config.json5 content = {
  modelVersion: '5.0.5',
  dependencies: {},
  execution: {},
  logging: {},
  debugging: {},
  nodeOptions: {}
}
[2025-07-31T19:05:47.794] [DEBUG] debug-file - session manager: set active socket. socketId=qC_CC4TlC_i7tQWuAAAH
[2025-07-31T19:05:47.797] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2025-07-31T19:05:47.810] [DEBUG] debug-file - Hvigor init with startParameters:{
  hvigorfileTypeCheck: false,
  parallelExecution: true,
  incrementalExecution: true,
  printStackTrace: false,
  daemon: true,
  analyze: 0,
  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }
}
[2025-07-31T19:05:47.815] [DEBUG] debug-file - Cache service initialization finished in 6 ms 
[2025-07-31T19:05:47.824] [DEBUG] debug-file - hvigorfile, resolving /Users/<USER>/github/LingXia/lingxia-sdk/harmony/hvigorfile.ts
[2025-07-31T19:05:47.830] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-07-31T19:05:47.830] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-07-31T19:05:47.836] [DEBUG] debug-file - Start initialize project's product build option map with build mode debug.
[2025-07-31T19:05:47.836] [DEBUG] debug-file - Picking option from product 'default' with build mode 'debug'.
[2025-07-31T19:05:47.836] [DEBUG] debug-file - Product 'default' build option: {}
[2025-07-31T19:05:47.836] [DEBUG] debug-file - End initialize project's product build option map with build mode 'debug'.
[2025-07-31T19:05:47.837] [DEBUG] debug-file - Product 'default' using build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  }
} in this build.
[2025-07-31T19:05:47.838] [DEBUG] debug-file - not found resModel json file in : /Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/ohosTest/module.json5
[2025-07-31T19:05:47.841] [DEBUG] debug-file - No signingConfig found, initRemoteHspCache failed.
[2025-07-31T19:05:47.848] [DEBUG] debug-file - Start recording SDK configuration permission data.
[2025-07-31T19:05:47.887] [DEBUG] debug-file - Sdk init in 45 ms 
[2025-07-31T19:05:47.898] [DEBUG] debug-file - Project task initialization takes 10 ms 
[2025-07-31T19:05:47.899] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-07-31T19:05:47.899] [DEBUG] debug-file - hvigorfile, no custom plugins were found in /Users/<USER>/github/LingXia/lingxia-sdk/harmony/hvigorfile.ts
[2025-07-31T19:05:47.899] [DEBUG] debug-file - hvigorfile, resolve finished /Users/<USER>/github/LingXia/lingxia-sdk/harmony/hvigorfile.ts
[2025-07-31T19:05:47.905] [DEBUG] debug-file - hvigorfile, resolving /Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/hvigorfile.ts
[2025-07-31T19:05:47.911] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-07-31T19:05:47.911] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-07-31T19:05:47.917] [DEBUG] debug-file - Start initialize module-target build option map, moduleName=lingxia, buildMode=debug
[2025-07-31T19:05:47.918] [DEBUG] debug-file - Target 'default' config: {}
[2025-07-31T19:05:47.919] [DEBUG] debug-file - Target 'ohosTest' config: {}
[2025-07-31T19:05:47.920] [DEBUG] debug-file - Module 'lingxia' target 'default' build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  }
}
[2025-07-31T19:05:47.920] [DEBUG] debug-file - Module 'lingxia' target 'ohosTest' build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  }
}
[2025-07-31T19:05:47.920] [DEBUG] debug-file - End initialize module-target build option map, moduleName=lingxia
[2025-07-31T19:05:47.920] [DEBUG] debug-file - Module 'lingxia' target 'default' using build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  }
} in this build.
[2025-07-31T19:05:47.922] [DEBUG] debug-file - Module lingxia task initialization takes 1 ms 
[2025-07-31T19:05:47.922] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-07-31T19:05:47.922] [DEBUG] debug-file - hvigorfile, no custom plugins were found in /Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/hvigorfile.ts
[2025-07-31T19:05:47.922] [DEBUG] debug-file - hvigorfile, resolve finished /Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/hvigorfile.ts
[2025-07-31T19:05:47.995] [DEBUG] debug-file - hvigorfile, resolve hvigorfile dependencies in 72 ms 
[2025-07-31T19:05:47.996] [DEBUG] debug-file - project has submodules:lingxia
[2025-07-31T19:05:47.996] [DEBUG] debug-file - module:lingxia no need to execute packageHap
[2025-07-31T19:05:47.997] [DEBUG] debug-file - start to load updatedOhPackageInfo to the disk
[2025-07-31T19:05:47.999] [DEBUG] debug-file - load to the disk finished
[2025-07-31T19:05:48.000] [DEBUG] debug-file - Start to initialize dependency information.
[2025-07-31T19:05:48.000] [DEBUG] debug-file - Module harmony Collected Dependency: 
[2025-07-31T19:05:48.001] [DEBUG] debug-file - Module harmony's total dependency: 0
[2025-07-31T19:05:48.001] [DEBUG] debug-file - Start to initialize dependency information.
[2025-07-31T19:05:48.002] [DEBUG] debug-file - Module lingxia Collected Dependency: 
[2025-07-31T19:05:48.002] [DEBUG] debug-file - Module lingxia's total dependency: 0
[2025-07-31T19:05:48.005] [DEBUG] debug-file - Configuration phase cost:184 ms 
[2025-07-31T19:05:48.007] [DEBUG] debug-file - Configuration task cost before running: 208 ms 
[2025-07-31T19:05:48.008] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:48.008] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:48.010] [DEBUG] debug-file - Executing task :lingxia:default@PreBuild
[2025-07-31T19:05:48.013] [DEBUG] debug-file - Incremental task lingxia:default@PreBuild pre-execution cost: 2 ms .
[2025-07-31T19:05:48.013] [DEBUG] debug-file - lingxia : default@PreBuild cost memory 0.1641693115234375
[2025-07-31T19:05:48.014] [INFO] debug-file - UP-TO-DATE :lingxia:default@PreBuild...  
[2025-07-31T19:05:48.016] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:48.016] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:48.018] [DEBUG] debug-file - Executing task :lingxia:default@ProcessOHPackageJson
[2025-07-31T19:05:48.018] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:48.018] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:48.019] [DEBUG] debug-file - lingxia : default@ProcessOHPackageJson cost memory 0.04402923583984375
[2025-07-31T19:05:48.019] [DEBUG] debug-file - runTaskFromQueue task cost before running: 221 ms 
[2025-07-31T19:05:48.020] [INFO] debug-file - Finished :lingxia:default@ProcessOHPackageJson... after 2 ms 
[2025-07-31T19:05:48.021] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:48.021] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:48.021] [DEBUG] debug-file - Executing task :lingxia:default@CreateHarBuildProfile
[2025-07-31T19:05:48.022] [DEBUG] debug-file - Task 'lingxia:default@CreateHarBuildProfile' cost while wrapping incremental declarations: 1 ms 
[2025-07-31T19:05:48.022] [DEBUG] debug-file - Incremental task lingxia:default@CreateHarBuildProfile pre-execution cost: 1 ms .
[2025-07-31T19:05:48.022] [DEBUG] debug-file - lingxia : default@CreateHarBuildProfile cost memory 0.07572174072265625
[2025-07-31T19:05:48.022] [INFO] debug-file - UP-TO-DATE :lingxia:default@CreateHarBuildProfile...  
[2025-07-31T19:05:48.023] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:48.023] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:48.023] [DEBUG] debug-file - Executing task :lingxia:default@CreateHarModuleInfo
[2025-07-31T19:05:48.024] [DEBUG] debug-file - Task 'lingxia:default@CreateHarModuleInfo' cost while wrapping incremental declarations: 1 ms 
[2025-07-31T19:05:48.024] [DEBUG] debug-file - Incremental task lingxia:default@CreateHarModuleInfo pre-execution cost: 1 ms .
[2025-07-31T19:05:48.024] [DEBUG] debug-file - lingxia : default@CreateHarModuleInfo cost memory 0.05773162841796875
[2025-07-31T19:05:48.024] [INFO] debug-file - UP-TO-DATE :lingxia:default@CreateHarModuleInfo...  
[2025-07-31T19:05:48.025] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:48.025] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:48.025] [DEBUG] debug-file - Executing task :lingxia:default@ConfigureCmake
[2025-07-31T19:05:48.025] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:48.025] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:48.026] [DEBUG] debug-file - lingxia : default@ConfigureCmake cost memory 0.04523468017578125
[2025-07-31T19:05:48.026] [DEBUG] debug-file - runTaskFromQueue task cost before running: 227 ms 
[2025-07-31T19:05:48.026] [INFO] debug-file - Finished :lingxia:default@ConfigureCmake... after 1 ms 
[2025-07-31T19:05:48.027] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:48.027] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:48.027] [DEBUG] debug-file - Executing task :lingxia:default@MergeProfile
[2025-07-31T19:05:48.028] [DEBUG] debug-file - Incremental task lingxia:default@MergeProfile pre-execution cost: 1 ms .
[2025-07-31T19:05:48.028] [DEBUG] debug-file - lingxia : default@MergeProfile cost memory 0.09062957763671875
[2025-07-31T19:05:48.028] [INFO] debug-file - UP-TO-DATE :lingxia:default@MergeProfile...  
[2025-07-31T19:05:48.029] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:48.029] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:48.032] [DEBUG] debug-file - Executing task :lingxia:default@GeneratePkgContextInfo
[2025-07-31T19:05:48.032] [DEBUG] debug-file - Task 'lingxia:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms 
[2025-07-31T19:05:48.032] [DEBUG] debug-file - Incremental task lingxia:default@GeneratePkgContextInfo pre-execution cost: 1 ms .
[2025-07-31T19:05:48.032] [DEBUG] debug-file - lingxia : default@GeneratePkgContextInfo cost memory 0.05744171142578125
[2025-07-31T19:05:48.032] [INFO] debug-file - UP-TO-DATE :lingxia:default@GeneratePkgContextInfo...  
[2025-07-31T19:05:48.033] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:48.033] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:48.034] [DEBUG] debug-file - Executing task :lingxia:default@ProcessIntegratedHsp
[2025-07-31T19:05:48.036] [DEBUG] debug-file - lingxia:default@ProcessIntegratedHsp is not up-to-date, since the output file '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/build/cache/default/integrated_hsp/integratedHspCache.json' does not exist.
[2025-07-31T19:05:48.036] [DEBUG] debug-file - Incremental task lingxia:default@ProcessIntegratedHsp pre-execution cost: 1 ms .
[2025-07-31T19:05:48.036] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:48.036] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:48.036] [DEBUG] debug-file - lingxia : default@ProcessIntegratedHsp cost memory 0.108489990234375
[2025-07-31T19:05:48.036] [DEBUG] debug-file - runTaskFromQueue task cost before running: 238 ms 
[2025-07-31T19:05:48.037] [INFO] debug-file - Finished :lingxia:default@ProcessIntegratedHsp... after 2 ms 
[2025-07-31T19:05:48.038] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:48.038] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:48.039] [DEBUG] debug-file - Executing task :lingxia:default@BuildNativeWithCmake
[2025-07-31T19:05:48.039] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:48.039] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:48.039] [DEBUG] debug-file - lingxia : default@BuildNativeWithCmake cost memory 0.04337310791015625
[2025-07-31T19:05:48.039] [DEBUG] debug-file - runTaskFromQueue task cost before running: 240 ms 
[2025-07-31T19:05:48.039] [INFO] debug-file - Finished :lingxia:default@BuildNativeWithCmake... after 1 ms 
[2025-07-31T19:05:48.040] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:48.040] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:48.041] [DEBUG] debug-file - Executing task :lingxia:default@ProcessProfile
[2025-07-31T19:05:48.043] [DEBUG] debug-file - Incremental task lingxia:default@ProcessProfile pre-execution cost: 1 ms .
[2025-07-31T19:05:48.043] [DEBUG] debug-file - lingxia : default@ProcessProfile cost memory 0.076080322265625
[2025-07-31T19:05:48.043] [INFO] debug-file - UP-TO-DATE :lingxia:default@ProcessProfile...  
[2025-07-31T19:05:48.045] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:48.045] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:48.046] [DEBUG] debug-file - Executing task :lingxia:default@ProcessRouterMap
[2025-07-31T19:05:48.047] [DEBUG] debug-file - lingxia:default@ProcessRouterMap is not up-to-date, since the output file '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/router_map/default/temp-router-map.json' does not exist.
[2025-07-31T19:05:48.048] [DEBUG] debug-file - Incremental task lingxia:default@ProcessRouterMap pre-execution cost: 2 ms .
[2025-07-31T19:05:48.048] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:48.048] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:48.048] [DEBUG] debug-file - lingxia : default@ProcessRouterMap cost memory 0.19696807861328125
[2025-07-31T19:05:48.048] [DEBUG] debug-file - runTaskFromQueue task cost before running: 249 ms 
[2025-07-31T19:05:48.048] [INFO] debug-file - Finished :lingxia:default@ProcessRouterMap... after 2 ms 
[2025-07-31T19:05:48.049] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:48.049] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:48.050] [DEBUG] debug-file - Executing task :lingxia:default@BuildNativeWithNinja
[2025-07-31T19:05:48.050] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:48.050] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:48.050] [DEBUG] debug-file - lingxia : default@BuildNativeWithNinja cost memory 0.0529937744140625
[2025-07-31T19:05:48.050] [DEBUG] debug-file - runTaskFromQueue task cost before running: 252 ms 
[2025-07-31T19:05:48.051] [INFO] debug-file - Finished :lingxia:default@BuildNativeWithNinja... after 1 ms 
[2025-07-31T19:05:48.053] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:48.053] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:48.054] [DEBUG] debug-file - restool module names: lingxia; moduleName=lingxia, taskName=default@ProcessResource
[2025-07-31T19:05:48.056] [DEBUG] debug-file - Executing task :lingxia:default@ProcessResource
[2025-07-31T19:05:48.057] [DEBUG] debug-file - Incremental task lingxia:default@ProcessResource pre-execution cost: 2 ms .
[2025-07-31T19:05:48.058] [DEBUG] debug-file - lingxia : default@ProcessResource cost memory 0.12593841552734375
[2025-07-31T19:05:48.062] [INFO] debug-file - UP-TO-DATE :lingxia:default@ProcessResource...  
[2025-07-31T19:05:48.064] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:48.064] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:48.067] [DEBUG] debug-file - Executing task :lingxia:default@GenerateLoaderJson
[2025-07-31T19:05:48.075] [DEBUG] debug-file - Incremental task lingxia:default@GenerateLoaderJson pre-execution cost: 5 ms .
[2025-07-31T19:05:48.076] [DEBUG] debug-file - lingxia : default@GenerateLoaderJson cost memory 0.5122756958007812
[2025-07-31T19:05:48.077] [INFO] debug-file - UP-TO-DATE :lingxia:default@GenerateLoaderJson...  
[2025-07-31T19:05:48.078] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:48.078] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:48.078] [DEBUG] debug-file - Executing task :lingxia:default@ProcessLibs
[2025-07-31T19:05:48.079] [DEBUG] debug-file - Incremental task lingxia:default@ProcessLibs pre-execution cost: 1 ms .
[2025-07-31T19:05:48.080] [DEBUG] debug-file - lingxia : default@ProcessLibs cost memory 0.10550689697265625
[2025-07-31T19:05:48.080] [INFO] debug-file - UP-TO-DATE :lingxia:default@ProcessLibs...  
[2025-07-31T19:05:48.081] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:48.081] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:48.081] [DEBUG] debug-file - restool module names: lingxia; moduleName=lingxia, taskName=default@CompileResource
[2025-07-31T19:05:48.082] [DEBUG] debug-file - Executing task :lingxia:default@CompileResource
[2025-07-31T19:05:48.083] [DEBUG] debug-file - lingxia:default@CompileResource is not up-to-date, since the output file '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/generated/r/default/ResourceTable.h' does not exist.
[2025-07-31T19:05:48.083] [DEBUG] debug-file - Incremental task lingxia:default@CompileResource pre-execution cost: 1 ms .
[2025-07-31T19:05:48.083] [DEBUG] debug-file - lingxia : default@CompileResource cost memory 0.131500244140625
[2025-07-31T19:05:48.084] [DEBUG] debug-file - runTaskFromQueue task cost before running: 285 ms 
[2025-07-31T19:05:48.084] [INFO] debug-file - Finished :lingxia:default@CompileResource... after 2 ms 
[2025-07-31T19:05:48.085] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:48.086] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:48.087] [DEBUG] debug-file - Executing task :lingxia:default@DoNativeStrip
[2025-07-31T19:05:48.087] [DEBUG] debug-file - Task 'lingxia:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms 
[2025-07-31T19:05:48.088] [DEBUG] debug-file - Incremental task lingxia:default@DoNativeStrip pre-execution cost: 1 ms .
[2025-07-31T19:05:48.088] [DEBUG] debug-file - lingxia : default@DoNativeStrip cost memory 0.0651092529296875
[2025-07-31T19:05:48.088] [INFO] debug-file - UP-TO-DATE :lingxia:default@DoNativeStrip...  
[2025-07-31T19:05:48.089] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:48.089] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:48.090] [DEBUG] debug-file - Executing task :lingxia:default@ProcessObfuscationFiles
[2025-07-31T19:05:48.090] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:48.090] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:48.090] [DEBUG] debug-file - lingxia : default@ProcessObfuscationFiles cost memory 0.03820037841796875
[2025-07-31T19:05:48.090] [DEBUG] debug-file - runTaskFromQueue task cost before running: 291 ms 
[2025-07-31T19:05:48.090] [INFO] debug-file - Finished :lingxia:default@ProcessObfuscationFiles... after 1 ms 
[2025-07-31T19:05:48.091] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:48.091] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:48.093] [DEBUG] debug-file - Executing task :lingxia:default@HarCompileArkTS
[2025-07-31T19:05:48.097] [DEBUG] debug-file - Incremental task lingxia:default@HarCompileArkTS pre-execution cost: 3 ms .
[2025-07-31T19:05:48.097] [DEBUG] debug-file - lingxia : default@HarCompileArkTS cost memory 0.40662384033203125
[2025-07-31T19:05:48.098] [INFO] debug-file - UP-TO-DATE :lingxia:default@HarCompileArkTS...  
[2025-07-31T19:05:48.100] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:48.100] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:48.101] [DEBUG] debug-file - Executing task :lingxia:default@CacheNativeLibs
[2025-07-31T19:05:48.101] [DEBUG] debug-file - Task 'lingxia:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms 
[2025-07-31T19:05:48.103] [DEBUG] debug-file - Incremental task lingxia:default@CacheNativeLibs pre-execution cost: 1 ms .
[2025-07-31T19:05:48.103] [DEBUG] debug-file - lingxia : default@CacheNativeLibs cost memory 0.0696563720703125
[2025-07-31T19:05:48.103] [INFO] debug-file - UP-TO-DATE :lingxia:default@CacheNativeLibs...  
[2025-07-31T19:05:48.105] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:48.105] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:48.109] [DEBUG] debug-file - Executing task :lingxia:default@ProcessHarArtifacts
[2025-07-31T19:05:48.115] [DEBUG] debug-file - lingxia:default@ProcessHarArtifacts is not up-to-date, since the input file '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/generated/pm/default' has been changed.
[2025-07-31T19:05:48.115] [DEBUG] debug-file - Incremental task lingxia:default@ProcessHarArtifacts pre-execution cost: 4 ms .
[2025-07-31T19:05:48.115] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:48.115] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:48.175] [DEBUG] debug-file - lingxia : default@ProcessHarArtifacts cost memory -2.2244796752929688
[2025-07-31T19:05:48.175] [DEBUG] debug-file - runTaskFromQueue task cost before running: 377 ms 
[2025-07-31T19:05:48.176] [INFO] debug-file - Finished :lingxia:default@ProcessHarArtifacts... after 66 ms 
[2025-07-31T19:05:48.178] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:48.178] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:48.180] [DEBUG] debug-file - Executing task :lingxia:default@PackageHar
[2025-07-31T19:05:48.183] [DEBUG] debug-file - lingxia:default@PackageHar is not up-to-date, since the output file '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/cache/default/default@PackageHar' has been changed.
[2025-07-31T19:05:48.183] [DEBUG] debug-file - Incremental task lingxia:default@PackageHar pre-execution cost: 3 ms .
[2025-07-31T19:05:48.183] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:48.183] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:48.222] [DEBUG] debug-file - lingxia : default@PackageHar cost memory -13.406349182128906
[2025-07-31T19:05:48.222] [DEBUG] debug-file - runTaskFromQueue task cost before running: 423 ms 
[2025-07-31T19:05:48.222] [INFO] debug-file - Finished :lingxia:default@PackageHar... after 42 ms 
[2025-07-31T19:05:48.223] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:48.223] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:48.225] [DEBUG] debug-file - Executing task :lingxia:default@PackageSignHar
[2025-07-31T19:05:48.225] [WARN] debug-file - Will skip sign 'har'. No signingConfigs profile is configured in current project.
             If needed, configure the signingConfigs in /Users/<USER>/github/LingXia/lingxia-sdk/harmony/build-profile.json5.
[2025-07-31T19:05:48.225] [DEBUG] debug-file - lingxia : default@PackageSignHar cost memory 0.04876708984375
[2025-07-31T19:05:48.225] [DEBUG] debug-file - runTaskFromQueue task cost before running: 427 ms 
[2025-07-31T19:05:48.226] [INFO] debug-file - Finished :lingxia:default@PackageSignHar... after 1 ms 
[2025-07-31T19:05:48.227] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:48.227] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:48.227] [DEBUG] debug-file - Executing task :lingxia:default@CollectDebugSymbol
[2025-07-31T19:05:48.229] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T19:05:48.229] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T19:05:48.230] [DEBUG] debug-file - lingxia : default@CollectDebugSymbol cost memory 0.2206268310546875
[2025-07-31T19:05:48.230] [DEBUG] debug-file - runTaskFromQueue task cost before running: 431 ms 
[2025-07-31T19:05:48.230] [INFO] debug-file - Finished :lingxia:default@CollectDebugSymbol... after 3 ms 
[2025-07-31T19:05:48.231] [DEBUG] debug-file - Executing task :lingxia:assembleHar
[2025-07-31T19:05:48.231] [DEBUG] debug-file - lingxia : assembleHar cost memory 0.01175689697265625
[2025-07-31T19:05:48.231] [DEBUG] debug-file - runTaskFromQueue task cost before running: 432 ms 
[2025-07-31T19:05:48.231] [INFO] debug-file - Finished :lingxia:assembleHar... after 1 ms 
[2025-07-31T19:05:48.232] [DEBUG] debug-file - BUILD SUCCESSFUL in 433 ms 
[2025-07-31T19:05:48.232] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task lingxia:default@PreBuild is up-to-date.
[2025-07-31T19:05:48.232] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task lingxia:default@CreateHarBuildProfile is up-to-date.
[2025-07-31T19:05:48.232] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task lingxia:default@CreateHarModuleInfo is up-to-date.
[2025-07-31T19:05:48.232] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task lingxia:default@MergeProfile is up-to-date.
[2025-07-31T19:05:48.232] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task lingxia:default@GeneratePkgContextInfo is up-to-date.
[2025-07-31T19:05:48.233] [DEBUG] debug-file - Update task lingxia:default@ProcessIntegratedHsp output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/build/cache/default/integrated_hsp/integratedHspCache.json cache.
[2025-07-31T19:05:48.233] [DEBUG] debug-file - Incremental task lingxia:default@ProcessIntegratedHsp post-execution cost:1 ms .
[2025-07-31T19:05:48.233] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task lingxia:default@ProcessProfile is up-to-date.
[2025-07-31T19:05:48.234] [DEBUG] debug-file - Update task lingxia:default@ProcessRouterMap input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/oh-package.json5 cache by regenerate.
[2025-07-31T19:05:48.234] [DEBUG] debug-file - Update task lingxia:default@ProcessRouterMap input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/oh-package.json5 cache by regenerate.
[2025-07-31T19:05:48.234] [DEBUG] debug-file - Update task lingxia:default@ProcessRouterMap input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/module.json5 cache by regenerate.
[2025-07-31T19:05:48.234] [DEBUG] debug-file - Update task lingxia:default@ProcessRouterMap input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/loader/default/pkgContextInfo.json cache by regenerate.
[2025-07-31T19:05:48.234] [DEBUG] debug-file - Update task lingxia:default@ProcessRouterMap output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/router_map/default/temp-router-map.json cache.
[2025-07-31T19:05:48.234] [DEBUG] debug-file - Update task lingxia:default@ProcessRouterMap output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/router_map/default/loader-router-map.json cache.
[2025-07-31T19:05:48.234] [DEBUG] debug-file - Incremental task lingxia:default@ProcessRouterMap post-execution cost:1 ms .
[2025-07-31T19:05:48.234] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task lingxia:default@ProcessResource is up-to-date.
[2025-07-31T19:05:48.234] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task lingxia:default@GenerateLoaderJson is up-to-date.
[2025-07-31T19:05:48.234] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task lingxia:default@ProcessLibs is up-to-date.
[2025-07-31T19:05:48.234] [DEBUG] debug-file - Update task lingxia:default@CompileResource input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/process_profile/default/module.json cache by regenerate.
[2025-07-31T19:05:48.235] [DEBUG] debug-file - Update task lingxia:default@CompileResource input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/res/default/resConfig.json cache by regenerate.
[2025-07-31T19:05:48.235] [DEBUG] debug-file - Update task lingxia:default@CompileResource output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/res/default cache.
[2025-07-31T19:05:48.236] [DEBUG] debug-file - Update task lingxia:default@CompileResource output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/generated/r/default/ResourceTable.h cache.
[2025-07-31T19:05:48.236] [DEBUG] debug-file - Update task lingxia:default@CompileResource output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/generated/r/default cache.
[2025-07-31T19:05:48.236] [DEBUG] debug-file - Incremental task lingxia:default@CompileResource post-execution cost:2 ms .
[2025-07-31T19:05:48.236] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task lingxia:default@DoNativeStrip is up-to-date.
[2025-07-31T19:05:48.236] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task lingxia:default@HarCompileArkTS is up-to-date.
[2025-07-31T19:05:48.236] [DEBUG] debug-file - There is no need to refresh cache, since the incremental task lingxia:default@CacheNativeLibs is up-to-date.
[2025-07-31T19:05:48.237] [DEBUG] debug-file - Update task lingxia:default@ProcessHarArtifacts input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/process_profile/default cache by regenerate.
[2025-07-31T19:05:48.238] [DEBUG] debug-file - Update task lingxia:default@ProcessHarArtifacts input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia cache by regenerate.
[2025-07-31T19:05:48.238] [DEBUG] debug-file - Update task lingxia:default@ProcessHarArtifacts input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/generated/pm/default cache from map.
[2025-07-31T19:05:48.238] [DEBUG] debug-file - Update task lingxia:default@ProcessHarArtifacts input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/stripped_native_libs/default cache by regenerate.
[2025-07-31T19:05:48.238] [DEBUG] debug-file - Update task lingxia:default@ProcessHarArtifacts input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/loader/default/pkgContextInfo.json cache by regenerate.
[2025-07-31T19:05:48.239] [DEBUG] debug-file - Update task lingxia:default@ProcessHarArtifacts output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/cache/default/default@PackageHar cache.
[2025-07-31T19:05:48.239] [DEBUG] debug-file - Incremental task lingxia:default@ProcessHarArtifacts post-execution cost:3 ms .
[2025-07-31T19:05:48.239] [DEBUG] debug-file - Update task lingxia:default@PackageHar output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/cache/default/default@PackageHar cache.
[2025-07-31T19:05:48.240] [DEBUG] debug-file - Update task lingxia:default@PackageHar output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/outputs/default cache.
[2025-07-31T19:05:48.240] [DEBUG] debug-file - Incremental task lingxia:default@PackageHar post-execution cost:1 ms .
[2025-07-31T19:05:48.240] [DEBUG] debug-file - Update task lingxia:default@PackageSignHar input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/cache/default/default@PackageHar cache by regenerate.
[2025-07-31T19:05:48.241] [DEBUG] debug-file - Update task lingxia:default@PackageSignHar input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/cache/default/default@PackageSignHar cache by regenerate.
[2025-07-31T19:05:48.241] [DEBUG] debug-file - Update task lingxia:default@PackageSignHar output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/outputs/default/lingxia-signed.har cache.
[2025-07-31T19:05:48.241] [DEBUG] debug-file - Incremental task lingxia:default@PackageSignHar post-execution cost:1 ms .
[2025-07-31T19:05:48.242] [DEBUG] debug-file - Update task lingxia:default@CollectDebugSymbol input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/loader_out/default/ets/sourceMaps.map cache by regenerate.
[2025-07-31T19:05:48.243] [DEBUG] debug-file - Update task lingxia:default@CollectDebugSymbol input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/libs/default cache by regenerate.
[2025-07-31T19:05:48.243] [DEBUG] debug-file - Update task lingxia:default@CollectDebugSymbol output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/outputs/default/symbol cache.
[2025-07-31T19:05:48.243] [DEBUG] debug-file - Incremental task lingxia:default@CollectDebugSymbol post-execution cost:2 ms .
[2025-07-31T19:05:48.251] [DEBUG] debug-file - hvigor build process will be closed.
[2025-07-31T19:05:48.317] [DEBUG] debug-file - session manager: send message to worker process.
[2025-07-31T19:05:48.318] [DEBUG] debug-file - session manager: send message to worker process.
[2025-07-31T19:05:48.319] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2025-07-31T19:05:48.319] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2025-07-31T21:44:17.557] [DEBUG] debug-file - env: nodejsVersion=v18.20.1
[2025-07-31T21:44:17.603] [DEBUG] debug-file - env: daemon=true
[2025-07-31T21:44:17.560] [DEBUG] debug-file - env: hvigor-config.json5 content = {
  modelVersion: '5.0.5',
  dependencies: {},
  execution: {},
  logging: {},
  debugging: {},
  nodeOptions: {}
}
[2025-07-31T21:44:18.513] [DEBUG] debug-file - session manager: set active socket. socketId=yosnGEcLyjm9y-KeAAAJ
[2025-07-31T21:44:18.576] [DEBUG] debug-file - watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.
[2025-07-31T21:44:18.686] [DEBUG] debug-file - Hvigor init with startParameters:{
  hvigorfileTypeCheck: false,
  parallelExecution: true,
  incrementalExecution: true,
  printStackTrace: false,
  daemon: true,
  analyze: 0,
  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }
}
[2025-07-31T21:44:18.712] [DEBUG] debug-file - Cache service initialization finished in 25 ms 
[2025-07-31T21:44:18.733] [DEBUG] debug-file - hvigorfile, resolving /Users/<USER>/github/LingXia/lingxia-sdk/harmony/hvigorfile.ts
[2025-07-31T21:44:18.850] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-07-31T21:44:18.850] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-07-31T21:44:18.880] [DEBUG] debug-file - Start initialize project's product build option map with build mode debug.
[2025-07-31T21:44:18.881] [DEBUG] debug-file - Picking option from product 'default' with build mode 'debug'.
[2025-07-31T21:44:18.881] [DEBUG] debug-file - Product 'default' build option: {}
[2025-07-31T21:44:18.881] [DEBUG] debug-file - End initialize project's product build option map with build mode 'debug'.
[2025-07-31T21:44:18.901] [DEBUG] debug-file - Product 'default' using build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  }
} in this build.
[2025-07-31T21:44:18.903] [DEBUG] debug-file - not found resModel json file in : /Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/ohosTest/module.json5
[2025-07-31T21:44:18.935] [DEBUG] debug-file - No signingConfig found, initRemoteHspCache failed.
[2025-07-31T21:44:18.955] [DEBUG] debug-file - Start recording SDK configuration permission data.
[2025-07-31T21:44:19.014] [DEBUG] debug-file - Sdk init in 77 ms 
[2025-07-31T21:44:19.036] [DEBUG] debug-file - Project task initialization takes 20 ms 
[2025-07-31T21:44:19.036] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-07-31T21:44:19.036] [DEBUG] debug-file - hvigorfile, no custom plugins were found in /Users/<USER>/github/LingXia/lingxia-sdk/harmony/hvigorfile.ts
[2025-07-31T21:44:19.036] [DEBUG] debug-file - hvigorfile, resolve finished /Users/<USER>/github/LingXia/lingxia-sdk/harmony/hvigorfile.ts
[2025-07-31T21:44:19.043] [DEBUG] debug-file - hvigorfile, resolving /Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/hvigorfile.ts
[2025-07-31T21:44:19.053] [DEBUG] debug-file - hvigorfile, require result:  { default: { plugins: [] } }
[2025-07-31T21:44:19.055] [DEBUG] debug-file - hvigorfile, binding system plugins null
[2025-07-31T21:44:19.068] [DEBUG] debug-file - Start initialize module-target build option map, moduleName=lingxia, buildMode=debug
[2025-07-31T21:44:19.068] [DEBUG] debug-file - Target 'default' config: {}
[2025-07-31T21:44:19.070] [DEBUG] debug-file - Target 'ohosTest' config: {}
[2025-07-31T21:44:19.071] [DEBUG] debug-file - Module 'lingxia' target 'default' build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  }
}
[2025-07-31T21:44:19.071] [DEBUG] debug-file - Module 'lingxia' target 'ohosTest' build option: {
  "debuggable": true,
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  }
}
[2025-07-31T21:44:19.071] [DEBUG] debug-file - End initialize module-target build option map, moduleName=lingxia
[2025-07-31T21:44:19.072] [DEBUG] debug-file - Module 'lingxia' target 'default' using build option: {
  "debuggable": true,
  "copyFrom": "default",
  "strictMode": {
    "caseSensitiveCheck": true,
    "useNormalizedOHMUrl": true
  }
} in this build.
[2025-07-31T21:44:19.084] [DEBUG] debug-file - Module lingxia task initialization takes 6 ms 
[2025-07-31T21:44:19.085] [DEBUG] debug-file - hvigorfile, binding custom plugins []
[2025-07-31T21:44:19.085] [DEBUG] debug-file - hvigorfile, no custom plugins were found in /Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/hvigorfile.ts
[2025-07-31T21:44:19.085] [DEBUG] debug-file - hvigorfile, resolve finished /Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/hvigorfile.ts
[2025-07-31T21:44:19.271] [DEBUG] debug-file - hvigorfile, resolve hvigorfile dependencies in 185 ms 
[2025-07-31T21:44:19.273] [DEBUG] debug-file - project has submodules:lingxia
[2025-07-31T21:44:19.273] [DEBUG] debug-file - module:lingxia no need to execute packageHap
[2025-07-31T21:44:19.275] [DEBUG] debug-file - start to load updatedOhPackageInfo to the disk
[2025-07-31T21:44:19.282] [DEBUG] debug-file - load to the disk finished
[2025-07-31T21:44:19.283] [DEBUG] debug-file - Start to initialize dependency information.
[2025-07-31T21:44:19.285] [DEBUG] debug-file - Module harmony Collected Dependency: 
[2025-07-31T21:44:19.285] [DEBUG] debug-file - Module harmony's total dependency: 0
[2025-07-31T21:44:19.285] [DEBUG] debug-file - Start to initialize dependency information.
[2025-07-31T21:44:19.287] [DEBUG] debug-file - Module lingxia Collected Dependency: 
[2025-07-31T21:44:19.287] [DEBUG] debug-file - Module lingxia's total dependency: 0
[2025-07-31T21:44:19.289] [DEBUG] debug-file - Configuration phase cost:567 ms 
[2025-07-31T21:44:19.292] [DEBUG] debug-file - Configuration task cost before running: 655 ms 
[2025-07-31T21:44:19.294] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T21:44:19.295] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T21:44:19.296] [DEBUG] debug-file - Executing task :lingxia:default@PreBuild
[2025-07-31T21:44:19.300] [DEBUG] debug-file - Incremental task lingxia:default@PreBuild pre-execution cost: 3 ms .
[2025-07-31T21:44:19.300] [DEBUG] debug-file - lingxia : default@PreBuild cost memory 0.16595458984375
[2025-07-31T21:44:19.302] [INFO] debug-file - UP-TO-DATE :lingxia:default@PreBuild...  
[2025-07-31T21:44:19.303] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T21:44:19.303] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T21:44:19.304] [DEBUG] debug-file - Executing task :lingxia:default@ProcessOHPackageJson
[2025-07-31T21:44:19.304] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T21:44:19.304] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T21:44:19.307] [DEBUG] debug-file - lingxia : default@ProcessOHPackageJson cost memory 0.05095672607421875
[2025-07-31T21:44:19.307] [DEBUG] debug-file - runTaskFromQueue task cost before running: 671 ms 
[2025-07-31T21:44:19.308] [INFO] debug-file - Finished :lingxia:default@ProcessOHPackageJson... after 4 ms 
[2025-07-31T21:44:19.309] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T21:44:19.310] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T21:44:19.312] [DEBUG] debug-file - Executing task :lingxia:default@CreateHarBuildProfile
[2025-07-31T21:44:19.313] [DEBUG] debug-file - Task 'lingxia:default@CreateHarBuildProfile' cost while wrapping incremental declarations: 1 ms 
[2025-07-31T21:44:19.313] [DEBUG] debug-file - lingxia:default@CreateHarBuildProfile is not up-to-date, since the output file '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/BuildProfile.ets' has been changed.
[2025-07-31T21:44:19.313] [DEBUG] debug-file - Incremental task lingxia:default@CreateHarBuildProfile pre-execution cost: 1 ms .
[2025-07-31T21:44:19.313] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T21:44:19.313] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T21:44:19.316] [DEBUG] debug-file - lingxia : default@CreateHarBuildProfile cost memory 0.1035308837890625
[2025-07-31T21:44:19.316] [DEBUG] debug-file - runTaskFromQueue task cost before running: 680 ms 
[2025-07-31T21:44:19.316] [INFO] debug-file - Finished :lingxia:default@CreateHarBuildProfile... after 4 ms 
[2025-07-31T21:44:19.320] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T21:44:19.321] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T21:44:19.322] [DEBUG] debug-file - Executing task :lingxia:default@CreateHarModuleInfo
[2025-07-31T21:44:19.322] [DEBUG] debug-file - Task 'lingxia:default@CreateHarModuleInfo' cost while wrapping incremental declarations: 1 ms 
[2025-07-31T21:44:19.323] [DEBUG] debug-file - lingxia:default@CreateHarModuleInfo is not up-to-date, since the output file '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/generated/profile/default/ModuleInfo.ts' does not exist.
[2025-07-31T21:44:19.324] [DEBUG] debug-file - Incremental task lingxia:default@CreateHarModuleInfo pre-execution cost: 1 ms .
[2025-07-31T21:44:19.325] [DEBUG] debug-file - lingxia : default@CreateHarModuleInfo cost memory 0.07030487060546875
[2025-07-31T21:44:19.325] [DEBUG] debug-file - runTaskFromQueue task cost before running: 688 ms 
[2025-07-31T21:44:19.325] [INFO] debug-file - Finished :lingxia:default@CreateHarModuleInfo... after 4 ms 
[2025-07-31T21:44:19.329] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T21:44:19.329] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T21:44:19.329] [DEBUG] debug-file - Executing task :lingxia:default@ConfigureCmake
[2025-07-31T21:44:19.330] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T21:44:19.330] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T21:44:19.330] [DEBUG] debug-file - lingxia : default@ConfigureCmake cost memory 0.03815460205078125
[2025-07-31T21:44:19.330] [DEBUG] debug-file - runTaskFromQueue task cost before running: 693 ms 
[2025-07-31T21:44:19.330] [INFO] debug-file - Finished :lingxia:default@ConfigureCmake... after 1 ms 
[2025-07-31T21:44:19.334] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T21:44:19.335] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T21:44:19.335] [DEBUG] debug-file - Executing task :lingxia:default@MergeProfile
[2025-07-31T21:44:19.336] [DEBUG] debug-file - lingxia:default@MergeProfile is not up-to-date, since the output file '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/merge_profile/default/module.json' has been changed.
[2025-07-31T21:44:19.336] [DEBUG] debug-file - Incremental task lingxia:default@MergeProfile pre-execution cost: 1 ms .
[2025-07-31T21:44:19.336] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T21:44:19.338] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T21:44:19.340] [DEBUG] debug-file - Change app api release type with 'Release'
[2025-07-31T21:44:19.343] [DEBUG] debug-file - Change app compile API version with '*********'
[2025-07-31T21:44:19.343] [DEBUG] debug-file - Change app target API version with '50005017'
[2025-07-31T21:44:19.343] [DEBUG] debug-file - Change app minimum API version with '50005017'
[2025-07-31T21:44:19.344] [DEBUG] debug-file - Use cli appEnvironment
[2025-07-31T21:44:19.350] [DEBUG] debug-file - lingxia : default@MergeProfile cost memory 0.24398040771484375
[2025-07-31T21:44:19.350] [DEBUG] debug-file - runTaskFromQueue task cost before running: 713 ms 
[2025-07-31T21:44:19.350] [INFO] debug-file - Finished :lingxia:default@MergeProfile... after 15 ms 
[2025-07-31T21:44:19.352] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T21:44:19.353] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T21:44:19.359] [DEBUG] debug-file - Executing task :lingxia:default@GeneratePkgContextInfo
[2025-07-31T21:44:19.359] [DEBUG] debug-file - Task 'lingxia:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms 
[2025-07-31T21:44:19.359] [DEBUG] debug-file - lingxia:default@GeneratePkgContextInfo is not up-to-date, since the output file '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/loader/default/pkgContextInfo.json' does not exist.
[2025-07-31T21:44:19.359] [DEBUG] debug-file - Incremental task lingxia:default@GeneratePkgContextInfo pre-execution cost: 1 ms .
[2025-07-31T21:44:19.361] [DEBUG] debug-file - lingxia : default@GeneratePkgContextInfo cost memory 0.0624542236328125
[2025-07-31T21:44:19.362] [DEBUG] debug-file - runTaskFromQueue task cost before running: 725 ms 
[2025-07-31T21:44:19.362] [INFO] debug-file - Finished :lingxia:default@GeneratePkgContextInfo... after 3 ms 
[2025-07-31T21:44:19.364] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T21:44:19.364] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T21:44:19.367] [DEBUG] debug-file - Executing task :lingxia:default@ProcessIntegratedHsp
[2025-07-31T21:44:19.368] [DEBUG] debug-file - lingxia:default@ProcessIntegratedHsp is not up-to-date, since the output file '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/build/cache/default/integrated_hsp/integratedHspCache.json' does not exist.
[2025-07-31T21:44:19.368] [DEBUG] debug-file - Incremental task lingxia:default@ProcessIntegratedHsp pre-execution cost: 2 ms .
[2025-07-31T21:44:19.368] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T21:44:19.369] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T21:44:19.369] [DEBUG] debug-file - lingxia : default@ProcessIntegratedHsp cost memory 0.1122283935546875
[2025-07-31T21:44:19.369] [DEBUG] debug-file - runTaskFromQueue task cost before running: 732 ms 
[2025-07-31T21:44:19.370] [INFO] debug-file - Finished :lingxia:default@ProcessIntegratedHsp... after 3 ms 
[2025-07-31T21:44:19.371] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T21:44:19.372] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T21:44:19.372] [DEBUG] debug-file - Executing task :lingxia:default@BuildNativeWithCmake
[2025-07-31T21:44:19.373] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T21:44:19.373] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T21:44:19.373] [DEBUG] debug-file - lingxia : default@BuildNativeWithCmake cost memory 0.0475616455078125
[2025-07-31T21:44:19.374] [DEBUG] debug-file - runTaskFromQueue task cost before running: 738 ms 
[2025-07-31T21:44:19.375] [INFO] debug-file - Finished :lingxia:default@BuildNativeWithCmake... after 2 ms 
[2025-07-31T21:44:19.378] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T21:44:19.378] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T21:44:19.379] [DEBUG] debug-file - Executing task :lingxia:default@ProcessProfile
[2025-07-31T21:44:19.380] [DEBUG] debug-file - lingxia:default@ProcessProfile is not up-to-date, since the input file '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/merge_profile/default/module.json' has been changed.
[2025-07-31T21:44:19.380] [DEBUG] debug-file - Incremental task lingxia:default@ProcessProfile pre-execution cost: 1 ms .
[2025-07-31T21:44:19.380] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T21:44:19.380] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T21:44:19.383] [DEBUG] debug-file - [
  '/Users/<USER>/Library/OpenHarmony/command-line-tools/tool/node/bin/node',
  '/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/build-tools/ets-loader/bin/ark/ts2abc.js',
  '--target-api-version',
  '17'
]
[2025-07-31T21:44:19.580] [DEBUG] debug-file - ********
[2025-07-31T21:44:19.583] [DEBUG] debug-file - lingxia : default@ProcessProfile cost memory 0.31446075439453125
[2025-07-31T21:44:19.584] [DEBUG] debug-file - runTaskFromQueue task cost before running: 947 ms 
[2025-07-31T21:44:19.585] [INFO] debug-file - Finished :lingxia:default@ProcessProfile... after 205 ms 
[2025-07-31T21:44:19.587] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T21:44:19.587] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T21:44:19.588] [DEBUG] debug-file - Executing task :lingxia:default@ProcessRouterMap
[2025-07-31T21:44:19.590] [DEBUG] debug-file - lingxia:default@ProcessRouterMap is not up-to-date, since the input file '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/loader/default/pkgContextInfo.json' has been changed.
[2025-07-31T21:44:19.591] [DEBUG] debug-file - Incremental task lingxia:default@ProcessRouterMap pre-execution cost: 2 ms .
[2025-07-31T21:44:19.591] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T21:44:19.591] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T21:44:19.591] [DEBUG] debug-file - lingxia : default@ProcessRouterMap cost memory 0.19377899169921875
[2025-07-31T21:44:19.592] [DEBUG] debug-file - runTaskFromQueue task cost before running: 956 ms 
[2025-07-31T21:44:19.595] [INFO] debug-file - Finished :lingxia:default@ProcessRouterMap... after 4 ms 
[2025-07-31T21:44:19.597] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T21:44:19.597] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T21:44:19.598] [DEBUG] debug-file - Executing task :lingxia:default@BuildNativeWithNinja
[2025-07-31T21:44:19.598] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T21:44:19.599] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T21:44:19.600] [DEBUG] debug-file - lingxia : default@BuildNativeWithNinja cost memory 0.0529937744140625
[2025-07-31T21:44:19.600] [DEBUG] debug-file - runTaskFromQueue task cost before running: 964 ms 
[2025-07-31T21:44:19.602] [INFO] debug-file - Finished :lingxia:default@BuildNativeWithNinja... after 3 ms 
[2025-07-31T21:44:19.603] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T21:44:19.603] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T21:44:19.604] [DEBUG] debug-file - restool module names: lingxia; moduleName=lingxia, taskName=default@ProcessResource
[2025-07-31T21:44:19.611] [DEBUG] debug-file - Executing task :lingxia:default@ProcessResource
[2025-07-31T21:44:19.612] [DEBUG] debug-file - lingxia:default@ProcessResource is not up-to-date, since the output file '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/res/default/resConfig.json' does not exist.
[2025-07-31T21:44:19.612] [DEBUG] debug-file - Incremental task lingxia:default@ProcessResource pre-execution cost: 1 ms .
[2025-07-31T21:44:19.613] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T21:44:19.613] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T21:44:19.616] [DEBUG] debug-file - lingxia : default@ProcessResource cost memory -5.3513031005859375
[2025-07-31T21:44:19.618] [DEBUG] debug-file - runTaskFromQueue task cost before running: 981 ms 
[2025-07-31T21:44:19.619] [INFO] debug-file - Finished :lingxia:default@ProcessResource... after 12 ms 
[2025-07-31T21:44:19.622] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T21:44:19.622] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T21:44:19.624] [DEBUG] debug-file - Executing task :lingxia:default@GenerateLoaderJson
[2025-07-31T21:44:19.631] [DEBUG] debug-file - lingxia:default@GenerateLoaderJson is not up-to-date, since the input file '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/loader/default/pkgContextInfo.json' has been changed.
[2025-07-31T21:44:19.632] [DEBUG] debug-file - Incremental task lingxia:default@GenerateLoaderJson pre-execution cost: 4 ms .
[2025-07-31T21:44:19.643] [DEBUG] debug-file - lingxia : default@GenerateLoaderJson cost memory 0.575897216796875
[2025-07-31T21:44:19.643] [DEBUG] debug-file - runTaskFromQueue task cost before running: 1 s 7 ms 
[2025-07-31T21:44:19.646] [INFO] debug-file - Finished :lingxia:default@GenerateLoaderJson... after 19 ms 
[2025-07-31T21:44:19.648] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T21:44:19.648] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T21:44:19.649] [DEBUG] debug-file - Executing task :lingxia:default@ProcessLibs
[2025-07-31T21:44:19.651] [DEBUG] debug-file - Incremental task lingxia:default@ProcessLibs pre-execution cost: 2 ms .
[2025-07-31T21:44:19.651] [DEBUG] debug-file - lingxia : default@ProcessLibs cost memory 0.10274505615234375
[2025-07-31T21:44:19.652] [INFO] debug-file - UP-TO-DATE :lingxia:default@ProcessLibs...  
[2025-07-31T21:44:19.653] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T21:44:19.653] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T21:44:19.654] [DEBUG] debug-file - restool module names: lingxia; moduleName=lingxia, taskName=default@CompileResource
[2025-07-31T21:44:19.655] [DEBUG] debug-file - Executing task :lingxia:default@CompileResource
[2025-07-31T21:44:19.657] [DEBUG] debug-file - lingxia:default@CompileResource is not up-to-date, since the input file '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/process_profile/default/module.json' has been changed.
[2025-07-31T21:44:19.657] [DEBUG] debug-file - Incremental task lingxia:default@CompileResource pre-execution cost: 1 ms .
[2025-07-31T21:44:19.657] [DEBUG] debug-file - lingxia : default@CompileResource cost memory 0.1045684814453125
[2025-07-31T21:44:19.657] [DEBUG] debug-file - runTaskFromQueue task cost before running: 1 s 21 ms 
[2025-07-31T21:44:19.658] [INFO] debug-file - Finished :lingxia:default@CompileResource... after 2 ms 
[2025-07-31T21:44:19.659] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T21:44:19.659] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T21:44:19.662] [DEBUG] debug-file - Executing task :lingxia:default@DoNativeStrip
[2025-07-31T21:44:19.662] [DEBUG] debug-file - Task 'lingxia:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms 
[2025-07-31T21:44:19.663] [DEBUG] debug-file - Incremental task lingxia:default@DoNativeStrip pre-execution cost: 1 ms .
[2025-07-31T21:44:19.663] [DEBUG] debug-file - lingxia : default@DoNativeStrip cost memory 0.0629425048828125
[2025-07-31T21:44:19.663] [INFO] debug-file - UP-TO-DATE :lingxia:default@DoNativeStrip...  
[2025-07-31T21:44:19.664] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T21:44:19.664] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T21:44:19.665] [DEBUG] debug-file - Executing task :lingxia:default@ProcessObfuscationFiles
[2025-07-31T21:44:19.665] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T21:44:19.665] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T21:44:19.665] [DEBUG] debug-file - lingxia : default@ProcessObfuscationFiles cost memory 0.03820037841796875
[2025-07-31T21:44:19.666] [DEBUG] debug-file - runTaskFromQueue task cost before running: 1 s 29 ms 
[2025-07-31T21:44:19.667] [INFO] debug-file - Finished :lingxia:default@ProcessObfuscationFiles... after 1 ms 
[2025-07-31T21:44:19.669] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T21:44:19.669] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T21:44:19.673] [DEBUG] debug-file - Executing task :lingxia:default@HarCompileArkTS
[2025-07-31T21:44:19.683] [DEBUG] debug-file - lingxia:default@HarCompileArkTS is not up-to-date, since the input file '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/loader/default' has been changed.
[2025-07-31T21:44:19.683] [DEBUG] debug-file - Incremental task lingxia:default@HarCompileArkTS pre-execution cost: 8 ms .
[2025-07-31T21:44:19.703] [DEBUG] debug-file - build config:
[2025-07-31T21:44:19.704] [DEBUG] debug-file - {
  moduleType: 'har',
  perf: 0,
  targetName: '.default',
  packageManagerType: 'ohpm',
  localPropertiesPath: '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/local.properties',
  isPreview: false,
  isOhosTest: false,
  isLocalTest: false,
  buildMode: 'Debug',
  watchMode: 'false',
  aceProfilePath: '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/res/default/resources/base/profile',
  etsLoaderPath: '/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/openharmony/ets/build-tools/ets-loader',
  modulePath: '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia',
  testFrameworkPar: {
    testMode: undefined,
    coveragePathFilter: undefined,
    coverageMode: undefined
  },
  needCoverageInsert: false,
  debugLine: false,
  projectTopDir: '/Users/<USER>/github/LingXia/lingxia-sdk/harmony',
  compileSdkVersion: 17,
  compatibleSdkVersion: 17,
  compatibleSdkVersionStage: undefined,
  bundleName: 'c***p',
  etsLoaderVersion: '*********',
  etsLoaderReleaseType: 'Release',
  aotCompileMode: 'type',
  apPath: '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/modules.ap',
  entryModuleName: 'lingxia',
  entryModuleVersion: '1.0.0',
  entryPackageName: 'lingxia',
  allModuleNameHash: '1be683a2a047c7a875d9c49d481b3c24',
  externalApiPaths: [
    '/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/hms/ets'
  ],
  compilerTypes: undefined,
  isCrossplatform: false,
  hvigorPluginFile: undefined,
  compilePluginPath: undefined,
  buildGeneratedProfilePath: '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/generated/profile/default',
  bundleType: 'app',
  arkTSVersion: undefined,
  apiVersion: 17,
  needCompleteSourcesMap: false,
  isFaMode: false,
  strictMode: {
    caseSensitiveCheck: true,
    useNormalizedOHMUrl: true,
    noExternalImportByPath: true
  },
  buildDir: '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build',
  deviceTypes: [ 'default', 'tablet' ],
  useNormalizedOHMUrl: true,
  pkgContextInfo: {
    lingxia: {
      packageName: 'lingxia',
      bundleName: '*****',
      moduleName: '',
      version: '1.0.0',
      entryPath: 'Index.ets',
      isSO: false,
      dependencyAlias: ''
    }
  },
  ohPackagePathMap: {},
  dependencyAliasMap: {},
  permission: { requestPermissions: [ [Object] ], definePermissions: undefined },
  integratedHsp: false,
  projectArkOption: undefined,
  sourceMapDir: undefined,
  branchElimination: false,
  transformLib: undefined,
  caseSensitiveCheck: true,
  tsImportSendable: false,
  resolveConflictMode: true,
  depName2RootPath: {},
  depName2DepInfo: {},
  rootPathSet: [ '/Users/<USER>/github/LingXia/lingxia-sdk/harmony' ],
  useNativeResolver: true,
  shouldEmitJs: true,
  autoLazyImport: undefined,
  allowEmptyBundleName: false,
  singleFileEmit: false,
  arkCompileCachePath: '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/cache/default/default@HarCompileArkTS/esmodule',
  reExportCheckMode: 'noCheck',
  aceModuleJsonPath: '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/res/default/ark_module.json',
  appResource: '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/res/default/ResourceTable.txt',
  rawFileResource: '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/res/default/resources/rawfile',
  resourceTableHash: undefined,
  runtimeOS: 'HarmonyOS',
  sdkInfo: 'false:17:*********:Release',
  aceModuleRoot: '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets',
  compileMode: 'esmodule',
  aceSuperVisualPath: '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/supervisual',
  aceBuildJson: '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/loader/default/loader.json',
  cachePath: '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/cache/default/default@HarCompileArkTS/esmodule/debug',
  aceModuleBuild: '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/loader_out/default/ets',
  supportChunks: true,
  declaredFilesPath: '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/loader_out/default/etsFortgz/lingxia',
  pkgNameToPkgBriefInfo: {
    lingxia: {
      pkgRoot: '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia',
      originalSourceRoots: undefined,
      sourceRoots: [Array],
      pkgName: 'lingxia'
    }
  },
  projectModel: {
    '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main': {
      moduleName: 'lingxia',
      modulePkgPath: '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia',
      belongProjectPath: '/Users/<USER>/github/LingXia/lingxia-sdk/harmony'
    },
    '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/src/ohosTest/ets': {
      moduleName: 'lingxia',
      modulePkgPath: '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia',
      belongProjectPath: '/Users/<USER>/github/LingXia/lingxia-sdk/harmony'
    },
    '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/ohosTest': {
      moduleName: 'lingxia',
      modulePkgPath: '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia',
      belongProjectPath: '/Users/<USER>/github/LingXia/lingxia-sdk/harmony'
    },
    '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia': {
      moduleName: 'lingxia',
      modulePkgPath: '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia',
      belongProjectPath: '/Users/<USER>/github/LingXia/lingxia-sdk/harmony'
    },
    '/Users/<USER>/github/LingXia/lingxia-sdk/harmony': {
      moduleName: 'harmony',
      modulePkgPath: '/Users/<USER>/github/LingXia/lingxia-sdk/harmony',
      belongProjectPath: '/Users/<USER>/github/LingXia/lingxia-sdk/harmony'
    }
  },
  pkgJsonFileHash: 'f6886ae0f7db79265d288ebee9eadaa8',
  allModulePaths: [ '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia' ],
  routerMap: {},
  obfuscationOptions: undefined,
  compileBlockPkg: [ '@ohos/hypium', '@ohos/hamock' ],
  mockParams: {
    decorator: '@MockSetup',
    packageName: '@ohos/hamock',
    etsSourceRootPath: 'src/main/ets',
    mockConfigPath: undefined,
    mockConfigKey2ModuleInfo: {}
  },
  copyCodeResourceEnable: true,
  copyCodeResourceExcludes: [],
  uiTransformOptimization: true,
  otherPaths: {
    'lingxia/*': [ '../*', '../../../build/default/generated/profile/default/*' ]
  },
  collectImportersConfig: undefined,
  byteCodeHar: true,
  obfuscate: undefined,
  skipBuildEnd: false
}
[2025-07-31T21:44:19.705] [DEBUG] debug-file - Compile arkts with external api path: /Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/hms/ets
[2025-07-31T21:44:19.707] [DEBUG] debug-file - default@HarCompileArkTS work[5] is submitted.
[2025-07-31T21:44:19.709] [DEBUG] debug-file - default@HarCompileArkTS work[5] is pushed to ready queue.
[2025-07-31T21:44:19.710] [DEBUG] debug-file - A work dispatched to worker[7] failed because unable to get work from ready queue.
[2025-07-31T21:44:19.710] [DEBUG] debug-file - A work dispatched to worker[6] failed because unable to get work from ready queue.
[2025-07-31T21:44:19.710] [DEBUG] debug-file - A work dispatched to worker[5] failed because unable to get work from ready queue.
[2025-07-31T21:44:19.711] [DEBUG] debug-file - default@HarCompileArkTS work[5] has been dispatched to worker[4].
[2025-07-31T21:44:19.712] [DEBUG] debug-file - default@HarCompileArkTS work[5] is dispatched.
[2025-07-31T21:44:19.712] [DEBUG] debug-file - CopyResources startTime: 652848055740126
[2025-07-31T21:44:19.712] [DEBUG] debug-file - default@HarCompileArkTS work[6] is submitted.
[2025-07-31T21:44:19.713] [DEBUG] debug-file - default@HarCompileArkTS work[6] is pushed to ready queue.
[2025-07-31T21:44:19.713] [DEBUG] debug-file - A work dispatched to worker[7] failed because unable to get work from ready queue.
[2025-07-31T21:44:19.714] [DEBUG] debug-file - Create  resident worker with id: 6.
[2025-07-31T21:44:19.720] [DEBUG] debug-file - default@HarCompileArkTS work[6] has been dispatched to worker[6].
[2025-07-31T21:44:19.720] [DEBUG] debug-file - default@HarCompileArkTS work[6] is dispatched.
[2025-07-31T21:44:19.720] [DEBUG] debug-file - lingxia : default@HarCompileArkTS cost memory 1.3804550170898438
[2025-07-31T21:44:19.722] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T21:44:19.722] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T21:44:19.724] [DEBUG] debug-file - Executing task :lingxia:default@CacheNativeLibs
[2025-07-31T21:44:19.725] [DEBUG] debug-file - Task 'lingxia:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms 
[2025-07-31T21:44:19.725] [DEBUG] debug-file - lingxia:default@CacheNativeLibs is not up-to-date, since the output file '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/patch/default/base_native_libs.json' does not exist.
[2025-07-31T21:44:19.725] [DEBUG] debug-file - Incremental task lingxia:default@CacheNativeLibs pre-execution cost: 1 ms .
[2025-07-31T21:44:19.726] [DEBUG] debug-file - jsonObjWithoutParam {} at undefined
[2025-07-31T21:44:19.726] [DEBUG] debug-file - jsonObjWithoutParam {"name":"lingxia","version":"1.0.0","description":"LingXia LxApp HAR module","main":"Index.ets","author":"","license":"Apache-2.0","dependencies":{}} at undefined
[2025-07-31T21:44:19.727] [DEBUG] debug-file - default@CacheNativeLibs work[7] is submitted.
[2025-07-31T21:44:19.730] [DEBUG] debug-file - default@CacheNativeLibs work[7] is pushed to ready queue.
[2025-07-31T21:44:19.732] [DEBUG] debug-file - A work dispatched to worker[7] failed because unable to get work from ready queue.
[2025-07-31T21:44:19.732] [DEBUG] debug-file - A work dispatched to worker[6] failed because of worker busy.
[2025-07-31T21:44:19.732] [DEBUG] debug-file - Create  resident worker with id: 5.
[2025-07-31T21:44:19.735] [DEBUG] debug-file - default@CacheNativeLibs work[7] has been dispatched to worker[5].
[2025-07-31T21:44:19.735] [DEBUG] debug-file - default@CacheNativeLibs work[7] is dispatched.
[2025-07-31T21:44:19.735] [DEBUG] debug-file - lingxia : default@CacheNativeLibs cost memory 0.287139892578125
[2025-07-31T21:44:20.841] [DEBUG] debug-file - worker[6] has one work done.
[2025-07-31T21:44:20.842] [DEBUG] debug-file - CopyResources is end, endTime: 652849185260029
[2025-07-31T21:44:20.842] [DEBUG] debug-file - default@HarCompileArkTS work[6] done.
[2025-07-31T21:44:20.842] [DEBUG] debug-file - A work dispatched to worker[6] failed because unable to get work from ready queue.
