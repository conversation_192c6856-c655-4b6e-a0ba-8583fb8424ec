{"HVIGOR_OHOS_PLUGIN": {"MODULES": [{"MODULE_NAME": "7069fb83d8ce6f61be364a66a7bb21a7", "API_TYPE": "stageMode", "INCREMENTAL_TASKS": {"COMPILE_ARKTS": false}}], "BUILD_MODE": "debug", "USE_NORMALIZED_OHMURL": true}, "HVIGOR": {"IS_INCREMENTAL": true, "IS_DAEMON": true, "IS_PARALLEL": true, "IS_HVIGORFILE_TYPE_CHECK": false, "TASK_TIME": {"7069fb83d8ce6f61be364a66a7bb21a7": {"ProcessOHPackageJson": 3463422, "CreateHarBuildProfile": 4068701, "CreateHarModuleInfo": 3424945, "ConfigureCmake": 614536, "MergeProfile": 14836187, "GeneratePkgContextInfo": 3153567, "ProcessIntegratedHsp": 2799057, "BuildNativeWithCmake": 1914480, "ProcessProfile": 205440456, "ProcessRouterMap": 6637383, "BuildNativeWithNinja": 3652662, "ProcessResource": 13583683, "GenerateLoaderJson": 22095301, "CompileResource": 2481442, "ProcessObfuscationFiles": 1609855, "CacheNativeLibs": 2872307726, "HarCompileArkTS": 8609687343, "ProcessHarArtifacts": 33461097, "PackageHar": 33563829, "PackageSignHar": 1475197, "CollectDebugSymbol": 4566807, "assembleHar": 880540}}, "TOTAL_TIME": 9746169433, "BUILD_ID": "202507312144186330"}}