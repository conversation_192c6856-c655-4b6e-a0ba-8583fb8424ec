{"version": "2.0", "ppid": 61930, "events": [{"head": {"id": "99d2e1a3-4b43-478f-a349-de4a71094312", "name": "hvigor build process will be closed.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 643336806182315}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b45699f-9e21-436a-86b8-a8e7fa06c383", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 643336873271838}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2eed45f7-3479-4082-8388-73bce1cb1152", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 643336873973622}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3cf0190b-cc9f-4e4f-9ce2-e90f587d9058", "name": "watch worker: worker id should be larger than 0. Nothing will be sent to any worker thread.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652846908406867}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c3edd99b-11fa-462c-9c02-2c826045c7aa", "name": "init", "description": "Initialize and build task graph.", "type": "duration"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652846992155203, "endTime": 652847635127522}, "additional": {"children": ["ca73eccc-3413-4bc7-84b7-cbc6f16d95ae", "cc76737d-b163-4c92-a421-47d78c3ab947", "6269ef70-0445-4444-825c-90054cc4e140", "f5a2c0be-8b15-429f-b61d-232ca99189c1", "b18209aa-7bc4-4398-bda9-0603fff73308", "1b69df01-b1fe-4a63-b551-80ed5bfb2d14", "20992356-c6ba-42c6-b8d7-5e4161931daa"], "state": "success", "targetName": "", "moduleName": "", "category": "Init", "taskRunReasons": [], "logId": "1a53298f-4d52-48c8-87b2-720f03cdd82d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ca73eccc-3413-4bc7-84b7-cbc6f16d95ae", "name": "create hvigor project model", "description": "Initialize hvigor project model.", "type": "duration"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652846992158626, "endTime": 652847055913930}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c3edd99b-11fa-462c-9c02-2c826045c7aa", "logId": "2dd8bbea-06f9-486e-bda3-3e32911c4beb"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cc76737d-b163-4c92-a421-47d78c3ab947", "name": "configure hvigor plugin", "description": "Configure hvigor plugin.", "type": "duration"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847055939529, "endTime": 652847633275023}, "additional": {"children": ["2651ce9d-910b-4931-9790-a7645f2c9321", "970849e0-0dd0-4775-9a65-e88905f52ff4", "45f60459-7a53-48b9-a7ab-bc3c5e81ae34", "bf2585fc-bb0d-4756-bed5-879e8b004098", "ede1f44c-10f5-47d7-98e1-825fe6db5e79", "af107e82-d5ab-4a78-91d2-9b1f8605f3c8", "dd66711c-527f-41c4-8fe4-3c2bf6ff132a", "dcaa1d8e-996c-4414-88f7-bf9d16d432d5", "4f4cd936-0911-428a-b269-d3a3f746feea"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c3edd99b-11fa-462c-9c02-2c826045c7aa", "logId": "433dfc14-80d6-42ba-8f10-d0194c6a4de0"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6269ef70-0445-4444-825c-90054cc4e140", "name": "build task graph", "description": "Build task graph.", "type": "duration"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847633310767, "endTime": 652847635106697}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c3edd99b-11fa-462c-9c02-2c826045c7aa", "logId": "5ad6fcf8-018a-4568-b83e-b4ac730d1bfa"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f5a2c0be-8b15-429f-b61d-232ca99189c1", "name": "init task execution option", "description": "Init task execution option.", "type": "duration"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847635113611, "endTime": 652847635121512}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c3edd99b-11fa-462c-9c02-2c826045c7aa", "logId": "b73c09b9-d4d7-4e64-afa5-027f62928a9c"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b18209aa-7bc4-4398-bda9-0603fff73308", "name": "\"configEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847009441872, "endTime": 652847012455593}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c3edd99b-11fa-462c-9c02-2c826045c7aa", "logId": "784b00f0-adf1-409a-9619-4be83bfc97a1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "784b00f0-adf1-409a-9619-4be83bfc97a1", "name": "\"configEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847009441872, "endTime": 652847012455593}, "additional": {"logType": "info", "children": [], "durationId": "b18209aa-7bc4-4398-bda9-0603fff73308", "parent": "1a53298f-4d52-48c8-87b2-720f03cdd82d"}}, {"head": {"id": "1b69df01-b1fe-4a63-b551-80ed5bfb2d14", "name": "\"nodesInitialized\" hook function", "description": "", "type": "duration"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847029218216, "endTime": 652847029238376}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c3edd99b-11fa-462c-9c02-2c826045c7aa", "logId": "8713bc6d-9387-4610-a805-f76c2ab8f6a8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8713bc6d-9387-4610-a805-f76c2ab8f6a8", "name": "\"nodesInitialized\" hook function", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847029218216, "endTime": 652847029238376}, "additional": {"logType": "info", "children": [], "durationId": "1b69df01-b1fe-4a63-b551-80ed5bfb2d14", "parent": "1a53298f-4d52-48c8-87b2-720f03cdd82d"}}, {"head": {"id": "5f7082ba-d53f-4ff9-a41a-16ab25b5e47c", "name": "Hvigor init with startParameters:{\n  hvigorfileTypeCheck: false,\n  parallelExecution: true,\n  incrementalExecution: true,\n  printStackTrace: false,\n  daemon: true,\n  analyze: 0,\n  logLevel: Level { level: 20000, levelStr: 'INFO', colour: 'green' }\n}", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847029601126}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b7278bd-1ead-4773-9389-d881751c4b45", "name": "Cache service initialization finished in 25 ms ", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847055707221}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2dd8bbea-06f9-486e-bda3-3e32911c4beb", "name": "create hvigor project model", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652846992158626, "endTime": 652847055913930}, "additional": {"logType": "info", "children": [], "durationId": "ca73eccc-3413-4bc7-84b7-cbc6f16d95ae", "parent": "1a53298f-4d52-48c8-87b2-720f03cdd82d"}}, {"head": {"id": "2651ce9d-910b-4931-9790-a7645f2c9321", "name": "init configuration", "description": "Initialize configuration.", "type": "duration"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847065947582, "endTime": 652847066732512}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cc76737d-b163-4c92-a421-47d78c3ab947", "logId": "8ddb2a0f-b352-4c6c-9934-21f30f4396bf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "970849e0-0dd0-4775-9a65-e88905f52ff4", "name": "configure project task", "description": "Configure project task.", "type": "duration"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847066775144, "endTime": 652847073338190}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cc76737d-b163-4c92-a421-47d78c3ab947", "logId": "9d339b0a-0f78-47b0-8e16-69a579bbfef6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "45f60459-7a53-48b9-a7ab-bc3c5e81ae34", "name": "eval project", "description": "Evaluate project.", "type": "duration"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847073367915, "endTime": 652847380717807}, "additional": {"children": ["8f10eba7-f389-42d9-bb4c-bcae1e4a0d47", "af890925-e139-4266-bd4e-260f72b8e646", "ef65b002-f4b4-4bde-8332-f0efb22d62aa"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cc76737d-b163-4c92-a421-47d78c3ab947", "logId": "9bd9450b-a96f-4e45-b24a-c4bf948923b6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bf2585fc-bb0d-4756-bed5-879e8b004098", "name": "eval modules", "description": "Evaluate modules.", "type": "duration"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847380750314, "endTime": 652847430409924}, "additional": {"children": ["6294ae78-6ebf-44f0-ad85-586504e6ecd0"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cc76737d-b163-4c92-a421-47d78c3ab947", "logId": "0d965d8a-eacf-48cc-894f-6793cc8b7c2e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ede1f44c-10f5-47d7-98e1-825fe6db5e79", "name": "add config dependencies", "description": "Add configuration dependencies.", "type": "duration"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847430420433, "endTime": 652847615660042}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cc76737d-b163-4c92-a421-47d78c3ab947", "logId": "8689d130-d114-4e45-acb9-f45bf898ee13"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "af107e82-d5ab-4a78-91d2-9b1f8605f3c8", "name": "ohpm install", "description": "Ohpm install event.", "type": "duration"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847616687569, "endTime": 652847626439341}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cc76737d-b163-4c92-a421-47d78c3ab947", "logId": "955d0ba5-fd07-4949-aa52-c30724638809"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dd66711c-527f-41c4-8fe4-3c2bf6ff132a", "name": "eval hook", "description": "EvaluateEvent hook.", "type": "duration"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847626467921, "endTime": 652847633054312}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cc76737d-b163-4c92-a421-47d78c3ab947", "logId": "798ed87c-dc6e-449f-9308-997874474bf1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dcaa1d8e-996c-4414-88f7-bf9d16d432d5", "name": "fin configuration", "description": "Finish configuration.", "type": "duration"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847633082672, "endTime": 652847633257382}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cc76737d-b163-4c92-a421-47d78c3ab947", "logId": "cc69b7d7-dcc1-4880-a3ac-a6ac60acbe16"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8ddb2a0f-b352-4c6c-9934-21f30f4396bf", "name": "init configuration", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847065947582, "endTime": 652847066732512}, "additional": {"logType": "info", "children": [], "durationId": "2651ce9d-910b-4931-9790-a7645f2c9321", "parent": "433dfc14-80d6-42ba-8f10-d0194c6a4de0"}}, {"head": {"id": "9d339b0a-0f78-47b0-8e16-69a579bbfef6", "name": "configure project task", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847066775144, "endTime": 652847073338190}, "additional": {"logType": "info", "children": [], "durationId": "970849e0-0dd0-4775-9a65-e88905f52ff4", "parent": "433dfc14-80d6-42ba-8f10-d0194c6a4de0"}}, {"head": {"id": "8f10eba7-f389-42d9-bb4c-bcae1e4a0d47", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847073967920, "endTime": 652847073986188}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "45f60459-7a53-48b9-a7ab-bc3c5e81ae34", "logId": "d1a8294b-e5a8-4927-b3a7-c29bc26294cd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d1a8294b-e5a8-4927-b3a7-c29bc26294cd", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847073967920, "endTime": 652847073986188}, "additional": {"logType": "info", "children": [], "durationId": "8f10eba7-f389-42d9-bb4c-bcae1e4a0d47", "parent": "9bd9450b-a96f-4e45-b24a-c4bf948923b6"}}, {"head": {"id": "af890925-e139-4266-bd4e-260f72b8e646", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847076279048, "endTime": 652847380096894}, "additional": {"children": ["bfd94856-9d4e-4ca4-88a8-87346959d83b", "366e792e-f057-457b-a8fe-69aa485ff25c"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "45f60459-7a53-48b9-a7ab-bc3c5e81ae34", "logId": "c722bdf5-e500-4092-93fd-bc42dc075b5e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bfd94856-9d4e-4ca4-88a8-87346959d83b", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847076280362, "endTime": 652847193842188}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "af890925-e139-4266-bd4e-260f72b8e646", "logId": "16519988-1df7-40f9-949d-b04f2aa1000a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "366e792e-f057-457b-a8fe-69aa485ff25c", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847193859915, "endTime": 652847380081377}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "af890925-e139-4266-bd4e-260f72b8e646", "logId": "8d56699e-4e4d-4fe1-b1d3-4345c78cf0e2"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "278ed28f-b02c-4b3e-a958-8987896e61ff", "name": "hvigorfile, resolving /Users/<USER>/github/LingXia/lingxia-sdk/harmony/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847076305074}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f124d115-80f6-4830-bcca-1723a4afdef3", "name": "hvigorfile, require result:  { default: { system: [Function: appTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847193618576}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "16519988-1df7-40f9-949d-b04f2aa1000a", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847076280362, "endTime": 652847193842188}, "additional": {"logType": "info", "children": [], "durationId": "bfd94856-9d4e-4ca4-88a8-87346959d83b", "parent": "c722bdf5-e500-4092-93fd-bc42dc075b5e"}}, {"head": {"id": "b77ab371-aecd-435f-863d-c6924c8ba106", "name": "hvigorfile, binding system plugins [Function: appTasks]", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847193895132}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7eea66e1-425d-4e85-b5fa-bf4486c3736d", "name": "Start initialize project's product build option map with build mode debug.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847223998288}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b10a29c4-8497-49fc-ae6e-01f50645b15d", "name": "Picking option from product 'default' with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847224307531}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "217ae30a-4d7f-41a7-84fc-32690d136e8c", "name": "Product 'default' build option: {}", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847224678175}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c9358e4f-6ad5-4cf3-b61e-822469d1ccef", "name": "End initialize project's product build option map with build mode 'debug'.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847224949874}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "64a7a97a-98f0-4636-b752-364463f4cddd", "name": "Product 'default' using build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847244219518}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d57efa91-57f4-4936-ace1-5b262e3e8033", "name": "not found resModel json file in : /Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/ohosTest/module.json5", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847246726073}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c6bf8ce-43b0-47ac-abfb-2e5a97cee869", "name": "No signingConfig found, initRemoteHspCache failed.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847278323303}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a796102-a1d5-4d81-8ce9-27dd22f8a51c", "name": "Start recording SDK configuration permission data.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847298492658}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52e4de29-b42b-415d-ac85-d6d5b59db498", "name": "Sdk init in 77 ms ", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847357436824}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "31cee035-0d40-4bf4-8a07-efe28f1b3f46", "name": "sdkVersion", "description": "*********", "type": "mark"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847357668123}, "additional": {"time": {"year": 2025, "month": 7, "day": 31, "hour": 21, "minute": 44}, "markType": "other"}}, {"head": {"id": "ac665472-f483-4f4c-b2c5-f80ea3a209ae", "name": "caseSensitiveCheckOn", "description": "caseSensitive<PERSON><PERSON><PERSON> check is on", "type": "mark"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847357732508}, "additional": {"time": {"year": 2025, "month": 7, "day": 31, "hour": 21, "minute": 44}, "markType": "other"}}, {"head": {"id": "44501d7f-2057-4afd-a164-501c1c46ab1f", "name": "Project task initialization takes 20 ms ", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847379700561}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f652192-5ba5-4512-8b08-6f9134a9dfa5", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847379868324}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b936960-7ce4-48ab-b2a7-7b4ab51c5067", "name": "hvigorfile, no custom plugins were found in /Users/<USER>/github/LingXia/lingxia-sdk/harmony/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847379944869}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23456146-9aae-4f1c-8f82-db4ba9dd61e6", "name": "hvigorfile, resolve finished /Users/<USER>/github/LingXia/lingxia-sdk/harmony/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847380003774}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d56699e-4e4d-4fe1-b1d3-4345c78cf0e2", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847193859915, "endTime": 652847380081377}, "additional": {"logType": "info", "children": [], "durationId": "366e792e-f057-457b-a8fe-69aa485ff25c", "parent": "c722bdf5-e500-4092-93fd-bc42dc075b5e"}}, {"head": {"id": "c722bdf5-e500-4092-93fd-bc42dc075b5e", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847076279048, "endTime": 652847380096894}, "additional": {"logType": "info", "children": ["16519988-1df7-40f9-949d-b04f2aa1000a", "8d56699e-4e4d-4fe1-b1d3-4345c78cf0e2"], "durationId": "af890925-e139-4266-bd4e-260f72b8e646", "parent": "9bd9450b-a96f-4e45-b24a-c4bf948923b6"}}, {"head": {"id": "ef65b002-f4b4-4bde-8332-f0efb22d62aa", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847380683131, "endTime": 652847380700880}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "45f60459-7a53-48b9-a7ab-bc3c5e81ae34", "logId": "ee3cf0fa-8cef-470d-a7d0-be91f0d8d749"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ee3cf0fa-8cef-470d-a7d0-be91f0d8d749", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847380683131, "endTime": 652847380700880}, "additional": {"logType": "info", "children": [], "durationId": "ef65b002-f4b4-4bde-8332-f0efb22d62aa", "parent": "9bd9450b-a96f-4e45-b24a-c4bf948923b6"}}, {"head": {"id": "9bd9450b-a96f-4e45-b24a-c4bf948923b6", "name": "eval project", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847073367915, "endTime": 652847380717807}, "additional": {"logType": "info", "children": ["d1a8294b-e5a8-4927-b3a7-c29bc26294cd", "c722bdf5-e500-4092-93fd-bc42dc075b5e", "ee3cf0fa-8cef-470d-a7d0-be91f0d8d749"], "durationId": "45f60459-7a53-48b9-a7ab-bc3c5e81ae34", "parent": "433dfc14-80d6-42ba-8f10-d0194c6a4de0"}}, {"head": {"id": "6294ae78-6ebf-44f0-ad85-586504e6ecd0", "name": "eval submodule", "description": "Evaluate submodule.", "type": "duration"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847381215303, "endTime": 652847430392593}, "additional": {"children": ["4c2347aa-c795-4294-afd7-1dfb1e99267c", "7d4d3d1d-e837-49d2-a30c-1f7cb4c44f14", "dd466bde-755f-422c-99b8-8c7180dc6ea3"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bf2585fc-bb0d-4756-bed5-879e8b004098", "logId": "146e27ee-aab1-4275-856b-284b5b631aa8"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4c2347aa-c795-4294-afd7-1dfb1e99267c", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847385626456, "endTime": 652847385647084}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6294ae78-6ebf-44f0-ad85-586504e6ecd0", "logId": "53a88b2a-60d1-4763-adc7-0ac9a885433b"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "53a88b2a-60d1-4763-adc7-0ac9a885433b", "name": "\"beforeNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847385626456, "endTime": 652847385647084}, "additional": {"logType": "info", "children": [], "durationId": "4c2347aa-c795-4294-afd7-1dfb1e99267c", "parent": "146e27ee-aab1-4275-856b-284b5b631aa8"}}, {"head": {"id": "7d4d3d1d-e837-49d2-a30c-1f7cb4c44f14", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "Evaluate hvigorfile.", "type": "duration"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847386808941, "endTime": 652847429187653}, "additional": {"children": ["9e2c6112-7a4a-482c-902c-6f4aecff18b0", "9aa8815a-d3d8-4b25-b214-3c40dfd4c49a"], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6294ae78-6ebf-44f0-ad85-586504e6ecd0", "logId": "c7b449ce-021a-4ae5-a10a-51d6ee8e7b5e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9e2c6112-7a4a-482c-902c-6f4aecff18b0", "name": "require hvigorfile", "description": "Require hvigorfile.", "type": "duration"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847386809962, "endTime": 652847396543105}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7d4d3d1d-e837-49d2-a30c-1f7cb4c44f14", "logId": "d08d9b25-c588-47dd-9e92-8dbc8c7b2533"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9aa8815a-d3d8-4b25-b214-3c40dfd4c49a", "name": "bind plugins", "description": "Bind plugins.", "type": "duration"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847396561503, "endTime": 652847429164243}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "7d4d3d1d-e837-49d2-a30c-1f7cb4c44f14", "logId": "4cbf8235-dd79-4ab5-821c-e9310e492fd9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "156fff0d-7b58-4ff5-ac81-1d3ca2c9f9e1", "name": "hvigorfile, resolving /Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847386815462}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d5db0a2-a9f9-45b5-aadc-b04975a61925", "name": "hvigorfile, require result:  { default: { system: [Function: harTasks], plugins: [] } }", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847396381489}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d08d9b25-c588-47dd-9e92-8dbc8c7b2533", "name": "require hvigorfile", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847386809962, "endTime": 652847396543105}, "additional": {"logType": "info", "children": [], "durationId": "9e2c6112-7a4a-482c-902c-6f4aecff18b0", "parent": "c7b449ce-021a-4ae5-a10a-51d6ee8e7b5e"}}, {"head": {"id": "1638a233-980b-41e8-8f99-39a6301b8c2f", "name": "hvigorfile, binding system plugins [Function: harTasks]", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847398358886}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac1030f7-a503-49da-b5d9-5e9fec3bb178", "name": "Start initialize module-target build option map, moduleName=lingxia, buildMode=debug", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847411561497}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d3dff45b-a350-4730-859f-077487c82b6b", "name": "Target 'default' config: {}", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847411740458}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ffb8ae42-2827-47ae-a472-740c14cea7df", "name": "Target 'ohosTest' config: {}", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847413164967}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d57ba6ef-37e8-46e8-8173-7b3e22b580e2", "name": "Module 'lingxia' target 'default' build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847414305267}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f610b24c-efc6-45c9-8d61-6195bc754882", "name": "Module 'lingxia' target 'ohosTest' build option: {\n  \"debuggable\": true,\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n}", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847414409052}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c2ccc6ba-a6f3-491e-9afe-1bdafac13fd4", "name": "End initialize module-target build option map, moduleName=lingxia", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847414458268}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d9fc47fb-9fa3-4874-bee9-2de33f37c44d", "name": "Module 'lingxia' target 'default' using build option: {\n  \"debuggable\": true,\n  \"copyFrom\": \"default\",\n  \"strictMode\": {\n    \"caseSensitiveCheck\": true,\n    \"useNormalizedOHMUrl\": true\n  }\n} in this build.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847415657707}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3626e01-617d-43a9-97d3-b59fc9d61702", "name": "Module lingxia task initialization takes 6 ms ", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847427706993}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "169b7645-8aa5-42a3-91b5-686a941355f6", "name": "hvigorfile, binding custom plugins []", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847428906707}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4aedb989-cd49-40ed-aaad-c2b2142e0570", "name": "hvigorfile, no custom plugins were found in /Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847429040339}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d68aced-4663-41ea-b6dd-1749dcb342da", "name": "hvigorfile, resolve finished /Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/hvigorfile.ts", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847429092483}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4cbf8235-dd79-4ab5-821c-e9310e492fd9", "name": "bind plugins", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847396561503, "endTime": 652847429164243}, "additional": {"logType": "info", "children": [], "durationId": "9aa8815a-d3d8-4b25-b214-3c40dfd4c49a", "parent": "c7b449ce-021a-4ae5-a10a-51d6ee8e7b5e"}}, {"head": {"id": "c7b449ce-021a-4ae5-a10a-51d6ee8e7b5e", "name": "eval h<PERSON><PERSON><PERSON><PERSON>", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847386808941, "endTime": 652847429187653}, "additional": {"logType": "info", "children": ["d08d9b25-c588-47dd-9e92-8dbc8c7b2533", "4cbf8235-dd79-4ab5-821c-e9310e492fd9"], "durationId": "7d4d3d1d-e837-49d2-a30c-1f7cb4c44f14", "parent": "146e27ee-aab1-4275-856b-284b5b631aa8"}}, {"head": {"id": "dd466bde-755f-422c-99b8-8c7180dc6ea3", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "duration"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847430357612, "endTime": 652847430373548}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "6294ae78-6ebf-44f0-ad85-586504e6ecd0", "logId": "bdcd4bbf-e8c8-4779-9061-9c1e22407893"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "bdcd4bbf-e8c8-4779-9061-9c1e22407893", "name": "\"afterNodeEvaluate\" hook function", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847430357612, "endTime": 652847430373548}, "additional": {"logType": "info", "children": [], "durationId": "dd466bde-755f-422c-99b8-8c7180dc6ea3", "parent": "146e27ee-aab1-4275-856b-284b5b631aa8"}}, {"head": {"id": "146e27ee-aab1-4275-856b-284b5b631aa8", "name": "eval submodule", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847381215303, "endTime": 652847430392593}, "additional": {"logType": "info", "children": ["53a88b2a-60d1-4763-adc7-0ac9a885433b", "c7b449ce-021a-4ae5-a10a-51d6ee8e7b5e", "bdcd4bbf-e8c8-4779-9061-9c1e22407893"], "durationId": "6294ae78-6ebf-44f0-ad85-586504e6ecd0", "parent": "0d965d8a-eacf-48cc-894f-6793cc8b7c2e"}}, {"head": {"id": "0d965d8a-eacf-48cc-894f-6793cc8b7c2e", "name": "eval modules", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847380750314, "endTime": 652847430409924}, "additional": {"logType": "info", "children": ["146e27ee-aab1-4275-856b-284b5b631aa8"], "durationId": "bf2585fc-bb0d-4756-bed5-879e8b004098", "parent": "433dfc14-80d6-42ba-8f10-d0194c6a4de0"}}, {"head": {"id": "ab1ddec8-7e45-47ca-8ca8-349e9b840cea", "name": "watch files: [\n  '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/hvigorfile.ts',\n  '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/hvigorfile.ts',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor-ohos-plugin/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor-ohos-plugin/src/plugin/factory/plugin-factory.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/log4js.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/debug/src/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/debug/src/node.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/debug/src/common.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/ms/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/rfdc/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/configuration.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/layouts.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/date-format/lib/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/levels.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/clustering.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/LoggingEvent.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/flatted/cjs/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/adapters.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/console.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/stdout.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/stderr.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/logLevelFilter.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/categoryFilter.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/noLogFilter.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/file.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/lib/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/lib/RollingFileWriteStream.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/fs/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/universalify/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/graceful-fs/graceful-fs.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/graceful-fs/polyfills.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/graceful-fs/legacy-streams.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/graceful-fs/clone.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/copy-sync/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/copy-sync/copy-sync.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/mkdirs/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/mkdirs/mkdirs.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/mkdirs/win32.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/mkdirs/mkdirs-sync.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/util/utimes.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/util/stat.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/copy/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/copy/copy.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/path-exists/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/empty/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/remove/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/remove/rimraf.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/file.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/link.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/symlink.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/symlink-paths.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/ensure/symlink-type.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/json/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/json/jsonfile.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/jsonfile/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/json/output-json.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/json/output-json-sync.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/move-sync/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/move-sync/move-sync.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/move/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/move/move.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/node_modules/fs-extra/lib/output/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/lib/now.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/lib/fileNameFormatter.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/lib/fileNameParser.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/lib/moveAndMaybeCompressFile.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/lib/RollingFileStream.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/streamroller/lib/DateRollingFileStream.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/dateFile.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/fileSync.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/tcp.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/categories.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/logger.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/connect-logger.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/log4js/lib/appenders/recording.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/src/common/util/local-file-writer.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/fs/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/universalify/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/copy/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/copy/copy.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/mkdirs/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/mkdirs/make-dir.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/mkdirs/utils.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/path-exists/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/util/utimes.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/util/stat.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/copy/copy-sync.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/empty/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/remove/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/ensure/index.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/ensure/file.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/ensure/link.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/ensure/symlink.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/ensure/symlink-paths.js',\n  '/Users/<USER>/Library/OpenHarmony/command-line-tools/hvigor/hvigor/node_modules/fs-extra/lib/ensure/symlink-type.js',\n  ... 1868 more items\n]", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847482715365}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "162023af-60d7-4947-831a-36617bfe7907", "name": "hvigorfile, resolve hvigorfile dependencies in 185 ms ", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847614801638}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8689d130-d114-4e45-acb9-f45bf898ee13", "name": "add config dependencies", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847430420433, "endTime": 652847615660042}, "additional": {"logType": "info", "children": [], "durationId": "ede1f44c-10f5-47d7-98e1-825fe6db5e79", "parent": "433dfc14-80d6-42ba-8f10-d0194c6a4de0"}}, {"head": {"id": "4f4cd936-0911-428a-b269-d3a3f746feea", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "duration"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847616344739, "endTime": 652847616672192}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "cc76737d-b163-4c92-a421-47d78c3ab947", "logId": "5543f4a0-6075-46ed-a1b6-846ec5ca1be4"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d65a249d-834b-4c3e-8586-bae289538014", "name": "project has submodules:lingxia", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847616403602}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ce25b2bd-b7f3-4f3b-baf2-3fe6a366615b", "name": "module:lingxia no need to execute packageHap", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847616583207}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5543f4a0-6075-46ed-a1b6-846ec5ca1be4", "name": "\"nodesEvaluated\" hook function", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847616344739, "endTime": 652847616672192}, "additional": {"logType": "info", "children": [], "durationId": "4f4cd936-0911-428a-b269-d3a3f746feea", "parent": "433dfc14-80d6-42ba-8f10-d0194c6a4de0"}}, {"head": {"id": "b76f065d-27dc-4a00-bdbc-ea731c511cf2", "name": "start to load updatedOhPackageInfo to the disk", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847618316079}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "87933aff-7695-4bca-8803-7b422aa9b28f", "name": "load to the disk finished", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847625968710}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "955d0ba5-fd07-4949-aa52-c30724638809", "name": "ohpm install", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847616687569, "endTime": 652847626439341}, "additional": {"logType": "info", "children": [], "durationId": "af107e82-d5ab-4a78-91d2-9b1f8605f3c8", "parent": "433dfc14-80d6-42ba-8f10-d0194c6a4de0"}}, {"head": {"id": "95e4178b-3a31-49ac-9041-56b76cde6773", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847626506830}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f04a4863-f19c-441f-8777-a4bf981a0aac", "name": "Module harmony Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847628318939}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f31adaf-974a-4ed6-9d8a-857fa14e9c41", "name": "Module harmony's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847628455243}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8478cbbd-9488-40f0-b5cb-689cade19523", "name": "Start to initialize dependency information.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847629040033}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "846865be-6a30-418b-b542-f98626b7b78a", "name": "Module ling<PERSON> Collected Dependency: ", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847630138107}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "538fc9ff-ecb5-49f2-9466-214ab2e1878e", "name": "Module lingxia's total dependency: 0", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847630278606}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "798ed87c-dc6e-449f-9308-997874474bf1", "name": "eval hook", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847626467921, "endTime": 652847633054312}, "additional": {"logType": "info", "children": [], "durationId": "dd66711c-527f-41c4-8fe4-3c2bf6ff132a", "parent": "433dfc14-80d6-42ba-8f10-d0194c6a4de0"}}, {"head": {"id": "e1ee821a-54d4-4044-adef-652953ddadb7", "name": "Configuration phase cost:567 ms ", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847633116531}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc69b7d7-dcc1-4880-a3ac-a6ac60acbe16", "name": "fin configuration", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847633082672, "endTime": 652847633257382}, "additional": {"logType": "info", "children": [], "durationId": "dcaa1d8e-996c-4414-88f7-bf9d16d432d5", "parent": "433dfc14-80d6-42ba-8f10-d0194c6a4de0"}}, {"head": {"id": "433dfc14-80d6-42ba-8f10-d0194c6a4de0", "name": "configure hvigor plugin", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847055939529, "endTime": 652847633275023}, "additional": {"logType": "info", "children": ["8ddb2a0f-b352-4c6c-9934-21f30f4396bf", "9d339b0a-0f78-47b0-8e16-69a579bbfef6", "9bd9450b-a96f-4e45-b24a-c4bf948923b6", "0d965d8a-eacf-48cc-894f-6793cc8b7c2e", "8689d130-d114-4e45-acb9-f45bf898ee13", "955d0ba5-fd07-4949-aa52-c30724638809", "798ed87c-dc6e-449f-9308-997874474bf1", "cc69b7d7-dcc1-4880-a3ac-a6ac60acbe16", "5543f4a0-6075-46ed-a1b6-846ec5ca1be4"], "durationId": "cc76737d-b163-4c92-a421-47d78c3ab947", "parent": "1a53298f-4d52-48c8-87b2-720f03cdd82d"}}, {"head": {"id": "20992356-c6ba-42c6-b8d7-5e4161931daa", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "duration"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847635067115, "endTime": 652847635088264}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "c3edd99b-11fa-462c-9c02-2c826045c7aa", "logId": "e58bef72-695d-4483-b09d-8b33f0c1bd87"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "e58bef72-695d-4483-b09d-8b33f0c1bd87", "name": "\"taskGraphResolved\" hook function", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847635067115, "endTime": 652847635088264}, "additional": {"logType": "info", "children": [], "durationId": "20992356-c6ba-42c6-b8d7-5e4161931daa", "parent": "1a53298f-4d52-48c8-87b2-720f03cdd82d"}}, {"head": {"id": "5ad6fcf8-018a-4568-b83e-b4ac730d1bfa", "name": "build task graph", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847633310767, "endTime": 652847635106697}, "additional": {"logType": "info", "children": [], "durationId": "6269ef70-0445-4444-825c-90054cc4e140", "parent": "1a53298f-4d52-48c8-87b2-720f03cdd82d"}}, {"head": {"id": "b73c09b9-d4d7-4e64-afa5-027f62928a9c", "name": "init task execution option", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847635113611, "endTime": 652847635121512}, "additional": {"logType": "info", "children": [], "durationId": "f5a2c0be-8b15-429f-b61d-232ca99189c1", "parent": "1a53298f-4d52-48c8-87b2-720f03cdd82d"}}, {"head": {"id": "1a53298f-4d52-48c8-87b2-720f03cdd82d", "name": "init", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652846992155203, "endTime": 652847635127522}, "additional": {"logType": "info", "children": ["2dd8bbea-06f9-486e-bda3-3e32911c4beb", "433dfc14-80d6-42ba-8f10-d0194c6a4de0", "5ad6fcf8-018a-4568-b83e-b4ac730d1bfa", "b73c09b9-d4d7-4e64-afa5-027f62928a9c", "784b00f0-adf1-409a-9619-4be83bfc97a1", "8713bc6d-9387-4610-a805-f76c2ab8f6a8", "e58bef72-695d-4483-b09d-8b33f0c1bd87"], "durationId": "c3edd99b-11fa-462c-9c02-2c826045c7aa"}}, {"head": {"id": "bae40ef1-c766-4a4a-8308-8a723ea7ec6c", "name": "Configuration task cost before running: 655 ms ", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847635487709}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "868bde1e-8b44-42c6-9178-e4e637a62ff5", "name": "lingxia:default@PreBuild", "description": "Pre-build in the stage model.", "type": "duration"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847639919295, "endTime": 652847644227402}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "lingxia", "category": "Verification", "taskRunReasons": [], "detailId": "d4a610bb-72f0-487a-8ce3-ca2d6716a326", "logId": "692c8098-eef9-44bd-92e4-6fc9f8fda366"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d4a610bb-72f0-487a-8ce3-ca2d6716a326", "name": "create lingxia:default@PreBuild task", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847637367345}, "additional": {"logType": "detail", "children": [], "durationId": "868bde1e-8b44-42c6-9178-e4e637a62ff5"}}, {"head": {"id": "47b68857-65f2-4639-b3de-71c499e9ecd3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847637964088}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "28d07b0a-c837-4e54-bb64-42b3635a176f", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847638195712}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b33436d1-c477-46be-8f38-448617619b8b", "name": "Executing task :lingxia:default@PreBuild", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847639934955}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5de4ca57-9713-4998-ba87-da3a04d1486e", "name": "Incremental task lingxia:default@PreBuild pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847643928614}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb299706-0dbb-4f1c-a1e4-396a063f30a5", "name": "lingxia : default@PreBuild cost memory 0.16595458984375", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847644116353}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "692c8098-eef9-44bd-92e4-6fc9f8fda366", "name": "UP-TO-DATE :lingxia:default@PreBuild", "description": "Pre-build in the stage model.", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847639919295, "endTime": 652847644227402}, "additional": {"logType": "info", "children": [], "durationId": "868bde1e-8b44-42c6-9178-e4e637a62ff5"}}, {"head": {"id": "3f4a488a-c684-4fed-b9b7-1c6f2f02620a", "name": "lingxia:default@ProcessOHPackageJson", "description": "Process HAR package.json in the stage model.", "type": "duration"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847647697504, "endTime": 652847651143881}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "lingxia", "category": "Generate", "taskRunReasons": [], "detailId": "9f616d1f-4491-4a42-8348-9617f8a1d38e", "logId": "de28947e-6110-4633-907f-92ca16e87610"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9f616d1f-4491-4a42-8348-9617f8a1d38e", "name": "create lingxia:default@ProcessOHPackageJson task", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847646483229}, "additional": {"logType": "detail", "children": [], "durationId": "3f4a488a-c684-4fed-b9b7-1c6f2f02620a"}}, {"head": {"id": "26c28a85-7da1-4ce9-9eed-d68e1224d67f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847646691949}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7a2bdd22-8596-4cf5-857d-c21f3e3a93c9", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847646779483}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d52bd3e-b741-4f82-89e7-edb7a7e8633c", "name": "Executing task :lingxia:default@ProcessOHPackageJson", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847647707887}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81047a22-d074-4732-982d-54b670c91bf3", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847647867593}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7970163-63ac-48a7-bda2-98c7d1bf0804", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847647959001}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "01a5c2c4-7d3f-4fec-b12b-d939dd5c9a5f", "name": "lingxia : default@ProcessOHPackageJson cost memory 0.05095672607421875", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847650795552}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a3f9a98-2953-4906-a179-3f9e8fa53f89", "name": "runTaskFromQueue task cost before running: 671 ms ", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847651045841}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de28947e-6110-4633-907f-92ca16e87610", "name": "Finished :lingxia:default@ProcessOHPackageJson", "description": "Process HAR package.json in the stage model.", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847647697504, "endTime": 652847651143881, "totalTime": 3303915}, "additional": {"logType": "info", "children": [], "durationId": "3f4a488a-c684-4fed-b9b7-1c6f2f02620a"}}, {"head": {"id": "82eaee14-df8e-41cc-94c4-bb91400632b9", "name": "lingxia:default@CreateHarBuildProfile", "description": "Create the BuildProfile.ets file for the HAR package.", "type": "duration"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847655880431, "endTime": 652847659910325}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "lingxia", "category": "Generate", "taskRunReasons": ["The output file '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/BuildProfile.ets' has been changed."], "detailId": "1b30a994-4670-49ac-9356-a7bd176b49e9", "logId": "a8be6b44-702e-42d3-8879-66e02a715a82"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "1b30a994-4670-49ac-9356-a7bd176b49e9", "name": "create lingxia:default@CreateHarBuildProfile task", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847652421034}, "additional": {"logType": "detail", "children": [], "durationId": "82eaee14-df8e-41cc-94c4-bb91400632b9"}}, {"head": {"id": "412b3c04-47fc-44ed-95cc-f3176ce17ccc", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847652645137}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ca2c7f76-8d12-40c2-a100-740c279b9029", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847653528126}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "073eaa86-545f-43a1-9c59-f09f7e588c2a", "name": "Executing task :lingxia:default@CreateHarBuildProfile", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847655898794}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7dd20ae-7a45-41d5-a73a-da21c0e8569f", "name": "Task 'lingxia:default@CreateHarBuildProfile' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847656257403}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f5ed705-4ce3-41d6-a2af-89812da06b9b", "name": "lingxia:default@CreateHarBuildProfile is not up-to-date, since the output file '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/BuildProfile.ets' has been changed.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847656700034}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2a9e56dc-8aac-439a-b8fb-2a2376763fb1", "name": "Incremental task lingxia:default@CreateHarBuildProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847656848767}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bed91c58-5b01-456f-87e1-6c944c02c4f8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847656969440}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7547b55-cea1-4944-a5d7-18f5a977e4d4", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847657065082}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8b5f9b89-71fa-4d85-a7a9-f1d41d320a2c", "name": "lingxia : default@CreateHarBuildProfile cost memory 0.1035308837890625", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847659517743}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c76415c7-4b23-4724-a315-eda96f659e4d", "name": "runTaskFromQueue task cost before running: 680 ms ", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847659793466}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8be6b44-702e-42d3-8879-66e02a715a82", "name": "Finished :lingxia:default@CreateHarBuildProfile", "description": "Create the BuildProfile.ets file for the HAR package.", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847655880431, "endTime": 652847659910325, "totalTime": 3871358}, "additional": {"logType": "info", "children": [], "durationId": "82eaee14-df8e-41cc-94c4-bb91400632b9"}}, {"head": {"id": "e386fe76-a5d9-433f-ab44-bf59c683ce60", "name": "lingxia:default@CreateHarModuleInfo", "description": "Create the ModuleInfo.ts file for the byte code HAR package.", "type": "duration"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847665303832, "endTime": 652847668681998}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "lingxia", "category": "Generate", "taskRunReasons": ["The output file '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/generated/profile/default/ModuleInfo.ts' does not exist."], "detailId": "ea0c81eb-d57d-4e5a-9d1c-5977d3cd73e1", "logId": "fa5c9bea-09c7-4f84-8d1e-ae1323756b8d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ea0c81eb-d57d-4e5a-9d1c-5977d3cd73e1", "name": "create lingxia:default@CreateHarModuleInfo task", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847662876634}, "additional": {"logType": "detail", "children": [], "durationId": "e386fe76-a5d9-433f-ab44-bf59c683ce60"}}, {"head": {"id": "d5702c23-76d4-4c56-8662-f27157c5fa21", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847663155632}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3a651e6-1afd-4e1b-9ac4-a177e39e7286", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847664544303}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9654f654-f187-4e3c-afe1-ffb7a3a3d43d", "name": "Executing task :lingxia:default@CreateHarModuleInfo", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847665325300}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eee87a7a-27fd-4984-ab49-46665dbebf2f", "name": "Task 'lingxia:default@CreateHarModuleInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847665606096}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d87ffded-339f-4908-8f36-281a224a8398", "name": "lingxia:default@CreateHarModuleInfo is not up-to-date, since the output file '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/generated/profile/default/ModuleInfo.ts' does not exist.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847667076324}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "97be54a5-16d6-463b-a0d8-af87e7e2339d", "name": "Incremental task lingxia:default@CreateHarModuleInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847667306732}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b4a86083-f37f-4a9d-a3cc-51637671859f", "name": "lingxia : default@CreateHarModuleInfo cost memory 0.07030487060546875", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847668345409}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db24c16d-0190-4809-a41b-06c4218aeb4b", "name": "runTaskFromQueue task cost before running: 688 ms ", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847668584204}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fa5c9bea-09c7-4f84-8d1e-ae1323756b8d", "name": "Finished :lingxia:default@CreateHarModuleInfo", "description": "Create the ModuleInfo.ts file for the byte code HAR package.", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847665303832, "endTime": 652847668681998, "totalTime": 3247195}, "additional": {"logType": "info", "children": [], "durationId": "e386fe76-a5d9-433f-ab44-bf59c683ce60"}}, {"head": {"id": "e7b6a696-081a-4354-908f-c2c21cbc419d", "name": "lingxia:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847673120209, "endTime": 652847673716349}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "lingxia", "category": "Native", "taskRunReasons": [], "detailId": "ed60b2dd-865b-47a1-9a1e-618c248e24c9", "logId": "88b5fe55-5eb3-44b3-9b1b-36cc1a8ac572"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "ed60b2dd-865b-47a1-9a1e-618c248e24c9", "name": "create lingxia:default@ConfigureCmake task", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847672412130}, "additional": {"logType": "detail", "children": [], "durationId": "e7b6a696-081a-4354-908f-c2c21cbc419d"}}, {"head": {"id": "e38d5363-47cc-4309-a794-3720b4f98006", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847672710611}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75cec992-af02-4b18-898f-4fcdf28f8218", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847672846631}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f73c334-3776-4861-a1e2-577a717f921c", "name": "Executing task :lingxia:default@ConfigureCmake", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847673132124}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d593c4a0-b22d-470d-a5c3-5c12ed1644b6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847673282375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bf4c7ea2-4ec6-4ae2-a1da-baea158cea8d", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847673361977}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5ab3c14c-cd32-4c8f-bf6b-ae893148dcb4", "name": "lingxia : default@ConfigureCmake cost memory 0.03815460205078125", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847673488037}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "354c527a-1e98-443e-8bfb-d1400dd80be1", "name": "runTaskFromQueue task cost before running: 693 ms ", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847673614522}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88b5fe55-5eb3-44b3-9b1b-36cc1a8ac572", "name": "Finished :lingxia:default@ConfigureCmake", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847673120209, "endTime": 652847673716349, "totalTime": 468728}, "additional": {"logType": "info", "children": [], "durationId": "e7b6a696-081a-4354-908f-c2c21cbc419d"}}, {"head": {"id": "033c8d18-044a-4a67-83e1-2f807ff5c2f4", "name": "lingxia:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847678902408, "endTime": 652847693615915}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "lingxia", "category": "Config", "taskRunReasons": ["The output file '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/merge_profile/default/module.json' has been changed."], "detailId": "54df940d-2419-4265-9f6b-259e5b42d42d", "logId": "3c90d936-5210-4aaa-900f-e14d501ad84f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "54df940d-2419-4265-9f6b-259e5b42d42d", "name": "create lingxia:default@MergeProfile task", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847677366159}, "additional": {"logType": "detail", "children": [], "durationId": "033c8d18-044a-4a67-83e1-2f807ff5c2f4"}}, {"head": {"id": "1d9d56ed-b724-4c05-a3bd-0c380e0ad767", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847677870987}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "84068bdf-e46a-4797-818f-24e62ca663f0", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847678246704}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd6f2531-e9d8-4314-96fd-b718e068b558", "name": "Executing task :lingxia:default@MergeProfile", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847678914284}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c59525ee-5134-45fe-9be3-c530024a8571", "name": "lingxia:default@MergeProfile is not up-to-date, since the output file '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/merge_profile/default/module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847679592866}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "060f9918-65b9-4484-8d7d-0cd16bc8007c", "name": "Incremental task lingxia:default@MergeProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847679736331}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eff1bcc4-c7fb-4595-a36b-5a5aa41de4ba", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847679835524}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f22c859-654e-40f2-957a-65c6b39076fa", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847681160867}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7eeb5727-c5c0-469e-a8f3-cf265682eefb", "name": "Change app api release type with 'Release'", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847683811436}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "490b2f01-7825-4c4c-9961-71da21da8385", "name": "Change app compile API version with '*********'", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847685630516}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6a8e483b-49a7-442b-a417-6df2182f56e7", "name": "Change app target API version with '50005017'", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847686763490}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7346064b-6eae-40d8-8d51-a1ce5329b025", "name": "Change app minimum API version with '50005017'", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847687009218}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9d6af016-4ae7-4854-8006-25a8050a1ada", "name": "Use cli appEnvironment", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847687666482}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7bc6d0fc-a838-4ecf-957b-00d35954e46b", "name": "lingxia : default@MergeProfile cost memory 0.24398040771484375", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847693268739}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "338d1f4a-334b-465c-9b28-f4a4d4068603", "name": "runTaskFromQueue task cost before running: 713 ms ", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847693492576}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3c90d936-5210-4aaa-900f-e14d501ad84f", "name": "Finished :lingxia:default@MergeProfile", "description": "Merge app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847678902408, "endTime": 652847693615915, "totalTime": 14545889}, "additional": {"logType": "info", "children": [], "durationId": "033c8d18-044a-4a67-83e1-2f807ff5c2f4"}}, {"head": {"id": "94c2a0c8-6324-4213-8e56-32e42e16d451", "name": "lingxia:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "duration"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847702417439, "endTime": 652847705528079}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "lingxia", "category": "Generate", "taskRunReasons": ["The output file '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/loader/default/pkgContextInfo.json' does not exist."], "detailId": "f0eb4ae2-f228-4ee2-8644-ab629929b1b4", "logId": "3ff7ecf4-5753-44f8-bcfa-90f06371381f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f0eb4ae2-f228-4ee2-8644-ab629929b1b4", "name": "create lingxia:default@GeneratePkgContextInfo task", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847695757416}, "additional": {"logType": "detail", "children": [], "durationId": "94c2a0c8-6324-4213-8e56-32e42e16d451"}}, {"head": {"id": "83975a6a-de1d-4d3f-a59e-8bf0a7f78a4d", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847696079978}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "db7f6875-ca87-4664-b937-ec01850d9222", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847696221075}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2244b6c9-6846-452c-8a69-a206a01766ce", "name": "Executing task :lingxia:default@GeneratePkgContextInfo", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847702435617}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c022dd1a-389a-4f45-a539-d0ff27d6ba92", "name": "Task 'lingxia:default@GeneratePkgContextInfo' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847702710939}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1cc9069a-ce57-46f6-b833-4ae19538f015", "name": "lingxia:default@GeneratePkgContextInfo is not up-to-date, since the output file '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/loader/default/pkgContextInfo.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847702945655}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "113423cd-b849-4f03-ac28-7c2e7949ab97", "name": "Incremental task lingxia:default@GeneratePkgContextInfo pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847703064318}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9a9b2bc7-a42e-42da-a8a4-2723112196e0", "name": "lingxia : default@GeneratePkgContextInfo cost memory 0.0624542236328125", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847704919421}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac0d0ada-df58-409f-ab67-1c0f1e08985a", "name": "runTaskFromQueue task cost before running: 725 ms ", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847705413463}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ff7ecf4-5753-44f8-bcfa-90f06371381f", "name": "Finished :lingxia:default@GeneratePkgContextInfo", "description": "Generate pkgContextInfo.json to ets-loader.", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847702417439, "endTime": 652847705528079, "totalTime": 2933810}, "additional": {"logType": "info", "children": [], "durationId": "94c2a0c8-6324-4213-8e56-32e42e16d451"}}, {"head": {"id": "9fc33571-30f1-4918-a8f0-48a437b7d1aa", "name": "lingxia:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "duration"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847710242379, "endTime": 652847712603956}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "lingxia", "category": "Sign", "taskRunReasons": ["The output file '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/build/cache/default/integrated_hsp/integratedHspCache.json' does not exist."], "detailId": "3dd9b926-6b11-4224-89d9-461090cfd13f", "logId": "967329cf-9195-486f-a460-af7e31cdd692"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3dd9b926-6b11-4224-89d9-461090cfd13f", "name": "create lingxia:default@ProcessIntegratedHsp task", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847707397192}, "additional": {"logType": "detail", "children": [], "durationId": "9fc33571-30f1-4918-a8f0-48a437b7d1aa"}}, {"head": {"id": "efab37e1-5dfa-4424-9a4b-5d0ce08b3019", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847707694415}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78a4d1c4-c950-49a4-b2cf-3731f51f0886", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847707814941}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bafb866b-dee6-4ca4-a9d0-e96bb12bfc46", "name": "Executing task :lingxia:default@ProcessIntegratedHsp", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847710260126}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c6acf9ac-78fb-492e-abba-aabcf698a7c6", "name": "lingxia:default@ProcessIntegratedHsp is not up-to-date, since the output file '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/build/cache/default/integrated_hsp/integratedHspCache.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847711779197}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a6aff1ff-b01d-4d93-b58f-5673914b49bf", "name": "Incremental task lingxia:default@ProcessIntegratedHsp pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847712013221}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0f0cb291-b3f1-4a99-9745-8f52704412bb", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847712153469}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81fe7488-fd16-4de7-b118-ea458d359d46", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847712230020}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4dfcadf6-6705-4af9-a61f-d7666898333c", "name": "lingxia : default@ProcessIntegratedHsp cost memory 0.1122283935546875", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847712373757}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3aa72f05-b41e-4fd8-ac5c-012cabbe374c", "name": "runTaskFromQueue task cost before running: 732 ms ", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847712508494}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "967329cf-9195-486f-a460-af7e31cdd692", "name": "Finished :lingxia:default@ProcessIntegratedHsp", "description": "Use package tool process integrated hsp.", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847710242379, "endTime": 652847712603956, "totalTime": 2238074}, "additional": {"logType": "info", "children": [], "durationId": "9fc33571-30f1-4918-a8f0-48a437b7d1aa"}}, {"head": {"id": "176daf73-f40c-4d99-920e-db812f00f712", "name": "lingxia:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "duration"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847716108299, "endTime": 652847717969049}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "lingxia", "category": "Native", "taskRunReasons": [], "detailId": "7d9981e2-14c8-4df7-8d66-e4b69a14f2fc", "logId": "42f0967e-1b07-4f8d-9956-846a2788cf39"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7d9981e2-14c8-4df7-8d66-e4b69a14f2fc", "name": "create lingxia:default@BuildNativeWithCmake task", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847714785655}, "additional": {"logType": "detail", "children": [], "durationId": "176daf73-f40c-4d99-920e-db812f00f712"}}, {"head": {"id": "debd5c42-362e-4bd2-9ad5-421245ef4509", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847715068152}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "870a170f-7fa4-4e74-9407-bbec35dd0e4e", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847715188278}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8cb23c11-4bd2-4c3e-b8be-5b9ac416ee05", "name": "Executing task :lingxia:default@BuildNativeWithCmake", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847716137555}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f0b23fa8-a3a3-4f84-a762-11d848222cbd", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847716423458}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1d76ecc4-7c55-43db-8b6c-837fd97c663b", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847716539362}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b07015c1-41bc-4185-bd59-0dfab39c0c40", "name": "lingxia : default@BuildNativeWithCmake cost memory 0.0475616455078125", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847716737773}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4d17a816-1a75-42ad-895b-41a9418aeeef", "name": "runTaskFromQueue task cost before running: 738 ms ", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847717826508}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "42f0967e-1b07-4f8d-9956-846a2788cf39", "name": "Finished :lingxia:default@BuildNativeWithCmake", "description": "Compile CPP source with CMake in the stage model.", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847716108299, "endTime": 652847717969049, "totalTime": 1702437}, "additional": {"logType": "info", "children": [], "durationId": "176daf73-f40c-4d99-920e-db812f00f712"}}, {"head": {"id": "262c8d1e-0c77-430e-9d85-2509dd19cc87", "name": "lingxia:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "duration"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847722790670, "endTime": 652847928195960}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "lingxia", "category": "Config", "taskRunReasons": ["The input file '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/merge_profile/default/module.json' has been changed."], "detailId": "7274b0b7-ae3e-4f61-8b82-47f4e9418c6e", "logId": "9914156f-8f78-4410-ba68-806e5e59bf18"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "7274b0b7-ae3e-4f61-8b82-47f4e9418c6e", "name": "create lingxia:default@ProcessProfile task", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847721425435}, "additional": {"logType": "detail", "children": [], "durationId": "262c8d1e-0c77-430e-9d85-2509dd19cc87"}}, {"head": {"id": "a916a167-5caf-4475-9408-d83d86ed776c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847721776114}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f9f4aa9f-97d0-4883-aea0-736472d10eb8", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847721896389}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c42c3e4e-0903-431f-b104-3dd50735776b", "name": "Executing task :lingxia:default@ProcessProfile", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847722801042}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd77667a-5b67-4a1d-8ce1-63534ab559fe", "name": "lingxia:default@ProcessProfile is not up-to-date, since the input file '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/merge_profile/default/module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847723442100}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29fb6a27-8f4c-43c2-b68f-f36d4434fe9a", "name": "Incremental task lingxia:default@ProcessProfile pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847723595902}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03fc062c-5452-4867-be37-0ee0ae479016", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847723700356}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65ba5201-aeb7-4d0d-857e-f4e55dd61e87", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847723775792}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f91b2c8e-00e1-4346-bfb0-e768cab495c5", "name": "********", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847923339551}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "170ef013-ce3f-4c23-a8b6-8859d59dd6f8", "name": "lingxia : default@ProcessProfile cost memory 0.31446075439453125", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847926812856}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23d8524c-071b-453c-ae16-0e21f1ecdee6", "name": "runTaskFromQueue task cost before running: 947 ms ", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847927033436}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9914156f-8f78-4410-ba68-806e5e59bf18", "name": "Finished :lingxia:default@ProcessProfile", "description": "Process app config manifest files in the stage model.", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847722790670, "endTime": 652847928195960, "totalTime": 204207018}, "additional": {"logType": "info", "children": [], "durationId": "262c8d1e-0c77-430e-9d85-2509dd19cc87"}}, {"head": {"id": "86f820a7-fecb-46e9-b02c-9f661b9e80e7", "name": "lingxia:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "duration"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847931755981, "endTime": 652847936833370}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "lingxia", "category": "Generate", "taskRunReasons": ["The input file '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/loader/default/pkgContextInfo.json' has been changed."], "detailId": "61f70e5a-1df1-4960-87c7-b9ab5190371e", "logId": "9365e5c7-8140-4088-87d0-61ef52d7419a"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "61f70e5a-1df1-4960-87c7-b9ab5190371e", "name": "create lingxia:default@ProcessRouterMap task", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847929940535}, "additional": {"logType": "detail", "children": [], "durationId": "86f820a7-fecb-46e9-b02c-9f661b9e80e7"}}, {"head": {"id": "36d2a130-7ab1-4208-bdcb-d0d3fabe65e8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847930246511}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9457b3ab-9e85-4a10-802e-d3f1e0c019eb", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847930359679}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb1ec334-17d3-4db9-b6e3-547e0a5e5553", "name": "Executing task :lingxia:default@ProcessRouterMap", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847931768754}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "06b845a3-bdf2-4c69-87ac-1c6b281df7ce", "name": "lingxia:default@ProcessRouterMap is not up-to-date, since the input file '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/loader/default/pkgContextInfo.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847934115206}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c58cdf4-4267-4ca6-a482-7908c61932ba", "name": "Incremental task lingxia:default@ProcessRouterMap pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847934273123}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "43d93219-00ee-482a-a229-535a42421598", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847934463732}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec8baf0e-0894-4680-94be-1e6c46069d20", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847934550662}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "689e6be5-2586-46a4-b8e8-328629a7e47a", "name": "lingxia : default@ProcessRouterMap cost memory 0.19377899169921875", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847934705276}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "73c9765f-925f-4273-815b-5fbbb20ef4d2", "name": "runTaskFromQueue task cost before running: 956 ms ", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847935790477}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9365e5c7-8140-4088-87d0-61ef52d7419a", "name": "Finished :lingxia:default@ProcessRouterMap", "description": "Process router map configuration.", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847931755981, "endTime": 652847936833370, "totalTime": 3966292}, "additional": {"logType": "info", "children": [], "durationId": "86f820a7-fecb-46e9-b02c-9f661b9e80e7"}}, {"head": {"id": "28022e94-5452-4eed-81ae-79066a306f29", "name": "lingxia:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "duration"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847941315133, "endTime": 652847943999223}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "lingxia", "category": "Native", "taskRunReasons": [], "detailId": "6f0e498a-a0d8-46f3-a472-15e40ee955a5", "logId": "3f552a2c-5928-46f6-a126-7311850fbc8d"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6f0e498a-a0d8-46f3-a472-15e40ee955a5", "name": "create lingxia:default@BuildNativeWithNinja task", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847940207912}, "additional": {"logType": "detail", "children": [], "durationId": "28022e94-5452-4eed-81ae-79066a306f29"}}, {"head": {"id": "c367ff21-e1e9-4293-bf09-319849d73d9f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847940463711}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2dc9a523-278d-49ad-bf7b-f15cfc7f01fd", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847940586062}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "39252b8e-a915-4246-998d-3bf56c33903c", "name": "Executing task :lingxia:default@BuildNativeWithNinja", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847941327548}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ce7bec1-91c3-4e9c-8e17-e7384a9d0cf6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847941475146}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5af8c4be-ae6d-4cc9-9b3c-682426f05a73", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847942457112}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dd1d8e18-4e61-49e1-bf39-a00bcb4b119b", "name": "lingxia : default@BuildNativeWithNinja cost memory 0.0529937744140625", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847943679905}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dee5b2e5-a6b7-45fb-b29a-19e1bb933cb3", "name": "runTaskFromQueue task cost before running: 964 ms ", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847943907465}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3f552a2c-5928-46f6-a126-7311850fbc8d", "name": "Finished :lingxia:default@BuildNativeWithNinja", "description": "Compile CPP source with Ninja in the stage model.", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847941315133, "endTime": 652847943999223, "totalTime": 2547886}, "additional": {"logType": "info", "children": [], "durationId": "28022e94-5452-4eed-81ae-79066a306f29"}}, {"head": {"id": "8ef50c3a-f533-46c8-9ccc-b6db952ef45a", "name": "lingxia:default@ProcessResource", "description": "Process resources in the stage model.", "type": "duration"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847948838337, "endTime": 652847961453064}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "lingxia", "category": "Resources", "taskRunReasons": ["The output file '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/res/default/resConfig.json' does not exist."], "detailId": "61614b72-8aa8-4e9f-a44d-5373cbaca2f3", "logId": "ff317767-2b35-4d5d-95a5-551a2c7d74ad"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "61614b72-8aa8-4e9f-a44d-5373cbaca2f3", "name": "create lingxia:default@ProcessResource task", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847946683908}, "additional": {"logType": "detail", "children": [], "durationId": "8ef50c3a-f533-46c8-9ccc-b6db952ef45a"}}, {"head": {"id": "d9bde89a-e3fe-4dab-a35f-81ec9d329d74", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847946953843}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5d1d696-0eae-4ee7-a1d9-38ad9c261509", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847947059046}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7351c002-6e24-4e90-bd8d-b071c1d58778", "name": "restool module names: lingxia; moduleName=lingxia, taskName=default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847947842126}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "34b0c341-0148-4d9c-afb0-5c46a7648cc2", "name": "Executing task :lingxia:default@ProcessResource", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847954422304}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "70990dad-ed63-4872-b0ac-99843fd2336d", "name": "lingxia:default@ProcessResource is not up-to-date, since the output file '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/res/default/resConfig.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847955399281}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d23c01d2-dbc5-44b6-bcb3-d7e0e6d13acb", "name": "Incremental task lingxia:default@ProcessResource pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847955516003}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd44524c-5a13-4685-a03c-a07115bfa96f", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847956643764}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec08dd96-4b94-47c8-ae6e-57bea6997327", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847956794225}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0cac886a-3433-42ce-a104-063d9e2f368b", "name": "lingxia : default@ProcessResource cost memory -5.3513031005859375", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847959454332}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5c9a403c-dfe0-49b3-ad69-aae7dd400246", "name": "runTaskFromQueue task cost before running: 981 ms ", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847961200850}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ff317767-2b35-4d5d-95a5-551a2c7d74ad", "name": "Finished :lingxia:default@ProcessResource", "description": "Process resources in the stage model.", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847948838337, "endTime": 652847961453064, "totalTime": 11425960}, "additional": {"logType": "info", "children": [], "durationId": "8ef50c3a-f533-46c8-9ccc-b6db952ef45a"}}, {"head": {"id": "f4cfdaf2-6cb0-476c-90eb-b16538f4fc3a", "name": "lingxia:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "duration"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847967689455, "endTime": 652847987092426}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "lingxia", "category": "Generate", "taskRunReasons": ["The input file '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/loader/default/pkgContextInfo.json' has been changed."], "detailId": "8b2e8a65-760a-4a43-9f45-ca99c6956ce8", "logId": "a91c8e5e-71d4-4121-8817-209d2dee173e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8b2e8a65-760a-4a43-9f45-ca99c6956ce8", "name": "create lingxia:default@GenerateLoaderJson task", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847965230186}, "additional": {"logType": "detail", "children": [], "durationId": "f4cfdaf2-6cb0-476c-90eb-b16538f4fc3a"}}, {"head": {"id": "b780d2ee-684e-443b-831c-62f811719378", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847965462345}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e907ae08-353f-4d1e-8953-ca0830494916", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847965567972}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "61042b39-9dcd-424c-a9e9-ac49e6c4264d", "name": "Executing task :lingxia:default@GenerateLoaderJson", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847967702710}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ed22fe92-2a88-4eda-a9d8-186109ef0e87", "name": "lingxia:default@GenerateLoaderJson is not up-to-date, since the input file '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/loader/default/pkgContextInfo.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847975074482}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7ddfcbc-dd34-45bd-bbae-a391ea980cfb", "name": "Incremental task lingxia:default@GenerateLoaderJson pre-execution cost: 4 ms .", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847975258999}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04e3792c-8114-4f37-9e97-d2342de40c14", "name": "lingxia : default@GenerateLoaderJson cost memory 0.575897216796875", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847986368337}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "78212122-3321-43cb-aa4a-29c81e990281", "name": "runTaskFromQueue task cost before running: 1 s 7 ms ", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847986865918}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a91c8e5e-71d4-4121-8817-209d2dee173e", "name": "Finished :lingxia:default@GenerateLoaderJson", "description": "Generate loader.json in the stage model.", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847967689455, "endTime": 652847987092426, "totalTime": 18952653}, "additional": {"logType": "info", "children": [], "durationId": "f4cfdaf2-6cb0-476c-90eb-b16538f4fc3a"}}, {"head": {"id": "b70cc5d4-2b94-4f21-8e8d-c4649665ec66", "name": "lingxia:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "duration"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847992210420, "endTime": 652847995045975}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "lingxia", "category": "Resources", "taskRunReasons": [], "detailId": "6bfbfc9d-7e06-488d-b884-e7e106281a3b", "logId": "ef885404-56be-4891-9e83-c70089f4b21f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6bfbfc9d-7e06-488d-b884-e7e106281a3b", "name": "create lingxia:default@ProcessLibs task", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847991212200}, "additional": {"logType": "detail", "children": [], "durationId": "b70cc5d4-2b94-4f21-8e8d-c4649665ec66"}}, {"head": {"id": "529b7500-8c37-4760-98bc-61552412ae1c", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847991435367}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2f19f72c-360d-4c64-b4c9-da23e1db15e6", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847991533740}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a85049f7-7f8b-4dd4-9f4f-4beb0d782315", "name": "Executing task :lingxia:default@ProcessLibs", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847992227289}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d4097ddc-856e-4655-8aae-951adaddab4e", "name": "Incremental task lingxia:default@ProcessLibs pre-execution cost: 2 ms .", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847994622418}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7185347b-1629-4c7e-be27-89e580fb27e0", "name": "lingxia : default@ProcessLibs cost memory 0.10274505615234375", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847994923085}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ef885404-56be-4891-9e83-c70089f4b21f", "name": "UP-TO-DATE :lingxia:default@ProcessLibs", "description": "Process .so files in the stage model.", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847992210420, "endTime": 652847995045975}, "additional": {"logType": "info", "children": [], "durationId": "b70cc5d4-2b94-4f21-8e8d-c4649665ec66"}}, {"head": {"id": "621f86bf-91f8-4755-bc61-b088421361e9", "name": "lingxia:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "duration"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847998935961, "endTime": 652848000995966}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "lingxia", "category": "Resources", "taskRunReasons": ["The input file '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/process_profile/default/module.json' has been changed."], "detailId": "95004cea-bfe3-47ac-9838-002698d5b268", "logId": "554aee76-6bce-4ea6-bcdf-a43fdfe2c548"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "95004cea-bfe3-47ac-9838-002698d5b268", "name": "create lingxia:default@CompileResource task", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847996370896}, "additional": {"logType": "detail", "children": [], "durationId": "621f86bf-91f8-4755-bc61-b088421361e9"}}, {"head": {"id": "862f465a-18df-4a86-84de-f4beddd10624", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847996583786}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7ac215e-7a47-40b5-8ac6-e1e7b71b1edf", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847996663853}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5d8400ca-d180-44e5-af06-a4c9e94488ea", "name": "restool module names: lingxia; moduleName=lingxia, taskName=default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847997241452}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1e604bd-3c18-4e55-938c-80f991c83561", "name": "Executing task :lingxia:default@CompileResource", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847998996515}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b021c368-fd2c-4083-9ed7-470a4dd51ef6", "name": "lingxia:default@CompileResource is not up-to-date, since the input file '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/process_profile/default/module.json' has been changed.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848000216323}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3a9118f7-9581-477b-a8cf-825446aded0d", "name": "Incremental task lingxia:default@CompileResource pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848000351323}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d7b15d73-789e-4d7a-b675-d8c83fa78480", "name": "lingxia : default@CompileResource cost memory 0.1045684814453125", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848000780760}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eb53783b-31ab-44b6-8d16-cf7024a5600e", "name": "runTaskFromQueue task cost before running: 1 s 21 ms ", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848000931723}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "554aee76-6bce-4ea6-bcdf-a43fdfe2c548", "name": "Finished :lingxia:default@CompileResource", "description": "Compile project resources in the stage model.", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652847998935961, "endTime": 652848000995966, "totalTime": 1940338}, "additional": {"logType": "info", "children": [], "durationId": "621f86bf-91f8-4755-bc61-b088421361e9"}}, {"head": {"id": "a9e42ca9-0611-441f-b66a-c300a15c7cc6", "name": "lingxia:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "duration"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848005485366, "endTime": 652848006563098}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "lingxia", "category": "Native", "taskRunReasons": [], "detailId": "cb4f3ab0-faeb-498f-9e1c-a14318f8a6af", "logId": "fd7a76a1-3360-48d1-9a28-f5f81da9f7a6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "cb4f3ab0-faeb-498f-9e1c-a14318f8a6af", "name": "create lingxia:default@DoNativeStrip task", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848002563789}, "additional": {"logType": "detail", "children": [], "durationId": "a9e42ca9-0611-441f-b66a-c300a15c7cc6"}}, {"head": {"id": "005020a1-57d0-4760-9612-eabad0bc5f7b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848002782328}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "36b87a0d-65c1-4a6e-928f-2d61fb5a04c3", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848002858004}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e7a30ad1-abbb-4159-a33c-640a0619eba4", "name": "Executing task :lingxia:default@DoNativeStrip", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848005520694}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e15ce56d-ee7e-4690-97e5-aaa6ce531193", "name": "Task 'lingxia:default@DoNativeStrip' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848005719146}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89b5b930-5440-4256-8f6d-2a8f1af05665", "name": "Incremental task lingxia:default@DoNativeStrip pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848006340834}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b28eecff-cc86-4815-950a-44164988a8c6", "name": "lingxia : default@DoNativeStrip cost memory 0.0629425048828125", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848006475788}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd7a76a1-3360-48d1-9a28-f5f81da9f7a6", "name": "UP-TO-DATE :lingxia:default@DoNativeStrip", "description": "Strip .so files to decrease size.", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848005485366, "endTime": 652848006563098}, "additional": {"logType": "info", "children": [], "durationId": "a9e42ca9-0611-441f-b66a-c300a15c7cc6"}}, {"head": {"id": "6a8cfaea-8dec-4908-b9ac-0f6c55431859", "name": "lingxia:default@ProcessObfuscationFiles", "description": "", "type": "duration"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848008603887, "endTime": 652848010184789}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "lingxia", "category": "Other", "taskRunReasons": [], "detailId": "0e2da447-fbc2-4402-a064-0d458eb97dff", "logId": "6ac2571e-96d9-4d8d-8742-e313bd1e8475"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0e2da447-fbc2-4402-a064-0d458eb97dff", "name": "create lingxia:default@ProcessObfuscationFiles task", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848007698133}, "additional": {"logType": "detail", "children": [], "durationId": "6a8cfaea-8dec-4908-b9ac-0f6c55431859"}}, {"head": {"id": "7573cb7b-1e92-40ad-a84e-4047c784145b", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848007917967}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6e5875ac-e9bf-4136-bd15-30e3a3f8fa48", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848007988167}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "88840c8f-dcf8-419b-919e-d2d27a397f25", "name": "Executing task :lingxia:default@ProcessObfuscationFiles", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848008616065}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0c53de9c-4617-406c-a86f-112d0bc08815", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848008821295}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "11f5138b-326c-4bdd-9974-897286661ce2", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848008911543}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "5f14c7d9-7485-4194-a698-e94d0630aeb3", "name": "lingxia : default@ProcessObfuscationFiles cost memory 0.03820037841796875", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848009134579}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1a7b581-2948-439f-a3c3-a7e62791bc24", "name": "runTaskFromQueue task cost before running: 1 s 29 ms ", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848009310948}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6ac2571e-96d9-4d8d-8742-e313bd1e8475", "name": "Finished :lingxia:default@ProcessObfuscationFiles", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848008603887, "endTime": 652848010184789, "totalTime": 647956}, "additional": {"logType": "info", "children": [], "durationId": "6a8cfaea-8dec-4908-b9ac-0f6c55431859"}}, {"head": {"id": "d9c3a37a-eb29-46c8-a37f-8c5261207d50", "name": "lingxia:default@HarCompileArkTS", "description": "Compile ArkTS components for packaged HAR files in the stage model.", "type": "duration"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848016356413, "endTime": 652856624206591}, "additional": {"children": ["147f573a-d66c-419d-9322-b206a8d5e5d3", "9f4e60a4-2ed8-46de-9d89-791e5853212c"], "state": "success", "targetName": "default", "moduleName": "lingxia", "category": "ArkTS", "taskRunReasons": ["The input file '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/loader/default' has been changed."], "detailId": "30eadcb5-d16c-48b8-b646-72b0a1295ddc", "logId": "17bb0fbd-d075-4c85-8596-863408331fcc"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "30eadcb5-d16c-48b8-b646-72b0a1295ddc", "name": "create lingxia:default@HarCompileArkTS task", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848012286367}, "additional": {"logType": "detail", "children": [], "durationId": "d9c3a37a-eb29-46c8-a37f-8c5261207d50"}}, {"head": {"id": "caf73029-38b0-44f7-9d50-b77b8519d677", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848012578995}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d680838-3716-4e9f-8dc8-1480dac1580a", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848012708523}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bb453c65-e91f-426a-b5bb-efff54226b0f", "name": "Executing task :lingxia:default@HarCompileArkTS", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848016373478}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "934b57b4-5a4e-417c-9e08-9f427dc64a2d", "name": "lingxia:default@HarCompileArkTS is not up-to-date, since the input file '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/loader/default' has been changed.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848026716341}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8fb723d4-7605-4372-b07c-7d612af493d2", "name": "Incremental task lingxia:default@HarCompileArkTS pre-execution cost: 8 ms .", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848026913375}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8d3452ed-273b-4b4b-a6b2-ba9f48f70392", "name": "build config:", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848046597814}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "03cf4666-ef08-4264-8cd9-fd9ce30ef543", "name": "Compile arkts with external api path: /Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/hms/ets", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848048997911}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7c0a976b-edb8-4429-b910-4e332076628c", "name": "default@HarCompileArkTS work[5] is submitted.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848050713202}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "147f573a-d66c-419d-9322-b206a8d5e5d3", "name": "lingxia:default@HarCompileArkTS", "description": "Compile ArkTS components for packaged HAR files in the stage model.", "type": "duration"}, "body": {"pid": 62120, "tid": "Worker4", "startTime": 652848145456748, "endTime": 652856611871948}, "additional": {"children": ["de8b5621-affa-4d75-b6d4-c9568e0f3cfa", "fe525cfb-d0d0-49f3-bb07-993f75da41f2", "8759aba5-fbbd-4d27-b7ab-b666222f4f16", "8d45884f-9c5a-4fc1-8d42-59d0a2877ad7", "39e83f86-444f-4089-9ee3-887b6dbadd8d", "16d90e5d-152b-4341-b25e-d9ac68b3dc49"], "state": "success", "targetName": "default", "moduleName": "lingxia", "category": "", "taskRunReasons": [], "parent": "d9c3a37a-eb29-46c8-a37f-8c5261207d50", "logId": "35de1033-07a5-4bd7-9d1f-61c6f9243b06"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3049eedf-4884-4c90-870a-e00e4ac9d72b", "name": "default@HarCompileArkTS work[5] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848052223937}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6eba856b-59c7-4ede-9e2d-be73855ff069", "name": "A work dispatched to worker[7] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848053183939}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1f368473-ce4c-46dd-b153-bcdb00fc8d2a", "name": "A work dispatched to worker[6] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848053551731}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "56eacbbd-55d8-4870-a73a-344aae1f05e5", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848053739339}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0e4e12cb-c884-4bae-a9b6-5a74d5ae64bc", "name": "default@HarCompileArkTS work[5] has been dispatched to worker[4].", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848055114048}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ea42dc1-5c57-4d00-b483-ebe38db06eb2", "name": "default@HarCompileArkTS work[5] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848055564388}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1df46ce9-e435-4f15-8ba0-33f39f56a75c", "name": "CopyResources startTime: 652848055740126", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848055745070}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d1a0b80e-f182-4826-a30d-47d55e122dd0", "name": "default@HarCompileArkTS work[6] is submitted.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848055875398}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9f4e60a4-2ed8-46de-9d89-791e5853212c", "name": "lingxia:default@HarCompileArkTS", "description": "Compile ArkTS components for packaged HAR files in the stage model.", "type": "duration"}, "body": {"pid": 62120, "tid": "Worker6", "startTime": 652849168461527, "endTime": 652849184684025}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "lingxia", "category": "", "taskRunReasons": [], "parent": "d9c3a37a-eb29-46c8-a37f-8c5261207d50", "logId": "69f45090-c922-4e7e-8fc8-21c5718b4b4f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6771cc94-8900-42e7-a4b8-a1c1db40d172", "name": "default@HarCompileArkTS work[6] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848056800791}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "291d57b8-5cb5-4ae8-95fc-890ad26d4fa6", "name": "A work dispatched to worker[7] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848056931608}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "77eb7c91-efe3-48aa-ae58-92d423e78d6f", "name": "Create  resident worker with id: 6.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848057792341}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "486bc582-ff99-442f-94e7-e17575d745e3", "name": "default@HarCompileArkTS work[6] has been dispatched to worker[6].", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848063156060}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89031947-6789-45f0-bf07-1a0743dd3770", "name": "default@HarCompileArkTS work[6] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848063340116}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5ae5b3d-1cbe-4e82-8623-f631c7b8a9c5", "name": "lingxia : default@HarCompileArkTS cost memory 1.3804550170898438", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848063561666}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "307e4d38-9817-4f8a-b7c0-570bcbe5481a", "name": "lingxia:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848067986671, "endTime": 652850940248320}, "additional": {"children": ["d787c425-6d07-4189-b3e7-dc7d12feba37"], "state": "success", "targetName": "default", "moduleName": "lingxia", "category": "Native", "taskRunReasons": ["The output file '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/patch/default/base_native_libs.json' does not exist."], "detailId": "b6015692-b549-4eb9-b685-a05f74410fe0", "logId": "75539c71-5a65-4f73-afb5-0c41d62e4a24"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b6015692-b549-4eb9-b685-a05f74410fe0", "name": "create lingxia:default@CacheNativeLibs task", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848065213720}, "additional": {"logType": "detail", "children": [], "durationId": "307e4d38-9817-4f8a-b7c0-570bcbe5481a"}}, {"head": {"id": "ab4b42c8-51c6-479c-a818-548e3bbe1f64", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848065471755}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b0e717ce-32fd-43b1-b3eb-635d5f8537e8", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848065595530}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d56c249c-a422-42cf-ac8b-d524a41c0045", "name": "Executing task :lingxia:default@CacheNativeLibs", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848068009786}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4ec5a20d-e629-42e4-b5b0-03730a20284c", "name": "Task 'lingxia:default@CacheNativeLibs' cost while wrapping incremental declarations: 1 ms ", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848068253972}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3df57b5e-9f61-4a49-b369-c3fbb1e0130f", "name": "lingxia:default@CacheNativeLibs is not up-to-date, since the output file '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/patch/default/base_native_libs.json' does not exist.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848068865390}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d87e6804-4795-4a6e-bcff-3e2615012091", "name": "Incremental task lingxia:default@CacheNativeLibs pre-execution cost: 1 ms .", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848069034304}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d3430ba-00ab-4c29-b724-a8516ef8c560", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848069280308}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "99c936fa-def2-4560-835d-af0006207ceb", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848069453711}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "54e3c14e-90f5-430d-bc07-27df979c660f", "name": "default@CacheNativeLibs work[7] is submitted.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848070758206}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d787c425-6d07-4189-b3e7-dc7d12feba37", "name": "lingxia:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "duration"}, "body": {"pid": 62120, "tid": "Worker5", "startTime": 652850921918950, "endTime": 652850939518528}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "lingxia", "category": "", "taskRunReasons": [], "parent": "307e4d38-9817-4f8a-b7c0-570bcbe5481a", "logId": "517e2382-01f1-47a6-b1fe-d03b19d9c7d5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "567a8564-0a42-421c-b26b-00e585166358", "name": "default@CacheNativeLibs work[7] is pushed to ready queue.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848074018853}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0689e81e-3a2a-4653-867f-d7c91bc9a66e", "name": "A work dispatched to worker[7] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848075720437}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4c71dc43-b4b8-44fc-8082-ea9b253549dc", "name": "A work dispatched to worker[6] failed because of worker busy.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848075862311}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b942cbc8-347d-4adb-a7bd-33aede0f8071", "name": "Create  resident worker with id: 5.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848075980425}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bcb332b3-33c5-4e0b-be76-547b2f94a08c", "name": "default@CacheNativeLibs work[7] has been dispatched to worker[5].", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848078510383}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bc342c44-e317-4306-b4e7-52666a6c2035", "name": "default@CacheNativeLibs work[7] is dispatched.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848078722795}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cd664cac-e59d-4ac8-bd4f-b72cc7a711d7", "name": "lingxia : default@CacheNativeLibs cost memory 0.287139892578125", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848078930944}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e4cbc0ca-faa7-4618-a4ab-bb2b6aff798e", "name": "worker[6] has one work done.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652849185027827}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ec0ae2af-20d4-4a78-9a39-2b38fa997623", "name": "CopyResources is end, endTime: 652849185260029", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652849185267493}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "29862150-4878-4760-b1bf-28e803daa471", "name": "default@HarCompileArkTS work[6] done.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652849185500514}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "69f45090-c922-4e7e-8fc8-21c5718b4b4f", "name": "lingxia:default@HarCompileArkTS", "description": "Compile ArkTS components for packaged HAR files in the stage model.", "type": "log"}, "body": {"pid": 62120, "tid": "Worker6", "startTime": 652849168461527, "endTime": 652849184684025}, "additional": {"logType": "info", "children": [], "durationId": "9f4e60a4-2ed8-46de-9d89-791e5853212c", "parent": "17bb0fbd-d075-4c85-8596-863408331fcc"}}, {"head": {"id": "c8d55201-ac57-4d7b-9ebb-056236ea48b0", "name": "A work dispatched to worker[6] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652849185902080}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c610fc4-5432-4e29-8604-dc10c2428b88", "name": "worker[5] has one work done.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652850939753505}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b1ad807-f7e4-47a5-90fa-7f04b8ad1675", "name": "default@CacheNativeLibs work[7] done.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652850939980625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "517e2382-01f1-47a6-b1fe-d03b19d9c7d5", "name": "lingxia:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 62120, "tid": "Worker5", "startTime": 652850921918950, "endTime": 652850939518528}, "additional": {"logType": "info", "children": [], "durationId": "d787c425-6d07-4189-b3e7-dc7d12feba37", "parent": "75539c71-5a65-4f73-afb5-0c41d62e4a24"}}, {"head": {"id": "9b6cdda3-d1b1-4710-89af-931f9ec7e144", "name": "A work dispatched to worker[5] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652850940097598}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "75539c71-5a65-4f73-afb5-0c41d62e4a24", "name": "Finished :lingxia:default@CacheNativeLibs", "description": "cache native strip .so fileInfo", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848067986671, "endTime": 652850940248320, "totalTime": 28658577}, "additional": {"logType": "info", "children": ["517e2382-01f1-47a6-b1fe-d03b19d9c7d5"], "durationId": "307e4d38-9817-4f8a-b7c0-570bcbe5481a"}}, {"head": {"id": "da3a8fbc-d0e4-4bd0-80f3-7ec54ce9aa56", "name": "worker[4] has one work done.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856615708866}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de8b5621-affa-4d75-b6d4-c9568e0f3cfa", "name": "generate configuration information", "description": "", "type": "duration"}, "body": {"pid": 62120, "tid": "Worker4", "startTime": 652848214022867, "endTime": 652848364941787}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "147f573a-d66c-419d-9322-b206a8d5e5d3", "logId": "0da083c1-50b7-4d7c-8fd3-58e944eb699e"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0da083c1-50b7-4d7c-8fd3-58e944eb699e", "name": "generate configuration information", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848214022867, "endTime": 652848364941787}, "additional": {"logType": "info", "children": [], "durationId": "de8b5621-affa-4d75-b6d4-c9568e0f3cfa", "parent": "35de1033-07a5-4bd7-9d1f-61c6f9243b06"}}, {"head": {"id": "fe525cfb-d0d0-49f3-bb07-993f75da41f2", "name": "read build package cache", "description": "", "type": "duration"}, "body": {"pid": 62120, "tid": "Worker4", "startTime": 652848378214024, "endTime": 652848378417183}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "147f573a-d66c-419d-9322-b206a8d5e5d3", "logId": "70c582fe-0125-497c-b980-43a14cc0fd83"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "70c582fe-0125-497c-b980-43a14cc0fd83", "name": "read build package cache", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848378214024, "endTime": 652848378417183}, "additional": {"logType": "info", "children": [], "durationId": "fe525cfb-d0d0-49f3-bb07-993f75da41f2", "parent": "35de1033-07a5-4bd7-9d1f-61c6f9243b06"}}, {"head": {"id": "8759aba5-fbbd-4d27-b7ab-b666222f4f16", "name": "compile ArkTS with rollup", "description": "", "type": "duration"}, "body": {"pid": 62120, "tid": "Worker4", "startTime": 652848379596321, "endTime": 652856435254022}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "147f573a-d66c-419d-9322-b206a8d5e5d3", "logId": "0164fccc-a44f-4874-b3e7-3740c5e594a6"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0164fccc-a44f-4874-b3e7-3740c5e594a6", "name": "compile ArkTS with rollup", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848379596321, "endTime": 652856435254022}, "additional": {"logType": "info", "children": [], "durationId": "8759aba5-fbbd-4d27-b7ab-b666222f4f16", "parent": "35de1033-07a5-4bd7-9d1f-61c6f9243b06"}}, {"head": {"id": "8d45884f-9c5a-4fc1-8d42-59d0a2877ad7", "name": "process importees", "description": "", "type": "duration"}, "body": {"pid": 62120, "tid": "Worker4", "startTime": 652856435302882, "endTime": 652856438651026}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "147f573a-d66c-419d-9322-b206a8d5e5d3", "logId": "c3764309-6404-4927-80b3-ea5e7c7a5cdf"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c3764309-6404-4927-80b3-ea5e7c7a5cdf", "name": "process importees", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856435302882, "endTime": 652856438651026}, "additional": {"logType": "info", "children": [], "durationId": "8d45884f-9c5a-4fc1-8d42-59d0a2877ad7", "parent": "35de1033-07a5-4bd7-9d1f-61c6f9243b06"}}, {"head": {"id": "39e83f86-444f-4089-9ee3-887b6dbadd8d", "name": "write build package cache", "description": "", "type": "duration"}, "body": {"pid": 62120, "tid": "Worker4", "startTime": 652856438680124, "endTime": 652856503546639}, "additional": {"children": ["a9737a2d-734c-4f34-af05-99e1f0ef4627", "19ed7fd9-6d25-43e0-a61b-e8ad8fbefd1a", "8cc8baf8-49ee-453d-9458-f2daafdc1c81"], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "147f573a-d66c-419d-9322-b206a8d5e5d3", "logId": "f6447d0a-da1b-4a5d-9c12-065fa65efcbd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "f6447d0a-da1b-4a5d-9c12-065fa65efcbd", "name": "write build package cache", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856438680124, "endTime": 652856503546639}, "additional": {"logType": "info", "children": ["9d029e67-55b5-42a3-b401-09dd3261dd97", "b06d7c29-2f67-4a97-838e-66829e34e1ad", "6ab93ce4-10d2-492a-a052-f12269898a73"], "durationId": "39e83f86-444f-4089-9ee3-887b6dbadd8d", "parent": "35de1033-07a5-4bd7-9d1f-61c6f9243b06"}}, {"head": {"id": "a9737a2d-734c-4f34-af05-99e1f0ef4627", "name": "get final cache", "description": "", "type": "duration"}, "body": {"pid": 62120, "tid": "Worker4", "startTime": 652856438810224, "endTime": 652856438884462}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "39e83f86-444f-4089-9ee3-887b6dbadd8d", "logId": "9d029e67-55b5-42a3-b401-09dd3261dd97"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9d029e67-55b5-42a3-b401-09dd3261dd97", "name": "get final cache", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856438810224, "endTime": 652856438884462}, "additional": {"logType": "info", "children": [], "durationId": "a9737a2d-734c-4f34-af05-99e1f0ef4627", "parent": "f6447d0a-da1b-4a5d-9c12-065fa65efcbd"}}, {"head": {"id": "19ed7fd9-6d25-43e0-a61b-e8ad8fbefd1a", "name": "pack cache", "description": "", "type": "duration"}, "body": {"pid": 62120, "tid": "Worker4", "startTime": 652856438893175, "endTime": 652856496358035}, "additional": {"children": ["c8764bf7-1ef5-491a-b1d4-ffb791f5fc9c", "d45dd16e-b099-4787-b357-93c42ae3cdf2", "9c8372fa-7ba0-4d0f-91a1-683f85169357"], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "39e83f86-444f-4089-9ee3-887b6dbadd8d", "logId": "b06d7c29-2f67-4a97-838e-66829e34e1ad"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "b06d7c29-2f67-4a97-838e-66829e34e1ad", "name": "pack cache", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856438893175, "endTime": 652856496358035}, "additional": {"logType": "info", "children": ["d2ae5435-1eb5-4edc-a3ab-16f82d4df1c5", "9fdee317-4d2f-489d-be30-27e0afe6e27f", "8b058134-422a-4318-b2fc-d47b683f4653"], "durationId": "19ed7fd9-6d25-43e0-a61b-e8ad8fbefd1a", "parent": "f6447d0a-da1b-4a5d-9c12-065fa65efcbd"}}, {"head": {"id": "c8764bf7-1ef5-491a-b1d4-ffb791f5fc9c", "name": "module 'LxApp.ets' pack", "description": "", "type": "duration"}, "body": {"pid": 62120, "tid": "Worker4", "startTime": 652856439057714, "endTime": 652856451966631}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "19ed7fd9-6d25-43e0-a61b-e8ad8fbefd1a", "logId": "d2ae5435-1eb5-4edc-a3ab-16f82d4df1c5"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "d2ae5435-1eb5-4edc-a3ab-16f82d4df1c5", "name": "module 'LxApp.ets' pack", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856439057714, "endTime": 652856451966631}, "additional": {"logType": "info", "children": [], "durationId": "c8764bf7-1ef5-491a-b1d4-ffb791f5fc9c", "parent": "b06d7c29-2f67-4a97-838e-66829e34e1ad"}}, {"head": {"id": "d45dd16e-b099-4787-b357-93c42ae3cdf2", "name": "module 'NavigationBar.ets' pack", "description": "", "type": "duration"}, "body": {"pid": 62120, "tid": "Worker4", "startTime": 652856452008901, "endTime": 652856463394928}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "19ed7fd9-6d25-43e0-a61b-e8ad8fbefd1a", "logId": "9fdee317-4d2f-489d-be30-27e0afe6e27f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "9fdee317-4d2f-489d-be30-27e0afe6e27f", "name": "module 'NavigationBar.ets' pack", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856452008901, "endTime": 652856463394928}, "additional": {"logType": "info", "children": [], "durationId": "d45dd16e-b099-4787-b357-93c42ae3cdf2", "parent": "b06d7c29-2f67-4a97-838e-66829e34e1ad"}}, {"head": {"id": "9c8372fa-7ba0-4d0f-91a1-683f85169357", "name": "module 'LxAppContainer.ets' pack", "description": "", "type": "duration"}, "body": {"pid": 62120, "tid": "Worker4", "startTime": 652856482441274, "endTime": 652856495455841}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "19ed7fd9-6d25-43e0-a61b-e8ad8fbefd1a", "logId": "8b058134-422a-4318-b2fc-d47b683f4653"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "8b058134-422a-4318-b2fc-d47b683f4653", "name": "module 'LxAppContainer.ets' pack", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856482441274, "endTime": 652856495455841}, "additional": {"logType": "info", "children": [], "durationId": "9c8372fa-7ba0-4d0f-91a1-683f85169357", "parent": "b06d7c29-2f67-4a97-838e-66829e34e1ad"}}, {"head": {"id": "8cc8baf8-49ee-453d-9458-f2daafdc1c81", "name": "write cache", "description": "", "type": "duration"}, "body": {"pid": 62120, "tid": "Worker4", "startTime": 652856496367378, "endTime": 652856503527347}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "39e83f86-444f-4089-9ee3-887b6dbadd8d", "logId": "6ab93ce4-10d2-492a-a052-f12269898a73"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "6ab93ce4-10d2-492a-a052-f12269898a73", "name": "write cache", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856496367378, "endTime": 652856503527347}, "additional": {"logType": "info", "children": [], "durationId": "8cc8baf8-49ee-453d-9458-f2daafdc1c81", "parent": "f6447d0a-da1b-4a5d-9c12-065fa65efcbd"}}, {"head": {"id": "16d90e5d-152b-4341-b25e-d9ac68b3dc49", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "duration"}, "body": {"pid": 62120, "tid": "Worker4", "startTime": 652856503581205, "endTime": 652856606636259}, "additional": {"children": [], "state": "success", "totalTime": 0, "frequency": 0, "fromHook": false, "parent": "147f573a-d66c-419d-9322-b206a8d5e5d3", "logId": "4faf9151-a87b-4589-bfb6-996fc83186a3"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4faf9151-a87b-4589-bfb6-996fc83186a3", "name": "wait for plug-in registration asynchronous task to complete", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856503581205, "endTime": 652856606636259}, "additional": {"logType": "info", "children": [], "durationId": "16d90e5d-152b-4341-b25e-d9ac68b3dc49", "parent": "35de1033-07a5-4bd7-9d1f-61c6f9243b06"}}, {"head": {"id": "c17ab527-d716-448f-a369-1f50d829caca", "name": "default@HarCompileArkTS work[5] done.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856623778133}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35de1033-07a5-4bd7-9d1f-61c6f9243b06", "name": "lingxia:default@HarCompileArkTS", "description": "Compile ArkTS components for packaged HAR files in the stage model.", "type": "log"}, "body": {"pid": 62120, "tid": "Worker4", "startTime": 652848145456748, "endTime": 652856611871948}, "additional": {"logType": "info", "children": ["0da083c1-50b7-4d7c-8fd3-58e944eb699e", "70c582fe-0125-497c-b980-43a14cc0fd83", "0164fccc-a44f-4874-b3e7-3740c5e594a6", "c3764309-6404-4927-80b3-ea5e7c7a5cdf", "f6447d0a-da1b-4a5d-9c12-065fa65efcbd", "4faf9151-a87b-4589-bfb6-996fc83186a3"], "durationId": "147f573a-d66c-419d-9322-b206a8d5e5d3", "parent": "17bb0fbd-d075-4c85-8596-863408331fcc"}}, {"head": {"id": "949e3c10-a8b3-4797-bbc8-8e509624d614", "name": "A work dispatched to worker[4] failed because unable to get work from ready queue.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856624008871}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "17bb0fbd-d075-4c85-8596-863408331fcc", "name": "Finished :lingxia:default@HarCompileArkTS", "description": "Compile ArkTS components for packaged HAR files in the stage model.", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652848016356413, "endTime": 652856624206591, "totalTime": 8513739176}, "additional": {"logType": "info", "children": ["35de1033-07a5-4bd7-9d1f-61c6f9243b06", "69f45090-c922-4e7e-8fc8-21c5718b4b4f"], "durationId": "d9c3a37a-eb29-46c8-a37f-8c5261207d50"}}, {"head": {"id": "e7f84349-a5b7-4ba7-9f7d-6569eea2aa5e", "name": "lingxia:default@ProcessHarArtifacts", "description": "process the HAR build artifacts in the stage model.", "type": "duration"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856632426867, "endTime": 652856664648518}, "additional": {"children": ["650e0748-0889-4508-b6b0-ad387e0f5cee"], "state": "success", "targetName": "default", "moduleName": "lingxia", "category": "Package", "taskRunReasons": ["The input file '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/process_profile/default' has been changed."], "detailId": "fb91be4b-12c8-4825-ba15-d4c27dca1ef5", "logId": "7d4096fe-807a-4e77-b350-d82c5ee9c0c1"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "fb91be4b-12c8-4825-ba15-d4c27dca1ef5", "name": "create lingxia:default@ProcessHarArtifacts task", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856628162054}, "additional": {"logType": "detail", "children": [], "durationId": "e7f84349-a5b7-4ba7-9f7d-6569eea2aa5e"}}, {"head": {"id": "eb418c32-62a8-41bb-b66e-6eb1fca1fcf6", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856628428289}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fadb313d-7312-45f6-95ba-c2009252ffdf", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856628542693}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cc9887ff-2399-4eb7-8539-e5417b3b6946", "name": "Executing task :lingxia:default@ProcessHarArtifacts", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856632456041}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1b101652-1a87-4166-acbd-52767932d679", "name": "lingxia:default@ProcessHarArtifacts is not up-to-date, since the input file '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/process_profile/default' has been changed.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856637791909}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e2340c39-d4b3-40eb-9b18-dbb8bf7b4027", "name": "Incremental task lingxia:default@ProcessHarArtifacts pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856638046510}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f7a89d37-f459-4792-ba61-89d5fa98b209", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856638223922}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e30b2ee-36e2-411f-8f6e-d5f59c4c3b32", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856638334029}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "650e0748-0889-4508-b6b0-ad387e0f5cee", "name": "verify and initialize packaging information", "description": "", "type": "duration"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856639393584, "endTime": 652856663949153}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "e7f84349-a5b7-4ba7-9f7d-6569eea2aa5e", "logId": "0023b05b-4f3d-47bc-b237-434946d73d50"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "0023b05b-4f3d-47bc-b237-434946d73d50", "name": "verify and initialize packaging information", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856639393584, "endTime": 652856663949153}, "additional": {"logType": "info", "children": [], "durationId": "650e0748-0889-4508-b6b0-ad387e0f5cee", "parent": "7d4096fe-807a-4e77-b350-d82c5ee9c0c1"}}, {"head": {"id": "01834b28-56aa-46fc-840e-80df7a14acc6", "name": "lingxia : default@ProcessHarArtifacts cost memory 1.0451126098632812", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856664055867}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8065fa0b-cbf4-4534-9f3a-845a4a4c39d5", "name": "runTaskFromQueue task cost before running: 9 s 684 ms ", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856664452108}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7d4096fe-807a-4e77-b350-d82c5ee9c0c1", "name": "Finished :lingxia:default@ProcessHarArtifacts", "description": "process the HAR build artifacts in the stage model.", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856632426867, "endTime": 652856664648518, "totalTime": 31975093}, "additional": {"logType": "info", "children": ["0023b05b-4f3d-47bc-b237-434946d73d50"], "durationId": "e7f84349-a5b7-4ba7-9f7d-6569eea2aa5e"}}, {"head": {"id": "247906ae-30ec-4679-88aa-29e925d6f361", "name": "lingxia:default@PackageHar", "description": "Build the HAR package in the stage model.", "type": "duration"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856669272842, "endTime": 652856702729472}, "additional": {"children": ["3565fe2f-8801-4639-8763-6769b25b445e"], "state": "success", "targetName": "default", "moduleName": "lingxia", "category": "Package", "taskRunReasons": ["The output file '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/cache/default/default@PackageHar' has been changed."], "detailId": "4a678bd8-0450-4600-b73e-95e22b04ce33", "logId": "52512515-4631-4413-822e-c2ea8dc5d0f9"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "4a678bd8-0450-4600-b73e-95e22b04ce33", "name": "create lingxia:default@PackageHar task", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856667597664}, "additional": {"logType": "detail", "children": [], "durationId": "247906ae-30ec-4679-88aa-29e925d6f361"}}, {"head": {"id": "2fbc31b3-a5a8-44ce-8090-bf553a53eef8", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856667884233}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "441ab8b2-44c6-40b0-8bc5-84da29b8c66f", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856667998906}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e3a81c33-d145-4ef4-8d04-afa23e1807f7", "name": "Executing task :lingxia:default@PackageHar", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856669289336}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15fe9567-cd6a-48a3-b02d-0c9f0c3f123d", "name": "lingxia:default@PackageHar is not up-to-date, since the output file '/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/cache/default/default@PackageHar' has been changed.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856671574950}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "81500681-f83d-43ce-901a-b55356698075", "name": "Incremental task lingxia:default@PackageHar pre-execution cost: 3 ms .", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856671873061}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e6fe91fd-dd2a-4958-9a6b-1c23f89c30e4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856672077226}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "bd639397-68af-4da9-9a4a-c1c625cd53c9", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856672215964}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3565fe2f-8801-4639-8763-6769b25b445e", "name": "execute ohpm packaging command", "description": "", "type": "duration"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856674329123, "endTime": 652856698361367}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "247906ae-30ec-4679-88aa-29e925d6f361", "logId": "422a4e25-cf29-4b40-80f5-fe97e25f1e6f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "422a4e25-cf29-4b40-80f5-fe97e25f1e6f", "name": "execute ohpm packaging command", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856674329123, "endTime": 652856698361367}, "additional": {"logType": "info", "children": [], "durationId": "3565fe2f-8801-4639-8763-6769b25b445e", "parent": "52512515-4631-4413-822e-c2ea8dc5d0f9"}}, {"head": {"id": "e0a9215a-e81d-4d49-9a2e-69829d0bc110", "name": "lingxia : default@PackageHar cost memory 0.722991943359375", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856702016698}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c050d1bf-45cb-4d18-bda3-7dd5ec64439a", "name": "runTaskFromQueue task cost before running: 9 s 722 ms ", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856702397118}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "52512515-4631-4413-822e-c2ea8dc5d0f9", "name": "Finished :lingxia:default@PackageHar", "description": "Build the HAR package in the stage model.", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856669272842, "endTime": 652856702729472, "totalTime": 33021759}, "additional": {"logType": "info", "children": ["422a4e25-cf29-4b40-80f5-fe97e25f1e6f"], "durationId": "247906ae-30ec-4679-88aa-29e925d6f361"}}, {"head": {"id": "f1c951b0-59d5-4571-bef7-4211d686d460", "name": "lingxia:default@PackageSignHar", "description": "Package and sign the HAR package.", "type": "duration"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856711127449, "endTime": 652856712571260}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "lingxia", "category": "Sign", "taskRunReasons": [], "detailId": "c457c0f9-aaa0-451a-8857-8cd751abc1b8", "logId": "ab7ca8cd-9d54-4e55-847a-e2e4687e0dcd"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "c457c0f9-aaa0-451a-8857-8cd751abc1b8", "name": "create lingxia:default@PackageSignHar task", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856705835954}, "additional": {"logType": "detail", "children": [], "durationId": "f1c951b0-59d5-4571-bef7-4211d686d460"}}, {"head": {"id": "5f6be82c-ccee-477e-a364-de792433e8ff", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856706302519}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "94353290-5670-4304-a1b2-bf58ad2043b3", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856706440434}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "25cc1c96-37f3-4d32-b72d-27c91f465fa7", "name": "Executing task :lingxia:default@PackageSignHar", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856711146816}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "eab1c09a-ba37-438c-994b-96b5e4f6c71d", "name": "Will skip sign 'har'. No signingConfigs profile is configured in current project.\n             If needed, configure the signingConfigs in /Users/<USER>/github/LingXia/lingxia-sdk/harmony/build-profile.json5.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856711451859}, "additional": {"logType": "warn", "children": [], "durationId": "f1c951b0-59d5-4571-bef7-4211d686d460"}}, {"head": {"id": "3592341d-7021-4ecc-9d1c-118d383ac256", "name": "lingxia : default@PackageSignHar cost memory 0.04869842529296875", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856712097327}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3688d268-329b-43ae-9800-48ef5047a039", "name": "runTaskFromQueue task cost before running: 9 s 732 ms ", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856712373907}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ab7ca8cd-9d54-4e55-847a-e2e4687e0dcd", "name": "Finished :lingxia:default@PackageSignHar", "description": "Package and sign the HAR package.", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856711127449, "endTime": 652856712571260, "totalTime": 1202699}, "additional": {"logType": "info", "children": [], "durationId": "f1c951b0-59d5-4571-bef7-4211d686d460"}}, {"head": {"id": "0630dd4e-d6d3-4fa8-b5ba-c77495f9ad72", "name": "lingxia:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "duration"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856716682981, "endTime": 652856721233749}, "additional": {"children": [], "state": "success", "targetName": "default", "moduleName": "lingxia", "category": "Generate", "taskRunReasons": [], "detailId": "3b523857-558d-4c15-87bc-12972cf0b10e", "logId": "a8922e9f-1aff-4eb5-8806-341f6e7c994f"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3b523857-558d-4c15-87bc-12972cf0b10e", "name": "create lingxia:default@CollectDebugSymbol task", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856715190878}, "additional": {"logType": "detail", "children": [], "durationId": "0630dd4e-d6d3-4fa8-b5ba-c77495f9ad72"}}, {"head": {"id": "e2b587aa-4aae-4044-8c98-a96df9f797a9", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856715702752}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "90689b1e-ac38-4128-926f-27db3553df79", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856715836185}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "cb6c2681-a162-4f97-b154-32508241cf02", "name": "Executing task :lingxia:default@CollectDebugSymbol", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856716700227}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "62c6bb3d-a416-4224-809a-923dcecc0de4", "name": "jsonObjWithoutParam {} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856720554164}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dcbcb664-6138-422a-8851-d375d02f155b", "name": "jsonObjWithoutParam {\"name\":\"lingxia\",\"version\":\"1.0.0\",\"description\":\"LingXia LxApp HAR module\",\"main\":\"Index.ets\",\"author\":\"\",\"license\":\"Apache-2.0\",\"dependencies\":{}} at undefined", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856720699518}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d792205c-2fa9-4de7-ab5b-4a7847c55c28", "name": "lingxia : default@CollectDebugSymbol cost memory 0.199249267578125", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856720828148}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2b01684a-26bc-4db3-a1d5-e020711a06ac", "name": "runTaskFromQueue task cost before running: 9 s 741 ms ", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856721088633}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a8922e9f-1aff-4eb5-8806-341f6e7c994f", "name": "Finished :lingxia:default@CollectDebugSymbol", "description": "Collect debug symbols to output dir.", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856716682981, "endTime": 652856721233749, "totalTime": 4320494}, "additional": {"logType": "info", "children": [], "durationId": "0630dd4e-d6d3-4fa8-b5ba-c77495f9ad72"}}, {"head": {"id": "e05cc5b6-da9d-4a8b-90bd-04a32b842601", "name": "lingxia:assembleHar", "description": "Assemble the task for the packaged HAR file.", "type": "duration"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856723679495, "endTime": 652856724532342}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "lingxia", "category": "Hook", "taskRunReasons": [], "detailId": "dd16b96f-148c-4ea1-86b0-e6f423ad14ce", "logId": "3b2366f3-b694-43d8-8ac6-4b5576976afe"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "dd16b96f-148c-4ea1-86b0-e6f423ad14ce", "name": "create lingxia:assembleHar task", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856723553550}, "additional": {"logType": "detail", "children": [], "durationId": "e05cc5b6-da9d-4a8b-90bd-04a32b842601"}}, {"head": {"id": "fccfc2d4-8520-4f3e-9270-10c55867011d", "name": "Executing task :lingxia:assembleHar", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856723716607}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "35c6cafc-fcc6-4154-8e3f-b2c45179ecf9", "name": "lingxia : assembleHar cost memory 0.01171875", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856724047371}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "04094351-a19f-4032-9ecb-8a3e313af754", "name": "runTaskFromQueue task cost before running: 9 s 744 ms ", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856724311526}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3b2366f3-b694-43d8-8ac6-4b5576976afe", "name": "Finished :lingxia:assembleHar", "description": "Assemble the task for the packaged HAR file.", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856723679495, "endTime": 652856724532342, "totalTime": 576389}, "additional": {"logType": "info", "children": [], "durationId": "e05cc5b6-da9d-4a8b-90bd-04a32b842601"}}, {"head": {"id": "d09faad6-898e-465b-b294-25580d9146d7", "name": "\"buildFinished\" hook function", "description": "", "type": "duration"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856726746197, "endTime": 652856726772777}, "additional": {"children": [], "state": "success", "targetName": "", "moduleName": "", "category": "", "taskRunReasons": [], "parent": "bad5e796-f043-46d9-a6e7-e4e40b9d0f5a", "logId": "3ae7ece9-6798-48b9-86b3-85ae5dd2a214"}, "log": {"_logger": {"category": "DurationEvent", "context": {}, "callStackSkipIndex": 0}, "_filelogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}, "anonymizeFileLogger": {"fileLogger": {"category": "debug-file", "context": {}, "callStackSkipIndex": 0}}}}, {"head": {"id": "3ae7ece9-6798-48b9-86b3-85ae5dd2a214", "name": "\"buildFinished\" hook function", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856726746197, "endTime": 652856726772777}, "additional": {"logType": "info", "children": [], "durationId": "d09faad6-898e-465b-b294-25580d9146d7"}}, {"head": {"id": "3d774d23-860c-498d-86b7-a2e3a7d5c352", "name": "BUILD SUCCESSFUL in 9 s 747 ms ", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856726831335}, "additional": {"logType": "info", "children": []}}, {"head": {"id": "f622190e-cb77-4d82-8747-468bc5729cca", "name": "assembleHar", "description": "", "type": "mark"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652846980626125, "endTime": 652856727313196}, "additional": {"time": {"year": 2025, "month": 7, "day": 31, "hour": 21, "minute": 44}, "completeCommand": "{\"prop\":[],\"_\":[\"assembleHar\"]};assembleHar", "hvigorVersion": "5.17.2", "markType": "history", "nodeVersion": "v18.20.1", "category": "build", "state": "success"}}, {"head": {"id": "b51a1fc0-2bad-4437-9154-c313f38f64fb", "name": "There is no need to refresh cache, since the incremental task lingxia:default@PreBuild is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856727355154}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3faf4682-04c6-47b3-88c4-ae9dec48037b", "name": "Update task lingxia:default@CreateHarBuildProfile input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/AppScope/app.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856727489530}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3e53e40f-2408-4bd1-8826-58b258acd203", "name": "Update task lingxia:default@CreateHarBuildProfile input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856727681464}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6b6e32a9-b90e-4179-9258-5330d0395df7", "name": "Update task lingxia:default@CreateHarBuildProfile output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/BuildProfile.ets cache.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856727877280}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9e9d9cc6-12c3-406f-8321-c5608ddb81b6", "name": "Incremental task lingxia:default@CreateHarBuildProfile post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856728083270}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9210c53b-7198-4762-98b6-20bf3155468b", "name": "Update task lingxia:default@CreateHarModuleInfo output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/generated/profile/default/ModuleInfo.ts cache.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856728253522}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "776e6cb4-1d32-4633-b7ef-a61f2ad3df6e", "name": "Incremental task lingxia:default@CreateHarModuleInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856728712403}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "843e3680-ecc1-4d76-a043-a83961da1a99", "name": "Update task lingxia:default@MergeProfile input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/AppScope/app.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856728835668}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0d8d0b41-a04a-4883-a1b5-4d8a788fb9aa", "name": "Update task lingxia:default@MergeProfile input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/build-profile.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856728980477}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fad67855-9ab0-4638-8208-82670797b132", "name": "Update task lingxia:default@MergeProfile input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/module.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856729078318}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc52a0f2-f68f-42b8-af44-f472634d1d83", "name": "Update task lingxia:default@MergeProfile output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/merge_profile/default/module.json cache.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856729180277}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ac38c66f-d443-4312-b486-4f8e849dbc17", "name": "Incremental task lingxia:default@MergeProfile post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856729318513}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "750cfacd-5bc9-440b-8976-59180f2d73da", "name": "Update task lingxia:default@GeneratePkgContextInfo output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/loader/default/pkgContextInfo.json cache.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856729386544}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48f06535-940e-4685-8755-a0c1232d463a", "name": "Incremental task lingxia:default@GeneratePkgContextInfo post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856729506596}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0b29f405-fa81-4a33-8dc4-0f5a2c43e46c", "name": "Update task lingxia:default@ProcessIntegratedHsp output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/build/cache/default/integrated_hsp/integratedHspCache.json cache.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856730033205}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "785697b2-b31f-4f5c-8561-558a162b30bb", "name": "Incremental task lingxia:default@ProcessIntegratedHsp post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856730317064}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "dc17fada-12df-4a69-9159-8c01fa90fbe7", "name": "Update task lingxia:default@ProcessProfile input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/merge_profile/default/module.json cache from map.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856730429859}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "45051f3d-9ad6-4cb6-b582-77ca7ae388cb", "name": "Update task lingxia:default@ProcessProfile output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/process_profile/default/module.json cache.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856730546139}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "89e188a1-b387-41ba-8deb-3800b2cb0572", "name": "Incremental task lingxia:default@ProcessProfile post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856730946597}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "d5543daf-071a-436c-b563-92c5bec6fa7c", "name": "Update task lingxia:default@ProcessRouterMap input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/oh-package.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856732470331}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1bc96913-3984-43ad-91ee-7315b138e359", "name": "Update task lingxia:default@ProcessRouterMap input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/oh-package.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856732816154}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "872ceff7-6f1f-4c88-aecc-528687ba7391", "name": "Update task lingxia:default@ProcessRouterMap input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/module.json5 cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856733103541}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6097b404-6f03-480b-8a58-c2750bb44692", "name": "Update task lingxia:default@ProcessRouterMap input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/loader/default/pkgContextInfo.json cache from map.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856733760863}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2bec07d6-4ccc-46e9-852a-64caaf6b36e0", "name": "Update task lingxia:default@ProcessRouterMap output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/router_map/default/temp-router-map.json cache.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856733934066}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e5ebd8b2-acb6-49a7-b61c-27ba7ecc1c9b", "name": "Update task lingxia:default@ProcessRouterMap output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/router_map/default/loader-router-map.json cache.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856734280591}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2698212e-668d-4c67-963c-5b5c1cd24ba5", "name": "Incremental task lingxia:default@ProcessRouterMap post-execution cost:4 ms .", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856734656093}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7fb1ef8a-a94b-44e8-8aac-fd0bcb2d52b7", "name": "Update task lingxia:default@ProcessResource output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/res/default/resConfig.json cache.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856734808315}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c339a52a-0629-4186-a27b-de0e382dcccc", "name": "Update task lingxia:default@ProcessResource output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/res/default/opt-compression.json cache.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856735037616}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "a7359793-addf-47b7-9dab-46a1aeea7566", "name": "Incremental task lingxia:default@ProcessResource post-execution cost:1 ms .", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856735297510}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "24f738b8-8244-496d-bce0-b13cde11d51f", "name": "Update task lingxia:default@GenerateLoaderJson input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/loader/default/pkgContextInfo.json cache from map.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856738295511}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "48825ead-83d7-4573-b2ac-7ec643f9502b", "name": "Update task lingxia:default@GenerateLoaderJson input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856738500964}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "3ad1d7a1-6057-4b76-a446-f46ca79cd3f4", "name": "Update task lingxia:default@GenerateLoaderJson input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/generated/profile/default/ModuleInfo.ts cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856739869235}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e82da1c5-104c-43b7-945c-81e893d0a7c6", "name": "Update task lingxia:default@GenerateLoaderJson output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/loader/default/loader.json cache.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856740172944}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "7f64c035-e10a-450c-96bd-3c4245035c5b", "name": "Incremental task lingxia:default@GenerateLoaderJson post-execution cost:6 ms .", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856740562303}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "65c60b82-7fd3-4092-b3c3-080c39f87805", "name": "There is no need to refresh cache, since the incremental task lingxia:default@ProcessLibs is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856740703685}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fdcd326d-24e0-435b-b5c4-53000d722364", "name": "Update task lingxia:default@CompileResource input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/process_profile/default/module.json cache from map.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856741405528}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1948ef1f-cc50-47df-a561-101a0dd35464", "name": "Update task lingxia:default@CompileResource input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/res/default/resConfig.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856741571853}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "855df459-07e0-411a-8f9a-d960793d904a", "name": "Update task lingxia:default@CompileResource output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/res/default cache.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856741897207}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c950dbc8-63f0-4555-ad99-7d978171f2d4", "name": "Update task lingxia:default@CompileResource output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/generated/r/default/ResourceTable.h cache.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856742558625}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "0287ad08-0886-46ad-85dd-53362c6d0f8d", "name": "Update task lingxia:default@CompileResource output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/generated/r/default cache.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856742890880}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8c5b1a26-ebf5-4584-bc09-918768f79599", "name": "Incremental task lingxia:default@CompileResource post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856743286248}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "691f8539-654d-43bc-80c6-12b634e4dcff", "name": "There is no need to refresh cache, since the incremental task lingxia:default@DoNativeStrip is up-to-date.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856743431132}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f56b44a1-1464-4838-91d1-93d7d11268cd", "name": "Update task lingxia:default@HarCompileArkTS input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/loader/default cache from map.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856745604695}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "066b5214-c7a9-4978-bf04-33b191227794", "name": "Update task lingxia:default@HarCompileArkTS input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/res/default/ark_module.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856745800006}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "47113b29-a967-4acc-b192-68e348bd7daa", "name": "Update task lingxia:default@HarCompileArkTS input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856746065771}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c56f4db4-54c6-4741-aaf5-c82482ef420d", "name": "Update task lingxia:default@HarCompileArkTS input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/Index.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856747257562}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ddd29bc-dca4-4fed-9e42-60bc20ff72fc", "name": "Update task lingxia:default@HarCompileArkTS input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/loader/default/pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856747569529}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "de02b21f-57d1-44a0-a9b6-24aa8d5b8819", "name": "Update task lingxia:default@HarCompileArkTS input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/BuildProfile.ets cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856748230227}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2d4f7809-137c-4dd4-b09b-ca5564d19ad3", "name": "Update task lingxia:default@HarCompileArkTS output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/loader_out/default/ets cache.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856748698272}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9221b86e-d5c0-447c-9c23-ecdec54f8092", "name": "Incremental task lingxia:default@HarCompileArkTS post-execution cost:6 ms .", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856749475170}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "becababc-b623-4ae5-8422-ec2fb024db9a", "name": "Update task lingxia:default@CacheNativeLibs input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/stripped_native_libs/default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856749677146}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9652fbc1-0ee4-4d57-93b6-5f11d20b3cef", "name": "Update task lingxia:default@CacheNativeLibs input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/libs/default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856750096029}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "f247bf9f-37d2-4ad3-8798-5a0a4617805b", "name": "Update task lingxia:default@CacheNativeLibs output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/patch/default/base_native_libs.json cache.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856750480212}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "23cd0416-d499-44b7-90a0-6654b5708bea", "name": "Incremental task lingxia:default@CacheNativeLibs post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856750822545}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "15581223-031b-4cd9-8ea2-7f8f94f199c8", "name": "Update task lingxia:default@ProcessHarArtifacts input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/process_profile/default cache from map.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856752061518}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "e8a2292e-f016-4595-942c-b76301a6fc62", "name": "Update task lingxia:default@ProcessHarArtifacts input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856752292203}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "80845b16-6f1f-447c-8f0e-be5d8ee6f85c", "name": "Update task lingxia:default@ProcessHarArtifacts input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/generated/pm/default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856754919427}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "37ecef82-f655-4022-8dce-7f3c1f56641f", "name": "Update task lingxia:default@ProcessHarArtifacts input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/stripped_native_libs/default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856755241488}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "2c160030-4a63-483b-9574-c1c0b35db3a7", "name": "Update task lingxia:default@ProcessHarArtifacts input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/loader/default/pkgContextInfo.json cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856755544412}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "58ee33a6-88ba-4008-9481-f51748bed39c", "name": "Update task lingxia:default@ProcessHarArtifacts output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/cache/default/default@PackageHar cache.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856755694940}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "fd2a5669-7833-4a82-9739-be79e4cbfbe0", "name": "Incremental task lingxia:default@ProcessHarArtifacts post-execution cost:6 ms .", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856756805320}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "4366cb4b-443f-42bc-bf93-586e360df30a", "name": "Update task lingxia:default@PackageHar output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/cache/default/default@PackageHar cache.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856756907545}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "c7bfc12f-186d-45f8-83f3-72fa9896c5da", "name": "Update task lingxia:default@PackageHar output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/outputs/default cache.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856758190981}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "860b7cd3-b723-46d4-94fc-25d962171678", "name": "Incremental task lingxia:default@PackageHar post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856758776390}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "1adcb9be-968f-4360-a65b-0418332c67a1", "name": "Update task lingxia:default@PackageSignHar input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/cache/default/default@PackageHar cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856758911155}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "b7938b5c-a1ac-4e4f-9075-978b8e43e106", "name": "Update task lingxia:default@PackageSignHar input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/cache/default/default@PackageSignHar cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856759974250}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "ebe5031e-66e0-4e1b-a596-b97c3a610a65", "name": "Update task lingxia:default@PackageSignHar output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/outputs/default/lingxia-signed.har cache.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856760150400}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "9beff2b9-4c48-477b-af31-007989018098", "name": "Incremental task lingxia:default@PackageSignHar post-execution cost:2 ms .", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856760344952}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "8ec7cae8-c5ce-4f43-b51c-c77c5da14c3b", "name": "Update task lingxia:default@CollectDebugSymbol input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/loader_out/default/ets/sourceMaps.map cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856761913504}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "12d68819-8f2b-4aa0-933f-44b16a0eda4a", "name": "Update task lingxia:default@CollectDebugSymbol input file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/libs/default cache by regenerate.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856762140409}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "6654842e-631e-4b46-a34a-323dd55e5a0e", "name": "Update task lingxia:default@CollectDebugSymbol output file:/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/outputs/default/symbol cache.", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856762330077}, "additional": {"logType": "debug", "children": []}}, {"head": {"id": "44fcc4f8-edf5-4899-88f8-81dfa5477ef5", "name": "Incremental task lingxia:default@CollectDebugSymbol post-execution cost:3 ms .", "description": "", "type": "log"}, "body": {"pid": 62120, "tid": "Main Thread", "startTime": 652856762574844}, "additional": {"logType": "debug", "children": []}}], "workLog": []}