{"modulePathMap": {"lingxia": "/Users/<USER>/github/Ling<PERSON>ia/lingxia-sdk/harmony/lingxia"}, "compileMode": "esmodule", "projectRootPath": "/Users/<USER>/github/Ling<PERSON>ia/lingxia-sdk/harmony", "nodeModulesPath": "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/loader_out/default/node_modules", "byteCodeHarInfo": {}, "declarationEntry": [], "moduleName": "lingxia", "hspNameOhmMap": {}, "harNameOhmMap": {}, "packageManagerType": "ohpm", "compileEntry": ["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/LxApp.ets", "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/LxAppContainer.ets", "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/LxAppNavigation.ets", "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/NativeBridge.ets", "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/NavigationBar.ets", "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/TabBar.ets", "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/src/main/ets/lxapp/WebView.ets", "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/generated/profile/default/ModuleInfo.ts"], "otherCompileFiles": [], "dynamicImportLibInfo": {}, "routerMap": [], "hspResourcesMap": {}, "updateVersionInfo": {}, "byteCodeHar": true}