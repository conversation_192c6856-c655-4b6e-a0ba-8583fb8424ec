import window from "@ohos.window";
interface NavigationInstance {
    openLxApp(appId: string, path: string): void;
    closeLxApp(appId: string): void;
}
export declare class LxApp {
    private static instance;
    static managerInstance: LxAppManager | null;
    static context: Context | null;
    static windowStage: window.WindowStage | null;
    static modalLxAppId: string | null;
    static modalLxAppPath: string | null;
    static modalLxAppVisible: boolean;
    static hideHomeLxApp: boolean;
    static navigationInstance: NavigationInstance | null;
    private homeLxAppId;
    static initialRouteCache: Map<string, string>;
    private constructor();
    static getInstance(): LxApp;
    static initialize(context?: Context, windowStage?: window.WindowStage): void;
    static openHomeLxApp(): void;
    static closeLxApp(appId: string): void;
    static openLxApp(appId: string, path: string): void;
    /**
     * Switch to a specific page within a LxApp
     * @param appId - LxApp ID
     * @param path - Page path to switch to
     */
    static switchPage(appId: string, path: string): boolean;
    /**
     * Get Home LxApp ID
     */
    getHomeLxAppId(): string | null;
    /**
     * Set manager instance (called by LxAppManager)
     */
    static setManagerInstance(manager: LxAppManager): void;
    /**
     * Set navigation instance (called by LxAppNavigation)
     */
    static setNavigationInstance(navigation: NavigationInstance): void;
    /**
     * Setup system status bar transparency (call after window is created)
     * Should be called in onWindowStageCreate
     */
    static setupSystemStatusBar(windowStage: window.WindowStage): void;
    /**
     * Switch to specific page in current LxApp
     * @param appId LxApp ID
     * @param path Page path to switch to
     * @returns Promise<boolean> Success status
     */
    static switchToPage(appId: string, path: string): Promise<boolean>;
    /**
     * Switch to specific page in current LxApp (internal implementation)
     * @param appId LxApp ID
     * @param path Page path to switch to
     * @returns Promise<boolean> Success status
     */
    switchToPageInternal(appId: string, path: string): Promise<boolean>;
}
/**
 * LxApp Manager Component - UI management for LxApps
 * Internal component
 */
@Component
declare struct LxAppManager {
    @State
    isReady: boolean;
    @State
    homeAppId: string;
    @State
    homeAppPath: string;
    @State
    homeAppCurrentPath: string;
    @State
    errorMessage: string;
    @State
    modalLxAppVisible: boolean;
    @State
    modalLxAppId: string;
    @State
    modalLxAppPath: string;
    @State
    hideHomeLxApp: boolean;
    private homeContainerRef;
    private modalContainerRef;
    aboutToAppear(): void;
    build(): void;
    /**
     * Open LxApp (unified method for both home and other miniapps)
     */
    doOpenLxApp(appId: string, path: string, isHomeLxApp?: boolean): void;
    /**
     * Determine if safe area should be expanded for status bar transparency
     */
    private shouldExpandSafeArea;
    /**
     * Update home miniapp current path (called when user switches tabs)
     */
    updateHomeLxAppCurrentPath(path: string): void;
    /**
     * Close modal miniapp
     */
    closeModalLxApp(): void;
    /**
     * Switch to specific page in any LxApp (External API for Rust)
     * @param appId LxApp ID
     * @param path Page path to switch to
     * @returns true if successful, false otherwise
     */
    switchToPage(appId: string, path: string): boolean;
    /**
     * Cache initial route for a LxApp
     */
    private cacheInitialRoute;
    /**
     * Check if a path is the initial route for a LxApp
     */
    static isInitialRoute(appId: string, path: string): boolean;
    /**
     * Get cached initial route for a LxApp
     */
    static getInitialRoute(appId: string): string | null;
}
export {};
