import webview from "@ohos.web.webview";
import { WebViewInfo } from './WebView';
import { type NavigationBarConfig } from './NavigationBar';
import { type TabBarConfig } from './TabBar';
@Component
export declare struct LxAppContainer {
    @Prop
    appId: string;
    @Prop
    initialPath: string;
    @Prop
    navigationMode: boolean;
    @Prop
    closeCallback: string;
    @State
    path: string;
    @State
    pageConfig: NavigationBarConfig | null;
    @State
    tabBarConfig: TabBarConfig | null;
    @State
    selectedTabIndex: number;
    @State
    webViewComponents: Map<string, webview.WebviewController>;
    @State
    currentWebTag: string;
    @State
    webViewKeys: string[];
    private pageShowTriggered;
    /**
     * Trigger onPageShow for a WebView if it hasn't been triggered yet
     */
    private triggerOnPageShowIfNeeded;
    @Prop
    externalPath: string;
    @State
    animationOffsetY: number;
    aboutToAppear(): void;
    /**
     * <PERSON>le external path changes from LxApp.switchPage API
     */
    onExternalPathChange(): void;
    /**
     * Handle WebView UI events from WebViewManager
     */
    handleWebViewUiEvent(action: string, info: WebViewInfo): void;
    storeWebViewComponent(webTag: string, controller: webview.WebviewController): void;
    build(): void;
    private isHomeLxApp;
    private shouldExpandSafeAreaForContainer;
    /**
     * Check if TabBar is transparent
     * When transparent, TabBar overlays WebView; when opaque, they are separate
     */
    private isTabBarTransparent;
    /**
     * Check if TabBar is vertical (left/right position)
     */
    private isTabBarVertical;
    /**
     * Get TabBar alignment for Stack layout
     */
    private getTabBarAlignment;
    /**
     * Build TabBar component
     */
    @Builder
    buildTabBar(): void;
    /**
     * Build NavigationBar component (shared by both branches)
     */
    @Builder
    buildNavigationBar(): void;
    @Builder
    buildStandaloneCapsuleButton(): void;
    /**
     * Check if should show capsule button
     * homeminiapp doesn't show capsule button, other miniapps do
     */
    private shouldShowCapsuleButton;
    /**
     * Build capsule button
     */
    @Builder
    buildCapsuleButton(): void;
    /**
     * Build three dots icon
     */
    @Builder
    buildThreeDots(): void;
    /**
     * Build close icon (outer circle LINE with inner dot, transparent middle)
     */
    @Builder
    buildCloseIcon(): void;
    /**
     * Build WebView area
     */
    @Builder
    buildWebViewArea(): void;
    private setInitialSelectedTab;
    private handleTabSelected;
    /**
     * Switch to tab by index
     */
    private switchToTabByIndex;
    /**
     * Switch to page internally (handles both tab and non-tab pages)
     * Called by external API or tab clicks
     */
    private switchToPageInternal;
    private handleNavigationBack;
    private handleCapsuleMore;
    private handleCapsuleClose;
    /**
     * Build individual WebView component for a specific webTag
     */
    @Builder
    buildWebViewComponent(webTag: string, controller: webview.WebviewController): void;
    /**
     * Check if current page is the initial route
     */
    private isInitialRoute;
}
