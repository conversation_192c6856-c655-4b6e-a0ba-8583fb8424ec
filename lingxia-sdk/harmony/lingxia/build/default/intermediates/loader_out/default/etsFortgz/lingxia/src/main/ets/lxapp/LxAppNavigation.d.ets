/**
 * LxApp Navigation with WebView preservation
 * - Hides LxApps instead of destroying them to preserve WebViews
 * - Uses independent hiddenApps Set to avoid miniAppStack mutations
 */
@Component
export declare struct LxAppNavigation {
    @State
    isReady: boolean;
    @State
    homeAppId: string;
    @State
    homeAppPath: string;
    @State
    errorMessage: string;
    @Prop
    autoOpenHome: boolean;
    @State
    private miniAppStack;
    @State
    private hiddenApps;
    @Provide
    navPageInfos: NavPathStack;
    aboutToAppear(): void;
    private initializeLxApp;
    openLxApp(appId: string, path: string): void;
    closeLxApp(appId: string): void;
    private showLxApp;
    private createNewLxApp;
    private hideLxAppWithAnimation;
    private isLxAppVisible;
    build(): void;
    @Builder
    buildFixedCapsuleButton(appId: string): void;
    /**
     * Build three dots icon
     */
    @Builder
    buildThreeDots(): void;
    /**
     * Build close icon
     */
    @Builder
    buildCloseIcon(): void;
    @Builder
    buildErrorPage(): void;
    /**
     * Build clean loading page
     */
    @Builder
    buildLoadingPage(): void;
}
