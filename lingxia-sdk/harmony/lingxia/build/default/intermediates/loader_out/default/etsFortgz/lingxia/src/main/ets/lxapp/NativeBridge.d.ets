/**
 * Get the callback function for native layer
 * Node-API ThreadSafe Function limitation: can only pass single string
 * Format: "function_name|arg1|arg2|arg3|..." (using | as separator to avoid conflicts with URLs)
 */
export declare function getNativeCallbackFunction(): (data: string) => Object | null;
/**
 * Initialize the native bridge
 * This should be called during LxApp initialization
 */
export declare function initNativeBridge(): void;
