/**
 * NavigationBar configuration interface - synced with native page config
 * Contains all page-level configuration including navigation bar settings
 */
export interface NavigationBarConfig {
    navigationBarTitleText: string;
    navigationBarBackgroundColor: string;
    navigationBarTextStyle: string;
    navigationStyle: number;
    backgroundColor?: string;
}
@Component
export declare struct NavigationBar {
    @Prop
    config: NavigationBarConfig;
    @Prop
    appId: string;
    @Prop
    currentPath: string;
    @Prop
    showCapsuleButton: boolean;
    onBackPressed?: (appId: string) => boolean;
    onMenuPressed?: (appId: string) => void;
    onCapsuleMore?: () => void;
    onCapsuleClose?: () => void;
    build(): void;
    private getBackgroundColor;
    private getTextColor;
    private shouldShowBackButton;
    private shouldShowMenuButton;
    /**
     * Build capsule button (integrated into NavigationBar)
     */
    @Builder
    buildCapsuleButton(): void;
    /**
     * Build three dots icon
     */
    @Builder
    buildThreeDots(): void;
    /**
     * Build close icon
     */
    @Builder
    buildCloseIcon(): void;
}
export declare class NavigationBarController {
    private static instance;
    private currentConfig;
    private appId;
    private currentPath;
    private constructor();
    static getInstance(): NavigationBarController;
    updateConfig(appId: string, path: string, config: NavigationBarConfig): void;
    setNavigationBarTitle(title: string): void;
    /**
     * Set navigation bar color - LxApp compatible API
     * @param backgroundColor Background color
     * @param textStyle Text style ('black' | 'white')
     */
    setNavigationBarColor(backgroundColor: string, textStyle?: 'black' | 'white'): void;
    showNavigationBarLoading(): void;
    hideNavigationBarLoading(): void;
    /**
     * Get current configuration
     */
    getCurrentConfig(): NavigationBarConfig | null;
    /**
     * Get current app ID
     */
    getCurrentAppId(): string;
    /**
     * Get current path
     */
    getCurrentPath(): string;
}
