export declare class TabBarPosition {
    static readonly BOTTOM: number;
    static readonly TOP: number;
    static readonly LEFT: number;
    static readonly RIGHT: number;
}
/**
 * TabBar configuration interfaces
 */
export interface TabBarItem {
    pagePath: string;
    text?: string;
    iconPath?: string;
    selectedIconPath?: string;
    selected?: boolean;
    badge?: string;
    redDot?: boolean;
    badgeStyle?: BadgeStyle;
}
export interface BadgeStyle {
    backgroundColor?: string;
    color?: string;
    fontSize?: number;
    borderRadius?: number;
}
export interface TabBarConfig {
    color?: string;
    selectedColor?: string;
    backgroundColor?: string;
    borderStyle?: string;
    position?: number;
    dimension?: number;
    list: TabBarItem[];
}
/**
 * TabBar component for LxApp
 * Displays bottom tab bar with navigation items
 */
@Component
export declare struct TabBar {
    @Prop
    config: TabBarConfig;
    @Prop
    appId: string;
    @Prop
    selectedIndex: number;
    onTabSelected?: (appId: string, index: number, item: TabBarItem) => void;
    aboutToAppear(): void;
    build(): void;
    @Builder
    buildTabItem(item: TabBarItem, index: number): void;
    /**
     * Get icon URL with proper file:// prefix for absolute paths
     */
    private getIconUrl;
    /**
     * Get normal text/icon color
     */
    private getNormalColor;
    /**
     * Get selected text/icon color
     */
    private getSelectedColor;
    /**
     * Get background color - supports transparent with better integration
     */
    private getBackgroundColor;
    /**
     * Get border color
     */
    private getBorderColor;
    /**
     * Get border width
     */
    private getBorderWidth;
    /**
     * Get tab bar size (height for horizontal, width for vertical)
     */
    private getTabBarSize;
    /**
     * Check if TabBar is vertical (left/right position)
     */
    private isVertical;
    /**
     * Check if TabBar is transparent
     */
    private isTransparent;
    /**
     * Get padding for horizontal layout
     */
    private getHorizontalPadding;
    /**
     * Get padding for vertical layout
     */
    private getVerticalPadding;
    /**
     * Get border for horizontal layout
     */
    private getHorizontalBorder;
    /**
     * Get border for vertical layout
     */
    private getVerticalBorder;
    /**
     * Get gradient for horizontal layout
     */
    private getHorizontalGradient;
    /**
     * Get gradient for vertical layout
     */
    private getVerticalGradient;
}
/**
 * TabBar controller for programmatic control
 */
export declare class TabBarController {
    private static instance;
    private currentConfig;
    private appId;
    private selectedIndex;
    private constructor();
    /**
     * Get TabBarController singleton instance
     */
    static getInstance(): TabBarController;
    /**
     * Update tab bar configuration
     */
    updateConfig(appId: string, config: TabBarConfig): void;
    /**
     * Set tab bar badge
     * @param index Tab index
     * @param text Badge text (e.g., "99+", "NEW")
     */
    setTabBarBadge(index: number, text: string): void;
    /**
     * Remove tab bar badge
     * @param index Tab index
     */
    removeTabBarBadge(index: number): void;
    /**
     * Show tab bar red dot
     * @param index Tab index
     */
    showTabBarRedDot(index: number): void;
    /**
     * Hide tab bar red dot
     * @param index Tab index
     */
    hideTabBarRedDot(index: number): void;
    /**
     * Set tab bar style
     * @param style Style configuration
     */
    setTabBarStyle(style: Partial<TabBarConfig>): void;
    /**
     * Switch to tab by page path
     */
    switchToTab(pagePath: string): boolean;
    /**
     * Show tab bar
     */
    showTabBar(): void;
    /**
     * Hide tab bar
     */
    hideTabBar(): void;
}
