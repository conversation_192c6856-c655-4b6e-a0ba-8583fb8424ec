import webview from "@ohos.web.webview";
/**
 * WebView information for management
 */
export interface WebViewInfo {
    webtag: string;
    controller: webview.WebviewController;
    visible: boolean;
    scrollListenerEnabled?: boolean;
}
/**
 * WebView Manager - handles WebView lifecycle management
 * Provides create, find and destroy functionality for WebViews
 */
export declare class WebViewManager {
    private static controllers;
    private static webViewInfos;
    private static uiCallback;
    /**
     * Set UI callback for WebView component management
     * @param callback - Callback function to handle WebView UI events
     */
    static setUiCallback(callback: (action: string, info: WebViewInfo) => void): void;
    /**
     * Create WebView by webtag
     * @param webtag - WebView tag
     * @returns true if created successfully, false if already exists or creation failed
     */
    static createWebview(webtag: string): boolean;
    /**
     * Find WebView by appId and path (only find, don't create)
     * @param appId - LxApp ID
     * @param path - Page path
     * @returns WebView controller or null if creation fails
     */
    static findWebview(appId: string, path: string): webview.WebviewController | null;
    /**
     * Destroy WebView by appId and path
     * Remove from map and release resources
     * @param webtag - WebView tag
     * @returns true if destroyed successfully, false if not found
     */
    static destroyWebview(webtag: string): boolean;
    /**
     * Load URL in WebView
     * @param webtag - WebView tag
     * @param url - URL to load
     * @returns true if URL loaded successfully, false if WebView not found or load failed
     */
    static loadUrl(webtag: string, url: string): boolean;
    /**
     * Set web debugging access for WebView
     * @param webtag - WebView tag
     * @param enable - Whether to enable web debugging
     * @returns true if setting applied successfully, false if WebView not found or setting failed
     */
    static setWebDebuggingAccess(webtag: string, enable: boolean): boolean;
    /**
     * Clear browsing data for WebView
     * @param webtag - WebView tag
     * @returns true if clearing succeeded, false if WebView not found or clearing failed
     */
    static clearBrowsingData(webtag: string): boolean;
    /**
     * Set user agent for WebView
     * @param webtag - WebView tag
     * @param userAgent - User agent string
     * @returns true if setting succeeded, false if WebView not found or setting failed
     */
    static setUserAgent(webtag: string, userAgent: string): boolean;
    /**
     * Set scroll listener enabled for WebView
     * @param webtag - WebView tag
     * @param enabled - Whether to enable scroll listener
     * @returns true if setting succeeded, false if WebView not found or setting failed
     */
    static setScrollListenerEnabled(webtag: string, enabled: boolean): boolean;
    /**
     * Handle scroll change event for WebView
     * This should be called from the UI component when scroll occurs
     * @param webtag - WebView tag
     * @param scrollX - Horizontal scroll position
     * @param scrollY - Vertical scroll position
     */
    static handleScrollChanged(webtag: string, scrollX: number, scrollY: number): void;
    /**
     * Get WebView controller by webtag (internal use)
     * @param webtag - WebView tag
     * @returns WebView controller or null
     */
    static getController(webtag: string): webview.WebviewController | null;
    /**
     * Get WebView info by webtag (internal use)
     * @param webtag - WebView tag
     * @returns WebView info or null
     */
    static getWebViewInfo(webtag: string): WebViewInfo | null;
    /**
     * Set WebView visibility
     * @param appId - LxApp ID
     * @param path - Page path
     * @param visible - Whether WebView should be visible
     */
    static setWebViewVisibility(appId: string, path: string, visible: boolean): void;
    /**
     * Get all WebView infos (for UI rendering)
     * @returns Array of WebView infos
     */
    static getAllWebViewInfos(): WebViewInfo[];
}
/**
 * Create webtag from appId and path
 * @param appId - LxApp ID
 * @param path - Page path
 * @returns webtag in format "appId-path"
 */
export declare function toWebTag(appId: string, path: string): string;
/**
 * WebTag parts interface
 */
export interface WebTagParts {
    appId: string;
    path: string;
}
/**
 * Extract appId and path from webtag
 * @param webtag - WebView tag in format "appId-path"
 * @returns Object with appId and path, or null if invalid format
 */
export declare function extractWebTag(webtag: string): WebTagParts | null;
/**
 * Set UI callback for WebView component management
 * @param callback - Callback function to handle WebView UI events
 */
export declare function setWebViewUiCallback(callback: (action: string, info: WebViewInfo) => void): void;
/**
 * Create WebView controller
 * @param webtag - WebView tag
 * @returns true if created successfully, false otherwise
 */
export declare function createWebViewController(webtag: string): boolean;
/**
 * Find WebView controller (only find, don't create)
 * @param appId - LxApp ID
 * @param path - Page path
 * @returns WebView controller or null
 */
export declare function findWebview(appId: string, path: string): webview.WebviewController | null;
/**
 * Destroy WebView controller
 * @param webtag - WebView tag
 * @returns true if destroyed successfully
 */
export declare function destroyWebViewController(webtag: string): boolean;
/**
 * Load URL in WebView
 * @param webtag - WebView tag
 * @param url - URL to load
 * @returns true if URL loaded successfully
 */
export declare function loadUrl(webtag: string, url: string): boolean;
/**
 * Set web debugging access for WebView
 * @param webtag - WebView tag
 * @param enable - Whether to enable web debugging
 * @returns true if setting applied successfully
 */
export declare function setWebDebuggingAccess(webtag: string, enable: boolean): boolean;
/**
 * Clear browsing data for WebView
 * @param webtag - WebView tag
 * @returns true if clearing succeeded
 */
export declare function clearBrowsingData(webtag: string): boolean;
/**
 * Set user agent for WebView
 * @param webtag - WebView tag
 * @param userAgent - User agent string
 * @returns true if setting succeeded
 */
export declare function setUserAgent(webtag: string, userAgent: string): boolean;
/**
 * Set scroll listener enabled for WebView
 * @param webtag - WebView tag
 * @param enabled - Whether to enable scroll listener
 * @returns true if setting succeeded
 */
export declare function setScrollListenerEnabled(webtag: string, enabled: boolean): boolean;
/**
 * Handle scroll change event for WebView
 * This should be called from UI components when scroll occurs
 * @param webtag - WebView tag
 * @param scrollX - Horizontal scroll position
 * @param scrollY - Vertical scroll position
 */
export declare function handleScrollChanged(webtag: string, scrollX: number, scrollY: number): void;
