{"app": {"bundleName": "com.lingxia.miniapp", "debug": true, "versionCode": 1000000, "versionName": "1.0.0", "minAPIVersion": 50005017, "targetAPIVersion": 50005017, "apiReleaseType": "Release", "compileSdkVersion": "5.0.5.165", "compileSdkType": "HarmonyOS", "appEnvironments": [], "bundleType": "app", "buildMode": "debug"}, "module": {"name": "lingxia", "type": "har", "deviceTypes": ["default", "tablet"], "requestPermissions": [{"name": "ohos.permission.INTERNET"}], "packageName": "lingxia", "installationFree": false, "virtualMachine": "ark12.0.6.0", "compileMode": "esmodule", "dependencies": []}}