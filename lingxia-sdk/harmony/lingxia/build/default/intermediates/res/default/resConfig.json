{"configPath": "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/process_profile/default/module.json", "packageName": "com.lingxia.miniapp", "output": "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/res/default", "moduleNames": "lingxia", "ResourceTable": ["/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/generated/r/default/ResourceTable.h"], "moduleResources": [], "dependencies": [], "iconCheck": false, "compression": "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/res/default/opt-compression.json", "ids": "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/res/default/ids_map", "definedIds": "/Users/<USER>/github/LingXia/lingxia-sdk/harmony/lingxia/build/default/intermediates/res/default/ids_map/id_defined.json", "definedSysIds": "/Users/<USER>/Library/OpenHarmony/command-line-tools/sdk/default/hms/toolchains/id_defined.json"}